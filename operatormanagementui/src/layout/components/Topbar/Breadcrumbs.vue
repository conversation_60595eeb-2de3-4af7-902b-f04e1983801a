<template>
  <el-breadcrumb
    separator="/"
    class="breadcrumb"
    :class="{ mobile: device === 'mobile' }"
  >
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbs"
      :key="index"
      :class="{ no_link: index === breadcrumbs.length - 1 }"
      :to="index < breadcrumbs.length - 1 ? item.path : ''"
    >
    <!-- item.path !== '/' || item.path !== '/home' || (homeFlag && (item.path === '/' || item.path === '/home')) -->
      <span v-if="(item.path !== '/' && item.path !== '/home') || (homeFlag && (item.path === '/' || item.path === '/home'))">{{ item.meta.title }}</span>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script lang="ts">
// import { rolesShowArr } from '@/utils'
import { defineComponent, computed, ref, onBeforeMount, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  setup() {
    const store = useStore()
    const device = computed(() => store.state.app.device)
    const router = useRouter()
    const route = router.currentRoute // 这里不使用useRoute获取当前路由，否则下面watch监听路由的时候会有警告
    const breadcrumbs = ref([])
    // const showArr = rolesShowArr()
    const homeFlag = computed(() => store.state.menu.menus.some(menu => menu.url === '/home'))
    // const roles = store.getters?.account?.userinfo?.roles || []

    const getBreadcrumbs = route => {
      const home = [{ path: '/', meta: { title: 'Dashboard' }}]
      if (route.name === 'home') {
        return home
      } else {
        const matched = route.matched.filter(
          item => !!item.meta && !!item.meta.title
        )

        return [...matched]
      }
    }

    onBeforeMount(() => {
      breadcrumbs.value = getBreadcrumbs(route.value)
    })

    watch(route, newRoute => {
      breadcrumbs.value = getBreadcrumbs(newRoute)
    })

    return {
      // roles,
      homeFlag,
      device,
      breadcrumbs
    }
  }
})
</script>

<style lang="scss" scoped>
.breadcrumb {
  margin-left: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  &:deep(a),
  &:deep(.is-link) {
    font-weight: normal;
  }
  &:deep(.el-breadcrumb__item) {
    // float: none;
  }
  .no_link {
    &:deep(.el-breadcrumb__inner) {
      color: #97a8be !important;
    }
  }
  &.mobile {
    display: none;
  }
}
</style>
