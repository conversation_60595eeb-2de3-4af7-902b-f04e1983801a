<template>
  <div>
    <div @click="dialogVisible = true">锁定屏幕</div>
    <el-dialog
      v-model="dialogVisible"
      title="锁定屏幕"
      width="640px"
      append-to-body
    >
      <Avatar />
      <el-form ref="lockForm" :model="lockModel" :rules="lockRules">
        <el-form-item label="锁屏密码" prop="password">
          <el-input
            v-model.trim="lockModel.password"
            type="password"
            autocomplete="off"
            @keyup.enter="submitForm"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            size="default"
            class="submit-btn"
            type="primary"
            @click="submitForm"
          >
            锁定屏幕
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import Avatar from '@/components/Avatar/index.vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  components: {
    Avatar,
  },
  setup() {
    const router = useRouter()
    const store = useStore()
    const state = reactive({
      dialogVisible: false,
      lockForm: null,
      lockModel: {
        password: '',
      },
      lockRules: {
        password: [{ required: true, message: '请输入锁屏密码' }],
      },
      submitForm: () => {
        state.lockForm.validate((valid) => {
          if (!valid) return false
          // 对密码加密并跟token保存在一起
          store.dispatch('app/setScreenCode', state.lockModel.password)
          if (process.env.NODE_ENV !== 'development') {
            store.commit('setRefreshalg', true)
          }
          // 跳转到锁屏页面
          router.push('/lock?redirect=' + router.currentRoute.value.fullPath)
        })
      },
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss">
.lock-modal[aria-modal] {
  max-width: 90%;
}
</style>
<style lang="scss" scoped>
.submit-btn {
  width: 100%;
}
</style>
