 .centerflex {
    display: flex;
    justify-content: center;
    align-items: center;
 }

  /*水平居中 - 换行*/
  .centerflex-w {
    display: flex;
    justify-content: center;
  }
  .centerflex-w-wrap {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }
  /*垂直居中 - 换行*/
  .centerflex-h {
    display: flex;
    align-items: center;
  }
  .centerflex-h-wrap {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

 // 按钮
 .btn-module {
     width: 100%;
     height: 90px;
     position: fixed;
     left: 0;
     bottom: 0;
     box-shadow: 0 0.02rem 0.12rem rgba(0, 0, 0, 0.07);

     .btn {
      width: 3.2rem;
     }
 }

.pd-w10 {
  padding: 0 0.1rem;
  box-sizing: border-box;
}
.none-warp-text-auto {
  // 单行文本溢出省略
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.footer-flex-end {
  display: flex;
  justify-content: flex-end
}
.el-transfer-panel {
  width: calc((100% - 170px) / 2) !important;
  height: 400px;
  // .el-checkbox__label{
  //   width: 70%;
  //   word-break: break-all !important;
  //   word-wrap: break-word !important;
  //   white-space: pre-wrap !important;
  // }
}
.el-transfer-panel__body {
  height: calc(100% - 42px) !important;
}
.el-transfer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center
}
.el-transfer-panel__filter {
  width: calc(100% - 30px) !important;
}
.el-transfer-panel__item.el-checkbox .el-checkbox__label {
  overflow: visible !important;
}