// @tailwind base;
// @layer base {
//     img {
//         border: none;
//     }
// }
@tailwind components;
@tailwind utilities;
@layer utilities {
    .mg-b-22-px{
        margin-bottom: 22px;
    }
    .mt-6-px{
        margin-top: 6px;
    }
    .editBtnBlue {
        color: #409eff;
        cursor: pointer; /*小手*/
    }
    /*通过 编辑表单 模块*/
    .common-form-module {
        background: #fff;
        padding: 20px;
        box-sizing: border-box;
        border-radius: 10px;
    }
    .min-w-50-px{
        min-width: 50px;
    }
    .min-w-60-px{
        min-width: 60px;
    }
    .min-w-70-px{
        min-width: 70px;
    }
    .min-w-80-px{
        min-width: 80px;
    }
    .min-w-90-px{
        min-width: 90px;
    }
    .min-w-100-px{
        min-width: 100px;
    }
    .min-w-110-px{
        min-width: 110px;
    }
    .order-management-details-bottom {
        width: 100%;
        .bottomBox {
          width: 100%;
          margin-top: 30px;
          display: flex;
          &:first-child {
            margin-top: 10px;
          }
          span {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            border: 1px solid #ccc;
            margin-top: 3px;
          }
          div {
            margin-left: 10px;
            p {
              margin: 0;
              color: #666;
            }
          }
        }
    }
    .font-color-000 {
        color: #000;
    }
    .ft-18-px {
        font-size: 18px
    }
    .el-button {
        font-family: 'Microsoft YaHei';
    }
}