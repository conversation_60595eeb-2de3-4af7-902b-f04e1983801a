<template>
  <div class="w-full">
    <!-- crfFieldType: 1, 文件集合
    crfFieldControl 
    1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件, 
    5 = 数字控件, 6 = 日期控件, 7 = 时间控件,
    8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件, 11 = 年月控件 -->

    <!-- 图片上传 -->
    <div v-if="item.crfFieldControl === 2 || item.crfFieldType === 1">
      <el-upload
        v-loading="item.loading"
        v-bind="$attrs"
        v-model:file-list="item.fileList"
        accept="image/jpg,image/jpeg,image/png,image/bmp,image/gif,image/jfif"
        multiple
        action="#"
        :http-request="uploadFile"
        :limit="15"
        list-type="picture-card"
      >
        <el-icon><Plus /></el-icon>
        <template #file="{ file }">
          <div v-loading="!file?.thumbUrl" style="width: 100%">
            <img
              class="el-upload-list__item-thumbnail"
              :src="file.thumbUrl"
              alt=""
            />
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-preview"
                @click="handlePreview(file)"
              >
                <el-icon><Search /></el-icon>
              </span>
              <span
                class="el-upload-list__item-delete"
                @click="handleRemove(file)"
              >
                <el-icon><Delete /></el-icon>
              </span>
            </span>
          </div>
        </template>
      </el-upload>
    </div>
    <!-- 单行文本控件 -->
    <div v-if="item.crfFieldControl === 3">
      <el-input
        v-model.trim="item.fieldValue"
        v-bind="$attrs"
        maxlength="999"
        placeholder="请输入"
        class="inputSingeLine"
        clearable
      >
        <template
          #suffix
          v-if="
            item.dctQuestUnit &&
            item.dctQuestUnit != null &&
            item.dctQuestUnit != ''
          "
        >
          <span> {{ item.dctQuestUnit }}</span>
        </template>
      </el-input>
    </div>
    <!-- 多行文本控件 -->
    <div v-if="item.crfFieldControl === 4">
      <el-input
        v-model.trim="item.fieldValue"
        v-bind="$attrs"
        maxlength="3999"
        class="inputMultiLine"
        :rows="5"
        type="textarea"
        placeholder="请输入"
        clearable
      />
    </div>
    <!-- 数字控件 -->
    <div v-if="item.crfFieldControl === 5">
      <el-input
        maxlength="15"
        v-bind="$attrs"
        v-model.trim="item.fieldValue"
        class="inputNumber"
        @input="changeNumber"
      >
        <template
          #suffix
          v-if="
            item.dctQuestUnit &&
            item.dctQuestUnit != null &&
            item.dctQuestUnit != ''
          "
        >
          <span> {{ item.dctQuestUnit }}</span>
        </template>
      </el-input>
    </div>
    <!-- 日期控件 年月日 支持uk-->
    <div v-if="item.crfFieldControl === 6">
      <div class="divDate">
        <el-form
          ref="checkForm"
          :model="item"
          :rules="checkRules"
          class="formInline"
        >
          <el-form-item prop="yearValue">
            <el-select
              v-bind="$attrs"
              v-model="item.yearValue"
              placeholder="年"
              class="selYear"
              @change="changeYear"
            >
              <el-option
                v-for="year in item.yearOptions"
                :key="year.value"
                :label="year.label"
                :value="year.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <div class="dateSplit">-</div>
          <el-form-item prop="monthValue">
            <el-select
              v-bind="$attrs"
              v-model="item.monthValue"
              placeholder="月"
              class="selMonth"
              @change="changeMonth"
            >
              <el-option
                v-for="month in item.monthOptions"
                :key="month.value"
                :label="month.label"
                :value="month.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <div class="dateSplit">-</div>
          <el-form-item prop="dayValue">
            <el-select
              v-bind="$attrs"
              v-model="item.dayValue"
              placeholder="日"
              class="selDay"
              @change="changeDay"
            >
              <el-option
                v-for="day in item.dayOptions"
                :key="day.value"
                :label="day.label"
                :value="day.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" link class="emptyBtn" @click="emptyDate">
            清空
          </el-button>
        </el-form>
      </div>
    </div>
    <!-- 时间控件 时分 -->
    <div v-if="item.crfFieldControl === 7">
      <div class="divTime">
        <el-form
          ref="checkForm"
          :model="item"
          :rules="checkRules"
          class="formInline"
        >
          <el-form-item prop="hourValue">
            <el-select
              v-bind="$attrs"
              v-model="item.hourValue"
              placeholder="时"
              class="selHour"
              @change="changeHour"
            >
              <el-option
                v-for="hour in item.hourOptions"
                :key="hour.value"
                :label="hour.label"
                :value="hour.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <div class="dateSplit">:</div>
          <el-form-item prop="minValue">
            <el-select
              v-bind="$attrs"
              v-model="item.minValue"
              placeholder="分"
              class="selMin"
              @change="changeMin"
            >
              <el-option
                v-for="min in item.minOptions"
                :key="min.value"
                :label="min.label"
                :value="min.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" link class="emptyBtn" @click="emptyTime">
            清空
          </el-button>
        </el-form>
      </div>
    </div>
    <!-- 时间控件 年月 -->
    <div v-if="item.crfFieldControl === 11">
      <div class="divDate">
        <el-form
          ref="checkForm"
          :model="item"
          :rules="checkRules"
          class="formInline"
        >
          <el-form-item prop="yearValue">
            <el-select
              v-bind="$attrs"
              v-model="item.yearValue"
              placeholder="年"
              class="selYear"
              @change="changeYear"
            >
              <el-option
                v-for="year in item.yearOptions"
                :key="year.value"
                :label="year.label"
                :value="year.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <div class="dateSplit">-</div>
          <el-form-item prop="monthValue">
            <el-select
              v-bind="$attrs"
              v-model="item.monthValue"
              placeholder="月"
              class="selMonth"
              @change="changeMonth"
            >
              <el-option
                v-for="month in item.monthOptions"
                :key="month.value"
                :label="month.label"
                :value="month.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" link class="emptyBtn" @click="emptyDate">
            清空
          </el-button>
        </el-form>
      </div>
    </div>
    <!-- 时间控件 年月日时分 -->
    <div v-if="item.crfFieldControl === 8">
      <div>
        <el-form ref="checkForm" :model="item" :rules="checkRules">
          <div class="divDate">
            <el-form-item prop="yearValue">
              <el-select
                v-bind="$attrs"
                v-model="item.yearValue"
                placeholder="年"
                class="selYear"
                @change="changeYear"
              >
                <el-option
                  v-for="year in item.yearOptions"
                  :key="year.value"
                  :label="year.label"
                  :value="year.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <div class="dateSplit">-</div>
            <el-form-item prop="monthValue">
              <el-select
                v-bind="$attrs"
                v-model="item.monthValue"
                placeholder="月"
                class="selMonth"
                @change="changeMonth"
              >
                <el-option
                  v-for="month in item.monthOptions"
                  :key="month.value"
                  :label="month.label"
                  :value="month.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <div class="dateSplit">-</div>
            <el-form-item prop="dayValue">
              <el-select
                v-bind="$attrs"
                v-model="item.dayValue"
                placeholder="日"
                class="selDay"
                @change="changeDay"
              >
                <el-option
                  v-for="day in item.dayOptions"
                  :key="day.value"
                  :label="day.label"
                  :value="day.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-button type="primary" link class="emptyBtn" @click="emptyDate">
              清空
            </el-button>
          </div>
          <div :style="{ height: item.lineWrap + 'px' }"></div>
          <div class="divTime">
            <el-form-item prop="hourValue">
              <el-select
                v-bind="$attrs"
                v-model="item.hourValue"
                placeholder="时"
                class="selHour"
                @change="changeHour"
              >
                <el-option
                  v-for="hour in item.hourOptions"
                  :key="hour.value"
                  :label="hour.label"
                  :value="hour.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <div class="dateSplit">:</div>
            <el-form-item prop="minValue">
              <el-select
                v-bind="$attrs"
                v-model="item.minValue"
                placeholder="分"
                class="selMin"
                @change="changeMin"
              >
                <el-option
                  v-for="min in item.minOptions"
                  :key="min.value"
                  :label="min.label"
                  :value="min.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-button type="primary" link class="emptyBtn" @click="emptyTime">
              清空
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 单选控件 -->
    <div v-if="item.crfFieldControl === 9">
      <el-select
        v-bind="$attrs"
        v-model="item.fieldValue"
        placeholder="请选择"
        class="selSingle"
        clearable
        @change="changeSingle"
      >
        <el-option
          v-for="itm in item.fieldItems"
          :key="itm.id"
          :label="itm.itemName"
          :value="itm.itemValue"
        ></el-option>
      </el-select>
    </div>
    <!-- 多选控件 -->
    <div v-if="item.crfFieldControl === 10">
      <el-select
        v-bind="$attrs"
        v-model="item.arrMulVals"
        placeholder="请选择"
        class="selMultiple"
        clearable
        @change="changeMultiple"
        multiple
      >
        <el-option
          v-for="itm in item.fieldItems"
          :key="itm.id"
          :label="itm.itemName"
          :value="itm.itemValue"
        ></el-option>
      </el-select>
    </div>
  </div>
  <el-dialog v-model="item.imgDialogVisible" top="7vh">
    <div class="imgShow">
      <img w-full h-full :src="item.dialogImageUrl" alt="图片预览" />
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount, ref } from 'vue'
// import {
//   monthArr,
//   hourArr,
//   minuteArr,
//   dayArr,
//   ThirtyDaysArr,
//   isLeapYear,
//   UKArr,
//   // clearHttpRequestingList
// } from '@/utils/debounce'
import { postQuestFile, deleteQuestFile } from '@/api/questionComponent'
import { Delete, Download, Plus, Search } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'Question', // 问卷
  inheritAttrs: false,
  components: {
    Delete, Download, Plus, Search
  },
  setup() {
    const state: any = reactive({
      // 测试控件
      item: {
        arrMulVals: [],
        lastArrMulVals: [],
        lineWrap: 10, // 外层调用组件可赋值，用于年月日与时分换行之间的间距，方便外部表单布局一致
        questId: '',
        yearValue: '',
        monthValue: '',
        dayValue: '',
        hourValue: '',
        minValue: '',
        yearOptions: [],
        monthOptions: [],
        dayOptions: [],
        hourOptions: [],
        minOptions: [],
        dialogImageUrl: '',
        imgDialogVisible: false,
        loading: false,
        children: [],
        clarify: {
          clarifyCount: 0,
          clarifyCloseCount: 0,
          totalClarifyCloseCount: 0,
        },
        crfFieldControl: 1,// 9
        crfFieldType: 4,
        crfItemId: '',
        datas: [],
        dctCode: '',
        dctDateControlDefault: null,
        dctQuestUnit: '',
        dischargeStandard: 0,
        fieldDescription: '',
        fieldItems: [],
        fieldLabel: '',
        isAllowUK: 1,
        isFutureDate: 0,
        isReadOnly: 0,
        isRequired: 0,
        maximumChoice: null,
        maximumYear: 2099,
        minimumChoice: null,
        minimumYear: 1900,
        refDctCode: '',
        refItemValue: '',
        refType: 0,
        specialFieldType: 0,
        fieldValue: '',
        fieldValueStr: '',
        fieldDisplayValue: '',
        // 文件上传组件相关字段
        fileList: [], // 文件列表，初始化可加载，需要包含thumbUrl
      },
      checkForm: null,
      checkRules: reactive<FormRules<typeof item>>({
        yearValue: [{
          validator: (rule: any, value: any, callback: any) => {
            if (((state.item.monthValue != '' && state.item.monthValue != undefined) || (state.item.dayValue != '' && state.item.dayValue != undefined)) && (state.item.yearValue === '' || state.item.yearValue === undefined)) {
              callback(new Error('请选择'));
            }
            callback()
          }, trigger: 'blur'
        }],
        monthValue: [{
          validator: (rule: any, value: any, callback: any) => {
            if (((state.item.yearValue != '' && state.item.yearValue != undefined) || (state.item.dayValue != '' && state.item.dayValue != undefined)) && (state.item.monthValue === '' || state.item.monthValue === undefined)) {
              callback(new Error('请选择'));
            }
            callback()
          }, trigger: 'blur'
        }],
        dayValue: [{
          validator: (rule: any, value: any, callback: any) => {
            if (((state.item.yearValue != '' && state.item.yearValue != undefined) || (state.item.monthValue != '' && state.item.monthValue != undefined)) && (state.item.dayValue === '' || state.item.dayValue === undefined)) {
              callback(new Error('请选择'));
            }
            callback()
          }, trigger: 'blur'
        }],
        hourValue: [{
          validator: (rule: any, value: any, callback: any) => {
            if (((state.item.minValue != '' && state.item.minValue != undefined)) && (state.item.hourValue === '' || state.item.hourValue === undefined)) {
              callback(new Error('请选择'));
            }
            callback()
          }, trigger: 'blur'
        }],
        minValue: [{
          validator: (rule: any, value: any, callback: any) => {
            if (((state.item.hourValue != '' && state.item.hourValue != undefined)) && (state.item.minValue === '' || state.item.minValue === undefined)) {
              callback(new Error('请选择'));
            }
            callback()
          }, trigger: 'blur'
        }],
      }),
      validateDateType: () => {
        return new Promise((resolve, reject) => {
          state.checkForm.validate((valid) => {
            if (valid) {
              resolve(true);
            } else {
              resolve(false);
            }
          });
        })
      },
      // 数字空件
      changeNumber: (e) => {
        state.item.fieldValue = e.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
      },
      // 加载数据
      onLoad: () => {
        // 如果为图片
        if ((state.item.crfFieldType === 1 || state.item.crfFieldControl === 2) && state.item.fieldValue != null && state.item.fieldValue.length > 0) {
          state.item.fileList = []
          state.item.fieldValue.split(',').forEach((t, index) => {
            state.item.fileList.push({
              name: t,
              url: t,
              thumbUrl: t,
              dctPatientFileId: state.item.fieldDisplayValue.split(',')[index]
            })
          })
        }
        // 日期格式处理
        if (state.item.crfFieldControl === 6) {
          if (state.item.fieldValue != null && state.item.fieldValue.length > 0) {
            state.item.yearValue = state.item.fieldValue.split('-')[0];
            state.item.monthValue = state.item.fieldValue.split('-')[1];
            state.item.dayValue = state.item.fieldValue.split('-')[2];
          }
          state.generateYear();
          state.generateMonth();
          state.generateDay();
        }
        if (state.item.crfFieldControl === 7) {
          if (state.item.fieldValue != null && state.item.fieldValue.length > 0) {
            state.item.hourValue = state.item.fieldValue.split(':')[0];
            state.item.minValue = state.item.fieldValue.split(':')[1];
          }
          state.generateHour();
          state.generateMin();
        }
        if (state.item.crfFieldControl === 11) {
          if (state.item.fieldValue != null && state.item.fieldValue.length > 0) {
            state.item.yearValue = state.item.fieldValue.split('-')[0];
            state.item.monthValue = state.item.fieldValue.split('-')[1];
          }
          state.generateYear();
          state.generateMonth();
        }
        if (state.item.crfFieldControl === 8) {
          if (state.item.fieldValue != null && state.item.fieldValue.length > 0) {
            let date = state.item.fieldValue.split(',')[0];
            let time = state.item.fieldValue.split(',')[1];

            state.item.yearValue = date.split('-')[0];
            state.item.monthValue = date.split('-')[1];
            state.item.dayValue = date.split('-')[2];
            state.item.hourValue = time.split(':')[0];
            state.item.minValue = time.split(':')[1];
          }

          state.generateYear();
          state.generateMonth();
          state.generateDay();
          state.generateHour();
          state.generateMin();
        }
        // 多选处理
        if (state.item.crfFieldControl === 10) {
          if (state.item.fieldValue != null && state.item.fieldValue.length > 0) {
            let arrVals = state.item.fieldValue.split(',');
            let arrLabs = state.item.fieldValueStr.split(',');
            state.item.arrMulVals = state.item.fieldValue.split(',');
            state.item.lastArrMulVals = state.item.arrMulVals;
          }
        }
      },
      changeSingle: (val) => {
        state.item.fieldValue = val;
        // state.item.fieldValueStr = state.item.fieldItems.filter(t => t.itemValue === val)[0].itemName;
      },
      changeMultiple: (itms) => {
        // 互斥特殊逻辑处理
        let mutualExculsionItem = state.item.fieldItems.filter(t => t.mutualExclusion === true);
        if (mutualExculsionItem.length > 0) {
          let curValue = itms.filter(t => !state.item.lastArrMulVals.includes(t)); // 找出差异项
          // 当前项为增加项
          if (itms.length > state.item.lastArrMulVals.length) {
            // 如果当前项为排他项，其它选项不需要勾选
            if (curValue[0] === mutualExculsionItem[0].itemValue) {
              itms = curValue
            }
            // 如果当前项不为排他项，且已经选择了排他项，需要将排他项去除
            else if (itms.includes(mutualExculsionItem[0].itemValue)) {
              itms = itms.filter(t => t != mutualExculsionItem[0].itemValue);
            }
          }
        }
        state.item.fieldValueStr = '';
        state.item.fieldValue = itms.join(',');
        state.item.arrMulVals = itms;
        state.item.lastArrMulVals = itms;
        // for (let i = 0; i < itms.length; i++) {
        //   let findItm = state.item.fieldItems.filter(t => t.itemValue === itms[i]);
        //   if (findItm.length > 0) {
        //     state.item.fieldValueStr += ',' + findItm[0].itemName;
        //   }
        // }
        // if (state.item.fieldValueStr.length > 0) {
        //   state.item.fieldValueStr = state.item.fieldValueStr.substr(1)
        // }
      },
      // 文件相关处理
      uploadFile: (file) => {
        if (state.item.questId) {
          const formData = new FormData();
          formData.append('CheckImageFiles', file.file);
          postQuestFile(state.item.questId, state.item.dctCode, formData).then(res => {
            // 后置对象处理
            // state.item.fieldValueStr += res?.dctPatientFileId;
            // state.item.fieldValue += res.thumbUrl;
            let curFile = state.item.fileList.filter(t => t.uid == file.file.uid)[0];
            curFile["thumbUrl"] = res.thumbUrl;
            curFile["dctPatientFileId"] = res.dctPatientFileId;
            state.item.fieldValueStr = state.item.fileList.map(a => a.dctPatientFileId).join(',');
            state.item.fieldValue = state.item.fileList.map(a => a.thumbUrl).join(',');
          })
        }
      },
      // 图片预览
      handlePreview: (file) => {
        state.item.dialogImageUrl = file.url
        state.item.imgDialogVisible = true
      },
      // 图片预览
      handleRemove: (file) => {
        if (file?.dctPatientFileId) {
          ElMessageBox.confirm(
            '确认删除？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
            }
          )
            .then(() => {
              deleteQuestFile(file?.dctPatientFileId).then(res => {
                // 从当前文件列表中删除当前文件
                state.item.fileList = state.item.fileList.filter(t => t.uid != file.uid);
                // 后置对象处理
                state.item.fieldValueStr = state.item.fileList.map(a => a.dctPatientFileId).join(',');
                state.item.fieldValue = state.item.fileList.map(a => a.thumbUrl).join(',');
              })
            })
        }
      },
      // 日期相关
      emptyDate: () => {
        // 清除日期校验
        state.checkForm.resetFields();
        state.item.yearValue = '';
        state.item.monthValue = '';
        state.item.dayValue = '';
        state.generateDay();
        state.changeDateTypeResult();
      },
      emptyTime: () => {
        // 清除时间校验
        state.checkForm.resetFields();
        state.item.hourValue = '';
        state.item.minValue = '';
        state.changeDateTypeResult();
      },
      // 获取最终日期结果
      changeDateTypeResult: () => {
        // 先校验未来日期，如果不支持未来日期，则需要清空当前值
        if (state.item.isFutureDate === 0) {
          if (state.item.monthOptions.length > 0 && parseInt(state.item.monthValue) > parseInt(state.item.monthOptions[state.item.monthOptions.length - 1].value)) {
            state.item.monthValue = '';
          }
          if (state.item.dayOptions.length > 0 && parseInt(state.item.dayValue) > parseInt(state.item.dayOptions[state.item.dayOptions.length - 1].value)) {
            state.item.dayValue = '';
          }
        }
        if (state.item.crfFieldControl === 6) {
          if (state.item.yearValue != '' && state.item.yearValue != undefined && state.item.monthValue != '' && state.item.monthValue != undefined && state.item.dayValue != '' && state.item.dayValue != undefined) {
            state.item.fieldValue = state.item.yearValue + '-' + state.item.monthValue + '-' + state.item.dayValue;
          } else {
            state.item.fieldValue = '';
          }
        } else if (state.item.crfFieldControl === 11) {
          if (state.item.yearValue != '' && state.item.yearValue != undefined && state.item.monthValue != '' && state.item.monthValue != undefined) {
            state.item.fieldValue = state.item.yearValue + '-' + state.item.monthValue
          } else {
            state.item.fieldValue = '';
          }
        } else if (state.item.crfFieldControl === 8) {
          if (state.item.yearValue != '' && state.item.yearValue != undefined && state.item.monthValue != '' && state.item.monthValue != undefined && state.item.dayValue != '' && state.item.dayValue != undefined) {
            state.item.fieldValue = state.item.yearValue + '-' + state.item.monthValue + '-' + state.item.dayValue;
          } else {
            state.item.fieldValue = '';
          }
          if (state.item.hourValue != '' && state.item.hourValue != undefined && state.item.minValue != '' && state.item.minValue != undefined) {
            state.item.fieldValue += ',' + state.item.hourValue + ':' + state.item.minValue;
          } else {
            if (state.item.fieldValue.length > 0) {
              state.item.fieldValue += ',';
            }
          }
        } else if (state.item.crfFieldControl === 7) {
          if (state.item.hourValue != '' && state.item.hourValue != undefined && state.item.minValue != '' && state.item.minValue != undefined) {
            state.item.fieldValue = state.item.hourValue + ':' + state.item.minValue;
          } else {
            state.item.fieldValue = '';
          }
        }
      },
      changeYear: (val) => {
        state.generateMonth();
        state.generateDay();
        state.changeDateTypeResult();
      },
      changeMonth: (val) => {
        state.generateDay();
        state.changeDateTypeResult();
      },
      changeDay: (val) => {
        state.changeDateTypeResult();
      },
      changeHour: (val) => {
        state.generateMin();
        state.changeDateTypeResult();
      },
      changeMin: (val) => {
        state.changeDateTypeResult();
      },
      // 生成年数据源
      generateYear: () => {
        if (state.item.isAllowUK) {
          state.item.yearOptions.push({
            value: 'UK',
            label: 'UK'
          });
        }
        let limitYear = state.item.maximumYear ? state.item.maximumYear : 2099;
        if (state.item.isFutureDate === 0 && limitYear > new Date().getFullYear()) {
          limitYear = new Date().getFullYear();
        }
        for (let i = state.item.minimumYear ? state.item.minimumYear : 1900; i <= limitYear; i++) {
          state.item.yearOptions.push({
            value: i + '',
            label: i + ''
          });
        }
      },
      // 生成月数据源
      generateMonth: () => {
        state.item.monthOptions = [];
        if (state.item.isAllowUK) {
          state.item.monthOptions.push({
            value: 'UK',
            label: 'UK'
          });
        }
        if (state.item.yearValue != 'UK') {
          let limitMonth = 12;
          if (state.item.isFutureDate === 0 && parseInt(state.item.yearValue) === new Date().getFullYear()) {
            limitMonth = new Date().getMonth() + 1;
          }
          for (let i = 1; i <= limitMonth; i++) {
            state.item.monthOptions.push({
              value: i <= 9 ? ('0' + i) : (i + ''),
              label: i <= 9 ? ('0' + i) : (i + '')
            });
          }
        } else {
          state.item.monthValue = 'UK'
        }
      },
      // 日数据源
      generateDay: () => {
        state.item.dayOptions = [];
        if (state.item.isAllowUK) {
          state.item.dayOptions.push({
            value: 'UK',
            label: 'UK'
          });
        }
        let limitDay;
        if (state.item.yearValue != 'UK' && state.item.monthValue != 'UK' && state.item.yearValue != '' && state.item.monthValue != '' && state.item.yearValue != undefined && state.item.monthValue != undefined) {
          if (state.item.isFutureDate === 0) {
            if (parseInt(state.item.yearValue) === new Date().getFullYear() && parseInt(state.item.monthValue) === new Date().getMonth() + 1) {
              limitDay = new Date().getDate();
            }
          }
          let dayCount = new Date(state.item.yearValue, state.item.monthValue, 0).getDate();
          if (state.item.dayValue > dayCount) {
            state.item.dayValue = '';
          }
          if (limitDay === undefined) {
            limitDay = dayCount;
          }
          for (let i = 1; i <= limitDay; i++) {
            state.item.dayOptions.push({
              value: i <= 9 ? ('0' + i) : (i + ''),
              label: i <= 9 ? ('0' + i) : (i + '')
            });
          }
        } else if (['01', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'].indexOf(state.item.monthValue) > -1) {
          if (['04', '06', '09', '11'].indexOf(state.item.monthValue) > -1) {
            if (limitDay === undefined) {
              limitDay = 30;
            }
            for (let i = 1; i <= limitDay; i++) {
              state.item.dayOptions.push({
                value: i <= 9 ? ('0' + i) : (i + ''),
                label: i <= 9 ? ('0' + i) : (i + '')
              });
            }
            if (state.item.dayValue != '' && state.item.dayValue != undefined && parseInt(state.item.dayValue) > 30) {
              state.item.dayValue = '';
            }
          } else {
            if (limitDay === undefined) {
              limitDay = 31;
            }
            for (let i = 1; i <= 31; i++) {
              state.item.dayOptions.push({
                value: i <= 9 ? ('0' + i) : (i + ''),
                label: i <= 9 ? ('0' + i) : (i + '')
              });
            }
            if (state.item.dayValue != '' && state.item.dayValue != undefined && parseInt(state.item.dayValue) > 31) {
              state.item.dayValue = '';
            }
          }
        }
        else if (state.item.yearValue == 'UK' || state.item.monthValue == 'UK') {
          state.item.dayValue = 'UK';
        }
        if (state.item.dayOptions.filter(t => t.value != 'UK').length == 0 && state.item.yearValue != 'UK' && state.item.monthValue != 'UK') {
          state.item.dayValue = '';
        }
      },
      // 时数据源
      generateHour: () => {
        state.item.hourOptions = [];
        if (state.item.isAllowUK) {
          state.item.hourOptions.push({
            value: 'UK',
            label: 'UK'
          });
        }
        for (let i = 0; i < 24; i++) {
          state.item.hourOptions.push({
            value: i <= 9 ? ('0' + i) : (i + ''),
            label: i <= 9 ? ('0' + i) : (i + '')
          });
        }
      },
      // 分数据源
      generateMin: () => {
        state.item.minOptions = [];
        if (state.item.isAllowUK) {
          state.item.minOptions.push({
            value: 'UK',
            label: 'UK'
          });
        }
        if (state.item.hourValue != 'UK') {
          for (let i = 0; i < 60; i++) {
            state.item.minOptions.push({
              value: i <= 9 ? ('0' + i) : (i + ''),
              label: i <= 9 ? ('0' + i) : (i + '')
            });
          }
        } else {
          state.item.minValue = 'UK'
        }
      },
      // 只能输入数字
      keyUpNum: (item) => {
        item.fieldValue = item.fieldValue.replace(
          /^\D*(\d*(?:\.\d{0,2})?).*$/g,
          '$1'
        )
      },
      // 年月日 - 时分 UK
      dateOptions: [
        {
          value: '2024',
          label: '2024',
          children: [
            {
              value: '01',
              label: '01',
              children: [
                {
                  value: '02',
                  label: '02',
                },]
            }]
        }],
      //
      handleChange: (e) => {
        console.log(e);
      }
    })

    onBeforeMount(() => {
      //
      // state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.imgShow {
  overflow: auto;
  max-height: 75vh;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #e9e9e9;
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: #c7c7c7;
    border-radius: 5px;
  }
}
.inputSingeLine,
.inputNumber,
.inputMultiLine {
  min-width: 320px;
}
:deep(.el-input-number) {
  .el-input__inner {
    text-align: right;
  }
}
.selYear {
  min-width: 100px;
}
.selMonth,
.selDay,
.selHour,
.selMin {
  min-width: 70px;
}
.divDate,
.divTime,
.divDatetime {
  display: inline-flex;
}
.dateSplit {
  height: inherit;
  width: 30px;
  padding: 5px 0px 0 0px;
  text-align: center;
}
.selSingle,
.selMultiple {
  min-width: 320px;
}
.emptyBtn {
  margin-left: 10px;
  margin-bottom: 18px;
}
.formInline {
  display: inline-flex;
}
</style>