
<template>
  <div>
    <!-- 上传单张图片 drag是否支持拖拽 -->
    <el-upload
      v-if="uploadImgFalg"
      class="avatar-uploader"
      accept="image/*"
      action="#"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      :http-request="uploadRequest"
    >
      <img v-if="imageUrl" :src="imageUrl" class="avatar">
      <el-icon v-else class="el-icon-plus avatar-uploader-icon">
        <Plus />
      </el-icon>
    </el-upload>

    <!-- 上传多个文件 list-type:文件列表的类型 -->
    <div v-if="uploadFileFalg" style="maxwidth: 360px">
      <el-upload
        ref="uploadFileRef"
        class="upload-demo"
        multiple
        :limit="limit"
        v-model:file-list="fileList"
        :accept="accept"
        action="#"
        :auto-upload="autoUpload"
        :before-upload="beforeFileUpload"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :http-request="requestFun"
        :disabled="disabled"
        :class="{'upload-file-btn': !uploadBtnShow}"
      >
        <template #trigger>
          <slot name="uploadBtn"></slot>
          <el-button v-if="uploadBtnShow && !customFlag" size="small" type="primary">选取文件</el-button>
        </template>
        <template #file="{file}">
          <slot name="fileName" :file="file" />
        </template>
        <el-button
          style="margin-left: 10px; display: none"
          size="small"
          type="success"
          :loading="loading"
          :before-upload="beforeAvatarUpload"
          @click="submitUpload"
        >上传到服务器</el-button>
        <!-- <template #tip>
          <div class="el-upload__tip">
            一次最多只能上传{{ limit }}个文件 -->
        <!-- 一次最多只能上传{{ limit }}个文件，且不超过 {{ fileSize * 1024 }}kb -->
        <!-- </div>
        </template> -->
      </el-upload>
    </div>
  </div>
</template>

<script lang="ts" >
import { defineComponent, reactive, toRefs, onUpdated, onMounted } from 'vue'
import { useStore } from 'vuex'
import { Plus } from '@element-plus/icons-vue'
import type {
  UploadFile,
  ElUploadProgressEvent,
  ElFile,
} from 'element-plus/es/components/upload/src/upload.type'

export default defineComponent({
  name: 'Upload',
  components: {
    Plus,
  },
  props: {
    // 上传文件类型
    accept: {
      type: String,
      default: '',
    },
    // 是否自定义上传文件
    customFlag: {
      type: Boolean,
      default: false,
    },
    // 是否展示 上传图片
    uploadImgFalg: {
      type: Boolean,
      default: false,
    },
    // 是否展示上传多文件
    uploadFileFalg: {
      type: Boolean,
      default: false,
    },
    // 一次最多上传几个文件
    limit: {
      type: Number,
      default: 1,
    },
    fileListArr: {
      type: Array,
    },
    // 不能超过多少MB
    fileSize: {
      type: Number,
      default: 2,
    },
    uploadImg: {
      type: String,
      default: '',
    },
    uploadFileUrl: {
      type: String,
      default: '',
    },
    // 上传接口
    request: {
      type: Function,
      default: () => {},
    },
    requestFun: {
      type: Function,
      default: () => {},
    },
    autoUpload: {
      type: Boolean,
      default: true,
    },
    // 回传的FormData
    customInformationFormData: {
      type: Object,
    },
    deletefile: {
      type: Function,
      default: () => {},
    },
    // 上传文件类型数组
    beforeFileUploadType: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    uploadBtnShow: {
      type: Boolean,
      default: true,
    }
  },
  setup(props) {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      imageUrl: '', // 回显图片url
      uploadFileRef: null,
      loading: false,
      beforeFileUploadTypeFlag: false,
      fileList: [
        // {
        //   url: '',
        //   name: ''
        // }
      ],
      // 上传图片的
      handleAvatarSuccess: (res: ElUploadProgressEvent, file: UploadFile) => {
        state.imageUrl = URL.createObjectURL(file.raw)
      },
      // 上传图片前 验证图片格式和大小等
      beforeAvatarUpload: (file: ElFile) => {
        const fileName = file.name
        const pos = fileName.lastIndexOf('.')
        const lastName = fileName.substring(pos, fileName.length)
        const limitFileType = lastName.toLowerCase()
        state.beforeFileUploadTypeFlag = false
        let pictureSize = true // 图片大小
        if (props?.beforeFileUploadType?.length) {
          props.beforeFileUploadType.forEach((item) => {
            if (item === limitFileType) {
              state.beforeFileUploadTypeFlag = true
              return
            }
          })
        } else {
          state.beforeFileUploadTypeFlag = true
        }
        if (!state.beforeFileUploadTypeFlag) {
          pictureSize = false
          ElMessage.error(
            `文件仅支持${props.beforeFileUploadType + ''}格式，请重新上传!`
          )
        }
        if (pictureSize) {
          const isLt2M = file.size / 1024 / 1024 < props.fileSize
          if (!isLt2M) {
            state.beforeFileUploadTypeFlag = false
            ElMessage.error(
              `上传图片大小不能超过 ${props.fileSize * 1024}KB!`
            )
          }
        }
        return state.beforeFileUploadTypeFlag // isJPG &&
      },
      // 上传图片前 验证图片格式和大小等
      beforeFileUpload: (file: ElFile) => {
        const fileName = file.name
        const pos = fileName.lastIndexOf('.')
        const lastName = fileName.substring(pos, fileName.length)
        const limitFileType = lastName.toLowerCase()
        state.beforeFileUploadTypeFlag = false
        if (props?.beforeFileUploadType?.length) {
          props.beforeFileUploadType.forEach((item) => {
            if (item === limitFileType) {
              state.beforeFileUploadTypeFlag = true
              return
            }
          })
        } else {
          state.beforeFileUploadTypeFlag = true
        }
        // const isJPG = file.type === "image/jpeg";
        if (!state.beforeFileUploadTypeFlag) {
          ElMessage.error(
            `文件仅支持${props.beforeFileUploadType + ''}格式，请重新上传!`
          )
        }
        return state.beforeFileUploadTypeFlag // isJPG &&
      },

      /**
       * 文件上传请求
       * @param fileObj
       * 覆盖默认的XHR行为，允许您实现自己的上传文件的请求
       */
      uploadRequest: (fileObj) => {
        const myFormDataObj = new FormData()
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        state.loading = true
        props
          .request(studyId, myFormDataObj)
          .then((res) => {
            state.loading = false
            props.requestFun(res)
            // 调用回调
            fileObj.onSuccess(fileObj)
          })
          .catch(() => {
            state.loading = false
          })
      },

      // 上传文件的
      submitUpload: () => {
        state.uploadFileRef.submit()
      },
      // 移除文件时的事件
      handleRemove: (file) => {
        props.deletefile(file)
        if (state.beforeFileUploadTypeFlag) ElMessage(`您已经移除${file.name}文件`)
      },
      // 点击文件列表中已上传的文件时的钩子
      handlePreview: (file) => {
        window.open(file.url)
      },
      // 超出个数限制的事件
      handleExceed: () => {
        ElMessage.warning(`一次最多只能上传${props.limit}个文件`)
      }
    })

    onUpdated(() => {
      if (props.uploadImg && !state.imageUrl) {
        state.imageUrl = props.uploadImg
      }
    })
    onMounted(() => {
      state.fileList = props.fileListArr
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scope>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.upload-file-btn {
  .el-upload {
    display: none;
  }
}
</style>
