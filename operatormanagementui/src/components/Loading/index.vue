<template>
  <div v-if="visible" class="loading-overlay">
    <div class="loading-spinner"></div>
    <p>{{ message }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
const visible = ref(false)
const message = ref('')

const open = (msg) => {
  message.value = msg
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(233, 242, 243, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  p {
    margin-top: 60px;
    font-size: 26px;
    color: #409eff;
  }
  @keyframes boxShadowOffset4 {
    0% {
      box-shadow: -38px -12px, -14px 0, 14px 0, 38px 0;
    }
    33% {
      box-shadow: -38px 0px, -14px -12px, 14px 0, 38px 0;
    }
    66% {
      box-shadow: -38px 0px, -14px 0, 14px -12px, 38px 0;
    }
    100% {
      box-shadow: -38px 0, -14px 0, 14px 0, 38px -12px;
    }
  }
  .loading-spinner {
    position: relative;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    color: #409eff;
    -webkit-animation: boxShadowOffset4 1s linear infinite alternate;
    animation: boxShadowOffset4 1s linear infinite alternate;
  }
}
</style>
