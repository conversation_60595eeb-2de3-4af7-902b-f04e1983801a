import axios from 'axios'
import store from '@/store'
// import router from '@/router'

const service = axios.create({
  baseURL:
    window?.TrialData_UI_Config?.VUE_APP_API_BASE_URL ||
    import.meta?.env?.VITE_APP_BASE_URL,
  timeout: 300000,
  withCredentials: true, //当前请求为跨域类型时是否在请求中协带cookie
})

// 拦截请求
service.interceptors.request.use(
  config => {
    const { authorization } = store.getters.app
    if (authorization) {
      config.headers['TrialAuth'] = authorization
    }
    // 用于加密
    if (store.state?.account?.userinfo?.studyId) {
      config.headers['DCTStudyId'] = store.state.account.userinfo.studyId
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 拦截响应
service.interceptors.response.use(
  // 响应成功进入第1个函数，该函数的参数是响应对象
  response => {
    // 导出时取文件名
    return response?.headers?.filedownloadname ? {
      headers: response.headers,
      data: response.data
    } : response.data
  },
  // 响应失败进入第2个函数，该函数的参数是错误对象
  async error => {
    // 如果响应码是 401 ，则请求获取新的 token
    // 响应拦截器中的 error 就是那个响应的错误对象
    if (error.response && error.response.status === 401) {
      // 校验是否有 refresh_token
      // const { authorization } = store.getters.app
      // if (!authorization) { //|| !authorization.refresh_token
        // const redirect = encodeURIComponent(window.location.href)
        // router.push(`/login?redirect=${redirect}`)
        // 返回主项目的登录页
        window.location.href = `${window.location.origin
          }/operatorui/#/login?flag=2`
        // 清除token
        store.dispatch('app/clearToken')
        store.commit('setStudyItem', '')
        // 清除标签栏
        store.dispatch('tags/delAllTags')
        // store.dispatch('app/clearToken')
        // 代码不要往后执行了
        return Promise.reject(error)
      // }
    }
    // const { Message, Details, details , message } = JSON.parse(error.request.responseText)
    // // 统一显示错误提示
    // ElMessage({
    //   message: Message || Details || details || message || error.message || '请求出错了',
    //   type: 'error',
    //   duration: 5 * 1000
    // })
    // console.dir(error) // 可在此进行错误上报
    ElMessage.error(error?.response?.data?.message || error?.response?.data?.detail || '')
    return Promise.reject(error)
  }
)

export default service
