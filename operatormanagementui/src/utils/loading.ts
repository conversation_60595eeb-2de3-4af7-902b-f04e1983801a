import { createApp } from 'vue'
import LoadingComponent from '@/components/Loading/index.vue'

let loadingInstance

const LoadingService = {
  open(message = '加载中...') {
    if (!loadingInstance) {
      const loadingApp = createApp(LoadingComponent)
      const mountNode = document.createElement('div')
      document.body.appendChild(mountNode)
      loadingInstance = loadingApp.mount(mountNode)
    }
    loadingInstance.open(message)
  },
  close() {
    if (loadingInstance) {
      loadingInstance.close()
    }
  },
}

export default LoadingService
