// 判断是不是瑞年 true 是 false 不是
export const isLeapYear = (year) => {
  if (year % 4 === 0 && year % 100 !== 0) {
      return true;
  } else if (year % 400 === 0) {
      return true;
  } else {
      return false;
  }
}
export const hourArr = [
  "00",
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
];
export const minuteArr = [
  "00",
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
  "24",
  "25",
  "26",
  "27",
  "28",
  "29",
  "30",
  "31",
  "32",
  "33",
  "34",
  "35",
  "36",
  "37",
  "38",
  "39",
  "40",
  "41",
  "42",
  "43",
  "44",
  "45",
  "46",
  "47",
  "48",
  "49",
  "50",
  "51",
  "52",
  "53",
  "54",
  "55",
  "56",
  "57",
  "58",
  "59",
];
export const monthArr = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
export const nowMonthArr = () => {
  return monthArr.filter((item: any) => item / 1 <= (new Date().getMonth() + 1) / 1);
}
export const dayArr = [
  "01",
  "02",
  "03",
  "04",
  "05",
  "06",
  "07",
  "08",
  "09",
  "10",
  "11",
  "12",
  "13",
  "14",
  "15",
  "16",
  "17",
  "18",
  "19",
  "20",
  "21",
  "22",
  "23",
  "24",
  "25",
  "26",
  "27",
  "28",
  "29",
  "30",
  "31",
];
export const nowDayArr = () => {
  return dayArr.filter(
    (item: any) => item / 1 <= new Date().getDate() / 1
  );
};
// 1 3 5 7 8 10 12 是31天
// 2是28 29
// 4 6 9 11月 30天
export const ThirtyDaysArr = ["04", "06", "09", "11"];
export const UKArr = ['UK']