/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xdc00 && code <= 0xdfff) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

/*数组扁平化*/
export  function flatten(arr) {
  return arr.reduce((pre, item) => {
    // 如果item是数组, 且这个数组至少有一个元素也是数组  ==> 先要对item进行降维
    if (Array.isArray(item) && item.some(cItem => Array.isArray(cItem))) {
      return pre.concat(flatten(item))
    }
    return pre.concat(item)
  }, [])
}

//节流函数
export function throttle (func,wait) {
  let timeout;
  return function() {
      let context = this;
      let args = arguments;
      if (!timeout) {
          timeout = setTimeout(() => {
              timeout = null;
              func.apply(context, args)
          }, wait)
      }
  }
}

//防抖
export const delay = (function () {
    let timer: any = 0
    return function (callback, ms) {
        clearTimeout(timer)
        timer = setTimeout(callback, ms)
    }
})()
/**
 * base64加密
 * @param str
 * @returns {string}
 */
 export function encode(str) {
  return window.btoa(unescape(encodeURIComponent(str)))
}

/**
 * base64解密
 * @param str
 * @returns {string}
 */
export function decode(str) {
  return decodeURIComponent(escape(window.atob(str)))
}

// 导出文件  file文件流  name：自定义名字，要带后缀
// 接口处加 responseType: 'blob'
export function downLoad(file, name) {
  const url = window.URL.createObjectURL(new Blob([file]),)
  const link = document.createElement('a')
  link.style.display = 'none'
  link.href = url
  link.setAttribute('download', name)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link) // 下载完成移除元素
  window.URL.revokeObjectURL(url) // 释放掉blob对象
}
// 下载跨域url文件
export function downloadFile(url, fileName) {
  const x = new XMLHttpRequest()
  x.open('GET', url, true)
  x.responseType = 'blob'
  x.onload = function() {
    const url = window.URL.createObjectURL(x.response)
    const elink = document.createElement('a')
    elink.href = url
    elink.target = '_self'
    // elink.setAttribute('download', row.fileTypeStr)
    elink.download = `${fileName || '附件'}`
    elink.style.display = 'none'
    document.body.appendChild(elink)
    setTimeout(() => {
      elink.click()
      document.body.removeChild(elink)
    }, 6)
  }
  x.send()
}

// 转为时分秒
export function hourMinutSecond(item) {
  const hour = Math.floor((item / 3600) % 24)
  const minute = Math.floor((item / 60) % 60)
  const second = Math.floor(item % 60)
  if (hour > 0) {
    item = `${hour}小时${minute}分${second}秒`
  } else {
    if (minute > 0) {
      item = `${minute}分${second}秒`
    } else {
      item = `${second}秒`
    }
  }
  return item
}

// 表格合并
/**
 * arr-表格数组
 * colFields-表格列配置
 * mergeProp-合并的数据
 * listArr-合并不超过的值(一般来说应该就有一个)
 */
export function tableDateChange(arr, columns, mergeProp, listArr) {
  const colFields = columns.map((item) => {
    return item.prop
  })
  const spanArr = []
  for (let i = 0; i < arr.length; i++) {
    const row = i
    if (row === 0) {
      // i 表示行 j表示列
      for (let j = 0; j < colFields.length; j++) {
        spanArr[i * colFields.length + j] = {
          rowspan: 1,
          colspan: 1,
        }
      }
    } else {
      for (let j = 0; j < colFields.length; j++) {
        // 当前和上一次的一样
        //  合并所有列的相同数据单元格
        // colFields[j] === 'name' ||
        // colFields[j] === 'time1'
        if (
          mergeProp.indexOf(colFields[j]) !== -1
        ) { // 指定合并哪些列
          // arr[row]['id'] !==
          // arr[row - 1]['id'] ||
          // arr[row]['time1'] !==
          // arr[row - 1]['time1']
          if (
            listArr.some((item) => { return arr[row][item] !== arr[row - 1][item] })
          ) {
            spanArr[row * colFields.length + j] = {
              rowspan: 1,
              colspan: 1,
            }
          } else if (
            arr[row][colFields[j]] ===
            arr[row - 1][colFields[j]]
          ) {
            const beforeItem = spanArr[(row - 1) * colFields.length + j]
            spanArr[row * colFields.length + j] = {
              rowspan: 1 + beforeItem.rowspan, // 合并几列
              colspan: 1, // 合并几行
            }
            beforeItem.rowspan = 0
            beforeItem.colspan = 0
          } else {
            // rowspan 和 colspan 都为1表格此单元格不合并
            spanArr[row * colFields.length + j] = {
              rowspan: 1,
              colspan: 1,
            }
          }
        }
      }
    }
  }
  // 对数据进行倒序
  const stack = []
  for (let i = 0; i < colFields.length; i++) {
    for (let j = 0; j < arr.length; j++) {
      // i 表示列 j表示行
      if (j === 0) {
        if (spanArr[j * colFields.length + i]?.rowspan === 0) {
          stack.push(spanArr[j * colFields.length + i])
        }
      } else {
        if (spanArr[j * colFields.length + i]?.rowspan === 0) {
          stack.push(spanArr[j * colFields.length + i])
        } else {
          stack.push(spanArr[j * colFields.length + i])
          while (stack.length > 0) {
            const pop = stack.pop()
            const len = stack.length
            spanArr[(j - len) * colFields.length + i] = pop
          }
        }
      }
    }
  }
  return spanArr
}

export function rolesShowArr() {
  return ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI','DM']
}
