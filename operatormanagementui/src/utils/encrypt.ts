import { encrypt, decrypt } from 'crypto-js/aes'
import { parse } from 'crypto-js/enc-utf8'
import pkcs7 from 'crypto-js/pad-pkcs7'
import ECB from 'crypto-js/mode-ecb'
import md5 from 'crypto-js/md5'
import UTF8 from 'crypto-js/enc-utf8'
import Base64 from 'crypto-js/enc-base64'

// 16位密钥
export const KEY_IV = {
  key: '_11111000001111@',
  iv: '@11111000001111_',
}

// AES加密和解密
export class AesEncryption {
  constructor(opt = KEY_IV) {
    const { key, iv } = opt
    if (key) {
      this.key = parse(key)
    }
    if (iv) {
      this.iv = parse(iv)
    }
  }

  get getOptions() {
    return {
      mode: ECB,
      padding: pkcs7,
      iv: this.iv,
    }
  }

  encryptByAES(cipherText) {
    return encrypt(cipherText, this.key, this.getOptions).toString()
  }

  decryptByAES(cipherText) {
    return decrypt(cipherText, this.key, this.getOptions).toString(UTF8)
  }
}

// Base64加密
export function encryptByBase64(cipherText) {
  return Base64.stringify(UTF8.parse(cipherText))
}

// Base64解密
export function decryptByBase64(cipherText) {
  return Base64.parse(cipherText).toString(UTF8)
}

// MD5加密，不可逆
export function encryptByMd5(password) {
  return md5(password).toString()
}
