import { DirectiveBinding } from 'vue';
import store from '@/store'
// 自定义权限指令
const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding;
    // 这里需要替换为实际获取用户权限的方法
    // console.log(store,'store')
    const userPermissions = store.state.studyItem.permissions || []
    if (value && value instanceof Array && value.length > 0) {
      const hasPermission = userPermissions.some((permission) => value.includes(permission));
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      // throw new Error('使用方式：v-permission=[\'admin\', \'editor\']');
    }
  }
};

export default permission;