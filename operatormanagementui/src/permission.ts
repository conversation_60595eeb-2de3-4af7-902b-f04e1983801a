import router from '@/router'
import store from '@/store'
import { TOKEN } from '@/store/modules/app' // TOKEN变量名

const getPageTitle = title => {
  const appTitle = store.getters.app.title
  if (title) {
    return `${title} - ${appTitle}`
  }
  return appTitle
}

// 白名单，里面是路由对象的name
const WhiteList = ['Login', 'forbidden', 'server-error', 'not-found', 'Lock']

// vue-router4的路由守卫不再是通过next放行，而是通过return返回true或false或者一个路由地址
router.beforeEach(async (to: any) => {
  // console.log(to)
  document.title = getPageTitle(!!to.meta && to.meta.title)

  if (WhiteList.includes(to.name)) {
    return true
  }
  if (!window.localStorage[TOKEN]) {
    return {
      name: 'Login',
      query: {
        redirect: to.fullPath, // redirect是指登录之后可以跳回到redirect指定的页面
      },
      replace: true,
    }
  } else {
    // 获取用户角色信息，根据角色判断权限
    let userinfo = store.getters.account.userinfo
    if (!userinfo) {
      try {
        // 获取用户信息
        userinfo = await store.dispatch('account/getUserinfo')
      } catch (err) {
        return false
      }
    }

    // 判断是否处于锁屏状态
    if (to.name !== 'Lock') {
      const { authorization } = store.getters.app
      if (!!authorization && !!authorization.screenCode) {
        return {
          name: 'Lock',
          query: {
            redirect: to.path,
          },
          replace: true,
        }
      }
    }
    // 检查是否有 /home 页面
    // console.log(store.state.menu.menus, 'check home', to.path)
    if (!store.state.menu.menus.some(menu => menu.url === '/home') && to.path === '/home') {
      // 如果菜单中没有 /home 页面，但仍然允许访问 /home
      // 不进行重定向，让用户直接访问 /home
      if (store.state.menu?.menus?.length > 0) {
        const firstMenuUrl = store.state.menu.menus[0].url
        // alert(`firstMenuUrl=${firstMenuUrl}`)
        if (firstMenuUrl !== '/study') {
          return { path: firstMenuUrl }
        } else {
          return true
        }
      }
      // alert('菜单中没有 /home 页面，但允许直接访问')
      return true
    }

    // 如果没有权限，跳转到403页面
    // if (
    //   !!to.meta &&
    //   !!to.meta.roles &&
    //   !to.meta.roles.includes(userinfo.role)
    // ) {
    //   return { path: '/403', replace: true }
    // }
  }
})
