<template>
  <el-config-provider :locale="locale">
    <router-view />
    <trial-dialog
      v-model="outLoginFlag.outLoginFlag"
      title="温馨提示"
      :my-dialog-body-style="{
        width: '30%',
        minHeight: '200px'
      }"
    >
      <template #DialogBody>
        <div class="w-full mg-b-22-px centerflex flex-wrap">
          <div class="w-full py-8 centerflex text-red-600">您已连续操作两小时，为确保信息安全，请重新登录后再使用</div>
        </div>
      </template>
      <template #footer>
        <div class="w-full">
          <el-button class="w-full" size="large" type="primary" @click="outLogin">重新登录</el-button>
        </div>
      </template>
    </trial-dialog>
  </el-config-provider>
</template>

<script>
import {
  defineComponent,
  provide,
  reactive,
  toRefs,
  onMounted,
  onBeforeUnmount
} from 'vue'
import { useStore } from 'vuex'
import * as echarts from 'echarts'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'dayjs/locale/zh-cn'

export default defineComponent({
  name: 'App',
  setup() {
    provide('ec', echarts)
    const store = useStore()
    const state = reactive({
      intervalId: null,
      locale: zhCn,
      outLoginFlag: {
        outLoginFlag: false,
      },
      outLogin: () => {
        localStorage.setItem('logingOutTime', '')
        state.outLoginFlag.outLoginFlag = false
        // 清除标签栏
        store.dispatch('tags/delAllTags')
        window.location.href = `${window.location.origin}/operatorui/#/login?flag=2`
      }
    })

    onMounted(() => {
      // 登录后 两小时后打开弹窗
      const timeFlag = location.href.includes('http://localhost:8087/') ? 6 : 2
      state.intervalId = setInterval(() => {
        const myLogingOutTime = localStorage.getItem('logingOutTime')
        if (store?.getters?.app?.authorization) {
          const timeDifference = 60 * 60 * 1000 * timeFlag
          const newDatems = Date.parse(Date())
          if (!myLogingOutTime || myLogingOutTime === 'null') {
            localStorage.setItem('logingOutTime', Date.parse(Date()))
          } else if ((newDatems - myLogingOutTime / 1) > timeDifference &&
          !state?.outLoginFlag?.outLoginFlag) {
            state.outLoginFlag.outLoginFlag = true
          }
        }
      }, 3000)
    })

    onBeforeUnmount(() => {
      if (state.intervalId) {
        clearInterval(state.intervalId)
      }
    })

    return {
      ...toRefs(state),
    }
  }
})
</script>

<style lang='less'>
@import '@/style/common.less';
</style>
<style lang="less">
html,
body,
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
.el-overlay {
  z-index: 20000 !important;
}
.my-dialog {
  z-index: 2999 !important;
}
// .w-e-text-container,.w-e-toolbar{
//   z-index: 0 !important;
// }
div[data-we-id] {
  z-index: 0;
}
.w-e-droplist {
  max-height: 300px;
  overflow: auto;
}
// 消息优先级调整
.el-message {
  z-index: 99999 !important;
}
</style>
