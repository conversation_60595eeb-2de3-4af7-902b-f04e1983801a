import { createApp } from 'vue'
import App from '@/App.vue'
// 引入element-plus
// import ElementPlus from 'element-plus'
import '@/assets/style/element-variables.scss'
import 'element-plus/dist/index.css'
// 引入中文语言包
// import 'dayjs/locale/zh-cn'
// import locale from 'element-plus/lib/locale/lang/zh-cn'
// 引入路由
import router from '@/router'
// 图片懒加载
import lazyPlugin from 'vue3-lazy'
// 引入store
import store from '@/store'
// 权限控制
import '@/permission'
// 引入svg图标注册脚本
import 'vite-plugin-svg-icons/register'
// 缓存vuex
import initStorePersistence from '@/store/persistence'
import '@/style/tailwind.less'
import trialWangEditor from '@trialdata/wang-editor'
import '@trialdata/wang-editor/style.css'
import trialTable from '@trialdata/table'
import '@trialdata/table/style.css'
import trialDialog from '@trialdata/dialog'
import '@trialdata/dialog/style.css'
import { getOperatorInfo, getUserPermissionTree } from '@/api/login';
import permission from '@/directives/permission';

initStorePersistence(store) // 缓存vuex

const app = createApp(App)

// 注册自定义指令
app.directive('permission', permission);
// 注册全局组件
import * as Components from '@/global-components'
Object.entries(Components).forEach(([key, component]) => {
  app.component(key, component)
})

// 错误日志
import useErrorHandler from '@/error-log'
useErrorHandler(app)

async function mountFun() {
  try {
    var url = window.location.href// 获取url地址
    const newsot = url.split('?newStore=')[1]// 获取newStore后面的参数
    if (newsot) {
      const de = decodeURI(newsot)
      const newUserinfo = JSON.parse(de)
      if (newUserinfo?.myLogingOutTime) {
        localStorage.setItem('logingOutTime', newUserinfo.myLogingOutTime)
      }
      if (newUserinfo?.token) {
        store.commit('app/setToken', newUserinfo.token)
        if (newUserinfo?.dctUserId && newUserinfo?.studyId) {
          const { dctUserId, studyId } = newUserinfo
          const res = await getOperatorInfo({
            dctUserId,
            studyId
          });
          // if (!res?.systemPermissionTree?.length) {
          //  res.systemPermissionTree = await getUserPermissionTree() || []
          // }
          res.studyId = studyId
          res.avatar = res?.avatarUrl || ''
          res.sites.forEach((item) => {
            item.name = item.siteName
            item.value = item.siteId
          })
          store.commit('account/setUserinfo', res)
          store.commit('setStudyItem', res)
          // appMountFun()
          // 清除URL上的querystring参数
          const newUrl = `${window.location.pathname}`;
          window.history.replaceState({}, document.title, newUrl);
          // window.location.replace(newUrl);
        }
      }
    } else if (newsot === undefined && !store.state.studyItem) {
      // 清除token
      localStorage.setItem('logingOutTime', '')
      store.dispatch('tags/delAllTags')
      store.dispatch('app/clearToken')
      window.location.href = `${window.location.origin}/operatorui/#/login?flag=2`
    }
    appMountFun()
  } catch (e) {
    appMountFun()
  }
}
mountFun()


function appMountFun() {
  app
    // .use(ElementPlus, {
    //   locale,
    // })
    .use(lazyPlugin, {
      loading: 'loading.gif',
      error: 'error.jpg',
    })
    .use(trialTable)
    .use(trialWangEditor)
    .use(trialDialog)
    .use(store)
    .use(router)
    .mount('#app')
}