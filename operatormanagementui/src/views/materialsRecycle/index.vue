<template>
  <div>
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="materialsRecycleTableRef"
        title=""
        :request="getmaterialsRecycleList"
        :columns="materialsRecycleColumns"
        :search="searchConfig"
        :pagination="paginationConfig"
        :span-method="spanMethod"
        :showbtnfalg-excel="rolesBtnShow"
        :request-export-excel="requestExportExcel"
        border
      >
        <template #operate="scope">
          <el-button
            v-permission="['manage.materialsRecycle.details']"
            size="small"
            text
            type="primary"
            @click="materialsRecycleEditItemForm(scope.row)"
          >
            详情
          </el-button>
        </template>
      </trial-table>
    </div>
    <MaterialsRecycleDetail v-if="RESEARCHCENTER_INFOS?.contentVisible" />
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { tableDateChange } from '@/utils'
import MaterialsRecycleDetail from '@/views/materialsRecycle/materialsRecycleDetails.vue'
import { getPatientMaterialDistributionRecovery } from '@/api/materialsRecycle'

export default defineComponent({
  name: 'MaterialsRecycle', // 物资发放回收
  components: {
    MaterialsRecycleDetail,
  },
  setup() {
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
      setTitle: '',
    })
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      rolesBtnShow: false,
      studyName: store.state.studyItem.studyName,
      materialsRecycleTableRef: null,
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20, 50],
        style: { textAlign: 'left' },
      },
      searchConfig: {
        // labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            label: '受试者',
            name: 'patientName',
            type: 'input',
            defaultValue: null,
          },
        ]
      },
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      materialsRecycleColumns: [
        // { type: 'selection' },
        { label: '中心', prop: 'siteName', minWidth: 260 },
        { label: '受试者', prop: 'patientNoOrNick', minWidth: 160 },
        { label: '物资名称', prop: 'materialName', minWidth: 200 },
        { label: '规格', prop: 'materialSpecs', minWidth: 180 },
        { label: '发放数量', prop: 'distributionNumber', minWidth: 130 },
        { label: '回收数量', prop: 'recoveryNumber', minWidth: 130 },
        {
          label: '操作',
          fixed: 'right',
          width: 100,
          prop: 'operate',
          align: 'left',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      spanMethod: ({ rowIndex, columnIndex }) => {
        return state.spanArr[rowIndex * state.materialsRecycleColumns.length + columnIndex]
      },
      // 请求函数
      spanArr: [],
      async getmaterialsRecycleList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const data = await getPatientMaterialDistributionRecovery(studyId, params)
          let list = []
          if (data?.items && data.items?.length) {
            list = state.listArr(data.items)
            state.spanArr = tableDateChange(list, state.materialsRecycleColumns, ['materialName', 'siteName', 'patientNoOrNick', 'operate'], ['patientId'])
          }

          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: list || [],
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      listArr: (list) => {
        const arr = []
        list.forEach((item) => {
          if (item.materialDistributionRecoveryViewModels?.length) {
            item.materialDistributionRecoveryViewModels.forEach((ite) => {
              if (ite.materialSpecsDistributionRecoveryViewModels?.length) {
                ite.materialSpecsDistributionRecoveryViewModels.forEach((it) => {
                  arr.push({ ...item, ...ite, ...it })
                })
              } else {
                arr.push({ ...item, ...ite })
              }
            })
          } else {
            arr.push({ ...item })
          }
        })
        return arr
      },

      // 刷新
      materialsRecycleRefresh: () => {
        state.materialsRecycleTableRef.refresh()
      },
      // 新增-编辑
      materialsRecycleEditItemForm: (row) => {
        if (row) {
          RESEARCHCENTER_INFOS.setTitle = '编辑'
        } else {
          RESEARCHCENTER_INFOS.setTitle = '新建'
        }
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.materialsRecycleTableRef
      },
      // 导出
      requestExportExcel: (searchModel) => {
        window.open(
          `${window.location.origin
          }/api/Operator/Study/${studyId}/PatientMaterialDistributionRecovery/Export?siteId=${searchModel.siteId || ''}&patientName=${searchModel.patientName || ''}`
        )
      }
    })
    onBeforeMount(() => {
      // const roles = ['admin', 'PM', '物资回收专员', 'PI', 'SUBI', 'CRA', 'CRC', 'Audit']
      // const role = store.state.account.userinfo?.roles || []
      // manage.materialsRecycle.exportExcel
      state.rolesBtnShow = store.state.studyItem.permissions.includes('manage.materialsRecycle.exportExcel')
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="scss" scoped>
.head {
  width: 100%;
  background: #fff;
  margin-top: 10px;
  padding: 20px 20px;
  box-sizing: border-box;
  h3 {
    margin: 0;
  }
}
</style>
