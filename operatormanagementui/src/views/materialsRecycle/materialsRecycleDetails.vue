<template>
  <div class="materialsRecycleDetails">
    <div
      class="materialsRecycleDetails-title flex items-center justify-between"
    >
      <div class="flex">
        <span class="mr-3 compensate-color">中心</span>
        <span>{{ materialsRecycleData?.siteName }}</span>
      </div>
      <div class="flex">
        <span class="mr-3 compensate-color">受试者</span>
        <span>{{ materialsRecycleData?.patientNoOrNick }}</span>
      </div>
      <el-button @click="backRefresh">返回</el-button>
    </div>
    <div class="mt-5">
      <trial-table
        ref="collectColumnsRef"
        style="maxwidth: 80%"
        title="汇总"
        :request="getcollectList"
        :columns="collectColumns"
        :pagination="false"
        :span-method="collectSpanMethod"
        border
      />
    </div>
    <div class="mt-5">
      <trial-table
        ref="recycleColumnsRef"
        title="回收记录"
        :request="getrecycleList"
        :columns="recycleColumns"
        :pagination="false"
        :span-method="recycleSpanMethod"
        border
      >
        <template #toolbar>
          <el-button
            v-permission="['manage.materialsRecycle.addRecycleRecord']"
            type="primary"
            @click="recycleClick(null, 0)"
            >新增回收</el-button
          >
        </template>
        <template #operate="scope">
          <el-button
            v-permission="['manage.materialsRecycle.editRecycleRecord']"
            size="small"
            text
            type="primary"
            @click="recycleClick(scope.row, 1)"
          >
            编辑
          </el-button>
          <el-button
            v-permission="['manage.materialsRecycle.viewRecycleRecordDetails']"
            size="small"
            text
            type="primary"
            @click="recycleClick(scope.row, 2)"
          >
            详情
          </el-button>
          <el-button
            v-permission="['manage.materialsRecycle.deleteRecycleRecord']"
            size="small"
            text
            type="danger"
            @click="recycleClick(scope.row, 3)"
          >
            删除
          </el-button>
        </template>
      </trial-table>
    </div>
    <div class="mt-5">
      <trial-table
        ref="grantColumnsRef"
        title="发放记录"
        :request="getgrantList"
        :columns="grantColumns"
        :pagination="false"
        :span-method="grantSpanMethod"
        border
      />
    </div>
    <trial-dialog
      v-model="materialsRecycleMyDialog"
      :title="myTitle"
      :my-dialog-body-style="myDialogBodyStyle"
      class="!z-[2000]"
    >
      <template #DialogBody>
        <div>
          <el-form
            ref="materialsRecycleFormRef"
            :model="ruleForm"
            :rules="rules"
            label-position="top"
          >
            <div class="flex justify-between">
              <el-form-item
                label="回收日期"
                prop="recoverDate"
                style="width: 21%"
              >
                <el-date-picker
                  v-model="ruleForm.recoverDate"
                  type="date"
                  placeholder="年/月/日"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                  :disabled="myTitleIndex === 2"
                />
              </el-form-item>
              <el-form-item label="回收人" prop="recoverMan" style="width: 21%">
                <el-input
                  v-model.trim="ruleForm.recoverMan"
                  placeholder="请输入"
                  class="w-full"
                  maxlength="666"
                  clearable
                  :disabled="myTitleIndex === 2"
                />
              </el-form-item>
              <el-form-item label="订单编号" prop="orderNo" style="width: 21%">
                <el-select
                  v-model="ruleForm.orderNo"
                  class="w-full"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入"
                  :filter-method="filterMethod"
                  :loading="selectLoading"
                  :disabled="myTitleIndex === 2"
                  clearable
                  @change="changeState"
                >
                  <el-option
                    v-for="(item, index) in associateOptions"
                    :key="index"
                    :label="item.orderNo"
                    :value="item.orderNo"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="订单状态"
                prop="orderStateName"
                style="width: 21%"
              >
                <el-input
                  v-model.trim="ruleForm.orderStateName"
                  placeholder="请输入"
                  class="w-full"
                  maxlength="666"
                  disabled
                />
              </el-form-item>
            </div>
            <div class="flex my-3">
              <span class="mr-3">回收信息</span>
              <el-icon
                v-if="myTitleIndex !== 2"
                color="#1296db"
                class="cursor-pointer"
                :size="20"
                @click="formAddorCloseClick(0)"
                ><CirclePlus
              /></el-icon>
            </div>
            <div
              v-for="(
                item, index
              ) in ruleForm.materialOrderRecoveryDetailEditViewModels"
              :key="index"
              class="w-full"
            >
              <div class="centerflex-h justify-between">
                <el-form-item
                  label="物资名称"
                  style="width: 17%"
                  :prop="`materialOrderRecoveryDetailEditViewModels[${index}].materialId`"
                  :rules="{
                    required: true,
                    message: '请输入',
                    trigger: 'blur',
                  }"
                >
                  <el-select
                    v-model="item.materialId"
                    class="w-full"
                    placeholder="请选择"
                    :disabled="myTitleIndex === 2"
                    @change="materiaChange"
                  >
                    <el-option
                      v-for="(ite, idx) in materialsOption"
                      :key="idx"
                      :label="ite.materialName"
                      :value="ite.id"
                      @click="materiaClick(index)"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="规格"
                  style="width: 17%"
                  :prop="`materialOrderRecoveryDetailEditViewModels[${index}].materialSpecId`"
                  :rules="{
                    required: true,
                    message: '请输入',
                    trigger: 'blur',
                  }"
                >
                  <el-select
                    v-model="item.materialSpecId"
                    class="w-full"
                    placeholder="请选择"
                    :disabled="myTitleIndex === 2"
                  >
                    <el-option
                      v-for="(ite, idx) in item.materialSpecsOption"
                      :key="idx"
                      :label="ite.specName"
                      :value="ite.id"
                    />
                  </el-select>
                </el-form-item>
                <!-- :rules="{ required: true, message: '请输入', trigger: 'blur' }" -->
                <el-form-item
                  label="药物编码"
                  style="width: 17%"
                  :prop="`materialOrderRecoveryDetailEditViewModels[${index}].itemCode`"
                >
                  <el-input
                    v-model.trim="item.itemCode"
                    placeholder="请输入"
                    class="w-full"
                    maxlength="666"
                    clearable
                    :disabled="myTitleIndex === 2"
                  />
                </el-form-item>
                <el-form-item
                  label="批次号"
                  style="width: 17%"
                  :prop="`materialOrderRecoveryDetailEditViewModels[${index}].batchNumber`"
                  :rules="{
                    required: true,
                    message: '请输入',
                    trigger: 'blur',
                  }"
                >
                  <el-input
                    v-model.trim="item.batchNumber"
                    placeholder="请输入"
                    class="w-full"
                    maxlength="666"
                    clearable
                    :disabled="myTitleIndex === 2"
                  />
                </el-form-item>
                <el-form-item
                  label="数量"
                  style="width: 17%"
                  :prop="`materialOrderRecoveryDetailEditViewModels[${index}].number`"
                  :rules="[
                    { required: true, message: '请输入', trigger: 'blur' },
                    {
                      pattern: /^[0-9]\d*$/,
                      message: '请输入整数',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model.trim="item.number"
                    placeholder="请输入"
                    class="w-full"
                    maxlength="666"
                    clearable
                    :disabled="myTitleIndex === 2"
                  />
                </el-form-item>
              </div>
              <div class="flex justify-between items-center">
                <el-form-item
                  label="备注"
                  style="width: 50%"
                  :prop="`materialOrderRecoveryDetailEditViewModels[${index}].remark`"
                >
                  <el-input
                    v-model.trim="item.remark"
                    placeholder="请输入"
                    class="w-full"
                    maxlength="666"
                    clearable
                    :disabled="myTitleIndex === 2"
                  />
                </el-form-item>
                <div
                  v-if="
                    ruleForm?.materialOrderRecoveryDetailEditViewModels
                      ?.length !== 1 && myTitleIndex !== 2
                  "
                  style="width: 5%"
                >
                  <el-icon
                    color="#d81e06"
                    class="cursor-pointer"
                    :size="20"
                    @click="formAddorCloseClick(1, index)"
                    ><CloseBold
                  /></el-icon>
                </div>
              </div>
            </div>
            <el-form-item label="回收说明" prop="recoverInstr">
              <el-input
                v-model="ruleForm.recoverInstr"
                :disabled="myTitleIndex === 2"
                placeholder="请输入"
                rows="6"
                maxlength="999"
                type="textarea"
              />
            </el-form-item>
            <div>
              <span class="mr-3">附件</span>
              <span class="compensate-color text-sm"
                >支持pdf、jpg、jpeg、png格式附件</span
              >
            </div>
            <MyUpload
              ref="materialsRecycleDetailsUploadRef"
              upload-file-falg
              :file-list-arr="fileListArr"
              :request-fun="requestFun"
              :before-file-upload-type="beforeFileUploadType"
              custom-handle-show
              :limit="99"
              :disabled="disabled"
              :upload-btn-show="uploadBtnShow"
            >
              <template #fileName="file">
                <div class="flex items-center">
                  <div>{{ file.file.name }}</div>
                  <div class="ml-8 flex-1">
                    <el-button
                      v-permission="['manage.materialsRecycle.previewAttachment']"
                      size="small"
                      type="primary"
                      text
                      @click="customHandle(file.file, 0)"
                      >预览</el-button
                    >
                    <el-button
                      v-permission="['manage.materialsRecycle.downloadAttachment']"
                      size="small"
                      type="primary"
                      text
                      @click="customHandle(file.file, 1)"
                      >下载</el-button
                    >
                    <el-button
                      v-if="myTitleIndex !== 2"
                      v-permission="['manage.materialsRecycle.deleteAttachment']"
                      size="small"
                      type="danger"
                      text
                      @click="customHandle(file.file, 2)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </template>
            </MyUpload>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="mt-10 text-center">
          <el-button
            size="large"
            :loading="loading"
            @click="materialsRecycleMyDialog = false"
            >取 消</el-button
          >
          <el-button
            v-if="myTitleIndex !== 2"
            size="large"
            type="primary"
            :loading="loading"
            @click="saveData(0)"
            >保 存</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <trial-dialog
      v-model="tipsMaterialsRecycleMyDialog"
      :my-dialog-body-style="myTipsDialogBodyStyle"
      title="提示"
    >
      <template #DialogBody>
        <div>
          {{ tipsTile }}
        </div>
      </template>
      <template #footer>
        <div class="mt-10 text-center">
          <el-button
            size="large"
            :loading="loading"
            @click="tipsMaterialsRecycleClose"
            >取 消</el-button
          >
          <el-button
            size="large"
            type="primary"
            :loading="loading"
            @click="saveData(1)"
            >继续保存</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <el-image-viewer
      v-if="imgShow"
      :url-list="imageViewer"
      hide-on-click-modal
      @close="imagePreviewClose"
    />
  </div>
</template>

<script lang='ts'>
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { delay, tableDateChange } from '@/utils'
import { CirclePlus, CloseBold } from '@element-plus/icons-vue'
import MyUpload from '@/components/Upload/index.vue'
import { deleteMaterialRecovery, getLogisticsOrderState, getMaterialRecovery, getMaterials, getPatientMaterialDistributionRecoveryDetail, postMaterialRecovery } from '@/api/materialsRecycle'
import { postPublicDocumentFile } from '@/api/home'
import { parseTime } from '@/utils'

export default defineComponent({
  name: 'MaterialsRecycleDetails', // 物资发放回收--详情
  components: {
    CirclePlus,
    CloseBold,
    MyUpload
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      fileListArr: [],
      selectLoading: false,
      selectOptions: [],
      associateOptions: [],
      rolesBtnShow: false,
      // 图片预览
      imgShow: false,
      imageViewer: [],
      // 弹窗
      tipsMaterialsRecycleMyDialog: false,
      tipsTile: '',

      loading: false,
      myTitle: '',
      myTitleIndex: 0,
      materialsRecycleMyDialog: false,
      myDialogBodyStyle: {
        width: '70%',
        minHeight: '0',
      },
      myTipsDialogBodyStyle: {
        width: '40%',
        minHeight: '100px',
      },
      materialsRecycleFormRef: null,
      ruleForm: {
        id: '',
        recoverDate: parseTime(new Date(), '{y}-{m}-{d}'),
        recoverMan: store.state.account.userinfo.name,
        orderNo: '',
        orderStateName: '',
        materialOrderRecoveryDetailEditViewModels: [
          {
            materialId: '',
            materialName: '',
            materialSpecs: '',
            batchNumber: '',
            number: '',
            remark: '',
            materialSpecId: '',
            materialSpecsOption: []
          }
        ],
        recoverInstr: ''
      },
      rules: {
        recoverDate: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        recoverMan: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
      },
      materialsRecycleDetailsUploadRef: null,
      materialsOption: [],

      beforeFileUploadType: ['.pdf', '.png', '.jpg', '.jpeg'],
      formAddorCloseClick: (type, index) => {
        if (type === 0) {
          state.ruleForm.materialOrderRecoveryDetailEditViewModels.push({
            materialId: '',
            materialName: '',
            materialSpecs: '',
            batchNumber: '',
            number: '',
            remark: '',
            materialSpecId: '',
            materialSpecsOption: []
          })
        } else if (type === 1) {
          state.ruleForm.materialOrderRecoveryDetailEditViewModels.splice(index, 1)
        }
      },
      fileNum: 0,
      disabled: false,
      uploadBtnShow: true,
      requestFun: async (fileObj) => {
        state.materialsRecycleDetailsUploadRef.fileList.forEach((itm, idx) => {
          if (!itm?.id) {
            state.materialsRecycleDetailsUploadRef.fileList.splice(idx, 1)
          }
        })
        state.fileNum++
        const myFormDataObj = new FormData()
        const fileName = fileObj.file.name
        const pos = fileName.lastIndexOf('.')
        const lastName = fileName.substring(pos, fileName.length)
        const limitFileType = lastName.toLowerCase()
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        let fileType = 0
        if (limitFileType === '.pdf') {
          fileType = 3
        } else if (limitFileType === '.png' || limitFileType === '.jpg' || limitFileType === '.jpeg') {
          fileType = 1
        }
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        postPublicDocumentFile(
          store.state.studyItem.studyId,
          1,
          fileType,
          myFormDataObj)
          .then((res) => {
            res.url = res?.fileUrl
            res.name = fileName
            state.materialsRecycleDetailsUploadRef.fileList.push(res)
            state.fileNum--
            if (state.fileNum === 0) {
              loading.close()
            }
          }).catch(() => {
            loading.close()
          })
      },
      // 自定义的操作
      customHandle: (file, index) => {
        if (index === 0) {
          if (file.fileType === 1) {
            // 图片预览
            state.imgShow = true
            state.imageViewer.push(file.url)
            return
          }
          window.open(file.url)
        } else if (index === 1) {
          // 下载的
          delay(() => {
            fetch(file.url)
              .then(response => response.blob())
              .then(blob => {
                const url = window.URL.createObjectURL(new Blob([blob]))
                const link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', file.name)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) // 下载完成移除元素
                window.URL.revokeObjectURL(url) // 释放掉blob对象
              })
              .catch(error => {
                console.log(error)
              })
          }, 600)
        } else if (index === 2) {
          ElMessageBox.confirm(
            '是否确认删除？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
            }
          )
            .then(() => {
              state.materialsRecycleDetailsUploadRef.fileList.forEach((item, index) => {
                if (item.id === file.id) {
                  state.materialsRecycleDetailsUploadRef.fileList.splice(index, 1)
                }
              })
            })
            .catch(() => { })
        }
      },
      imagePreviewClose: () => {
        state.imageViewer = []
        state.imgShow = false
      },
      materiaChangeVal: '',
      materiaClick: (idx) => {
        state.materialsOption.forEach((ite: any) => {
          if (ite?.id === state.materiaChangeVal) {
            state.ruleForm.materialOrderRecoveryDetailEditViewModels[idx].materialSpecId = ''
            state.ruleForm.materialOrderRecoveryDetailEditViewModels[idx].materialName = ''
            state.ruleForm.materialOrderRecoveryDetailEditViewModels[idx].materialSpecsOption = ite.materialSpecsOption
          }
        })
      },
      materiaChange: (val) => {
        state.materiaChangeVal = val
        // state.materialsOption.forEach((ite: any) => {
        //   if (ite?.id === val) {
        //     state.ruleForm.materialOrderRecoveryDetailEditViewModels[num].materialSpecId = ''
        //     state.ruleForm.materialOrderRecoveryDetailEditViewModels[num].materialSpecsOption = ite.materialSpecsOption
        //   }
        // })
      },
      tipsMaterialsRecycleClose: () => {
        state.tipsMaterialsRecycleMyDialog = false
        state.materialsRecycleMyDialog = true
      },
      saveData: (index) => {
        if (index === 1) {
          state.saveMaterialRecovery()
          return
        }
        state.materialsRecycleFormRef.validate((valid) => {
          if (valid) {
            state.fileListArr = state.materialsRecycleDetailsUploadRef.fileList
            state.ruleForm.attachmentEditViewModels = state.materialsRecycleDetailsUploadRef.fileList
            if (!state.ruleForm?.orderStateName && index !== 1) {
              if (state.ruleForm?.orderNo) {
                state.tipsTile = '系统中未找到该物流订单编号，若是外部物流订单，建议您在回收说明中说明后再保存。'
              } else {
                state.tipsTile = '您未录入物流订单编号，若是线下回收，建议您在回收说明中说明后再保存。'
              }
              state.tipsMaterialsRecycleMyDialog = true
              state.materialsRecycleMyDialog = false
              return
            }
            state.saveMaterialRecovery()
          }
        })
      },
      // 保存数据接口
      saveMaterialRecovery: () => {
        // 为空判断
        state.loading = true
        postMaterialRecovery(studyId, RESEARCHCENTER_INFOS.researchContent?.siteId, RESEARCHCENTER_INFOS.researchContent?.patientId, state.ruleForm?.id || '0', state.ruleForm).then(() => {
          state.loading = false
          ElMessage.success('保存成功')
          state.onLoad()
          state.tipsMaterialsRecycleMyDialog = false
          state.materialsRecycleMyDialog = false
        }).catch(() => {
          state.loading = false
        })
      },
      filterMethod: (val) => {
        state.selectLoading = true
        if (val) {
          state.ruleForm.orderNo = val
          // 粘贴的时候到不了change 这里进行复制
          state.ruleForm.orderStateName = ''
          state.selectOptions.forEach((item) => {
            if (item?.orderNo === val) {
              state.ruleForm.orderStateName = item.state
            }
          })
          state.selectLoading = true
          delay(() => {
            state.selectLoading = false
            state.associateOptions = state.selectOptions.filter((item) => {
              return item.orderNo.toLowerCase().includes(val.toLowerCase())
            })
          }, 500)
        } else {
          state.associateOptions = []
        }
      },
      changeState: (val) => {
        state.ruleForm.orderStateName = ''
        state.selectOptions.forEach((item: any) => {
          if (item?.orderNo === val) {
            state.ruleForm.orderStateName = item.state
          }
        })
      },
      // all数据
      materialsRecycleData: {},
      // 汇总表格all
      collectTableArr: [],
      collectColumnsRef: null,
      collectColumns: [
        { label: '物资名称', prop: 'materialName', minWidth: 260 },
        { label: '规格', prop: 'materialSpecs', minWidth: 200 },
        { label: '发放数量', prop: 'distributionNumber', minWidth: 150 },
        { label: '回收数量', prop: 'recoveryNumber', minWidth: 150 },
      ],
      async getcollectList() {
        return {
          data: [],
        }
      },
      collectSpanMethod: ({ rowIndex, columnIndex }) => {
        return state.collectTableArr[rowIndex * state.collectColumns.length + columnIndex]
      },

      // 回收记录表格all
      recycleTableArr: [],
      recycleColumnsRef: null,
      recycleColumns: [
        { label: '回收日期', prop: 'recoverDate', minWidth: 140 },
        { label: '回收人', prop: 'recoverMan', minWidth: 120 },
        { label: '订单编号', prop: 'orderNo', minWidth: 200 },
        { label: '订单状态', prop: 'orderStateName', minWidth: 120 },
        { label: '物资名称', prop: 'materialName', minWidth: 180 },
        { label: '规格', prop: 'materialSpecs', minWidth: 150 },
        { label: '药物编码', prop: 'itemCode', minWidth: 150 },
        { label: '批次号', prop: 'batchNumber', minWidth: 150 },
        { label: '数量', prop: 'number', minWidth: 130 },
        { label: '备注', prop: 'remark', minWidth: 150 },
        { label: '回收说明', prop: 'recoverInstr', minWidth: 200 },
        {
          label: '操作',
          fixed: 'right',
          width: 200,
          prop: 'operate',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      async getrecycleList() {
        return {
          data: [],
        }
      },
      recycleSpanMethod: ({ rowIndex, columnIndex }) => {
        return state.recycleTableArr[rowIndex * state.recycleColumns.length + columnIndex]
      },
      recycleClick: (row, index) => {
        state.fileListArr = []
        state.ruleForm = {
          id: '',
          recoverDate: parseTime(new Date(), '{y}-{m}-{d}'),
          recoverMan: store.state.account.userinfo.name,
          orderNo: '',
          orderStateName: '',
          materialOrderRecoveryDetailEditViewModels: [
            {
              materialId: '',
              materialName: '',
              materialSpecs: '',
              batchNumber: '',
              number: '',
              remark: '',
              materialSpecId: '',
              materialSpecsOption: []
            }
          ],
          recoverInstr: ''
        }
        state.myTitleIndex = index
        state.disabled = false
        state.uploadBtnShow = true
        if (index === 0) {
          state.materialsRecycleMyDialog = true
          state.myTitle = '新建'
        } else if (row && (index === 1 || index === 2)) {
          state.materialsRecycleMyDialog = true
          const editLoading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          if (index === 1) {
            state.myTitle = '编辑'
          } else if (index === 2) {
            state.disabled = true
            state.uploadBtnShow = false
            state.myTitle = '详情'
          }
          getMaterialRecovery(studyId, RESEARCHCENTER_INFOS.researchContent?.siteId, RESEARCHCENTER_INFOS.researchContent?.patientId, row?.id).then((res) => {
            if (res.materialOrderRecoveryDetailEditViewModels?.length) {
              res.materialOrderRecoveryDetailEditViewModels.forEach((item) => {
                state.materialsOption.forEach((ite: any) => {
                  if (item?.materialId === ite.id) {
                    item.materialSpecsOption = ite.materialSpecLists
                  }
                })
              })
            }
            if (res?.attachmentEditViewModels?.length) {
              res.attachmentEditViewModels.forEach((item) => {
                item.url = item?.fileUrl
                item.name = item?.fileName
              })
              state.materialsRecycleDetailsUploadRef.fileList = res.attachmentEditViewModels
            }
            state.ruleForm = res
            editLoading.close()
          })
        } else if (index === 3) {
          ElMessageBox.confirm(
            '是否确认删除？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
            }
          )
            .then(() => {
              const deleteLoading = ElLoading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0, 0, 0, 0.7)',
              })
              deleteMaterialRecovery(studyId, RESEARCHCENTER_INFOS.researchContent?.siteId, RESEARCHCENTER_INFOS.researchContent?.patientId, row?.id).then(() => {
                ElMessage.success('删除成功')
                state.collectColumnsRef.tableData = []
                state.recycleColumnsRef.tableData = []
                state.grantColumnsRef.tableData = []
                state.onLoad()
                deleteLoading.close()
              }).catch(() => {
                deleteLoading.close()
              })
            })
            .catch(() => { })
        }
      },
      // 发放记录表格all
      grantTableArr: [],
      grantColumnsRef: null,
      grantColumns: [
        { label: '订单时间', prop: 'createTime', minWidth: 200 },
        { label: '下单人', prop: 'creator', minWidth: 140 },
        { label: '订单编号', prop: 'orderNo', minWidth: 170 },
        { label: '订单状态', prop: 'orderStateName', minWidth: 130 },
        { label: '发放名称', prop: 'materialDistributionRuleName', minWidth: 200 },
        { label: '物资名称', prop: 'materialName', minWidth: 180 },
        { label: '规格', prop: 'materialSpecs', minWidth: 150 },
        { label: '药物编码', prop: 'materialNumber', minWidth: 150 },
        { label: '批次号', prop: 'batchNumberStr', minWidth: 150 },
        { label: '有效期', prop: 'shelfDate', minWidth: 150 },
        { label: '数量', prop: 'number', minWidth: 150 },
      ],
      async getgrantList() {
        return {
          data: [],
        }
      },
      grantSpanMethod: ({ rowIndex, columnIndex }) => {
        return state.grantTableArr[rowIndex * state.grantColumns.length + columnIndex]
      },

      backRefresh: () => {
        RESEARCHCENTER_INFOS.contentVisible = false
        RESEARCHCENTER_INFOS.resetMethod.refresh()
      },
      // 进入页面加载，写在了onMounted中
      onLoad: async () => {
        getLogisticsOrderState(studyId, { orderNo: '' }).then((res) => {
          state.selectOptions = res
        })
        const data = await getPatientMaterialDistributionRecoveryDetail(studyId, RESEARCHCENTER_INFOS.researchContent?.siteId, RESEARCHCENTER_INFOS.researchContent?.patientId)
        state.materialsRecycleData = data
        // 汇总列表
        if (data?.materialDistributionRecoveryViewModels?.length) {
          const list = data?.materialDistributionRecoveryViewModels || []
          const arr = []
          list.forEach((item) => {
            if (item.materialSpecsDistributionRecoveryViewModels?.length) {
              item.materialSpecsDistributionRecoveryViewModels.forEach((ite) => {
                arr.push({ ...item, ...ite })
              })
            } else {
              arr.push({ ...item })
            }
          })
          state.collectColumnsRef.tableData = arr
          state.collectTableArr = tableDateChange(arr || [], state.collectColumns, ['materialName'], ['id'])
        }
        // 回收记录列表
        if (data?.materialOrderRecoveryRecordEditViewModels?.length) {
          const list = data?.materialOrderRecoveryRecordEditViewModels || []
          const arr = []
          list.forEach((item) => {
            if (item.materialOrderRecoveryDetailEditViewModels?.length) {
              item.materialOrderRecoveryDetailEditViewModels.forEach((ite) => {
                arr.push({ ...item, ...ite })
              })
            } else {
              arr.push({ ...item })
            }
          })
          state.recycleColumnsRef.tableData = arr
          const mergeList = ['recoverDate', 'recoverMan', 'orderNo', 'orderStateName', 'recoverInstr', 'operate']
          state.recycleTableArr = tableDateChange(arr || [], state.recycleColumns, mergeList, ['id'])
        }
        // 发放记录列表
        if (data?.materialOrderDistributionRecordViewModels?.length) {
          const list = data?.materialOrderDistributionRecordViewModels || []
          const arr: any = []
          list.forEach((item) => {
            if (item.materialOrderAndMaterialInfoRelationViewModels?.length) {
              item.materialOrderAndMaterialInfoRelationViewModels.forEach((ite) => {
                if (ite?.batchNumber && ite.batchNumber.includes(',')) {
                  ite.batchNumberArr = ite.batchNumber.split(',')
                  let num = 0
                  if (ite?.itemList) {
                    ite.itemList.forEach((it, index) => {
                      if (it?.materialNumber) {
                        num++
                      }
                      if (num !== 0) { // 随机的
                        it.batchNumberStr = ite.batchNumberArr[index]
                        ite.number = 1
                      }
                    })
                  }
                  if (num === 0) {
                    ite.batchNumberStr = ite.batchNumber
                  }
                }
                if (ite.itemList?.length) {
                  ite.itemList.forEach((e) => {
                    arr.push({ ...item, ...ite, ...e })
                  })
                } else {
                  arr.push({ ...item, ...ite })
                }
              })
            } else {
              arr.push({ ...item })
            }
          })
          state.grantColumnsRef.tableData = arr
          const mergeList = ['createTime', 'creator', 'orderNo', 'orderStateName', 'materialDistributionRuleName', 'materialName']
          state.grantTableArr = tableDateChange(arr || [], state.grantColumns, mergeList, ['id'])
          // getMaterials(studyId).then((res) => {
          //   if (res) {
          //     res.forEach((ite) => {
          //       ite.materialSpecsOption = []
          //       data?.materialDistributionRecoveryViewModels.forEach((item) => {
          //         if (ite.id === item.id) {
          //           ite.materialSpecsOption = item.materialSpecsDistributionRecoveryViewModels
          //         }
          //       })
          //     })
          //   }
          //   state.materialsOption = res
          // })
        }
        getMaterials(studyId).then((res) => {
          if (res) {
            res.forEach((ite) => {
              ite.materialSpecsOption = ite.materialSpecLists
            })
          }
          state.materialsOption = res
        })
      }
    })
    onMounted(() => {
      // 处理按钮权限
      // const roles = ['admin', '物资回收专员']
      // const role = store.state.account.userinfo?.roles || []
      // state.rolesBtnShow = roles.some((item) => role.includes(item))
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.compensate-color {
  color: #9c9898;
}
.materialsRecycleDetails-title {
  padding: 20px;
  background: #fff;
}
</style>
