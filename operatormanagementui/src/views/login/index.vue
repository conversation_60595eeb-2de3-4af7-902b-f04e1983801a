<template>
  <div class="login">
    <el-form ref="loginForm" class="form" :model="model" :rules="rules">
      <h1 class="title">eDCT后台</h1>
      <el-form-item v-if="showCodeLogin" prop="nameOrMail">
        <el-input
          v-model.trim="model.nameOrMail"
          class="text"
          maxlength="50"
          prefix-icon="el-icon-user-solid"
          clearable
          placeholder="用户名"
        />
      </el-form-item>
      <el-form-item v-if="showCodeLogin" prop="password">
        <el-input
          v-model.trim="model.password"
          class="text"
          maxlength="50"
          prefix-icon="el-icon-lock"
          show-password
          clearable
          placeholder="密码"
        />
      </el-form-item>
      <el-form-item v-if="!showCodeLogin" prop="mobile">
        <el-input
          v-model.trim="model.mobile"
          class="text"
          maxlength="11"
          prefix-icon="el-icon-mobile-phone"
          clearable
          placeholder="手机号"
        />
      </el-form-item>
      <el-form-item v-if="!showCodeLogin" prop="verifyCode">
        <div class="verifycode-modules">
          <el-input
            v-model.trim="model.verifyCode"
            class="text"
            maxlength="6"
            prefix-icon="el-icon-chat-line-round"
            show-password
            clearable
            placeholder="验证码"
          />
          <el-button type="primary" class="btn" @click="getVerificationCode">
            {{ VER_TIME > 59 ? '获取验证码' : VER_TIME }}
          </el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          :loading="loading"
          type="primary"
          class="btn"
          size="large"
          @click="submit"
        >
          {{ btnText }}
        </el-button>
      </el-form-item>
      <div class="checkout-login">
        <div v-if="showCodeLogin" @click="showCodeLogin = false">手机验证码登录</div>
        <div v-else @click="showCodeLogin = true">密码登录</div>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed, onUnmounted } from 'vue' // getCurrentInstance,
import { Login, postLoginByCode, postSentVerifyCode } from '@/api/login'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { deepClone, encode } from '@/utils'

export default defineComponent({
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    const state = reactive({
      model: {
        nameOrMail: '',
        password: '',
        mobile: '',
        verifyCode: ''
        // openId: ''
      },
      VER_TIME: 60,
      rules: {
        nameOrMail: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {
            min: 6,
            max: 50,
            message: '长度在 6 到 50 个字符',
            trigger: 'blur'
          }
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { min: 6, max: 6, message: '验证码长度在 6位', trigger: 'blur' },
        ],
      },
      loading: false,
      btnText: computed(() => (state.loading ? '登录中...' : '登录')),
      loginForm: null,
      showCodeLogin: true,
      // 获取验证码
      getVerificationCode: () => {
        const MYREG = /^1[3-9][0-9]\d{8}$/
        const MOBILE = state.model.mobile
        if (state.VER_TIME / 1 < 60) {
          return
        } else if (!MYREG.test(MOBILE)) {
          ElMessage({
            message: '请输入正确的手机号',
            type: 'error',
          })
          return
        } else {
          state.VER_TIME--
          const TIMEOUT = setInterval(() => {
            state.VER_TIME--
            if (state.VER_TIME / 1 === 0) {
              state.VER_TIME = 60
              clearInterval(TIMEOUT)
            }
          }, 1000)
          postSentVerifyCode({ 'mobile': state.model.mobile })
        }
      },
      submit: () => {
        if (state.loading) {
          return
        }
        state.loginForm.validate(valid => {
          if (valid) {
            state.loading = true
            const model = deepClone(state.model)
            model.password = encode(model.password)
            const Logins = state.showCodeLogin ? Login : postLoginByCode
            if (state.showCodeLogin) {
              model.mobile = null
              model.verifyCode = null
            } else {
              model.nameOrMail = null
              model.password = null
            }
            Logins(model).then((res) => {
              // 存token
              res.role = 'admin'
              res.avatar = res.avatarUrl
              store.commit('account/setUserinfo', res)
              store.commit('app/setToken', res.token)
              ElMessage.success({
                message: '登录成功',
                duration: 1000
              })

              const targetPath = decodeURIComponent(route.query.redirect)
              if (targetPath.startsWith('http')) {
                // 如果是一个url地址
                window.location.href = targetPath
              } else if (targetPath.startsWith('/')) {
                // 如果是内部路由地址
                router.push(targetPath)
              } else {
                router.push('/')
              }
              state.loading = false
            }).catch((e) => { state.loading = false })
          }
        })
      },
      keyDown: (e) => {
      // 如果是回车则执行登录方法
        if (e.keyCode === 13) {
          state.submit()
        }
      }
    })
    // 登录绑定事件
    window.addEventListener('keydown', state.keyDown)
    onUnmounted(() => {
      window.removeEventListener('keydown', state.keyDown, false)
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.login {
  transition: transform 1s;
  transform: scale(1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #2d3a4b;
  .form {
    width: 520px;
    max-width: 100%;
    padding: 0 24px;
    box-sizing: border-box;
    margin: 160px auto 0;
    .title {
      color: #fff;
      text-align: center;
      font-size: 24px;
      margin: 0 0 24px;
    }
    .text {
      font-size: 16px;
      :deep(.el-input__inner) {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.1);
        color: #fff;
        height: 48px;
        line-height: 48px;
        &::placeholder {
          color: rgba(255, 255, 255, 0.2);
        }
      }
    }
    .btn {
      width: 100%;
    }
    .verifycode-modules {
      display: flex;
      align-items: center;
      .el-button,.el-button--primary,.btn {
        width: 160px;
        height: 40px;
        margin-left: 20px;
      }
    }
    // 切换登录方式
    .checkout-login{
      color: #fff;
      &:hover{
        cursor: pointer;
      }
    }
  }
}
</style>
