<template>
  <div class="study">
    <div class="nav">
      <div class="loginout" @click="logout">退出登录</div>
    </div>
    <div class="study-body">
      <div v-for="(item,index) in studyList" :key="index">
        <h3 @click="checkedStudy(item)">
          {{ item.studyName }}
        </h3>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// import { getUserStudyBreifInfo } from '@/api/login'
import { defineComponent, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'Study', // 课题列表
  setup() {
    const router = useRouter()
    const store = useStore()
    // router.replace('/home')
    const state = reactive({
      studyList: [],

      onLoad: () => {
        // if (store?.getters?.account?.userinfo?.dctUserId) {
        //   getUserStudyBreifInfo(store.getters.account.userinfo.dctUserId).then((res: any) => {
        //     state.studyList = res
        //   }).catch(() => {
        //     state.studyList = store.getters?.account?.userinfo?.studies || []
        //   })
        // } else {
        //   state.studyList = store.getters?.account?.userinfo?.studies || []
        // }
      },
      // 进入某个项目
      checkedStudy: (item) => {
        store.commit('setStudyItem', item)
        router.push('/home')
      },
      // 退出登录
      logout: () => {
        // 清除token
        store.dispatch('app/clearToken')
        store.commit('setStudyItem', '')
        // 清除标签栏
        store.dispatch('tags/delAllTags')
        router.push('/login')
      },
    })
    
    // onMounted(() => {
    //   state.onLoad()
    // })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.study {
  width: 100%;
  background: #fff;
  padding-bottom: 30px;
  box-sizing: border-box;
  .nav {
    width: 100%;
    height: 300px;
    background: #5e72e4;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
    position: relative;
    .loginout{
      position: absolute;
      top: 20%;
      right: 30px;
      font-size: 18px;
      color: #fff;
      &:hover{
        cursor: pointer;
      }
    }
  }
  .study-body {
    width: 100%;
    div {
      width: 80%;
      padding: 20px 20px;
      margin: 0 auto;
      border: 1px solid #e9ecef;
      margin-top: 30px;
      cursor: pointer;
      color: #32325d;
    }
  }
}
</style>
