<template>
  <div class="ForgetPassword">
    <el-form ref="ForgetPasswordRef" class="form" :model="model" :rules="rules">
      <h1 class="title">修改密码</h1>
      <el-form-item prop="mobile">
        <el-input
          v-model.trim="model.mobile"
          class="text"
          maxlength="11"
          prefix-icon="el-icon-mobile-phone"
          clearable
          placeholder="手机号"
        />
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model.trim="model.password"
          class="text"
          maxlength="50"
          prefix-icon="el-icon-lock"
          show-password
          clearable
          placeholder="新密码"
        />
      </el-form-item>
      <el-form-item prop="newPassword">
        <el-input
          v-model.trim="model.newPassword"
          class="text"
          maxlength="50"
          prefix-icon="el-icon-lock"
          show-password
          clearable
          placeholder="确认密码"
        />
      </el-form-item>
      <el-form-item prop="verifyCode">
        <div class="verifycode-modules">
          <el-input
            v-model.trim="model.verifyCode"
            class="text"
            maxlength="6"
            prefix-icon="el-icon-chat-line-round"
            show-password
            clearable
            placeholder="验证码"
          />
          <el-button type="primary" class="btn" @click="getVerificationCode">
            {{ VER_TIME > 59 ? '获取验证码' : VER_TIME }}
          </el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button
          :loading="loading"
          class="btn"
          type="primary"
          size="large"
          @click="submit"
        >
          {{ btnText }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed } from 'vue'
// import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'ForgetPassword',
  setup() {
    // const store = useStore()
    const router = useRouter()
    const VALIDATEPASS = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入确认密码'))
      } else if (value !== state.model.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    const state = reactive({
      model: {
        mobile: '',
        password: '',
        newPassword: '',
        verifyCode: '',
      },
      rules: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {
            min: 6,
            max: 50,
            message: '长度在 6 到 50 个字符',
            trigger: 'blur',
          },
        ],
        newPassword: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          { min: 6, max: 50, message: '密码长度在 6~50位', trigger: 'blur' },
          { required: true, validator: VALIDATEPASS, trigger: 'blur' },
        ],
        verifyCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { min: 6, max: 6, message: '验证码长度在 6位', trigger: 'blur' },
        ],
      },
      VER_TIME: 60,
      loading: false,
      btnText: computed(() => (state.loading ? '修改中...' : '确认修改')),
      ForgetPasswordRef: null,
      // 获取验证码
      getVerificationCode: () => {
        const MYREG = /^1[3-9][0-9]\d{8}$/
        const MOBILE = state.model.mobile
        if (state.VER_TIME / 1 < 60) {
          return
        } else if (!MYREG.test(MOBILE)) {
          ElMessage({
            message: '请输入正确的手机号',
            type: 'error',
          })
          return
        } else {
          // getSendVerifyCode({"mobile": state.model.mobile})
          // .then(()=>{
          const TIMEOUT = setInterval(() => {
            state.VER_TIME--
            if (state.VER_TIME / 1 === 0) {
              state.VER_TIME = 60
              clearInterval(TIMEOUT)
            }
          }, 1000)
          // })
        }
      },

      submit: () => {
        if (state.loading) {
          return
        }
        state.ForgetPasswordRef.validate((valid) => {
          if (valid) {
            state.loading = true
            ElMessage.success('修改成功')
            state.loading = false
            router.push('/') // router.go(-1)
          }
        })
      }
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.ForgetPassword {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #2d3a4b;
  .form {
    width: 520px;
    max-width: 100%;
    padding: 0 24px;
    box-sizing: border-box;
    margin: 160px auto 0;
    .title {
      color: #fff;
      text-align: center;
      font-size: 24px;
      margin: 0 0 24px;
    }
    .text {
      font-size: 16px;
      :deep(.el-input__inner) {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.1);
        color: #fff;
        height: 48px;
        line-height: 48px;
        &::placeholder {
          color: rgba(255, 255, 255, 0.2);
        }
      }
    }
    .btn {
      width: 100%;
    }
    .verifycode-modules {
      display: flex;
      align-items: center;
      .el-button,.el-button--primary,.btn {
        width: 160px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }
}
</style>
