<template>
  <trial-dialog
    v-model="dialogVisible"
    :my-dialog-body-style="{ width: '50%' }"
    class="!z-[2000]"
    @close="handleClose"
  >
    <template #DialogBody>
      <div class="font-bold mb-[20px]">关闭计划外修改窗口</div>
      <el-form
        ref="closeFormRef"
        :model="closeForm"
        :rules="closeRules"
        label-position="left"
        label-width="80px"
      >
        <el-form-item label="原因" prop="closeWindowReason">
          <el-input
            v-model="closeForm.closeWindowReason"
            type="textarea"
            :rows="6"
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="centerflex mt-[80px]">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { saveOrUpdateQuestUnplannedWindow } from '@/api/planChange'

export default defineComponent({
  name: 'ClosePlanChangeDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const dialogVisible = ref(props.modelValue)
    const closeFormRef = ref<FormInstance>()
    const closeForm = ref({
      closeWindowReason: '',
    })
    const closeRules = reactive<FormRules>({
      closeWindowReason: [{
        required: true, message: '请输入', trigger: 'blur'
      }],
    })
    const loading = ref(false)

    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (!newVal && closeFormRef.value) {
        closeFormRef.value.resetFields()
      }
    })

    const handleClose = () => {
      emit('update:modelValue', false)
    }

    const handleConfirm = async () => {
      if (!closeFormRef.value) return
      loading.value = true
      try {
        await closeFormRef.value.validate()
        closeForm.value.questWindowStatus = 2
        await saveOrUpdateQuestUnplannedWindow(closeForm.value)
        emit('confirm', closeForm.value)
        emit('update:modelValue', false)
      } catch {
        // 校验失败或接口异常都会进这里
      } finally {
        loading.value = false
      }
    }

    const handleCancel = () => {
      emit('cancel')
      emit('update:modelValue', false)
    }

    return {
      dialogVisible,
      closeFormRef,
      closeForm,
      closeRules,
      loading,
      handleClose,
      handleConfirm,
      handleCancel,
    }
  },
})
</script> 