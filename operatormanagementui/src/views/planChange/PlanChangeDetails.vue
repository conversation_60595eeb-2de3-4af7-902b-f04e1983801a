<template>
  <div class="plan-change-details-layout">
    <div class="plan-change-details-header">
      <el-button @click="$emit('back')">返回</el-button>
      <el-button
        v-show="row?.questWindowStatus !== 2"
        v-permission="['manage.planChange.closePlanChange']"
        type="danger"
        @click="closePlanChangeWindow"
      >关闭计划外修改窗口</el-button>
    </div>
    <div class="plan-change-details-main">
      <div class="plan-change-details-left">
        <el-card class="mb-[16px]">
          <div class="section-title">参与者</div>
          <div class="info-row"><span>中心</span><span>{{ row?.siteName }}</span></div>
          <div class="info-row"><span>参与者编号</span><span>{{ row?.patNumber }}</span></div>
          <div class="info-row"><span>参与者状态</span><span>{{ row?.questWindowStatus === 2 ? '已关闭' : '进行中' }}</span></div>
          <div class="info-row"><span>入组日期</span><span>{{ row?.inGroupDate }}</span></div>
          <div class="info-row"><span>组别</span><span>{{ row?.group }}</span></div>
        </el-card>
        <el-card>
          <div class="section-title">表单信息</div>
          <div class="info-row"><span>表单父级</span><span>{{ row?.parentType }}</span></div>
          <div class="info-row"><span>表单名称</span><span>{{ row?.templateName }}</span></div>
        </el-card>
      </div>
      <div class="plan-change-details-right">
        <el-card style="height: calc(100vh - 180px);">
          <div class="section-title">计划外修改</div>
          <div class="info-row"><span>创建时间</span><span>{{ row?.oprtTimeStr }}</span></div>
          <div class="info-row"><span>创建人</span><span>{{ row?.oprtUserName }}</span></div>
          <div class="bg-[#777] my-[25px] w-full h-[0.5px]"/>
          <!-- <div class="info-row"><span>操作人类型</span><span>{{ row?.oprtUserType }}</span></div> -->
          <div class="info-row"><span>修改窗口</span><span>{{ row?.windowStartTimeStr }} ~ {{ row?.windowEndTimeStr }}</span></div>
          <!-- <div class="info-row"><span>状态</span><span>{{ row?.questWindowStatus }}</span></div> -->
          <div class="info-row align-top"><span>原因</span><span class="pre-line">{{ row?.openWindowReason }}</span></div>
          <!-- <div v-else class="info-row align-top"><span>原因</span><span class="pre-line">{{ row?.closeWindowReason }}</span></div> -->
          <div class="info-row whitespace-pre-wrap break-all"><span>附件</span><span>{{ row?.fileUrl || '无' }}</span></div>
          <div class="info-row"><span>反馈</span><span>{{ row?.feedBack }}</span></div>
          <div class="info-row"><span>反馈时间</span><span>{{ row?.feedBackTimeStr }}</span></div>
        </el-card>
      </div>
    </div>
    <ClosePlanChangeDialog
      v-model="closeDialogVisible"
      ref="closePlanChangeDialogRef"
      @confirm="handleConfirmClose"
      @cancel="handleCancelClose"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import ClosePlanChangeDialog from '@/views/planChange/ClosePlanChangeDialog.vue'
import { deepClone } from '@/utils'

export default defineComponent({
  name: 'PlanChangeDetails',
  // props: {
  //   row: {
  //     type: Object as PropType<Record<string, any>>,
  //     default: () => ({}),
  //   },
  // },
  components: {
    ClosePlanChangeDialog,
  },
  setup(props, { emit }) {
    const row = ref({})
    const closeDialogVisible = ref(false)
    const closePlanChangeDialogRef = ref<FormInstance>()
    const closeForm = reactive({
      reason: '',
    })
    const closeRules = reactive<FormRules>({
      reason: [{
        required: true, message: '请输入', trigger: 'blur'
      }],
    })

    const closePlanChangeWindow = () => {
      if (closePlanChangeDialogRef.value) {
        closePlanChangeDialogRef.value.closeForm = deepClone(row.value)
      }
      closeDialogVisible.value = true
    }

    const handleConfirmClose = () => {
      row.value.questWindowStatus = 2
      emit('back')
      // TODO: 调用关闭计划外修改窗口接口
      // closeDialogVisible.value = false; // ClosePlanChangeDialog already handles this
    }

    const handleCancelClose = () => {
      // console.log('Dialog cancelled')
      // closeDialogVisible.value = false; // ClosePlanChangeDialog already handles this
    }

    return {
      row,
      closeDialogVisible,
      closePlanChangeDialogRef,
      closeForm,
      closeRules,
      closePlanChangeWindow,
      handleConfirmClose,
      handleCancelClose,
    }
  }
})
</script>

<style scoped>
.plan-change-details-layout {
  width: 100%;
  height: calc(100vh - 120px);
  box-sizing: border-box;
  overflow: auto;
}
.plan-change-details-header {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-bottom: 16px;
}
.plan-change-details-main {
  display: flex;
  /* gap: 24px; */
}
.plan-change-details-left {
  width: 30%;
  min-width: 260px;
  margin-right: 1%;
  display: flex;
  flex-direction: column;
  /* gap: 16px; */
}
.plan-change-details-right {
  /* max-width: 70% */
  max-width: 69%;
  flex: 1
}
.section-title {
  font-weight: bold;
  margin-bottom: 12px;
  font-size: 18px;
}
.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 15px;
}
.info-row > span:first-child {
  text-align: right;
  width: 100px;
  padding-right: 12px;
  color: #7F7F7F;
}
.info-row > span:last-child {
  text-align: left;
  flex: 1;
}
.info-row.align-top {
  align-items: flex-start;
}
.pre-line {
  white-space: pre-line;
  word-break: break-all;
}
</style> 