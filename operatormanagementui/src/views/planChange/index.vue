<template>
  <div>
    <div v-show="!contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="planChangeTableRef"
        :request="getPlanChangeList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
        :showbtnfalg-excel="showbtnfalgExcel"
        :request-export-excel="exportFun"
      >
        <template #questWindowStatusStr="scope">
          <span>{{ scope.row.questWindowStatus === 2 ? '已关闭' : '进行中' }}</span>
        </template>
        <template #operate="scope">
          <span v-permission="['manage.planChange.details']" class="editBtnBlue" @click="showDetail(scope.row)">详情</span>
        </template>
      </trial-table>
    </div>
    <PlanChangeDetails
      v-if="contentVisible"
      ref="currentRowRef"
      @back="() => {
        contentVisible = false
        planChangeTableRef.refresh()
      }"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue'
import PlanChangeDetails from '@/views/planChange/PlanChangeDetails.vue'
import { useStore } from 'vuex'
import { exportQuestUnPlannedWindow, getQuestUnPlannedWindowDetail, postQuestUnPlannedWindowList } from '@/api/planChange'
import { saveAs } from 'file-saver'
// import { getPlanChangeList } from '@/api/planChange' // 需实现

export default defineComponent({
  name: 'PlanChange', // 计划外修改
  components: {
    PlanChangeDetails,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const planChangeTableRef = ref()
    const contentVisible = ref(false)
    const currentRowRef: any = ref(null)
    // 权限再控制
    const showbtnfalgExcel = ref(store.state.studyItem.permissions.includes('manage.planChange.exportExcel'))
    const studyName = ref(store.state.studyItem.studyName)
    // 表格配置
    const columns = reactive([
      { prop: 'oprtTimeStr', label: '创建时间', minWidth: 180 },
      { tdSlot: 'questWindowStatusStr', label: '状态', minWidth: 100 },
      { prop: 'siteName', label: '中心', minWidth: 180 },
      { prop: 'patNumber', label: '参与者编号', minWidth: 130 },
      { prop: 'parentType', label: '表单父级', minWidth: 180 },
      { prop: 'templateName', label: '表单名称', minWidth: 180 },
      { prop: 'windowStartTimeStr', label: '修改窗口-开始', minWidth: 180 },
      { prop: 'windowEndTimeStr', label: '修改窗口-截止', minWidth: 180 },
      { prop: 'openWindowReason', label: '原因', minWidth: 180 },
      { prop: 'updateUserName', label: '修改人', minWidth: 100 },
      { prop: 'feedBack', label: '反馈', minWidth: 180 },
      { label: '操作', tdSlot: 'operate', fixed: 'right' },
    ])
    const searchConfig = reactive({
        // labelWidth: '90px', // 必须带上单
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '状态',
            name: 'questWindowStatus',
            defaultValue: null,
            options: [
              {
                value: 1,
                name: '进行中',
              },
              {
                value: 2,
                name: '已关闭',
              },
            ],
          },
          {
            type: 'select',
            label: '中心',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            label: '参与者编号',
            name: 'patientNumber',
            type: 'input',
            defaultValue: null,
          },
          {
            label: '问卷名称',
            name: 'templateName',
            type: 'input',
            defaultValue: null,
          },
        ],
      },)
    const paginationConfig = reactive({
      page: 1,
      pageSize: 20,
      total: 0,
    })
    const paramsPlanChange = ref({})
    // 查询方法
    const getPlanChangeList = async (params: any) => {
      // console.log('getPlanChangeList',params)
      paramsPlanChange.value = params
      if (!params.questWindowStatus) {
        params.questWindowStatus = 0
      }
      try { 
        const res: any = await postQuestUnPlannedWindowList({studyId, ...params})
        // TODO: 调用接口获取数据
        return { data: res.items , total: res.totalItemCount }
      } catch {
        return { data: [] , total: 0 }
      }
    }
    // 详情
    const showDetail = (row: any) => {
      getQuestUnPlannedWindowDetail(row.id)
      .then((res: any) => {
        currentRowRef.value.row = res
      })
      contentVisible.value = true
    }
    const exportLoading = ref(false)
    const exportFun = () => {
      if (exportLoading.value) {
        return
      }
      exportLoading.value = true
      exportQuestUnPlannedWindow(studyId, {
        studyId,
        ...paramsPlanChange.value
      })
        .then((res: any) => {
          saveAs(new Blob([res.data]), res.headers?.filedownloadname)
          exportLoading.value = false
        })
        .catch(() => {
          exportLoading.value = false
        })
    }
    return {
      showbtnfalgExcel,
      exportFun,
      currentRowRef,
      studyName,
      planChangeTableRef,
      columns,
      searchConfig,
      paginationConfig,
      getPlanChangeList,
      contentVisible,
      showDetail,
    }
  },
})
</script>

<style lang="less" scoped>
.head {
  width: 100%;
  background: #fff;
  margin-top: 10px;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
  h3 {
    margin: 0;
  }
}
</style>