<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getMeetingList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
      >
        <template #icfVersionNumber="scope">
          <div class="tdTooltip" v-for="item in scope.row.icfVersions">
            <div v-if="!showToolTip(item.icfVersionNumber)">
              {{ item.icfVersionNumber }}
            </div>
            <el-tooltip
              v-else
              class="box-item"
              effect="dark"
              :content="item.icfVersionNumber"
              placement="top"
            >
              {{ item.icfVersionNumber }}
            </el-tooltip>
          </div>
        </template>
        <template #icfVersionDate="scope">
          <div class="tdTooltip" v-for="item in scope.row.icfVersions">
            {{ item.icfVersionDate }}
          </div>
        </template>
        <template #operate="scope">
          <span
            v-permission="['manage.meetingManagement.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible" style="height: 100%">
      <Details :request="onLoad" style="height: 100%" />
    </div>
  </div>
</template>
  
  <script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import Details from '@/views/meetingManagement/MeetingDetails.vue'
import { getStudyConferenceList } from '@/api/meeting'
import { useRoute } from 'vue-router'

export default defineComponent({
  name: 'MeetingManagement', // 会议管理
  components: {
    Details,
  },
  setup() {
    const route = useRoute()
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { label: '中心', prop: 'siteName', minWidth: 100 },
        { label: '会议号', prop: 'conferenceId', minWidth: 100 },
        { label: '会议主题', prop: 'conferenceTitle', minWidth: 250 },
        { label: '创建时间', prop: 'createTime', minWidth: 150 },
        { label: '创建人', prop: 'createUserName', minWidth: 100 },
        { label: '计划开始时间', prop: 'startTime', minWidth: 150 },
        { label: '计划结束时间', prop: 'endTime', minWidth: 150 },
        { label: '状态', prop: 'conferenceStatus', minWidth: 100 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        // labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '状态',
            name: 'conferenceStatus',
            defaultValue: null,
            options: [
              {
                value: 0,
                name: '未开始',
              },
              {
                value: 1,
                name: '进行中',
              },
              {
                value: 2,
                name: '已结束',
              },
            ],
          },
          {
            label: '会议号',
            name: 'conferenceId',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 获取列表数据方法
      getMeetingList: async (params) => {
        try {
          const rest = await getStudyConferenceList(studyId, params)
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
        }
      },
      // 编辑某项中心
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      onLoad: async () => {
        if (route.query.id != undefined) {
          RESEARCHCENTER_INFOS.contentVisible = true
        }
      },
      showToolTip: (txt) => {
        let cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0;
        let otCount = txt.length - cnCount
        return (cnCount * 2 + otCount) > 21
      }
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
  </script>
  <style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  overflow: hidden;
  display: contents;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    h3 {
      margin: 0;
    }
  }
}
.tdTooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.editBtnBlue {
  cursor: pointer;
}
</style>
  
  