<template>
  <div>
    <div class="consent">
      <div class="consent_info">
        <!-- {{ '关联知情：' + detailInfo?.versionNames }} -->
      </div>
      <div class="return_back">
        <el-button
          v-if="RESEARCHCENTER_INFOS.researchContent.id != undefined"
          @click="backRefresh"
          >返回</el-button
        >
      </div>
    </div>
    <div class="meetingInfoWithConsent">
      <div class="areaLeft">
        <h3>会议信息</h3>
        <div class="fieldLine">
          <span>中心</span>
          <span>{{ detailInfo?.siteName }}</span>
        </div>
        <div class="fieldLine">
          <span>会议号</span>
          <span>{{ detailInfo?.conferenceId }}</span>
        </div>
        <div class="fieldLine">
          <span>会议主题</span>
          <span>{{ detailInfo?.conferenceTitle }}</span>
        </div>
        <div class="fieldLine">
          <span>会议属性</span>
          <span>{{ detailInfo?.conferenceType }}</span>
        </div>
        <div class="fieldLine" v-if="detailInfo?.versionNames != null">
          <span>关联知情</span>
          <span>{{ detailInfo?.versionNames }}</span>
        </div>
        <div class="fieldLine" v-if="detailInfo?.visitName != null">
          <span>关联访视</span>
          <span>{{ detailInfo?.visitName }}</span>
        </div>
        <div class="fieldLine">
          <span>创建时间</span>
          <span>{{ detailInfo?.createTime }}</span>
        </div>
        <div class="fieldLine">
          <span>创建人</span>
          <span>{{ detailInfo?.createUserName }}</span>
        </div>
        <div class="fieldLine">
          <span>计划开始时间</span>
          <span>{{ detailInfo?.startTime }}</span>
        </div>
        <div class="fieldLine">
          <span>计划结束时间</span>
          <span>{{ detailInfo?.endTime }}</span>
        </div>
        <div class="fieldLine">
          <span>状态</span>
          <span>{{ detailInfo?.conferenceStatus }}</span>
        </div>
      </div>
      <div class="areaRight">
        <h3>会议成员</h3>
        <div>
          <div class="fieldLine">
            <span>拟参会</span>
            <span>{{ plannedUser }}</span>
          </div>
          <div class="fieldLine">
            <span>已参会</span>
            <span>{{ joinedUser }}</span>
          </div>
          <div class="fieldLine">
            <span>未参会</span>
            <span>{{ notJoinedUser }}</span>
          </div>
        </div>
        <h3>会议轨迹</h3>
        <div style="margin-right: 40px">
          <div class="fieldLine" v-for="record in detailInfo?.records">
            <span>{{ record.createTime }}</span>
            <span>{{
              record.roleName +
              '（' +
              record.userName +
              (record.displayName == null || record.displayName == ''
                ? ''
                : '，' + record.displayName) +
              '），' +
              record.operateContent
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted, nextTick, ref } from 'vue'
import { getConferenceDetail } from '@/api/meeting'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'

export default defineComponent({
  name: 'MeetingDetails',
  props: {
    request: {
      type: Function,
      default: () => { },
    },
  },
  setup({ request }) {
    const store = useStore()
    const route = useRoute()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const { studyId } = store.state.account.userinfo
    const state = reactive({
      plannedUser: '',
      joinedUser: '',
      notJoinedUser: '',
      detailInfo: null,
      onLoad: () => {
        getConferenceDetail(
          RESEARCHCENTER_INFOS.researchContent.id == undefined ? route.query.id : RESEARCHCENTER_INFOS.researchContent.id
        ).then((res) => {
          state.detailInfo = res
          // 构造会议成员三个字段
          for (let i = 0; i < res.users.length; i++) {
            var tmpStr = res.users[i].roleName.replaceAll(",", "，");
            var tmpUserStr = (res.users[i].userName == null || res.users[i].userName == '') ? "" : "（" + (res.users[i].userType == 4 ? ((res.users[i]?.patNumber == null || res.users[i]?.patNumber == '') ? res.users[i].userName : res.users[i].userName + "，" + res.users[i]?.patNumber) : res.users[i].userName) + "）";
            if (res.users[i].isJoin) {
              state.joinedUser += tmpStr + tmpUserStr + "、";
            }
            else {
              state.notJoinedUser += tmpStr + tmpUserStr + "、";
            }
            state.plannedUser += tmpStr + tmpUserStr + "、";
          }
          state.joinedUser = state.joinedUser.length > 0 ? state.joinedUser.substring(0, state.joinedUser.length - 1) : '';
          state.notJoinedUser = state.notJoinedUser.length > 0 ? state.notJoinedUser.substring(0, state.notJoinedUser.length - 1) : '';
          state.plannedUser = state.plannedUser.length > 0 ? state.plannedUser.substring(0, state.plannedUser.length - 1) : '';
        })
      },
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.refresh()
        RESEARCHCENTER_INFOS.contentVisible = false
      }
    })

    onMounted(async () => {
      state.onLoad();
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  }
})
</script>
<style lang="less" scoped>
.consent {
  display: inline-flex;
  width: 100%;
  margin-bottom: 10px;
  font-weight: bold;
  & > .consent_info {
    width: 80%;
  }
  & > .return_back {
    width: 20%;
    text-align: right;
    bottom: 4px;
    position: relative;
  }
}
.meetingInfoWithConsent {
  width: 100%;
  height: calc(100% - 40px);
  display: flex;
}
.meetingInfoWithoutConsent {
  width: 100%;
  height: calc(100% - 5px);
  display: flex;
}
.fieldLine {
  width: 100%;
  display: flex;
  margin-top: 20px;
  & > span:first-child {
    width: 30%;
    text-align: right;
    color: rgb(156, 152, 152);
  }
  & > span:nth-child(2) {
    width: 70%;
    margin-left: 20px;
    margin-right: 20px;
  }
}
.areaLeft {
  width: 30%;
  background: #ffffff;
  margin-right: 20px;
  & > h3 {
    margin: 20px;
  }
}
.areaRight {
  width: 70%;
  background: #ffffff;
  padding-bottom: 20px;
  background: #ffffff;
  & > h3 {
    margin: 20px;
  }
  & > div:first-of-type {
    overflow-y: hidden;
    padding-bottom: 20px;
    height: 15%;
  }
  & .fieldLine {
    width: 100%;
    display: flex;
    margin-top: 20px;
    & > span:first-child {
      width: 62px;
      text-align: left;
      color: rgb(156, 152, 152);
      margin-left: 40px;
    }
    & > span:nth-child(2) {
      width: calc(100% - 62px);
      margin-left: 20px;
      margin-right: 20px;
    }
  }
  & .fieldLine:first-child {
    margin-top: 0;
  }
  & > div:nth-of-type(2) {
    & > .fieldLine {
      & > span:first-child {
        width: 190px;
      }
      & > span:nth-child(2) {
        width: calc(100% - 190px);
      }
    }
    height: calc(85% - 150px);
    overflow-y: auto;
    // display: none;
    &::-webkit-scrollbar {
      // display: none;
      width: 6px;
    }
    /* 修改滚动条的滑块颜色 */
    &::-webkit-scrollbar-thumb {
      background-color: #e9e9e9;
      border-radius: 5px;
    }
    /* 修改滚动条的滑块悬停时的颜色 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #c7c7c7;
      border-radius: 5px;
    }
  }
}
</style>