<template>
  <div>
    <div class="consent">
      <div
        :style="{
          visibility: detailInfo?.versionNames != null ? '' : 'hidden',
        }"
        class="consent_info"
      ></div>
      <div class="return_back">
        <el-button @click="backRefresh">返回</el-button>
        <el-button
          v-if="detailInfo?.clarifyTypeStatus != 1"
          v-permission="['manage.clarifyManagement.closeClarification']"
          type="primary"
          @click="closeClarify"
          >关闭澄清</el-button
        >
      </div>
    </div>
    <div class="meetingInfoWithConsent">
      <div class="areaLeft">
        <div class="areaLeftTop">
          <h3>受试者</h3>
          <div class="fieldLine">
            <span>中心</span>
            <span>{{ detailInfo?.siteName }}</span>
          </div>
          <div class="fieldLine">
            <span>受试者编号</span>
            <span>{{ detailInfo?.patNumber }}</span>
          </div>
        </div>
        <div class="areaLeftBottom">
          <h3>数据</h3>
          <div class="mxHeight">
            <div class="fieldLine">
              <span>表单父级</span>
              <span>{{ detailInfo?.parentTypeStr }}</span>
            </div>
            <div class="fieldLine">
              <span>表单名称</span>
              <span>{{ detailInfo?.questName }}</span>
            </div>
            <div class="fieldLine">
              <span>行号</span>
              <span>{{ detailInfo?.rowNum }}</span>
            </div>
            <div class="fieldLine">
              <span>题目</span>
              <span v-html="detailInfo?.fieldLabel" class="showHtml"></span>
            </div>
            <div class="fieldLine">
              <span>结果</span>
              <span v-if="detailInfo?.crfFieldControl != 2">{{
                detailInfo?.fieldDisplayValue 
              }}</span>
              <div
                class="el-image-box"
                v-if="
                  detailInfo?.crfFieldControl === 2 && detailInfo?.fieldValue
                "
              >
                <div
                  v-for="(iteImgChildren, inx) in detailInfo?.fieldValue.split(
                    ','
                  )"
                  :key="inx"
                >
                  <el-image
                    v-if="iteImgChildren"
                    style="width: 100px; height: 100px"
                    class="el-image-more"
                    :src="iteImgChildren"
                    :preview-src-list="previewUrllist"
                    @click="elimgclick(detailInfo, inx)"
                  />
                </div>
              </div>
            </div>
            <div class="fieldLine">
              <span>填写人</span>
              <span>{{ detailInfo?.oprtUserTypeName }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="areaRight">
        <h3>澄清</h3>
        <div>
          <div
            class="fieldLineLeft"
            v-for="(question, index) in detailInfo?.clarifyReply"
          >
            <div>
              <span>{{
                question?.contentType == 1
                  ? '提问'
                  : question?.contentType == 2
                  ? '回复'
                  : '关闭澄清'
              }}</span>
              <span>{{ question?.content }}</span>
            </div>
            <div>
              <span>操作人</span>
              <span>{{ question?.oprtUserName }}</span>
            </div>
            <div>
              <span>操作时间</span>
              <span>{{ question?.oprtTime }}</span>
            </div>
            <el-divider
              class="divider"
              v-if="index != detailInfo?.clarifyReply.length - 1"
            />
          </div>
        </div>
      </div>
    </div>
    <trial-dialog
      v-model="dialogVisible"
      :my-dialog-body-style="{ width: '40%' }"
      class="closeClarifyDialog"
    >
      <template #DialogBody>
        <div class="mb-5">
          <span class="mr-5 ft-18-px font-semibold">关闭澄清</span>
        </div>
        <el-form
          ref="closeClarifyRef"
          :model="closeClarifyFormData"
          :rules="rules"
          label-position="top"
        >
          <el-form-item label="关闭理由" prop="closeClarifyReason">
            <el-input
              v-model="closeClarifyFormData.closeClarifyReason"
              type="textarea"
              maxlength="200"
              rows="8"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="centerflex">
          <el-button :loading="loading" @click="dialogVisible = false"
            >取 消</el-button
          >
          <el-button
            type="primary"
            :loading="loading"
            @click="submitCloseClarify"
            >确 定</el-button
          >
        </div>
      </template>
    </trial-dialog>
  </div>
</template>
  <script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted, nextTick, ref } from 'vue'
import { getClarifyDetail, closeClarify } from '@/api/clarify'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { getQuestFileImage } from '@/api/subjectsList'

export default defineComponent({
  name: 'ClarifyDetails',
  props: {
    request: {
      type: Function,
      default: () => { },
    },
  },
  setup() {
    const store = useStore()
    const route = useRoute()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    // const { studyId } = store.state.account.userinfo
    const state = reactive({
      btnCloseClarifyVisible: false,
      previewUrllist: [],
      closeClarifyRef: null,
      loading: false,
      closeClarifyFormData: {
        closeClarifyReason: ''
      },
      rules: {
        closeClarifyReason: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      dialogVisible: false,
      detailInfo: null,
      onLoad: () => {
        // 关闭澄清权限
        const closeClarifyRoles = ['admin', 'PI', 'SUBI', 'CRC', 'DM']
        const role = store.state.account.userinfo?.roles || []
        state.btnCloseClarifyVisible = closeClarifyRoles.some((item) => role.includes(item))
        getClarifyDetail(
          RESEARCHCENTER_INFOS.researchContent.id == undefined ? route.query.id : RESEARCHCENTER_INFOS.researchContent.id
        ).then((res) => {
          state.detailInfo = res
        })
      },
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.refresh()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      closeClarify: () => {
        state.dialogVisible = true
      },
      submitCloseClarify: () => {
        if (state.detailInfo?.oprtUserId && state.detailInfo?.oprtUserId === store.state.studyItem?.dctUserId) {
          ElMessage.error("发起澄清和关闭澄清的不能是同一个人");
          return
        }
        state.closeClarifyRef.validate((valid) => {
          state.loading = true
          if (valid) {
            closeClarify({ id: RESEARCHCENTER_INFOS.researchContent.id, closeClarifyReason: state.closeClarifyFormData.closeClarifyReason, dctQuestFieldDataId: state.detailInfo.dctQuestFieldDataId }).then(res => {
              if (res) {
                ElMessage.success('关闭澄清成功');
                state.loading = false
                RESEARCHCENTER_INFOS.resetMethod.handleReset()
                RESEARCHCENTER_INFOS.contentVisible = false
              }
            })
          }
        })
      },
      elimgclick: (item, index) => {
        state.previewUrllist = []
        const fileId = item.fieldDisplayValue.split(',')[index]
        getQuestFileImage(fileId).then((res: any) => {
          state.previewUrllist.push(res?.url)
        })
      },
    })

    onMounted(async () => {
      state.onLoad();
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  }
})
  </script>
  <style lang="less" scoped>
.consent {
  display: inline-flex;
  width: 100%;
  margin-bottom: 10px;
  font-weight: bold;
  & > .consent_info {
    width: 80%;
  }
  & > .return_back {
    width: 20%;
    text-align: right;
    bottom: 4px;
    position: relative;
  }
}
.meetingInfoWithConsent {
  width: 100%;
  height: calc(100% - 40px);
  display: flex;
}
.meetingInfoWithoutConsent {
  width: 100%;
  height: calc(100% - 5px);
  display: flex;
}
.fieldLine {
  width: 100%;
  display: flex;
  margin-top: 20px;
  & > span:first-child {
    width: 20%;
    text-align: right;
    color: rgb(156, 152, 152);
  }
  & > span:nth-child(2) {
    width: 80%;
    margin-left: 20px;
  }
}
.areaLeft {
  width: 30%;
  & > .areaLeftTop {
    background: #ffffff;
    margin: 0 20px 20px 0;
    & > h3 {
      margin: 20px;
      display: inline-flex;
    }
    height: 25%;
  }
  & > .areaLeftBottom {
    margin: 0 20px 20px 0;
    background: #ffffff;
    & > h3 {
      margin: 20px;
      display: inline-flex;
    }
    height: calc(75% - 20px);
  }
}
.areaRight {
  width: 70%;
  background: #ffffff;
  padding-bottom: 20px;
  background: #ffffff;
  & > h3 {
    margin: 20px;
  }
  & > div {
    overflow-y: auto;
    padding-bottom: 20px;
    height: calc(100% - 80px);
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #e9e9e9;
      border-radius: 5px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: #c7c7c7;
      border-radius: 5px;
    }
  }
}
.fieldLineLeft {
  width: calc(100% - 40px);
  display: block;
  margin: 20px 0 0 20px;
  & > div {
    margin: 20px;
    display: block;
    & > span:nth-child(odd) {
      width: 10%;
      text-align: right;
      color: rgb(156, 152, 152);
      display: inline-block;
    }
    & > span:nth-child(even) {
      width: 90%;
      margin-left: 20px;
    }
  }
  & > .divider {
    width: calc(100% - 40px);
  }
}
.closeClarifyDialog {
  :deep(.my-dialog-body) {
    min-height: auto;
    h4 {
      margin: 0;
    }
  }
  .rescreen-tips {
    color: #f59a23;
  }
}
.showHtml {
  overflow: hidden;
  text-overflow: ellipsis;
  :deep(*) {
    margin: 0;
  }
}
.el-image-box {
  display: flex;
  width: 80%;
  margin-left: 20px;
  flex-wrap: wrap;
  .el-image-more {
    margin-right: 0.2rem;
  }
}
.mxHeight {
  overflow-y: auto;
  height: calc(100% - 100px);
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #e9e9e9;
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: #c7c7c7;
    border-radius: 5px;
  }
}
</style>