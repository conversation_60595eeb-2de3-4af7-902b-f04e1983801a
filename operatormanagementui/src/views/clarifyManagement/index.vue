<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getClarifyList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
      >
        <template #fieldLabel="scope">
          <div v-html="scope.row.fieldLabel" class="showHtml"></div>
        </template>
        <template #fieldDisplayValue="scope">
          <div
            v-html="scope.row.fieldDisplayValue"
            v-if="scope.row.crfFieldControl != 2"
          ></div>
          <div v-else style="display: flex">
            <div
              v-for="(iteImgChildren, inx) in scope.row?.fieldValue.split(',')"
              :key="inx"
            >
              <el-image
                v-if="iteImgChildren"
                style="width: 100px; height: 100px; margin-left: 5px"
                class="el-image-more"
                :src="iteImgChildren"
                :preview-src-list="scope.row?.fieldValue.split(',')"
              />
            </div>
          </div>
        </template>
        <template #operate="scope">
          <span
            v-permission="['manage.clarifyManagement.details']"
            class="editBtnBlue"
            @click="showClarifyDetail(scope.row)">
            详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible" style="height: 100%">
      <Details :request="onLoad" style="height: 100%" />
    </div>
  </div>
</template>
    
<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import Details from '@/views/clarifyManagement/ClarifyDetails.vue'
import { getClarifyList } from '@/api/clarify'
import { useRoute } from 'vue-router'
import { getQuestFileImage } from '@/api/subjectsList'

export default defineComponent({
  name: 'ClarifyManagement',
  components: {
    Details,
  },
  setup() {
    const route = useRoute()
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      previewUrllist: [],
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { label: '澄清ID', prop: 'id', minWidth: 80 },
        { label: '中心', prop: 'siteName', minWidth: 150 },
        { label: '受试者编号', prop: 'patNumber', minWidth: 100 },
        { label: '表单父级', prop: 'parentTypeStr', minWidth: 100 },
        { label: '表单名称', prop: 'questName', minWidth: 150 },
        { label: '行号', prop: 'rowNum', minWidth: 80 },
        { label: '题目', prop: 'fieldLabel', minWidth: 350, tdSlot: 'fieldLabel', width: 350 },
        { label: '结果', prop: 'fieldDisplayValue', minWidth: 350, tdSlot: 'fieldDisplayValue' },
        { label: '澄清发起人', prop: 'oprtUserName', minWidth: 100 },
        { label: '澄清发起时间', prop: 'oprtTime', minWidth: 150 },
        { label: '提问', prop: 'question', minWidth: 350 },
        { label: '状态', prop: 'clarifyTypeStatusStr', minWidth: 80 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        // labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '状态',
            name: 'status',
            defaultValue: null,
            options: [
              {
                value: 2,
                name: '未关闭',
              },
              {
                value: 1,
                name: '已关闭',
              },
            ],
          },
          {
            label: '澄清ID',
            name: 'clarifyId',
            type: 'input',
            defaultValue: null,
          },
          {
            label: '题目',
            name: 'fieldLabel',
            type: 'input',
            defaultValue: null,
          },
          {
            label: '受试者编号',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 获取列表数据方法
      getClarifyList: async (params) => {
        try {
          const rest = await getClarifyList(studyId, params)
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
        }
      },
      // 编辑某项中心
      showClarifyDetail: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      onLoad: async () => {
        if (route.query.id != undefined) {
          RESEARCHCENTER_INFOS.contentVisible = true
        }
      },
      elimgclick: (item, index) => {
        state.previewUrllist = []
        const fileId = item.fieldDisplayValue.split(',')[index]
        getQuestFileImage(fileId).then((res: any) => {
          state.previewUrllist.push(res?.url)
        })
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  overflow: hidden;
  display: contents;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    h3 {
      margin: 0;
    }
  }
}
.tdTooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.editBtnBlue {
  cursor: pointer;
}
.showHtml {
  overflow: hidden;
  text-overflow: ellipsis;
  :deep(*) {
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 0;
    margin-bottom: 0;
  }
}
// 暂时通过样式解决td里面图片预览问题
:deep(.el-table__cell),
:deep(.el-table__row) {
  z-index: unset !important;
  position: relative !important;
}
:deep(.el-image-viewer__mask) {
  z-index: 10000;
}
:deep(.el-image-viewer__close),
:deep(.el-image-viewer__prev),
:deep(.el-image-viewer__next),
:deep(.el-image-viewer__actions) {
  z-index: 10001;
}
</style>
    
    