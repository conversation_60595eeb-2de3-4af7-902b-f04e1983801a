<template>
  <div>
    <trial-table
      v-show="!showDetailsModule && !showManualReplenishmentFlag"
      ref="MaterialOrderTableRef"
      title=""
      search-form-label-position="right"
      :request="getList"
      :columns="materialOrderTableColumns"
      :search="searchConfig"
      :pagination="paginationConfig"
      @selectionChange="handleSelectionChange"
    >
      <template #toolbar>
        <el-button v-permission="['manage.materialOrder.manualOrder']" type="primary" @click="handleManualReplenishment">
          人工补单
        </el-button>
      </template>
      <template #operate="scope">
        <el-button v-permission="['manage.materialOrder.details']" :loading="detailsLoading" size="small" type="primary" text @click="editItemForm(scope.row,'Details')">
          详情
        </el-button>
        <el-button v-permission="['manage.materialOrder.orderRecords']" size="small" type="primary" text @click="editItemForm(scope.row,'log')">
          订单记录
        </el-button>
      </template>
      <template #orderTypeSort="scope">
        <span v-if="scope.row.orderType === 1">
          常规订单
        </span>
        <span v-else-if="scope.row.orderType === 2">
          人工补单
        </span>
      </template>
      <template #orderStateSlot="scope">
        <span v-if="scope.row?.orderState === 0">已下单</span>
        <span v-else-if="scope.row.orderState === 1">待发货</span>
        <span v-else-if="scope.row?.orderState === 2">待收货</span>
        <span v-else-if="scope.row?.orderState === 3">已完成</span>
        <span v-else-if="scope.row?.orderState === 4">已取消</span>
      </template>
      <template #materialsSlot="scope">
        <div
          v-for="(el,idx) in scope.row.materials"
          :key="idx"
        >
          <span v-if="el?.materialName">{{ el.materialName }},</span>
          <span v-if="el?.number">数量{{ el.number }}</span>
        </div>
      </template>
      <template #recipientsSlotProp="scope">
        <div>
          {{ scope.row.rcptName }}
        </div>
        <div>
          {{ scope.row.rcptPhoneNo }}
        </div>
      </template>
      <template #logisticsSlot="scope">
        <div>
          {{ scope.row.rcptProvinceName + scope.row.rcptCityName + scope.row.rcptAreaName }}
        </div>
        <div>
          {{ scope.row.rcptAddressDetail }}
        </div>
      </template>
      <template #logisticsProviderSlot="scope">
        <div>
          {{ scope.row.logisticsProvider }}
        </div>
        <div>
          {{ scope.row.waybillNo }}
        </div>
      </template>
    </trial-table>
    <!-- 订单详情模块 -->
    <div v-show="showDetailsModule" class="common-form-module">
      <div class="centerflex-h justify-between">
        <!-- 0 = 已下单, 1 = 待发货, 2 = 待收货, 3 = 已完成, 4 = 已取消 -->
        <h2 class="font-bold">订单状态：
          <span v-if="detailsModuleObj?.orderState === 0">已下单</span>
          <span v-else-if="detailsModuleObj?.orderState === 1">待发货</span>
          <span v-else-if="detailsModuleObj?.orderState === 2">待收货</span>
          <span v-else-if="detailsModuleObj?.orderState === 3">已完成</span>
          <span v-else-if="detailsModuleObj?.orderState === 4">已取消</span>
        </h2>
        <div>
          <el-button v-if="detailsModuleObj?.orderState === 0" type="primary" @click="handleCancelOrder">取消订单</el-button>
          <el-button
            @click="handleBackShowDetailsModule"
          >返回</el-button>
        </div>
      </div>
      <!-- 内容区 -->
      <div class="mb-9">
        <el-descriptions title="" :column="4" border>
          <el-descriptions-item label="订单类型:" label-align="left" align="center">
            <span v-if="detailsModuleObj?.orderType === 1"> 常规订单</span>
            <span v-else-if="detailsModuleObj?.orderType === 2"> 人工补单</span>
          </el-descriptions-item>
          <el-descriptions-item label="订单编号:" label-align="left" align="center">{{ detailsModuleObj?.orderNo || '' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间:" label-align="left" align="center">{{ detailsModuleObj?.createTimeStr || '' }}</el-descriptions-item>
          <el-descriptions-item label="创建人:" label-align="left" align="center">{{ detailsModuleObj?.creator || '' }}</el-descriptions-item>
          <el-descriptions-item label="中心:" label-align="left" align="center">{{ detailsModuleObj?.dctSiteName || '' }}</el-descriptions-item>
          <el-descriptions-item label="本次发放名称:" label-align="left" align="center">{{ detailsModuleObj?.ruleName || '' }}</el-descriptions-item>
          <el-descriptions-item label="取消时间:" label-align="left" align="center">{{ detailsModuleObj?.cancelTimeStr || '' }}</el-descriptions-item>
          <el-descriptions-item label="取消人:" label-align="left" align="center">{{ detailsModuleObj?.lastUpdator || '' }}</el-descriptions-item>
          <el-descriptions-item label="订单备注:" label-align="" align="left">
            {{ detailsModuleObj?.remark || '' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="font-bold mb-3">物资信息</div>
      <trial-table
        v-if="showDetailsModule"
        ref="MaterialsListRef"
        title=""
        :request="getMaterialsList"
        :span-method="spanMethod"
        :columns="materialsListColumns"
        :pagination="false"
        class="tab-pro"
      />
      <div class="mt-6 mb-3 font-bold">收件信息</div>
      <div class="mb-3">
        <el-descriptions title="" :column="3" border>
          <el-descriptions-item label="联系人:" label-align="left" align="center">
            {{ detailsModuleObj?.rcptName || '' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系人电话:" label-align="left" align="center">{{ detailsModuleObj?.rcptPhoneNo || '' }}</el-descriptions-item>
          <el-descriptions-item label="所在地区:" label-align="left" align="center">{{ (detailsModuleObj?.rcptProvinceName || '')	+ (detailsModuleObj?.rcptCityName || '') + (detailsModuleObj?.rcptAreaName || '') }}</el-descriptions-item>
          <el-descriptions-item label="详细地址:" label-align="left" align="left">{{ detailsModuleObj?.rcptAddressDetail || '' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="mt-9 mb-3 font-bold">物流信息</div>
      <div class="flex mb-6">
        <el-descriptions title="" :column="3" border>
          <el-descriptions-item min-width="130px" label="物流供应商:" label-align="left" align="center">
            {{ detailsModuleObj?.logisticsProvider || '' }}
          </el-descriptions-item>
          <el-descriptions-item min-width="130px" label="快递单号:" label-align="left" align="center">{{ detailsModuleObj?.waybillNo || '' }}</el-descriptions-item>
          <el-descriptions-item min-width="130px" label="物流状态:" label-align="left" align="center">{{ detailsModuleObj?.traceStatusStr || '' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 附件信息 -->
      <div class="w-full mb-[20px] bg-white rounded overflow-hidden">
        <div class="mb-3 font-bold">附件信息</div>
        <trial-table
          ref="annexTableRef"
          style="width: 60%;"
          border
          :request="getAnnexTableList"
          :columns="annexTableColumns"
          :pagination="false"
          class="tab-pro"
        >
          <template #operate="scope">
            <el-button text size="small" type="primary" @click="previewItemForm(scope.row)">
              预览
            </el-button>
            <el-button text size="small" type="primary" @click="downloadItemForm(scope.row)">
              下载
            </el-button>
          </template>
        </trial-table>
      </div>
      <div class="mb-3 font-bold">运单详情</div>
      <div class="order-management-details-bottom">
        <div
          v-for="(item, idx) in detailsModuleObj.materialOrderTraces"
          :key="idx"
          class="bottomBox"
        >
          <span />
          <div class="w-full">
            <p v-if="item?.time" v-html="item.time" />
            <p v-if="item?.desc" style="margin-top: 5px" v-html="item.desc" />
          </div>
        </div>
      </div>
    </div>
    <!-- 订单记录模块 -->
    <OrderHistoryForm
      ref="OrderHistoryFormRef"
      :form-data="formData"
      :request="refresh"
    />
    <div
      v-show="showManualReplenishmentFlag"
      class="common-form-module"
    >
      <div class="mb-5 centerflex-h justify-between">
        <div>人工补单<span class="text-orange-400">（适用于计划外物资发放的场景）</span></div>
        <div class="text-center">
          <el-button @click="closeDialogVisible">返回</el-button>
          <el-button
            type="primary"
            :loading="loadingManualReplenishment"
            @click="handleManualReplenishmentSubmit"
          >确认</el-button>
        </div>
      </div>
      <!-- 表单 -->
      <el-form ref="materialsFormRef" :model="manualReplenishmentruleForm" :rules="rules" label-position="top">
        <div class="mb-5">
          补发对象
        </div>
        <div class="flex">
          <el-form-item label="中心" prop="dctSiteId" class="mr-[1%]" style="width: 24%">
            <el-select
              v-model="manualReplenishmentruleForm.dctSiteId"
              class="w-full"
              placeholder="请选择"
              @change="dctSiteChange"
            >
              <el-option
                v-for="(item, index) in labourRep.sites"
                :key="index"
                :label="item.siteName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="OMS渠道映射" prop="omsChannelId" class="mr-[1%]" style="width: 24%">
            <el-select
              v-model="manualReplenishmentruleForm.omsChannelId"
              class="w-full"
              placeholder="请选择"
              @change="substanceTab"
            >
              <el-option
                v-for="(item, index) in labourRep.omsChannel"
                :key="index"
                :label="item.omsChannelName"
                :value="item.omsChannelId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="发放对象" class="mr-[1%]" style="width: 24%">
            <el-input v-model="manualReplenishmentruleForm.distributionTarget" class="w-full" disabled />
            <!-- <el-select
              v-model="manualReplenishmentruleForm.distributionTarget"
              class="w-full"
              placeholder="请选择"
              disabled
            >
              <el-option label="受试者端" value="受试者端" disabled />
            </el-select> -->
          </el-form-item>
          <el-form-item label="选择用户" prop="patientId" style="width: 24%">
            <el-select
              v-model="manualReplenishmentruleForm.patientId"
              class="w-full"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in patientsUserInfo"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </div>
        <!-- <div class="flex">
          <el-form-item
            label="物资"
            prop="replenishmentMaterial.materialId"
            class="mr-[1%]"
            style="width: 24%"
          >
            <el-select
              v-model="manualReplenishmentruleForm.replenishmentMaterial.materialId"
              class="w-full"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in labourRep.materialArr"
                :key="index"
                :label="item.materialName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="规格" class="mr-[1%]" style="width: 24%">
            <el-input v-model.trim="manualReplenishmentruleForm.materialSpecs" disabled />
          </el-form-item>
          <el-form-item
            label="数量"
            prop="replenishmentMaterial.num"
            style="width: 24%"
          >
            <el-input
              v-model.trim="manualReplenishmentruleForm.replenishmentMaterial.num"
              placeholder="请输入"
              maxlength="666"
            />
          </el-form-item>
        </div> -->
        <div class="mb-5">
          收件信息
        </div>
        <div class="flex">
          <el-form-item label="联系人" prop="rcptName" class="mr-[1%]" style="width: 30%">
            <el-input
              v-model.trim="manualReplenishmentruleForm.rcptName"
              class="w-full"
              placeholder="请输入"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item label="联系人电话" prop="rcptPhoneNo" class="mr-[1%]" style="width: 30%">
            <el-input
              v-model.trim="manualReplenishmentruleForm.rcptPhoneNo"
              class="w-full"
              placeholder="请输入"
              maxlength="666"
            />
          </el-form-item>
        </div>
        <el-form-item label="所在地区" prop="address" style="width: 100%">
          <el-cascader
            v-model="manualReplenishmentruleForm.address"
            clearable
            style="width: 50%;max-width: 450px;"
            :options="cityOptions"
            :props="{
              expandTrigger: 'hover',
            }"
          />
        </el-form-item>
        <el-form-item label="详细地址" prop="rcptAddressDetail" class="mr-[1%]" style="width: 100%">
          <el-input
            v-model.trim="manualReplenishmentruleForm.rcptAddressDetail"
            class="w-full"
            placeholder="请输入"
            maxlength="200"
          />
        </el-form-item>
        <!-- 物资信息 -->
        <div class="centerflex-h">
          <h3 class="mr-4">物资信息</h3>
          <el-button type="primary" @click="handleApplyClick">选择</el-button>
        </div>
        <trial-table
          ref="releaseplanSubstanceRef"
          class="tab-pro mb-5"
          title=""
          :request="getSubstanceList"
          :columns="substanceColumns"
          :showbtnfalg="true"
        >
          <template #isRandomStr="scope">
            <span>
              {{ !scope.row?.isRandom ? '否' : '是' }}
            </span>
          </template>
          <template #materialTypeStr="scope">
            <span>
              {{ scope.row.materialType === '0' ? '实物' : '' }}
            </span>
          </template>
          <template #operate="scope">
            <span class="editBtnBlue mr-3" @click="substanceClick(scope.row)">
              设置
            </span>
          </template>
        </trial-table>
        <el-form-item label="订单备注">
          <el-input
            v-model.trim="manualReplenishmentruleForm.remark"
            class="w-full"
            placeholder="请输入"
            maxlength="200"
          />
        </el-form-item>
      </el-form>
    </div>
    <trial-dialog
      v-model="substanceFlagShow"
      :my-dialog-body-style="{
        width: '60%'
      }"
      :dialog-close="shuttleCancel"
    >
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="物资名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain size @click="shuttleCancel">取消</el-button>
          <el-button type="primary" size @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog
      v-model="substanceSetMes"
      :my-dialog-body-style="{
        width: '40%'
      }"
      :dialog-close="closeSubstanceSetMes"
    >
      <template #footer>
        <div class="substanceSetMes-title mb-5">{{ substanceForm.materialName }}</div>
        <el-form ref="substanceFormRef" :model="substanceForm" :rules="substanceRules" label-position="top">
          <el-form-item label="规格" prop="materialSpecs">
            <el-input
              v-model.trim="substanceForm.materialSpecs"
              class="w-full"
              maxlength="666"
              disabled
            />
          </el-form-item>
          <el-form-item label="数量" prop="number">
            <el-input
              v-model.trim="substanceForm.number"
              class="w-full"
              placeholder="请输入"
              maxlength="666"
            />
          </el-form-item>
        </el-form>
        <div class="flex justify-center">
          <el-button plain size @click="closeSubstanceSetMes">取消</el-button>
          <el-button type="primary" size @click="substanceConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from 'vue'
import OrderHistoryForm from '@/views/orderManagement/OrderHistoryForm.vue'
import { getManualReplenishment, getMaterialOrder,
  getMaterialOrderDetails, putCancelMaterialOrder,
  postManualReplenishment, getChannels } from '@/api/orderManagement'
import { useStore } from 'vuex'
import { getCitys } from '@/api/home'
import { getMaterials } from '@/api/orderManagement'
import { downloadFile } from '@/utils/index'
import { tableDateChange } from '@/utils'

export default defineComponent({
  name: 'MaterialOrder', // 物资订单
  components: {
    OrderHistoryForm
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    const { sites } = store.state.studyItem
    const state = reactive({
      MaterialOrderTableRef: null,
      MaterialsListRef: null,
      materialsListColumns: [
        { label: '物资ID', prop: 'omsMaterialSpecIdStr', minWidth: 160 },
        { label: '物资名称', prop: 'materialName', minWidth: 280 },
        { label: '规格', prop: 'materialSpecs', minWidth: 160 },
        // itemList-》materialNumber药物编码-》shelfDate有效期
        { label: '药物编码', prop: 'materialNumber', minWidth: 160 },
        { label: '批次号', prop: 'batchNumberStr', minWidth: 160 },
        { label: '有效期', prop: 'shelfDate', minWidth: 160 },
        { label: '数量', prop: 'number', minWidth: 100 },
      ],
      showDetailsModule: false,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      materialOrderTableColumns: [
        // { type: 'selection' },
        { label: '订单类型', numberToStingSort: 'orderTypeSort', minWidth: 120 },
        { label: '中心', prop: 'dctSiteName', options: [], minWidth: 180 },
        { label: '创建时间', prop: 'createTimeStr', minWidth: 180 },
        { label: '订单编号', prop: 'orderNo', minWidth: 220 },
        { label: '订单状态', tdSlot: 'orderStateSlot', minWidth: 120 },
        { label: '物资信息', tdSlot: 'materialsSlot', minWidth: 220 },
        {
          label: '收件人',
          width: 180,
          tdSlot: 'recipientsSlotProp', // 自定义单元格内容的插槽名称
        },
        {
          label: '收件地址',
          width: 280,
          tdSlot: 'logisticsSlot', // 自定义单元格内容的插槽名称
        },
        { label: '物流信息', tdSlot: 'logisticsProviderSlot', minWidth: 180 },
        // orderNo
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '70px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '订单类型',
            options: [
              {
                name: '常规订单',
                value: '1'
              },
              {
                name: '人工补单',
                value: '2'
              },
            ],
            name: 'orderType',
            defaultValue: '',
          },
          {
            type: 'select',
            label: '中心',
            name: 'siteId',
            defaultValue: null,
            options: sites,
            filterable: true,
          },
          {
            type: 'input',
            label: '物资信息',
            name: 'materialName',
          },
          {
            type: 'daterange',
            label: '创建日期',
            name: 'createTimeEndOrCreateTimeBegin',
            // defaultValue: [
            //   new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
            //   new Date(),
            // ],
            filterable: true,
            clearable: false,
          },
          {
            type: 'select',
            label: '订单状态',
            name: 'orderState',
            defaultValue: null,
            options: [
              {
                name: '已下单',
                value: '0'
              },
              {
                name: '待发货',
                value: '1'
              },
              {
                name: '待收货',
                value: '2'
              },
              {
                name: '已完成',
                value: '3'
              },
              {
                name: '已取消',
                value: '4'
              },
            ],
            filterable: true,
          },
          {
            type: 'input',
            label: '订单编号',
            name: 'orderNo',
            defaultValue: '',
          },
          {
            type: 'input',
            label: '快递单号',
            name: 'waybillNo',
            defaultValue: '',
          },
        ],
      },
      // 1 = 常规订单, 2 = 人工补单
      // 0 = 已下单, 1 = 待发货, 2 = 待收货, 3 = 已完成, 4 = 已取消
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      selectedItems: [],
      //
      annexTableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      annexTableColumns: [
        { label: '名称', prop: 'fileTypeStr', minWidth: 150 },
        { label: '创建时间', prop: 'createTime', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          width: 130,
          align: 'center',
          tdSlot: 'operate',
        },
      ],
      // 请求函数
      async getAnnexTableList() {
        try {
          // fileType类型 fileTypeStr名称 url createTime时间
          // order.value.materialOrderAttachment
          const res = await state.detailsModuleObj
          return {
            data: res.materialOrderAttachments,
            total: +res.materialOrderAttachments?.length || 0,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 预览
      previewItemForm: (row) => {
        window.open(row.url)
      },
      // 下载
      downloadItemForm: (row) => {
        downloadFile(row.url, row.fileTypeStr)
      },
      OrderHistoryFormRef: null,
      formData: { orderRecord: '' },
      detailsModuleObj: {},
      // 选择
      handleSelectionChange: (arr) => {
        state.selectedItems = arr
      },
      spanArr: [],
      spanMethod: ({ rowIndex, columnIndex }) => {
        return state.spanArr[rowIndex * state.materialsListColumns.length + columnIndex]
      },
      listArr: (list) => {
        const arr: any = []
        // let a = true
        list.forEach((item) => {
          // if (a && item.itemList?.length) {
          //   console.log(item.itemList)
          //   item.itemList.push(item.itemList[0])
          //   a = false
          // }
          if (item.itemList?.length) {
            item.itemList.forEach((ite) => {
              arr.push({ ...item, ...ite })
            })
          } else {
            arr.push({ ...item })
          }
        })
        return arr
      },
      getMaterialsList: () => {
        let list = []
        if (state.detailsModuleObj?.materials && state.detailsModuleObj?.materials?.length) {
          state.detailsModuleObj.materials.forEach((item) => {
            if (item?.batchNumber && item.batchNumber.includes(',')) {
              item.batchNumberArr = item.batchNumber.split(',')
              let num = 0
              if (item?.itemList) {
                item.itemList.forEach((ite, index) => {
                  if (ite?.materialNumber) {
                    num++
                  }
                  if (num !== 0) { // 随机的
                    ite.batchNumberStr = item.batchNumberArr[index]
                    ite.number = 1
                  }
                })
              }
              if (num === 0) {
                item.batchNumberStr = item.batchNumber
              }
            }
          })
          list = state.listArr(state.detailsModuleObj?.materials)
          state.spanArr = tableDateChange(list, state.materialsListColumns, ['omsMaterialSpecIdStr', 'materialName', 'materialSpecs', 'batchNumber', 'operate'], ['omsMaterialSpecIdStr'])
        }
        return {
          data: list || [],
          total: state.detailsModuleObj?.materials?.length || 0,
        }
      },
      // 请求函数
      async getList(params) {
        if (params.createTimeEndOrCreateTimeBegin) {
          params.createTimeBegin = params.createTimeEndOrCreateTimeBegin[0]
          params.createTimeEnd = params.createTimeEndOrCreateTimeBegin[1]
        }
        delete params.createTimeEndOrCreateTimeBegin
        try {
          const { items, totalItemCount } = await getMaterialOrder(studyId, params)
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: items || [],
            total: totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      refresh: () => {
        state.MaterialOrderTableRef.refresh()
      },
      loadingManualReplenishment: false,
      // 补单-表单信息
      manualReplenishmentruleForm: {
        dctSiteId: '',
        omsChannelId: '',
        channelId: '',
        patientId: '',
        remark: '',
        rcptName: '',
        rcptPhoneNo: '',
        address: [],
        rcptProvinceCode: '',
        rcptProvinceName: '',
        rcptCityCode: '',
        rcptCityName: '',
        rcptAreaCode: '',
        rcptAreaName: '',
        rcptAddressDetail: '',
        // replenishmentMaterial: {
        //   materialId: '',
        //   num: null
        // },
        distributionTarget: '受试者端'
      },
      labourRep: {}, // 人工补单内的所有数据
      patientsUserInfo: [],
      // 中心选择的dctSiteChange
      dctSiteChange: (val) => {
        state.patientsUserInfo = []
        state.manualReplenishmentruleForm.omsChannelId = ''
        // OMS渠道映射
        getChannels(val).then((res) => {
          state.labourRep.omsChannel = res
        })
        // 选择用户 state.ruleForm.patientId = ''
        state.manualReplenishmentruleForm.patientId = ''
        if (state.labourRep?.patients?.length) {
          state.labourRep.patients.forEach((item) => {
            // console.log(val, item.dctSiteId)
            if (val === item.dctSiteId) {
              state.patientsUserInfo.push(item)
            }
          })
        }
      },
      // 城市
      cityOptions: [],
      // 人工补单=》弹窗
      showManualReplenishmentFlag: false,
      materialsFormRef: null,
      //
      rules: {
        dctSiteId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        omsChannelId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        patientId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        // 'replenishmentMaterial.materialId': [
        //   { required: true, message: '请选择', trigger: 'change' },
        // ],
        // 'replenishmentMaterial.num': [
        //   { required: true, message: '请输入', trigger: 'blur' },
        //   {
        //     pattern: /^[1-9]\d*$/,
        //     message: '请填写正整数',
        //     trigger: 'blur',
        //   },
        // ],
        rcptName: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        rcptPhoneNo: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        address: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        rcptAddressDetail: [
          { required: true, message: '请输入', trigger: 'blur' },
        ]
      },
      closeDialogVisible: () => {
        state.showManualReplenishmentFlag = false
        state.materialsFormRef.resetFields()
      },
      // 人工补单确定
      handleManualReplenishmentSubmit: () => {
        // 可能会处理城市
        state.materialsFormRef.validate((valid) => {
          if (valid) {
            // 处理城市
            if (state.manualReplenishmentruleForm.address) {
              state.cityOptions.forEach((item: any) => {
                item.children.forEach((itemClild: any) => {
                  itemClild.children.forEach((itemClildClild: any) => {
                    if (itemClildClild.value === state.manualReplenishmentruleForm.address[2]) {
                      state.manualReplenishmentruleForm.rcptProvinceName = item.label
                      state.manualReplenishmentruleForm.rcptCityName = itemClild.label
                      state.manualReplenishmentruleForm.rcptAreaName = itemClildClild.label
                      return
                    }
                  })
                })
              })
              state.manualReplenishmentruleForm.rcptProvinceCode = state.manualReplenishmentruleForm.address[0]
              state.manualReplenishmentruleForm.rcptCityCode = state.manualReplenishmentruleForm.address[1]
              state.manualReplenishmentruleForm.rcptAreaCode = state.manualReplenishmentruleForm.address[2]
            }
            state.labourRep.sites.forEach((item) => {
              if (item.id === state.manualReplenishmentruleForm.dctSiteId) {
                state.manualReplenishmentruleForm.dctSiteName = item.siteName
              }
            })
            let errorFlag = false
            if (state.releaseplanSubstanceRef.tableData?.length < 1) {
              ElMessage.error('请选择物资信息')
              return
            }
            const replenishmentMaterial = state.releaseplanSubstanceRef.tableData.map((e) => {
              e.materialSpecId = e.id
              if (!e.number) {
                errorFlag = true
              }
              return e
            })
            if (errorFlag) {
              ElMessage.error('请填写数量')
              return
            }
            state.loadingManualReplenishment = true
            const data = {
              ...state.manualReplenishmentruleForm,
              orderType: 2,
              dctPatientMaterialDistributionActionId: '',
              armCode: '',
              replenishmentMaterial
            }
            data.channelId = state.manualReplenishmentruleForm.omsChannelId
            state.labourRep?.patients.map(el => {
              if (el.id === state.manualReplenishmentruleForm.patientId) {
                data.armCode = el.armCode
              }
            })
            postManualReplenishment(store.state.studyItem.studyId, data).then(() => {
              ElMessage.success('保存成功')
              state.materialsFormRef.resetFields()
              state.showManualReplenishmentFlag = false
              state.loadingManualReplenishment = false
            }).catch(() => { state.loadingManualReplenishment = false })
          }
        })
      },
      // 人工补单
      handleManualReplenishment: () => {
        state.showManualReplenishmentFlag = true
        state.patientsUserInfo = []
        state.manualReplenishmentruleForm.remark = ''
        state.releaseplanSubstanceRef.tableData = []
        state.shuttleValue = []
        getManualReplenishment(studyId).then((res) => {
          state.labourRep = res
        })
      },
      substanceFlagShow: false,
      shuttleProps: {
        key: 'id',
        label: `label`,
        // label: 'materialName'
      },
      shuttleValue: [],
      shuttleOldData: [],
      shuttleData: [],
      releaseplanSubstanceRef: null,
      // 穿梭框的取消
      shuttleCancel: () => {
        state.substanceFlagShow = false
      },
      // 确认
      shuttleConfirm: () => {
        if (state.substanceFlagShow) {
          const tableList: any = []
          state.shuttleValue.forEach((item) => {
            state.shuttleOldData.forEach((e: any) => {
              if (e.id === item) {
                tableList.push(e)
              }
            })
          })
          state.releaseplanSubstanceRef.tableData = [...tableList]
          state.substanceFlagShow = false
        }
      },
      // 物资处理表格数据
      substanceTab: () => {
        getMaterials(studyId, { isPlan: true, channelId: state.manualReplenishmentruleForm.omsChannelId })
          .then((res: any) => {
            state.shuttleOldData = res
            state.shuttleData = res
          })
      },
      async getSubstanceList() {
        return {
          data: []
        }
      },
      //
      substanceColumns: [
        { label: '类型', tdSlot: 'materialTypeStr', width: '150' },
        { label: '物资ID', prop: 'omsMaterialId', width: '200' },
        { label: '物品名称', prop: 'materialName', width: '260' },
        { label: '随机发药', prop: 'isRandom', width: '120', tdSlot: 'isRandomStr' },
        { label: '规格', prop: 'materialSpecs', width: '200' },
        { label: '数量', prop: 'number', width: '150' },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 点击设置
      substanceClick: (row) => {
        state.substanceObj = row
        state.substanceForm = { ...row }
        state.substanceSetMes = true
      },
      substanceFormRef: null,
      substanceForm: {},
      substanceRules: {
        materialSpecs: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        number: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^[1-9]\d*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ]
      },
      substanceSetMes: false,
      // 关闭设置框
      closeSubstanceSetMes: () => {
        state.substanceSetMes = false
      },
      substanceObj: {}, // 选中的某行物资
      // 确认设置
      substanceConfirm: () => {
        state.substanceFormRef.validate((valid) => {
          if (valid) {
            state.substanceObj.number = state.substanceForm?.number
            state.closeSubstanceSetMes()
          }
        })
      },
      // 选择物资
      handleApplyClick: () => {
        if (!state.manualReplenishmentruleForm.omsChannelId) {
          ElMessage.warning('请先选择OMS渠道映射')
          return
        }
        state.substanceFlagShow = true
        // state.shuttleValue = []
        // state.shuttleData = []
        state.shuttleData.map((item: any) => {
          item.label = `${item.materialName},${item?.materialSpecs || ''}`
        })
      },
      detailsLoading: false,
      // 新增-编辑
      editItemForm: (row, flag) => {
        if (flag === 'Details') {
          state.detailsLoading = true
          getMaterialOrderDetails(studyId, row.id)
            .then((res) => {
              state.detailsModuleObj = res
              if (state.detailsModuleObj?.materialOrderAttachments) {
                state.annexTableRef.tableData = state.detailsModuleObj?.materialOrderAttachments
              }
              state.showDetailsModule = true
              state.detailsLoading = false
            }).catch(() => {
              state.detailsLoading = false
            })
        } else {
          // 订单记录弹窗
          state.formData = row
          state.OrderHistoryFormRef.dialogVisible = true
          state.OrderHistoryFormRef.myFormData = row
        }
      },
      handleBackShowDetailsModule: () => {
        state.showDetailsModule = false
        state.refresh()
      },
      handleCancelOrder: () => {
        ElMessageBox.confirm(
          '是否确认取消订单？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            putCancelMaterialOrder(studyId, state.detailsModuleObj.id)
              .then(() => {
                ElMessage.success('取消成功')
                state.handleBackShowDetailsModule()
              })
          })
          .catch(() => {})
      }
    })
    onBeforeMount(() => {
      // 城市
      getCitys().then((res: any) => {
        if (res && res?.length && Array.isArray(res)) {
          state.cityOptions = res
        }
      })
    })
    return {
      ...toRefs(state),
    }
  }
})
</script>

<style lang="less" scoped>
.w-full.bg-white.rounded.overflow-hidden,.tab-pro {
  :deep(.head) {
    padding: 0;
  }
}
.tab-pro {
  :deep(.my-el-table) {
    padding: 0 !important;
  }
}
</style>
