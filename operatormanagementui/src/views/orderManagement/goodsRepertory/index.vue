<template>
  <div>
    <div v-for="(item, index) in goodsArr" :key="index">
      <div class="flex items-center mb-3">
        <div class="mr-5">{{ item?.materialName }}</div>
        <el-button v-permission="['manage.goodsRepertory.exportExcel']" type="primary" @click="exportExcel(item)">导出</el-button>
      </div>
    </div>
  </div>
</template>

<script lang='ts'>
import { getMaterials } from '@/api/orderManagement'
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'GoodsRepertory', // 物资库存记录
  setup() {
    const store = useStore()
    const state = reactive({
      goodsArr: [],
      exportExcel: (item) => {
        window.open(
          `${
            window.location.origin
          }/api/Operator/Study/Material/Stock/Export?materialId=${item?.omsMaterialId}`
        )
      },
      // 进入页面加载，写在了onMounted中
      onLoad: async() => {
        const res: any = await getMaterials(store.state.studyItem.studyId)
        state.goodsArr = res
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
</style>
