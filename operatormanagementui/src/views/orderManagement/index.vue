<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title="运单列表"
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
      >
        <template #logistics="scope">
          <span> 订单编号：{{ scope.row.orderNo }} </span> <br>
          <span> 快递单号：{{ scope.row.waybillNo }} </span>
        </template>

        <template #shipper="scope">
          <span>
            {{ scope.row.shipperProvinceName }} {{ scope.row.shipperCityName }}
            {{ scope.row.shipperAreaName }}
          </span>
          <br>
          <span> {{ scope.row.shipperAddressDetail }} </span>
        </template>
        <template #rcpt="scope">
          <span>
            {{ scope.row.rcptProvinceName }} {{ scope.row.rcptCityName }}
            {{ scope.row.rcptAreaName }}
          </span>
          <br>
          <span> {{ scope.row.rcptAddressDetail }} </span>
        </template>
        <template #operate="scope">
          <span
            v-permission="['manage.orderManagement.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            订单详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <OrderManagementDetails :request="onLoad" />
    </div>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import OrderManagementDetails from '@/views/orderManagement/OrderManagementDetails.vue'

import {
  getLogisticsOrder, // 查询订单信息
  getGoodsType, // 获取课题下寄送物品类型
} from '@/api/orderManagement'
export default defineComponent({
  name: 'OrderManagement', // 订单管理
  components: {
    OrderManagementDetails,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { label: '中心', prop: 'siteName', width: 150 },
        { label: '创建人', prop: 'creator', width: 120 },
        { label: '创建时间', prop: 'createTime', width: 180 },
        {
          label: '物流',
          prop: 'logisticsProvider',
          width: 150,
        },
        { label: '编号', prop: 'orderNo', width: 250, tdSlot: 'logistics' },
        { label: '订单状态', prop: 'state', width: 120 },
        { label: '物品信息', prop: 'goodsTypeName', width: 150 },
        { label: '寄件地址', prop: 'shipper', width: 250, tdSlot: 'shipper' },
        { label: '收件地址', prop: ' rcpt', width: 250, tdSlot: 'rcpt' },
        { label: '期望上门时间', prop: 'expectPickupTime', width: 180 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
          width: 120,
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '物品信息',
            name: 'goodsTypeName',
            defaultValue: null,
            options: [],
            filterable: true,
          },
          {
            type: 'daterange',
            label: '创建日期',
            name: 'data',
            defaultValue: [
              new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
              new Date(),
            ],
            filterable: true,
            clearable: false,
          },
          {
            type: 'input',
            label: '创建人',
            name: 'creator',
            defaultValue: null,
          },
          {
            type: 'select',
            label: '订单状态',
            name: 'waybillState',
            defaultValue: null,
            options: [
              {
                value: 0,
                name: '已下单',
              },
              {
                value: 1,
                name: '待取件',
              },
              {
                value: 2,
                name: '配送中',
              },
              {
                value: 3,
                name: '已完成',
              },
              {
                value: 4,
                name: '已取消',
              },
            ],
          },
          {
            label: '订单编号',
            name: 'orderNo',
            type: 'input',
            defaultValue: null,
          },
          {
            label: '快递单号',
            name: 'waybillNo',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 获取列表数据方法
      getResearchCenterList: async(params) => {
        try {
          const myParams = { ...params }
          if (myParams.data) {
            myParams.createTimeBegin = myParams.data[0]
            myParams.createTimeEnd = myParams.data[1]
          } else {
            myParams.createTimeBegin = new Date(
              new Date().getTime() - 3600 * 1000 * 24 * 30
            )
            myParams.createTimeEnd = new Date()
          }
          delete myParams.data
          if (myParams?.goodsTypeName && state.searchConfig.fields[1]?.options.length) {
            state.searchConfig.fields[1].options.forEach((item: any) => {
              if (myParams.goodsTypeName === item.id) {
                myParams.goodsTypeName = item.goodsTypeName
              }
            })
          }
          const rest = await getLogisticsOrder(studyId, myParams)
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          //
        }
      },
      // 编辑某项中心
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      onLoad: async() => {
        getGoodsType(studyId).then((res:any) => {
          if (res?.length) {
            res.forEach((val) => {
              val.name = val.goodsTypeName
              val.value = val.id
            })
            state.searchConfig.fields[1].options = res
          }
        })
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
  <style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    h3 {
      margin: 0;
    }
  }
}
</style>
