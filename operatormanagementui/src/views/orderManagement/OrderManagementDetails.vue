<template>
  <div class="details">
    <div class="content-top">
      <div class="top">
        <h3>订单状态：{{ DetailsList?.state }}</h3>
        <div class="">
          <el-button
            v-show="DetailsList?.state == '已下单' || DetailsList?.state == '待取件'"
            size="large"
            style="background: #409eff; color: #fff"
            @click="cancelBtn"
          >取消订单</el-button>
          <el-button size="large" @click="backRefresh">返回</el-button>
        </div>
      </div>
    </div>
    <div class="contentTable">
      <div>
        <span>创建时间：{{ DetailsList?.createTime }}</span>
        <span>创建人：{{ DetailsList?.creator }}</span>
        <span style="display: flex">取消时间：
          <span
            v-if="DetailsList?.state == '已取消'"
            style="flex: 1; padding: 0; margin: 0"
          >{{ DetailsList?.lastUpdateTime }}</span>
        </span>
        <span style="display: flex">取消人：
          <span
            v-if="DetailsList?.state == '已取消'"
            style="flex: 1; padding: 0; margin: 0"
          >{{ DetailsList?.lastUpdator }}</span></span>
      </div>
      <div>
        <span>订单编号: {{ DetailsList?.orderNo }}</span>
        <span>快递单号：{{ DetailsList?.waybillNo }}</span>
        <span>物品信息：{{ DetailsList?.goodsTypeName }}</span>
        <span>寄件方式：{{ DetailsList?.shippingMethod }}</span>
      </div>
    </div>
    <div class="content">
      <div class="content-left">
        <h4>寄件人信息</h4>
        <div>联系人：{{ DetailsList?.shipperName }}</div>
        <div>联系人电话：{{ DetailsList?.shipperPhoneNo }}</div>
        <div>
          所在地区：{{ DetailsList?.shipperProvinceName }}
          {{ DetailsList?.shipperCityName }}{{ DetailsList?.shipperAreaName }}
        </div>
        <div>详细地址：{{ DetailsList?.shipperAddressDetail }}</div>
        <div>期望上门时间：{{ DetailsList?.expectPickupTime }}</div>
      </div>
      <div class="content-right">
        <h4>收件人信息</h4>
        <div>联系人：{{ DetailsList?.rcptName }}</div>
        <div>联系人电话：{{ DetailsList?.rcptPhoneNo }}</div>
        <div>
          所在地区: {{ DetailsList?.rcptProvinceName
          }}{{ DetailsList?.rcptCityName }}{{ DetailsList?.rcptAreaName }}
        </div>
        <div>详细地址：{{ DetailsList?.rcptAddressDetail }}</div>
      </div>
    </div>
    <h4 style="margin: 50px 0 10px 0">备注</h4>
    <div>
      {{ DetailsList?.remark }}
    </div>
    <h4 style="margin: 50px 0 10px 0">运单详情</h4>
    <div class="order-management-details-bottom">
      <div
        v-for="(item, idx) in DetailsList?.waybillRecords"
        :key="idx"
        class="bottomBox"
      >
        <span />
        <div class="w-full">
          <p v-html="item?.waybillTime" />
          <p style="margin-top: 5px" v-html="item?.logisticsInfo" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  getLogisticsOrderOrderId, // 获取物流订单详情信息
  putCancelLogisticsOrderOrderId, // 取消订单
} from '@/api/orderManagement'
import { useStore } from 'vuex'
import { parseTime } from '@/utils'
export default defineComponent({
  name: 'ElectronicInfosDetails', // 电子知情详情
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup({ request }) {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      studyName: store.state.studyItem.studyName,
      DetailsList: {},
      // 返回
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      // 取消订单
      cancelBtn: () => {
        putCancelLogisticsOrderOrderId(
          studyId,
          RESEARCHCENTER_INFOS.researchContent.id
        ).then((res) => {
          ElMessage.success({
            message: '取消成功',
            duration: 1000,
          })
          state.onLoad()
        })
      },
      onLoad: () => {
        getLogisticsOrderOrderId(
          studyId,
          RESEARCHCENTER_INFOS.researchContent.id
        ).then((res: any) => {
          if (res?.createTime) {
            res.createTime = parseTime(res.createTime, '{y}-{m}-{d} {h}:{i}')
          }
          if (res?.lastUpdateTime) {
            res.lastUpdateTime = parseTime(res.lastUpdateTime, '{y}-{m}-{d} {h}:{i}')
          }
          state.DetailsList = res
        })
      },
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.details {
  width: 100%;
  background: #fff;
  padding: 20px 20px;
  box-sizing: border-box;
  .content-top {
    width: 100%;
    background: #fff;
    margin-bottom: 20px;
    .top {
      display: flex;
      justify-content: space-between;
      h3 {
        margin: 0;
        line-height: 40px;
      }
    }
  }
  .contentTable {
    width: 100%;
    div {
      width: 98%;
      border: 1px solid #ccc;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      span {
        width: 25%;
        padding: 10px 20px;
        box-sizing: border-box;
        border-right: 1px solid #ccc;
        &:last-child {
          border-right: none;
        }
      }
      &:last-child {
        border-top: none;
      }
    }
  }
  .content {
    width: 100%;
    display: flex;
    margin-top: 20px;
    .content-left,
    .content-right {
      width: 50%;
      h4 {
        margin: 20px 0 10px 0;
      }
      div {
        margin-top: 10px;
      }
    }
  }
}
</style>
