<template>
  <el-dialog
    v-model="dialogVisible"
    :title="OrderHistoryFormTitle"
  >
    <el-form ref="OrderHistoryFormRef" :model="myFormData" :rules="rules" label-position="top">
      <el-form-item
        label="内容"
        :label-width="formLabelWidth"
        prop="orderRecord"
      >
        <el-input
          v-model.trim="myFormData.orderRecord"
          class="min-height-300-px"
          type="textarea"
          maxlength="999"
          autocomplete="off"
          autosize
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer centerflex">
        <el-button
          @click="closeDialogVisible"
        >取 消</el-button>
        <el-button v-permission="['manage.materialOrder.editRecord']" type="primary" :loading="submitLoading" @click="submit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
} from 'vue'
// import { useStore } from 'vuex'
// import { deepClone } from '@/utils'
import { postMaterialOrderRecord } from '@/api/orderManagement'

export default defineComponent({
  name: 'OrderHistoryForm', // 订单记录
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    },
    formData: {
      type: Object,
      // default: () => {}
    },
  },
  setup(props) {
    // const store = useStore()
    const state = reactive({
      myFormData: {
        orderRecord: '',
      },
      OrderHistoryFormTitle: '订单记录',
      OrderHistoryFormRef: null,
      dialogVisible: false,
      formLabelWidth: '86px',
      rules: {
        orderRecord: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      },
      submitLoading: false,
      closeDialogVisible: () => {
        state.dialogVisible = false
      },
      submit: () => {
        state.OrderHistoryFormRef.validate((valid) => {
          if (valid) {
            state.submitLoading = true
            postMaterialOrderRecord(state.myFormData)
              .then(() => {
                state.closeDialogVisible()
                props.request()
                state.submitLoading = false
                // eslint-disable-next-line no-undef
                ElMessage.success('保存成功')
              }).catch(() => {
                state.submitLoading = false
              })
          }
        })
      },
    })
    return {
      ...toRefs(state),
    }
  }
})
</script>

<style>
.min-height-300-px .el-textarea__inner {
  min-height: 300px !important;
}
</style>
