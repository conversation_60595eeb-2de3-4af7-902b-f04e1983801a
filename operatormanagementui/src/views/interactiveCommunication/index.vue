<template>
  <div class="researchCenter">
    <div>
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="subjectStatisticsRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :hideCenter="true"
        :search="searchConfig"
        :defaultSort="defaultSort"
        :pagination="paginationConfig"
        :showbtnfalgExcel="showbtnfalgExcel"
        :requestExportExcel="getResearchCenterExport"
      >
        <template #hideCenter>
          <div class="hideCenter">
            <div id="echartLine" class="echartDiv" />
          </div>
          <div class="Line" />
        </template>
      </trial-table>
    </div>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import * as echarts from 'echarts'
import {
  defineComponent,
  onBeforeMount,
  reactive,
  toRefs,
  onMounted,
} from 'vue'
import {
  getChatPatient, // 获取患者聊天
  getChatPatientExport, // 导出某课题下受试者沟通统计
} from '@/api/interactiveCommunication'
import { parseTime } from '@/utils'
export default defineComponent({
  name: 'InteractiveCommunication', // 受试者沟通统计
  components: {},
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    const echartInit = () => {
      var myChart = echarts.init(document.getElementById('echartLine'))
      // 指定图表的配置项和数据
      var option = {
        title: {
          text: '时间分布',
          subtext: '消息（条）',
        },
        grid: {
          top: '20%',
          bottom: '5%',
          left: '5%',
          right: '5%',
        },
        legend: {
          data: ['受试者发送消息次数', '研究人员回复次数'],
        },
        calculable: true,
        xAxis: [
          {
            type: 'category',
            data: state.echarts.xAxis,
            axisLabel: {
              padding: [0, 0, 0, -50], // 四个数字分别为上右下左与原位置距离
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: [
          {
            name: '受试者发送消息次数',
            type: 'bar',
            data: state.echarts.patientYAxis,
            color: '#0b9fc0',
            label: { show: true, position: 'top' },
          },
          {
            name: '研究人员回复次数',
            type: 'bar',
            data: state.echarts.doctorYAxis,
            color: '#e16f3a',
            label: { show: true, position: 'top' },
          },
        ],
      }
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
    }
    const state = reactive({
      // manage.interactiveCommunication.exportExcel
      showbtnfalgExcel: store.state.studyItem.permissions.includes(
        'manage.interactiveCommunication.exportExcel'
      ),
      subjectStatisticsRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      echarts: {
        xAxis: [],
        doctorYAxis: [],
        patientYAxis: [],
      },
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { label: '中心名称', prop: 'siteName', width: 250 },
        { label: '受试者编号', prop: 'patNumber', width: 100 },
        { label: '入组日期', prop: 'inGroup', width: 250 },
        {
          label: '受试者发送（条）',
          prop: 'numberOfSend',
          width: 180,
          sortable: true,
        },
        {
          label: '研究人员回复（条）',
          prop: 'numberOfReceive',
          width: 180,
          sortable: true,
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            // defaultValue: store.state.studyItem.sites[0].siteId,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'daterange',
            label: '日期',
            name: 'data',
            defaultValue: [
              new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
              new Date(),
            ],
            filterable: true,
            clearable: false,
          },
          {
            label: '病例编号',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 高亮当前排序，prop指定某一个参数，ascending 表示升序，descending 表示降序
      defaultSort: { prop: 'numberOfSend', order: 'descending' },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20, 50, 100],
        style: { textAlign: 'left' },
      },
      // 导出Excel方法
      getResearchCenterExport: async(params) => {
        const myParams = { ...params }
        // 如果没有默认选择第一条数据
        // if (!myParams.siteId) {
        //   myParams.siteId = store.state.studyItem.sites[0].siteId
        // }
        if (!myParams.siteId) {
          myParams.siteId = ''
        }
        if (!myParams.patNumber) {
          myParams.patNumber = ''
        }
        if (myParams.data) {
          myParams.startTime = myParams.data[0]
          myParams.endTime = myParams.data[1]
        } else {
          myParams.startTime = new Date(
            new Date().getTime() - 3600 * 1000 * 24 * 30
          )
          myParams.endTime = new Date()
        }
        delete myParams.data
        getChatPatientExport(studyId, myParams)
        window.open(
          `${window.location.origin}/api/Operator/DoctorPatient/${studyId}/Chat/Patient/Export?siteId=${myParams.siteId}&patNumber=${myParams.patNumber}&startTime=${myParams.startTime}&endTime=${myParams.endTime}`
        )
      },
      // 获取列表数据方法
      getResearchCenterList: async(params) => {
        const { studyId } = store.state.studyItem
        const myParams = { ...params }
        try {
          if (!myParams.siteId) {
            myParams.siteId = ''
          }
          if (myParams.data) {
            myParams.startTime = myParams.data[0]
            myParams.endTime = myParams.data[1]
          } else {
            myParams.startTime = new Date(
              new Date().getTime() - 3600 * 1000 * 24 * 30
            )
            myParams.endTime = new Date()
          }
          delete myParams.data
          // 如果没有默认选择第一条数据
          // if (!myParams.siteId) {
          //   myParams.siteId = store.state.studyItem.sites[0].siteId
          // }
          const rest = await getChatPatient(studyId, myParams)
          if (rest?.patientChat?.items && rest?.patientChat?.items.length) {
            rest?.patientChat?.items.forEach((item) => {
              if (item.inGroup) {
                item.inGroup = parseTime(new Date(item.inGroup), '{y}-{m}-{d}')
              }
            })
          }
          state.echarts = rest?.echarts
          echartInit()
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.patientChat.items,
            total: +rest.patientChat.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      onLoad: async() => {
        //
      },
    })
    // 挂载
    onMounted(() => {
      echartInit()
    })
    onBeforeMount(() => {
      state.onLoad()
    })
    return {
      echartInit,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    h3 {
      margin: 0;
    }
  }
}
.hideCenter {
  width: 100%;
  height: 400px;
  background: #fff;
  padding: 10px;
  box-sizing: border-box;
  .echartDiv {
    width: 100%;
    height: 100%;
  }
}
.Line {
  width: 100%;
  height: 10px;
  background: #f0f2f5;
}
</style>

