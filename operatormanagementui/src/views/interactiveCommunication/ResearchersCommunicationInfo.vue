<template>
  <div class="researchCenter">
    <div>
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="statisticsOfResearchersRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :default-sort="defaultSort"
        :search="searchConfig"
        :pagination="paginationConfig"
        :showbtnfalg-excel="showbtnfalgExcel"
        :request-export-excel="getResearchCenterExport"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, reactive, toRefs } from 'vue'
import {
  getChatDoctor, // 获取研究者聊天
  getChatDoctorExport, // 导出某课题下研究者沟通统计
} from '@/api/interactiveCommunication'
export default defineComponent({
  name: 'ResearchersCommunicationInfo', // 研究人员沟通统计
  components: {},
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    const state = reactive({
      // manage.researchersCommunicationInfo.exportExcel
      showbtnfalgExcel: store.state.studyItem.permissions.includes('manage.researchersCommunicationInfo.exportExcel'),
      statisticsOfResearchersRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { label: '中心名称', prop: 'siteName', width: 250 },
        { label: '姓名', prop: 'userName', width: 100 },
        { label: '角色', prop: 'userRoleName', width: 250 },
        {
          label: '回复受试者人数',
          prop: 'numberOfReplyUser',
          width: 180,
          sortable: true,
        },
        {
          label: '回复消息（条）',
          prop: 'numberOfReply',
          width: 180,
          sortable: true,
        },
        {
          label: '人均回复（条）',
          prop: 'averageNumberOfRecovery',
          width: 180,
          sortable: true,
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            // defaultValue: store.state.studyItem.sites[0].siteId,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'daterange',
            label: '日期',
            name: 'data',
            defaultValue: [
              new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
              new Date(),
            ],
            filterable: true,
            clearable: false,
          },
          {
            label: '姓名',
            name: 'doctorName',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 高亮当前排序，prop指定某一个参数，ascending 表示升序，descending 表示降序
      defaultSort: { prop: 'averageNumberOfRecovery', order: 'descending' },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 导出Excel方法
      getResearchCenterExport: async(params) => {
        const myParams = { ...params }
        // 如果没有默认选择第一条数据
        // if (!myParams.siteId) {
        //   myParams.siteId = store.state.studyItem.sites[0].siteId
        // }
        if (!myParams.siteId) {
          myParams.siteId = ''
        }
        if (!myParams.patNumber) {
          myParams.patNumber = ''
        }
        if (myParams.data) {
          myParams.startTime = myParams.data[0]
          myParams.endTime = myParams.data[1]
        } else {
          myParams.startTime = new Date(
            new Date().getTime() - 3600 * 1000 * 24 * 30
          )
          myParams.endTime = new Date()
        }
        if (!myParams.doctorName) {
          myParams.doctorName = ''
        }
        delete myParams.data
        getChatDoctorExport(studyId, myParams)
        window.open(
          `${window.location.origin}/api/Operator/DoctorPatient/${studyId}/Chat/Doctor/Export?siteId=${myParams.siteId}&doctorName=${myParams.doctorName}&startTime=${myParams.startTime}&endTime=${myParams.endTime}`
        )
      },
      // 获取列表数据方法
      getResearchCenterList: async(params) => {
        const { studyId } = store.state.studyItem
        const myParams = { ...params }
        try {
          if (!myParams.siteId) {
            myParams.siteId = ''
          }
          if (myParams.data) {
            myParams.startTime = myParams.data[0]
            myParams.endTime = myParams.data[1]
          } else {
            myParams.startTime = new Date(
              new Date().getTime() - 3600 * 1000 * 24 * 30
            )
            myParams.endTime = new Date()
          }
          delete myParams.data
          // 如果没有默认选择第一条数据
          // if (!myParams.siteId) {
          //   myParams.siteId = store.state.studyItem.sites[0].siteId
          // }
          const rest = await getChatDoctor(studyId, myParams)
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest?.doctorChat?.items,
            total: +rest?.doctorChat?.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      onLoad: async() => {
        //
      },
    })
    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px 0 20px;
    box-sizing: border-box;
    h3 {
      margin: 0;
    }
  }
}
</style>

