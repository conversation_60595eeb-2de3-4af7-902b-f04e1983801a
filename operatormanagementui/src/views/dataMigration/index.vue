<template>
  <div>
    <trial-table
      ref="DataMigrationTableRef"
      title=""
      search-form-label-position="right"
      :request="getDataMigrationList"
      :columns="DataMigrationColumns"
      :search="DataMigrationSearchConfig"
      :pagination="DataMigrationPaginationConfig"
      @selectionChange="DataMigrationHandleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #head-nav-slot>
        <div class="w-full mb-6" v-html="studyName" />
      </template>
      <template #toolbar>
        <el-button v-permission="['manage.dataMigration.migrateOrBatchMigrate']" type="primary" @click="DataMigrationEditItemForm(null,'operationAll')">
          批量迁移
        </el-button>
      </template>
      <template #operate="scope">
        <div class="flex">
          <el-button v-permission="['manage.dataMigration.migrateOrBatchMigrate']" size="small" text type="primary" @click="DataMigrationEditItemForm(scope.row,'operation')">
            迁移
          </el-button>
          <el-button
            v-if="scope.row?.hasRecord"
            size="small"
            type="primary"
            text
            @click="DataMigrationEditItemForm(scope.row,'check')"
          >查看迁移记录</el-button>
        </div>
      </template>
    </trial-table>
    <!-- 查看迁移记录 -->
    <CheckMigrationLogForm
      ref="myCheckMigrationLogFormRef"
    />
    <!-- 迁移操作 -->
    <OperationMigrationForm
      ref="OperationMigrationFormRef"
      :request="DataMigrationRefresh"
      :versions-arr="DataMigrationSearchConfig.fields[1].options"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import CheckMigrationLogForm from '@/views/dataMigration/CheckMigrationLogForm.vue'
import OperationMigrationForm from '@/views/dataMigration/OperationMigration.vue'
import { getPatientListInStudy } from '@/api/dataMigration'
import { getDropStudyVersionsCRF } from '@/api/home'
// import { RefreshRight } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'DataMigrationTable', // 数据迁移
  components: {
    CheckMigrationLogForm,
    OperationMigrationForm
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    const { sites } = store.state.studyItem
    const state = reactive({
      OperationMigrationFormRef: null,
      myCheckMigrationLogFormRef: null,
      studyName: store?.state?.studyItem?.studyName || '',
      permissions: store.state.studyItem.permissions,
      DataMigrationTableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      DataMigrationColumns: [
        { type: 'selection' },
        { label: '中心', prop: 'siteName', minWidth: 300 },
        { label: '受试者编号', prop: 'patientNumber', minWidth: 200 },
        { label: '版本', prop: 'versionNumber', minWidth: 200 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      DataMigrationSearchConfig: {
        // labelWidth: '50px', // 必须带上单位
        inputWidth: '280px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心',
            name: 'siteId',
            defaultValue: null,
            options: sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '版本',
            name: 'version',
            defaultValue: null,
            options: [],
            filterable: true,
          },
          {
            type: 'text',
            label: '受试者编号',
            name: 'patientNumber',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      DataMigrationPaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      DataMigrationSelectedItems: [],
      // 选择
      DataMigrationHandleSelectionChange(arr) {
        state.DataMigrationSelectedItems = arr
      },
      // 请求函数
      async getDataMigrationList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const data = await getPatientListInStudy(studyId, params)
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data.items,
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      DataMigrationRefresh: () => {
        state.DataMigrationTableRef.refresh()
      },
      // 新增-编辑
      DataMigrationEditItemForm: (row, flag) => {
        if (flag === 'operationAll' && state.DataMigrationSelectedItems?.length < 2) {
          // eslint-disable-next-line no-undef
          ElMessage.warning('请选择两条或以上要迁移的数据')
          return
        }
        if (flag === 'check') {
          // 查看迁移记录
          state.myCheckMigrationLogFormRef.CheckMigrationLogDialogVisible = true
          state.myCheckMigrationLogFormRef.CheckMigrationLogFormData = row
        } else if (flag === 'operationAll' || flag === 'operation') {
          // 批量-单个-迁移
          state.OperationMigrationFormRef.OperationMigrationDialogVisible = true
          const rowFormData = {
            migrateType: '1',
            migrateContent: ['1']
          }
          let maxPublishDate = ''
          if (flag === 'operationAll' && state.DataMigrationSelectedItems?.length) {
            const currentVersionArr = []
            state.DataMigrationSearchConfig.fields[1].options.forEach((item) => {
              state.DataMigrationSelectedItems.forEach((ite) => {
                if (ite.currentVersion === item.studyVersionId) {
                  currentVersionArr.push(item)
                }
              })
            })
            maxPublishDate = currentVersionArr.reduce((item, it) => {
              return new Date(item.publishDate).getTime() > new Date(it.publishDate).getTime() ? item.publishDate : it.publishDate
            })
          } else if (flag === 'operation') {
            state.DataMigrationSearchConfig.fields[1].options.forEach((item) => {
              if (item.studyVersionId === row.currentVersion) {
                maxPublishDate = item.publishDate
              }
            })
          }
          state.DataMigrationSearchConfig.fields[1].options.forEach((item) => {
            if (maxPublishDate && new Date(item.publishDate).getTime() <= new Date(maxPublishDate).getTime()) {
              item.disabled = true
            } else {
              item.disabled = false
            }
          })
          state.OperationMigrationFormRef.OperationMigrationTableData = flag === 'operationAll' ? [...state.DataMigrationSelectedItems] : []
          state.OperationMigrationFormRef.OperationMigrationFormData = flag !== 'operationAll' ? { ...rowFormData, ...row } : rowFormData
          state.OperationMigrationFormRef.OperationMigrationTitle = flag === 'operationAll' ? '批量迁移' : '迁移'
          state.OperationMigrationFormRef.OperationMigrationNum = flag === 'operationAll' ? '2' : '1'
        }
        if (state.myCheckMigrationLogFormRef?.CheckMigrationLogDialogTableRef?.refresh) {
          state.myCheckMigrationLogFormRef.CheckMigrationLogDialogTableRef.refresh()
        }
      },
    })
    onBeforeMount(() => {
      getDropStudyVersionsCRF(studyId)
        .then((res) => {
          if (res?.length) {
            res.forEach((el) => {
              el.name = el.versionNumber
              el.value = el.studyVersionId
            })
            state.DataMigrationSearchConfig.fields[1].options = res
          }
        })
    })
    return {
      ...toRefs(state),
    }
  }
})
</script>

<style scoped lang="less">
:deep(.el-form.search){
  .el-form-item__label{
    white-space: nowrap;
    // margin: 0 0 0 15px;
  }
}
</style>
