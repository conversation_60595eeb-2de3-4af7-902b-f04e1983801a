<template>
  <trial-dialog v-model="MigrationLogDialogVisible" :title="MigrationLogTitle">
    <template #DialogBody>
      <trial-table
        ref="MigrationLogDialogTableRef"
        title=""
        :request="getMigrationLogDialogList"
        :columns="MigrationLogDialogColumns"
        :pagination="MigrationLogDialogPagination"
      >
        <template #operate="scope">
          <span
            v-show="scope.row?.migrateOprType === 2"
            v-permission="['manage.migrationLog.downloadReportOrBatchDownload']"
            class="editBtnBlue" @click="downloadReport(scope.row)">
            下载报告
          </span>
        </template>
      </trial-table>
    </template>
    <template #footer>
      <span class="dialog-footer centerflex mt-32">
        <el-button @click="closeDialogVisible">取 消</el-button>
        <el-button
          v-show="MigrationLogFormData?.migrateOprType !== 1"
          v-permission="['manage.migrationLog.downloadReportOrBatchDownload']"
          type="primary"
          @click="downloadReportAll"
        >批量下载报告</el-button>
      </span>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
// import { useStore } from 'vuex'
import { getExportMigrateDataById, getVisitRecord } from '@/api/dataMigration'

export default defineComponent({
  name: 'MigrationLogForm', // 查看明细弹窗
  setup() {
    // const store = useStore()
    const state: any = reactive({
      MigrationLogFormData: {},
      MigrationLogTitle: '查看明细',
      MigrationLogFormRef: null,
      MigrationLogDialogVisible: false,
      MigrationLogDialogTableRef: null,
      MigrationLogDialogColumns: [
        { label: '中心', prop: 'siteName', minWidth: 160 },
        { label: '受试者编号', prop: 'patientNumber', minWidth: 100 },
        { label: '原版本', prop: 'oringialVersion', minWidth: 100 },
        { label: '迁移后版本', prop: 'currentVersion', minWidth: 100 },
        { label: '迁移内容', prop: 'migrateTypeStr', minWidth: 100 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 分页配置
      MigrationLogDialogPagination: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20],
        style: { textAlign: 'left' },
      },
      closeDialogVisible: () => {
        state.MigrationLogDialogVisible = false
      },
      downloadReport: (row) => {
        const params = {
          visitMigrateType: row.visitMigrateType,
          batchExport: false
        }
        getExportMigrateDataById(row.id, params)
        window.open(
          `${
            window.location.origin
          }/api/Operator/Study/${row.id}/ExportMigrateDataById?visitMigrateType=${row.visitMigrateType}&batchExport=false`
        )
      },
      downloadReportAll: () => {
        const params = {
          visitMigrateType: state.MigrationLogFormData?.visitMigrateType,
          batchExport: true
        }
        getExportMigrateDataById(state.MigrationLogFormData?.id, params)
        window.open(
          `${
            window.location.origin
          }/api/Operator/Study/${state.MigrationLogFormData?.id}/ExportMigrateDataById?visitMigrateType=${state.MigrationLogFormData?.visitMigrateType}&batchExport=true`
        )
      },
      getMigrationLogDialogList: async(params) => {
        params.recordId = state.MigrationLogFormData.id
        const data: any = await getVisitRecord(params)
        return {
          data: data.items,
          total: +data.totalItemCount,
        }
      },
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
