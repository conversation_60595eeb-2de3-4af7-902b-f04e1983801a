<template>
  <div>
    <trial-table
      ref="MigrationLogTableRef"
      title=""
      :request="getMigrationLogList"
      :columns="MigrationLogColumns"
      :search="MigrationLogSearchConfig"
      :pagination="MigrationLogPaginationConfig"
      @selectionChange="MigrationLogHandleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #head-nav-slot>
        <div class="w-full mb-6" v-html="studyName" />
      </template>
      <template #migrateOprTypeSlot="scope">
        <!-- <span v-if="scope.row?.migrateOprType === 0">未知</span> -->
        <span v-if="scope.row.migrateOprType === 1">回滚</span>
        <span v-else-if="scope.row?.migrateOprType === 2">迁移</span>
      </template>
      <template #operate="scope">
        <div class="flex">
          <el-button v-permission="['manage.migrationLog.viewDetails']" size="small" text type="primary" @click="MigrationLogEditItemForm(scope.row)">
            查看明细
          </el-button>
          <el-button
            v-show="scope.row?.migrateOprType === 2 && !scope.row?.isRollBack"
            v-permission="['manage.migrationLog.rollback']"
            size="small"
            type="danger"
            text
            @click="MigrationLogDeleteItem(scope.row)"
          >回滚</el-button>
        </div>
      </template>
    </trial-table>
    <!-- 明细 -->
    <MigrationLogForm
      ref="myMigrationLogFormRef"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import MigrationLogForm from '@/views/dataMigration/MigrationLogForm.vue'
import { getMigrateRecord, getMigrateRollBack } from '@/api/dataMigration'

export default defineComponent({
  name: 'MigrationLog', // 迁移记录
  components: {
    MigrationLogForm
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    const state = reactive({
      myMigrationLogFormRef: null,
      studyName: store?.state?.studyItem?.studyName || '',
      MigrationLogTableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      MigrationLogColumns: [
        // { type: 'selection' },
        { label: '操作时间', prop: 'lastUpdatetimeStr', minWidth: 220 },
        { label: '操作人', prop: 'lastUpdator', minWidth: 160 },
        { label: '操作类型', tdSlot: 'migrateOprTypeSlot', minWidth: 160 },
        { label: '影响人数', prop: 'patientCount', minWidth: 160 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      MigrationLogSearchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'daterange',
            label: '操作日期',
            name: 'createTimeEndOrCreateTimeBegin',
            filterable: true,
            clearable: false,
          },
        ],
      },
      // 分页配置
      MigrationLogPaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      MigrationLogSelectedItems: [],
      // 选择
      MigrationLogHandleSelectionChange(arr) {
        state.MigrationLogSelectedItems = arr
      },
      // 请求函数
      async getMigrationLogList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          if (params?.createTimeEndOrCreateTimeBegin) {
            params.startDate = params.createTimeEndOrCreateTimeBegin[0]
            params.endDate = params.createTimeEndOrCreateTimeBegin[1]
            delete params.createTimeEndOrCreateTimeBegin
          }
          const data = await getMigrateRecord(studyId, params)
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data.items,
            total: +data.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      // 刷新
      MigrationLogRefresh: () => {
        state.MigrationLogTableRef.refresh()
      },
      // 新增-编辑
      MigrationLogEditItemForm: (row) => {
        state.myMigrationLogFormRef.MigrationLogDialogVisible = true
        state.myMigrationLogFormRef.MigrationLogFormData = row
        if (state.myMigrationLogFormRef?.MigrationLogDialogTableRef?.refresh) {
          state.myMigrationLogFormRef.MigrationLogDialogTableRef.refresh()
        }
      },
      // 删除
      MigrationLogDeleteItem: (row) => {
        ElMessageBox.confirm(
          '是否确认执行回滚？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            const loadingMigrateRollBack = ElLoading.service({
              lock: true,
              text: 'Loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            getMigrateRollBack(studyId, row.id).then(() => {
              state.MigrationLogRefresh()
              ElMessage.success('回滚成功')
              loadingMigrateRollBack.close()
            }).catch(() => {
              loadingMigrateRollBack.close()
            })
          })
          .catch(() => {})
      }
    })
    return {
      ...toRefs(state),
    }
  }
})
</script>
