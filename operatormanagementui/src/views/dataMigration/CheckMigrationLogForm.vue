<template>
  <trial-dialog v-model="CheckMigrationLogDialogVisible" :title="CheckMigrationLogTitle">
    <template #DialogBody>
      <trial-table
        ref="CheckMigrationLogDialogTableRef"
        title=""
        :request="getCheckMigrationLogDialogList"
        :columns="CheckMigrationLogDialogColumns"
        :pagination="CheckMigrationLogDialogPagination"
      >
        <template #migrateOprTypeSlot="scope">
          <!-- <span v-if="scope.row?.orderState === 0">未知</span> -->
          <span v-if="scope.row?.migrateOprType === 1">回滚</span>
          <span v-else-if="scope.row?.migrateOprType === 2">迁移</span>
        </template>
        <template #operate="scope">
          <span v-if="scope.row?.migrateOprType === 2" class="editBtnBlue" @click="downloadReport(scope.row)">
            下载报告
          </span>
        </template>
      </trial-table>
    </template>
    <template #footer>
      <span class="dialog-footer centerflex mt-32">
        <el-button @click="closeDialogVisible">取 消</el-button>
      </span>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
// import { useStore } from 'vuex'
import { getExportMigrateDataById, getVisitRecord } from '@/api/dataMigration'

export default defineComponent({
  name: 'CheckMigrationLogForm', // 查看迁移记录
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    // const store = useStore()

    const state = reactive({
      CheckMigrationLogFormData: {},
      CheckMigrationLogTitle: '查看迁移记录',
      CheckMigrationLogFormRef: null,
      CheckMigrationLogDialogVisible: false,
      CheckMigrationLogDialogTableRef: null,
      CheckMigrationLogDialogColumns: [
        { label: '操作时间', prop: 'lastUpdatetimeStr', minWidth: 130 },
        { label: '操作人', prop: 'lastUpdator', minWidth: 80 },
        { label: '操作类型', tdSlot: 'migrateOprTypeSlot', minWidth: 80 },
        { label: '原版本', prop: 'oringialVersion', minWidth: 80 },
        { label: '迁移后版本', prop: 'currentVersion', minWidth: 80 },
        { label: '迁移内容', prop: 'migrateTypeStr', minWidth: 80 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 分页配置
      CheckMigrationLogDialogPagination: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20],
        style: { textAlign: 'left' },
      },
      closeDialogVisible: () => {
        state.CheckMigrationLogDialogVisible = false
      },
      // 下载报告
      downloadReport: (row) => {
        const params = {
          visitMigrateType: row.visitMigrateType,
          batchExport: false
        }
        getExportMigrateDataById(row.id, params)
        window.open(
          `${
            window.location.origin
          }/api/Operator/Study/${row.id}/ExportMigrateDataById?visitMigrateType=${row.visitMigrateType}&batchExport=false`
        )
      },
      getCheckMigrationLogDialogList: async(params) => {
        params.patientId = state.CheckMigrationLogFormData.id
        const data = await getVisitRecord(params)
        return {
          data: data.items,
          total: +data.totalItemCount,
        }
      },
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
