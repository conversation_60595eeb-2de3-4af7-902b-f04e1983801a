<template>
  <div>
    <trial-dialog
      v-model="OperationMigrationDialogVisible"
      :title="OperationMigrationTitle"
      my-title-class="flex justify-center my-1"
      class="!z-[2000]"
    >
      <template #DialogBody>
        <div v-if="OperationMigrationTitle === '迁移'">
          <div class="flex mb-2">
            <div class="w-24 text-right">中心：</div>
            <div v-if="OperationMigrationFormData?.siteName" v-html="OperationMigrationFormData.siteName" />
          </div>
          <div class="flex mb-2">
            <div class="w-24 text-right">受试者编号：</div>
            <div v-if="OperationMigrationFormData?.patientNumber" v-html="OperationMigrationFormData.patientNumber" />
          </div>
          <div class="flex mb-6">
            <div class="w-24 text-right">版本：</div>
            <div v-if="OperationMigrationFormData?.versionNumber" v-html="OperationMigrationFormData.versionNumber" />
          </div>
        </div>
        <el-form
          v-if="OperationMigrationTitle === '迁移' || OperationMigrationTitle === '批量迁移'"
          ref="OperationMigrationFormRef"
          :model="OperationMigrationFormData"
          :rules="OperationMigrationRules"
        >
          <el-form-item
            label="迁移类别:"
            :label-width="OperationMigrationFormLabelWidth"
            prop="migrateType"
          >
            <el-radio-group v-model="OperationMigrationFormData.migrateType">
              <el-radio label="1" size="large">原版本上迁移</el-radio>
              <el-radio label="2" size="large">跨版本迁移</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            v-if="OperationMigrationFormData.migrateType === '1'"
            label="迁移内容:"
            :label-width="OperationMigrationFormLabelWidth"
            prop="migrateContent"
          >
            <el-checkbox-group v-model="OperationMigrationFormData.migrateContent">
              <el-checkbox v-for="(item,index) in checkedMigrationContents" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item
            v-if="OperationMigrationFormData.migrateType === '2'"
            label="目标版本:"
            :label-width="OperationMigrationFormLabelWidth"
            prop="targetVersionId"
          >
            <el-select v-model="OperationMigrationFormData.targetVersionId">
              <el-option
                v-for="(item, index) in versionsArr"
                :key="index"
                :label="`${item?.name}${item.versionStatus === 2 ? '正式版本': item.versionStatus === 1 ? '测试版本' : ''}`"
                :value="item?.value"
                :disabled="item?.disabled"
              />
            </el-select>
            <span class="ml-3">原版本和目标版本不可相同</span>
          </el-form-item>
        </el-form>
        <trial-table
          v-if="OperationMigrationTitle === '批量迁移'"
          ref="OperationAllMigrationTableRef"
          title=""
          :request="getOperationAllMigrationList"
          :columns="OperationAllMigrationColumns"
          :pagination="false"
        />
        <el-form
          v-if="OperationMigrationTitle === '预生成迁移报告'"
          ref="pregenerationRef"
          :model="pregenerationFormData"
          :rules="pregenerationRule"
        >
          <div class="flex items-center">
            <el-form-item prop="emailAddress" style="width: 40%" label="邮箱">
              <el-input v-model="pregenerationFormData.emailAddress" />
            </el-form-item>
            <div class="ml-3 pregeneration-tips">预估每个受试者需要2分钟，请耐心等待~</div>
          </div>
          <div>
            <h3 class="text-center">迁移说明</h3>
            <div class="overflow-hidden explain" v-html="explainHtml" />
          </div>
        </el-form>
      </template>
      <template #footer>
        <div v-if="OperationMigrationTitle === '迁移' || OperationMigrationTitle === '批量迁移'" class="centerflex mt-40">
          <el-button
            :loading="OperationMigrationLoading"
            type="primary"
            plain
            @click="OperationMigrationSubmit"
          >确定执行迁移</el-button>
          <el-button :loading="OperationMigrationLoading" plain @click="closeDialogVisible">取消</el-button>
          <el-button
            :loading="OperationMigrationLoading"
            type="primary"
            @click="OperationMigrationSubmit(1)"
          >预生成迁移报告</el-button>
        </div>
        <div v-if="OperationMigrationTitle === '预生成迁移报告'" class="centerflex">
          <el-button :loading="OperationMigrationLoading" plain @click="closeDialogVisible">取消</el-button>
          <el-button
            :loading="OperationMigrationLoading"
            type="primary"
            @click="pregenerationClick"
          >确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <!-- tips -->
    <trial-dialog v-if="tipsMyDialog || tipsFinish" title="提示" :my-dialog-body-style="myDialogBodyStyle">
      <template #DialogBody>
        <div v-if="tipsMyDialog">
          <div>
            正在迁移中...预估每个受试者需要2分钟，请勿关闭页面并耐心等待~
          </div>
          <div class="mt-5" style="color: #9a9a9a;">
            迁移成功后将弹窗提示
          </div>
        </div>
        <div v-if="tipsFinish">
          {{ tipsFinishTitle }}
        </div>
      </template>
      <template #footer>
        <div v-if="tipsFinish" class="centerflex mt-40">
          <el-button plain @click="tipsFinish = false">关闭</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted } from 'vue'
import { useStore } from 'vuex'
import { getSaveMigrateData, postExportMigrateData } from '@/api/dataMigration'

export default defineComponent({
  name: 'OperationMigrationForm', // 批量迁移和单个迁移弹窗
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
    versionsArr: {
      type: Array
    },
  },
  setup(props) {
    const store = useStore()
    const { studyId } = store.state.account.userinfo

    const state = reactive({
      explainHtml: `<p style=\"text-align: left;\">同CRF版本迁移：</p><p style=\"text-align: left;\">
      1、仅迁移访视计划（不包含新增的访视、不包含原有访视下新增或删除问卷）；</p><p style=\"text-align: left;\"><br></p>
      <p style=\"text-align: left;\">跨CRF版本迁移：</p><p style=\"text-align: left;\">1、访视计划、初筛问卷、访视问卷、不适记录、合并用药、服药问卷、用药方案；</p>
      <p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">概览：</p><p style=\"text-align: left;\">
      1、展示预生成迁移报告的时间、操作人；</p><p style=\"text-align: left;\">2、展示本次迁移涉及的全量受试者信息；
      </p><p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">类型：</p><p style=\"text-align: left;\">
      1、无变更：迁移前有，迁移后有，且值未变更；</p><p style=\"text-align: left;\">2、新增：迁移前无，迁移后有；</p>
      <p style=\"text-align: left;\">3、丢失：迁移前有，迁移后无；</p><p style=\"text-align: left;\">4、变更：迁移前有，迁移后有，且值变更；</p>
      <p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">访视：</p><p style=\"text-align: left;\">
      1、基于单个受试者&amp;单个子访视校验；</p><p style=\"text-align: left;\">
      2、访视预约日期：根据迁移前后的计划发生日期判定：</p><p style=\"text-align: left;\">
      ①若新计划发生日期≠原计划发生日期，则初始化新访视预约日期=新计划发生日期，且初始化预约状态为未反馈；</p>
      <p style=\"text-align: left;\">②若新计划发生日期=原计划发生日期，则新访视预约日期=原访视预约日期，且新预约状态=原预约状态；</p>
      <p style=\"text-align: left;\">3、实际发生日期迁移前后不变更；</p><p style=\"text-align: left;\">
      4、excel中仅列举出关键字段供审阅；</p><p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">问卷与访视关联：</p>
      <p style=\"text-align: left;\">1、基于单个受试者&amp;单个问卷校验；</p><p style=\"text-align: left;\">
      2、问卷范围：初筛问卷、访视问卷、不适记录、合并用药、服药问卷，校验问卷名称、以及与访视的关联；</p><p style=\"text-align: left;\"><br>
      </p><p style=\"text-align: left;\">初筛问卷：</p><p style=\"text-align: left;\">1、基于单个受试者&amp;初筛问卷类型下的单个问卷（只有1个）的单个字段校验；</p>
      <p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">访视问卷：</p><p style=\"text-align: left;\">
      1、基于单个受试者&amp;访视问卷类型下的单个问卷的单个字段校验；</p><p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">
      不适记录：</p><p style=\"text-align: left;\">1、基于单个受试者&amp;不适记录问卷类型下的单个问卷的单个字段校验；</p>
      <p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">合并用药：</p><p style=\"text-align: left;\">
      1、基于单个受试者&amp;合并用药问卷类型下的单个问卷的单个字段校验；</p><p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">
      服药问卷：</p><p style=\"text-align: left;\">1、基于单个受试者&amp;服药问卷类型下的单个问卷的单个字段校验；</p><p style=\"text-align: left;\">
      2、含系统已推送、患者自己添加的服药记录；</p><p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">
      用药方案：</p><p style=\"text-align: left;\">1、基于单个受试者&amp;单个用药方案下的单个用药周期校验；</p><p style=\"text-align: left;\">
      2、不影响历史已发生的用药计划；</p><p style=\"text-align: left;\">3、对于进行中的用药计划，迁移后，基于迁移前的开始用药日期，及迁移后用药周期的其他配置字段进行更新，今天已生成的数据不变更；
      </p><p style=\"text-align: left;\">4、对于待确认的用药计划，迁移后，基于新的用药周期配置进行更新；
      </p><p style=\"text-align: left;\">5、对于已结束的用药计划，迁移后，不做任何更新；</p><p style=\"text-align: left;\">6、excel中仅列举出关键字段供审阅；</p>
      <p style=\"text-align: left;\"><br></p><p style=\"text-align: left;\">稽查轨迹：</p><p style=\"text-align: left;\">
      1、对于新增字段，稽查轨迹中新增一条记录，备注字段会赋值为：迁移后新增；</p><p style=\"text-align: left;\">
      2、对于字段value变更，稽查轨迹中新增一条记录，备注字段会赋值为：迁移后变更；</p><p style=\"text-align: left;\">
      3、对于字段选项丢失，稽查轨迹中新增一条记录，只是选项软删除了，字段还在，所以页面上会体现，备注字段会赋值为：迁移后变更；</p><p style=\"text-align: left;\">
      4、对于丢失字段，稽查轨迹中新增一条记录，但是因为这个字段软删除了，所以页面上并不会体现，备注字段会赋值为：迁移后丢失；</p>`,
      myDialogBodyStyle: {
        width: '30%',
        minHeight: '200px'
      },
      OperationAllMigrationTableRef: null,
      checkedMigrationContents: [
        {
          label: '访视',
          value: '1',
        },
        // {
        //   label: '问卷',
        //   value: '2',
        // }
      ],
      OperationAllMigrationColumns: [
        { label: '中心', prop: 'siteName', minWidth: 180 },
        { label: '受试者编号', prop: 'patientNumber', minWidth: 120 },
        { label: '原版本', prop: 'versionNumber', minWidth: 120 },
      ],
      OperationMigrationFormData: {
        migrateType: '1',
        migrateContent: ['1'],
        targetVersionId: ''
      },
      OperationMigrationTableData: [],
      OperationMigrationTitle: '迁移',
      OperationMigrationFormRef: null,
      OperationMigrationDialogVisible: false,
      OperationMigrationFormLabelWidth: '86px',
      OperationMigrationRules: {
        migrateType: [{ required: true, message: '请选择', trigger: 'blur' }],
        migrateContent: [{ required: true, message: '请选择', trigger: 'blur' }],
        targetVersionId: [{ required: true, message: '请选择', trigger: 'blur' }]
      },
      OperationMigrationLoading: false,
      tipsMyDialog: false,
      tipsFinish: false,
      // 预生成报告的
      pregenerationRef: null,
      pregenerationFormData: {
        emailAddress: ''
      },
      pregenerationRule: {
        emailAddress: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/ig,
            message: '邮箱格式不正确',
            trigger: 'blur',
          },
        ]
      },
      tipsFinishTitle: '迁移成功',
      OperationMigrationNum: '',
      closeDialogVisible: () => {
        state.OperationMigrationDialogVisible = false
      },
      OperationMigrationSubmit: (index) => {
        state.OperationMigrationFormRef.validate((valid) => {
          if (valid) {
            if (index === 1) {
              state.pregenerationFormData.emailAddress = ''
              state.OperationMigrationTitle = '预生成迁移报告'
              return
            }
            state.OperationMigrationLoading = true
            const patientIds = []
            if (state.OperationMigrationTitle === '迁移') {
              patientIds.push(state.OperationMigrationFormData.id)
            } else {
              state.OperationMigrationTableData.forEach((el) => {
                patientIds.push(el.id)
              })
            }
            const myParams = {
              patientIds,
              migrateType: state.OperationMigrationFormData.migrateType,
              migrateContent: state.OperationMigrationFormData.migrateContent[0],
              targetVersionId: state.OperationMigrationFormData.targetVersionId
            }
            state.closeDialogVisible()
            state.tipsMyDialog = true
            getSaveMigrateData(studyId, myParams)
              .then(() => {
                props.request()
                state.tipsMyDialog = false
                state.OperationMigrationLoading = false
                state.tipsFinish = true
              }).catch(() => {
                // state.tipsFinishTitle = '迁移失败'
                state.OperationMigrationLoading = false
                state.tipsMyDialog = false
                // state.tipsFinish = true
              })
          }
        })
      },
      pregenerationClick: () => {
        state.pregenerationRef.validate((valid) => {
          if (valid) {
            const patientIds = []
            if (state.OperationMigrationNum === '1') {
              patientIds.push(state.OperationMigrationFormData.id)
            } else {
              state.OperationMigrationTableData.forEach((el) => {
                patientIds.push(el.id)
              })
            }
            state.OperationMigrationLoading = true
            const data = {
              patientIds,
              migrateType: state.OperationMigrationFormData.migrateType,
              migrateContent: state.OperationMigrationFormData.migrateContent[0],
              targetVersionId: state.OperationMigrationFormData.targetVersionId,
              emailAddress: state.pregenerationFormData?.emailAddress
            }
            postExportMigrateData(studyId, data).then(() => {
              ElMessage.success('保存成功')
              state.OperationMigrationLoading = false
              state.closeDialogVisible()
            }).catch(() => {
              state.OperationMigrationLoading = false
              state.closeDialogVisible()
            })
          }
        })
      },
      // 批量时获取table数据
      getOperationAllMigrationList: () => {
        return {
          data: state.OperationMigrationTableData,
          total: state.OperationMigrationTableData.length,
        }
      }
    })
    onMounted(() => {
      //
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style scoped lang="less">
:deep(.el-form-item__label){
  white-space: nowrap;
  margin: 3px 0 0 0px;
}
.el-checkbox-group {
  margin: 4px 0 0 0px;
}
.pregeneration-tips {
  color: #f59e2b;
  margin-bottom: 18px;
}
.explain {
  height: 400px;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
