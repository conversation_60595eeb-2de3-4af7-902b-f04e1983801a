<template>
  <div class="details">
    <div class="content-top">
      <div class="top">
        <h3 v-html="studyName" />
        <el-button size="large" @click="backRefresh">返回</el-button>
      </div>
      <div class="bottom">
        <div>
          <span>中心名称:</span>
          <span>{{ DetailsList?.siteName }}</span>
        </div>
        <div>
          <span>病例编号:</span>
          <span>{{ DetailsList?.patNumber }}</span>
        </div>
        <div>
          <span>性别:</span>
          <span>{{ DetailsList?.genderText }}</span>
        </div>
      </div>
      <div class="bottom">
        <div>
          <span>组别:</span>
          <span>{{ DetailsList?.group }}</span>
        </div>
        <div>
          <span>期望完成日期:</span>
          <span>{{ DetailsList?.expectedTime }}</span>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-title">
        <div class="content-titleTop">
          <div class="content-title-left">V{{ DetailsList?.versionNumber }}——{{ DetailsList?.visitName }}</div>
          <div class="content-title-center">{{ DetailsList?.minWaitTime }} ~ {{ DetailsList?.maxWaitTime }}</div>
          <div class="content-title-right">{{ DetailsList?.finishStatusText }}</div>
        </div>
        <div class="content-titleBottom">
          <div class="content-titleBottom-left">
            <div class="content-titleBottom-user-picture">
              <img src="@/assets/img/yjzIcon.svg">
              <span>研究人员-访视任务</span>
            </div>
            <div v-for="(item, index) in DetailsList?.doctorQuests" :key="index" class="content-titleBottom-word">
              <!-- <img v-if="item.finishStatus === 1" src="@/assets/img/unfinish.svg" class="img"> -->
              <div class="img-box">
                <img v-if="item.finishStatus === 2" src="@/assets/img/finish.svg" class="img" alt="">
              </div>
              <span>
                {{ item.questName }}
              </span>
            </div>
          </div>
          <div class="content-titleBottom-right">
            <div class="content-titleBottom-user-picture">
              <img src="@/assets/img/sszIcon.svg">
              <span>受试者-访视任务</span>
            </div>
            <div v-for="(item, index) in DetailsList?.patientQuests" :key="index" class="content-titleBottom-word">
              <!-- <img v-if="item.finishStatus === 1" src="@/assets/img/unfinish.svg" class="img"> -->
              <div class="img-box">
                <img v-if="item.finishStatus === 2" src="@/assets/img/finish.svg" class="img" alt="">
              </div>
              <span>
                {{ item.questName }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  getStudyPatinetOverQuests,
} from '@/api/visitOutside'
import { useStore } from 'vuex'
export default defineComponent({
  name: 'VisitInsideDetails', // 访视任务超窗详情
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup({ request }) {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      studyName: store.state.studyItem.studyName,
      DetailsList: [],
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      onLoad: () => {
        // 获取全部问卷列表数据
        getStudyPatinetOverQuests(
          RESEARCHCENTER_INFOS.researchContent.visitId
        ).then((res) => {
          state.DetailsList = res
        })
      },
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.details {
  width: 100%;
  .content-top {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    .top {
      display: flex;
      justify-content: space-between;
      h3 {
        margin: 0;
        line-height: 40px;
      }
    }
    .bottom {
      width: 100%;
      display: flex;
      margin-top: 10px;
      div {
        width: 33%;
        display: flex;
        span {
          font-weight: normal;
          &:first-child {
            width: 150px;
            text-align: right;
          }
          &:last-child {
            margin-left: 10px;
            color: rgb(156, 152, 152);
          }
        }
      }
    }
  }
  .content {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    .content-title {
      margin: 0;
      .content-titleTop {
        display: flex;
        justify-content: space-between;
        .content-title-right {
          color: #a30014;
        }
      }
      .content-titleBottom {
        display: flex;
        justify-content: space-between;
        padding: 20px 10px;
        .content-titleBottom-left {
          flex: 1;
        }
        .content-titleBottom-right {
          flex: 1;
        }
        .content-titleBottom-word {
          margin-top: 10px;
          display: flex;
          align-items: center;
          .img-box {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
          .img {
            width: 100%;
          }
        }
        .content-titleBottom-user-picture {
          display: flex;
          align-items: center;
          img {
            margin-right: 5px;
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
}
</style>
