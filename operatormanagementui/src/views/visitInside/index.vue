<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
        :hide-center="true"
        :showbtnfalg-excel="showbtnfalgExcel"
        :request-export-excel="getResearchCenterExport"
        :table-row-class-name="tableRowClassName"
      >
        <template #operate="scope">
          <span
            v-permission="['manage.visitInside.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            详情
          </span>
        </template>
        <template #hideCenter>
          <div class="researchCenter-hideCenter">
            <div class="hideCenter">
              <span class="center-block center-block-one" />
              <span>预期访视当天</span>
            </div>
            <div class="hideCenter">
              <span class="center-block center-block-two" />
              <span>预期窗口期最后一天</span>
            </div>
          </div>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <Details :request="onLoad" />
    </div>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import Details from '@/views/visitInside/VisitInsideDetails.vue'
import {
  getWindowPhasePeople,
  getWindowPhasePeopleExport,
} from '@/api/visitInside'

export default defineComponent({
  name: 'VisitInside', // 访视窗口期内
  components: {
    Details,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      //  store.state.permissions.includes(
      //   'manage.visitInside.exportExcel'
      // )
      showbtnfalgExcel: store.state.studyItem.permissions.includes('manage.visitInside.exportExcel'),
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        // doctorInfo.hospital + doctorInfo.department
        { label: '中心名称', prop: 'siteName', width: 250 },
        { label: '病例编号', prop: 'patientNo', width: 100 },
        { label: '性别', prop: 'gender', width: 100 },
        { label: '入组时间', prop: 'inGroupDate', width: 180 },
        { label: '组别', prop: 'group', width: 120 },
        { label: '访视名称', prop: 'visitName', width: 160 },
        { label: '未完成任务', prop: 'unFinishQuestStr', width: 220 },
        { label: '期望完成日期', prop: 'expectedTime', width: 180 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '任务状态',
            name: 'questOfVisitStatus',
            defaultValue: null,
            options: [
              {
                value: 1,
                name: '未全部完成',
              },
              {
                value: 2,
                name: '全部完成',
              },
            ],
          },
          {
            type: 'select',
            label: '日期属性',
            name: 'dateProperty',
            defaultValue: null,
            options: [
              {
                value: 2,
                name: '预期访视当天',
              },
              {
                value: 3,
                name: '预期窗口最后一天',
              },
            ],
          },
          {
            label: '病例编号',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },

      tableRowClassName: (row, rowIndex) => {
        if (state.tyPingList[row.rowIndex].isLastDay) {
          return 'success-row'
        } else if (state.tyPingList[row.rowIndex].isExpectedDay) {
          return 'warning-row'
        }
        return 'white-row'
      },
      // 导出Excel方法
      getResearchCenterExport: async (params) => {
        const myParams = { ...params }
        if (myParams.siteId == null) {
          myParams.siteId = ''
        }
        if (myParams.patNumber == null) {
          myParams.patNumber = ''
        }
        getWindowPhasePeopleExport(studyId, myParams)
        window.open(
          `${window.location.origin
          }/api/Operator/DoctorPatient/${studyId}/PatientInVisit/Export?siteId=${myParams.siteId
          }&patNumber=${myParams.patNumber}`
        )
      },
      // 获取列表数据方法
      getResearchCenterList: async (params) => {
        try {
          if (!params.questOfVisitStatus) {
            params.questOfVisitStatus = 0
          }
          if (!params.dateProperty) {
            params.dateProperty = 0
          }
          const rest = await getWindowPhasePeople(studyId, params)
          state.tyPingList = rest.items
          rest.items.forEach((item, idx) => {
            item.id = idx + 1
          })
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      // 编辑某项中心
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      onLoad: async () => {
        //
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    h3 {
      margin: 0;
    }
  }
}
:deep(.el-table) {
  .warning-row {
    background-color: #cae0a9;
  }
  .success-row {
    background-color: #f5e8d8;
  }
  .white-row {
    background-color: #fff;
  }
}
.researchCenter-hideCenter {
  margin: 20px 0;
  display: flex;
}
.hideCenter {
  margin-right: 20px;
  display: flex;
  align-items: center;
  .center-block {
    margin-right: 10px;
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 2px;
  }
  .center-block-one {
    background-color: #40b592;
  }
  .center-block-two {
    background-color: #fcc08c;
  }
}
</style>

