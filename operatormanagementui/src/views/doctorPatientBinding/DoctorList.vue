<template>
  <div class="doctorList">
    <trial-table
      ref="doctorListTableRef"
      title="医生列表"
      :request="getList"
      :columns="columns"
      :search="searchConfig"
      :pagination="paginationConfig"
      @selectionChange="handleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #head-nav-slot>
        <div class="w-full mb-6" v-html="studyName" />
      </template>
      <template #toolbar>
        <el-button
          v-permission="['manage.doctorList.edit']"
          type="success"
          @click="bindAllItem"
        >批量编辑</el-button>
      </template>
      <template #operate="scope">
        <el-button
          v-permission="['manage.doctorList.edit']"
          size="small"
          type="primary"
          text
          @click="bindItem(scope.row)"
        >
          编辑
        </el-button>
      </template>
    </trial-table>
    <!-- 编辑 -->
    <trial-dialog
      v-if="editDialogShowFlag > 0"
      title="编辑"
      my-title-class="flex justify-center my-1"
    >
      <template #DialogBody>
        <div
          v-if="editDialogShowFlag === 2 && selectedItems?.length > 1"
          class="my-3"
        >已选择{{ selectedItems.length }}条数据</div>
        <div v-if="editDialogShowFlag === 2 && selectedItems?.length > 1" class="mb-3 wrap2">
          医生:
          <span v-for="(item, index) in selectedItems" :key="item.dctUserId">
            {{ item.realName }}
            <span v-if="index+1 < selectedItems.length">,</span>
          </span>
        </div>
        <div v-else-if="bindItemRow?.realName" class="mb-3 wrap2">
          医生:
          {{ bindItemRow.realName }}
        </div>
        <hr>
        <div class="mt-4 flex">
          <div class="text-sm mt-6-px">分组标签：</div>
          <el-checkbox-group v-model="doctorCheckList">
            <el-checkbox
              v-for="(item) in grouPingList"
              :key="item.armCode"
              :disabled="item.armCode === 'MT'"
              :label="item.armCode">{{ item?.armName }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="w-full mt-3 mb-24 pl-16 text-orange-300">
          <div>此配置项应用于患者自动绑定医生：</div>
          <div class="mt-1">1、请先和项目配置人员确认已开启此功能</div>
          <div class="mt-1">2、更新医生的分组标签，只影响未来入组的患者</div>
        </div>
      </template>
      <template #footer>
        <div class="flex justify-end">
          <el-button plain @click="editDialogShowFlag = 0">取消</el-button>
          <el-button
            :loading="saveLoadingFlag"
            type="primary"
            @click="saveDoctorData"
          >保存</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, } from 'vue'
import { useStore } from 'vuex'
import { getUserArmInfos, getStudyArmsInfo,
  postUserArmInfo } from '@/api/doctorPatientBinding'

export default defineComponent({
  name: 'DoctorList', // 医生列表
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      editDialogShowFlag: 0,
      saveLoadingFlag: false,
      bindItemRow: null,
      listParams: null,
      studyName: store?.state?.studyItem?.studyName || '',
      doctorListTableRef: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { type: 'selection' },
        { label: '中心名称', prop: 'siteName', width: 300 },
        { label: '姓名', prop: 'realName', width: 260 },
        { label: '角色', prop: 'userRoleName', minWidth: 150 },
        { label: '分组标签', prop: 'armNames', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            options: store.state.studyItem.sites,
            clearable: true,
            defaultValue: store.state.studyItem.sites[0].siteId,
          },
          {
            type: 'text',
            label: '姓名',
            name: 'name',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20],
        style: { textAlign: 'left' },
      },
      selectedItems: [],
      doctorCheckList: [],
      grouPingList: [],

      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      // 请求函数
      async getList(params) {
        if (!params?.siteId) {
          params.siteId = store.state.studyItem.sites[0].siteId
        }
        state.listParams = params
        try {
          const data = await getUserArmInfos(studyId, params)
          return {
            data: data.items,
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      refresh: () => {
        state.doctorListTableRef.refresh()
      },
      // 绑定
      bindItem: (row) => {
        state.bindItemRow = { ...row }
        if (row?.armCodes) {
          state.doctorCheckList = [...row.armCodes.split('、')]
        } else {
          state.doctorCheckList = []
        }
        state.editDialogShowFlag = 1
        
      },
      // 多个绑定
      bindAllItem: () => {
        if (state.selectedItems.length === 1) {
          state.doctorCheckList = [...state.selectedItems[0].armCodes.split('、')]
        } else {
          state.doctorCheckList = []
        }
        if (state.selectedItems.length) {
          state.editDialogShowFlag = 2
        } else {
          ElMessage.warning('请选择要编辑的数据')
        }
      },
      // 保存
      saveDoctorData: () => {
        const dctUserIds = []
        const armInfos = []
        if (state.doctorCheckList?.length) {
          state.grouPingList.forEach((grouPingEl) => {
            state.doctorCheckList.forEach((checkEl) => {
              if (grouPingEl.armCode === checkEl) {
                armInfos.push(grouPingEl)
              }
            })
          })
        }
        if (state.editDialogShowFlag === 1) {
          dctUserIds[0] = state.bindItemRow.dctUserId
        } else if (state.editDialogShowFlag === 2) {
          state.selectedItems.forEach(el => {
            dctUserIds.push(el.dctUserId)
          })
        }
        const param = {
          dctSiteId: state.listParams.siteId,
          dctUserIds,
          armInfos,
        }
        state.saveLoadingFlag = true
        postUserArmInfo(studyId, param)
          .then(() => {
            state.doctorCheckList.length = 0
            state.saveLoadingFlag = false
            state.saveDoctorDataSuccess()
          }).catch(() => {
            state.saveLoadingFlag = false
          })
      },
      // 成功保存
      saveDoctorDataSuccess: () => {
        // 绑定成功后更新页面
        ElMessage.success('保存成功')
        state.editDialogShowFlag = 0
        state.refresh()
      },
    })
    onMounted(() => {
      getStudyArmsInfo(studyId).then((res) => {
        state.grouPingList = res
      })
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>
