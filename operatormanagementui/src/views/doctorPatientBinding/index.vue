<template>
  <div class="doctorPatientBinding">
    <trial-table
      ref="doctorPatientBindingTableRef"
      :title="doctorPatientBindingTableRef?.searchModel?.userType === '1' ? '以医生维度绑定受试者' : '以受试者维度绑定医生'"
      :request="getList"
      :columns="doctorPatientBindingTableRef?.searchModel?.userType === '1' ? columns : patientColumns"
      :search="searchConfig"
      :pagination="paginationConfig"
      @selectionChange="handleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #head-nav-slot>
        <div class="w-full mb-6" v-html="studyName" />
      </template>
      <template #toolbar>
        <el-button
         v-permission="['manage.DoctorPatientBinding.bindOrBatchBind']"
         type="success"
         @click="bindAllItem"
        >批量绑定</el-button>
      </template>
      <template #operate="scope">
        <el-button v-permission="['manage.DoctorPatientBinding.bindOrBatchBind']" size="small" type="primary" text @click="bindItem(scope.row)">
          绑定
        </el-button>
      </template>
    </trial-table>
    <!-- 绑定 -->
    <trial-dialog
      v-if="bindDialogShowFlag > 0"
      :my-dialog-body-style="{width: '60%'}"
      :title="doctorPatientBindingTableRef?.searchModel?.userType === '1' ? '以医生维度绑定受试者' : '以受试者维度绑定医生'"
      my-title-class="flex justify-center my-1"
    >
      <template #DialogBody>
        <div
          v-if="bindDialogShowFlag === 2 && selectedItems?.length > 1"
          class="my-3"
        >已选择{{ selectedItems.length }}
          条数据（以下数据为选中
          {{ doctorPatientBindingTableRef?.searchModel?.userType === '1' ? '医生' : '受试者' }}
          的交集数据）</div>
        <div v-if="bindDialogShowFlag === 2 && selectedItems?.length > 1" class="mb-3 wrap2">{{ doctorPatientBindingTableRef?.searchModel?.userType === '1' ? '医生' : '受试者' }}:
          <span v-for="(item, index) in selectedItems" :key="item.dctUserId">
            {{ doctorPatientBindingTableRef?.searchModel?.userType === '1' ? item.doctorName : item.patNumber }}
            <span v-if="index+1 < selectedItems.length">,</span>
          </span>
        </div>
        <div v-else-if="bindItemRow?.doctorName" class="mb-3 wrap2">
          {{ doctorPatientBindingTableRef?.searchModel?.userType === '1' ? '医生' : '受试者' }}:
          {{ bindItemRow.doctorName }}
        </div>
        <!-- 单个受试者绑定是展示病例编号 -->
        <div v-else-if="bindItemRow?.patNumber" class="mb-3 wrap2">
          {{ doctorPatientBindingTableRef?.searchModel?.userType === '1' ? '医生' : '受试者' }}:
          {{ bindItemRow.patNumber }}
        </div>
        <hr>
        <el-transfer
          v-model="transferValue"
          filterable
          :titles="doctorPatientBindingTableRef?.searchModel?.userType === '1' ? titles : doctorTitles"
          class="flex justify-center"
          :props="transferProps"
          :data="transferData"
          :filter-placeholder="doctorPatientBindingTableRef?.searchModel?.userType === '1' ? '请输入受试者编号或姓名' : '请输入医生姓名'"
        />
      </template>
      <template #footer>
        <div class="flex justify-center">
          <el-button plain @click="bindDialogShowFlag = 0">取消</el-button>
          <el-button
            :loading="saveLoadingFlag"
            type="primary"
            @click="saveBindData"
          >保存</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, } from 'vue'
import { useStore } from 'vuex'
import {
  getUserSubordinates,
  getUserSubordinatesTransfer,
  postUserSubordinates
} from '@/api/doctorPatientBinding'

export default defineComponent({
  name: 'DoctorPatientBinding',
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      bindDialogShowFlag: false,
      saveLoadingFlag: false,
      studyName: store?.state?.studyItem?.studyName || '',
      doctorPatientBindingTableRef: null,
      myParams: null,
      bindItemRow: null,
      // dimensionality: '1',
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { type: 'selection' },
        { label: '中心名称', prop: 'dctSiteName', width: 300 },
        { label: '医生姓名', prop: 'doctorName', width: 260 },
        { label: '已绑定受试者', prop: 'bindedNum', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      patientColumns: [
        { type: 'selection' },
        // 受试者列表:
        { label: '中心名称', prop: 'dctSiteName', width: 300 },
        { label: '受试者编号', prop: 'patNumber', width: 180 },
        { label: '入组日期', prop: 'inGroupDate', width: 180 },
        { label: '已绑定医生', prop: 'bindedNum', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            options: store.state.studyItem.sites,
            clearable: true,
            defaultValue: store.state.studyItem.sites[0].siteId,
          },
          {
            type: 'selectChangeSearch',
            label: '操作方式',
            name: 'userType',
            options: [
              {
                name: '以医生维度绑定受试者',
                value: '1'
              },
              {
                name: '以受试者维度绑定医生',
                value: '2'
              },
            ],
            clearable: true,
            defaultValue: '1',
          },
          {
            type: 'text',
            label: '姓名',
            name: 'nameOrNumber',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20],
        style: { textAlign: 'left' },
      },
      selectedItems: [],
      transferValue: [],
      titles: ['未绑定的受试者', '已绑定的受试者'],
      doctorTitles: ['未绑定的医生', '已绑定的医生'],
      transferData: [],
      transferProps: {
        key: 'id',
        label: 'displayValueStr'
      },

      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      // 请求函数
      async getList(params) {
        if (params.userType === '2' && state.searchConfig?.fields[2]?.label !== '受试者编号') {
          state.searchConfig.fields[2].label = '受试者编号'
        } else if (params.userType === '1' && state.searchConfig?.fields[2].label !== '姓名') {
          state.searchConfig.fields[2].label = '姓名'
        }
        try {
          if (!params?.siteId) {
            params.siteId = store.state.studyItem.sites[0].siteId
          }
          if (!params?.userType) {
            params.userType = '1'
          }
          state.myParams = { ...params }
          const data = await getUserSubordinates(studyId, params)
          return {
            data: data.items,
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      refresh: () => {
        state.doctorPatientBindingTableRef.refresh()
      },
      // 绑定
      bindItem: (row) => {
        state.bindItemRow = { ...row }
        const params = {
          siteId: row.dctSiteId,
          userType: state.myParams?.userType || '1',
          userOrPatIds: state.myParams?.userType === '1' ? row.dctUserId : row.dctPatientId
        }
        getUserSubordinatesTransfer(studyId, params)
          .then((res) => {
            state.transferValue = []
            state.transferData = [...res.unBindedList]
            res.bindedList.forEach(el => {
              state.transferData.push(el)
              state.transferValue.push(el.id)
            })
            state.bindDialogShowFlag = 1
          })
      },
      // 多个绑定
      bindAllItem: (row) => {
        if (state.selectedItems.length) {
          const dctUserIdArr = []
          state.selectedItems.forEach(item => {
            if (state.myParams?.userType === '2') {
              dctUserIdArr.push(item.dctPatientId)
            } else {
              dctUserIdArr.push(item.dctUserId)
            }
          })
          const params = {
            siteId: state.selectedItems[0].dctSiteId,
            userType: state.myParams?.userType || '1',
            userOrPatIds: dctUserIdArr + ''
          }
          getUserSubordinatesTransfer(studyId, params)
            .then((res) => {
              state.transferValue = []
              state.transferData = [...res.unBindedList]
              res.bindedList.forEach(el => {
                state.transferData.push(el)
                state.transferValue.push(el.id)
              })
              state.bindDialogShowFlag = 2
            })
        } else {
          ElMessage.warning('请选择要绑定的数据')
        }
      },
      // 保存
      saveBindData: () => {
        const data = {
          dctSiteId: state.myParams.siteId,
          userType: state.myParams.userType,
          userIds: [],
          patientIds: []
        }
        const selectedIdArr = []
        state.selectedItems.forEach(el => {
          if (state.myParams?.userType === '2') {
            selectedIdArr.push(el.dctPatientId)
          } else {
            selectedIdArr.push(el.dctUserId)
          }
        })
        if (state.myParams?.userType === '2') {
          data.userIds = [...state.transferValue]
          if (state.bindDialogShowFlag === 1) {
            data.patientIds = [state.bindItemRow.dctPatientId]
          } else if (state.bindDialogShowFlag === 2) {
            data.patientIds = [...selectedIdArr]
          }
        } else {
          data.patientIds = [...state.transferValue]
          if (state.bindDialogShowFlag === 1) {
            data.userIds = [state.bindItemRow.dctUserId]
          } else if (state.bindDialogShowFlag === 2) {
            data.userIds = [...selectedIdArr]
          }
        }
        state.saveLoadingFlag = true
        postUserSubordinates(studyId, data)
          .then((res) => {
            state.saveLoadingFlag = false
            state.saveBindDataSuccess()
          }).catch(() => {
            state.saveLoadingFlag = false
          })
      },
      // 成功保存
      saveBindDataSuccess: () => {
        // 绑定成功后更新页面
        ElMessage.success('保存成功')
        state.editDialogShowFlag = 0
        state.refresh()
      },
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>
