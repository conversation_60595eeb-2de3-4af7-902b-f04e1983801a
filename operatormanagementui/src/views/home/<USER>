<template>
  <div v-if="showModuleFlag" class="relative w-full h-full">
    <el-button
      v-permission="['manage.home.exportMedicationData']"
      type="primary"
      class="mb-3"
      @click="exportExcel(0)"
    >导出用药数据</el-button>
    <br>
    <el-button
      v-permission="['manage.home.exportCustomTasks']" 
      class="mb-3"
      type="primary"
      @click="exportExcel(1)"
    >
      导出自定义任务
    </el-button>
    <br>
    <el-button
      v-permission="['manage.home.exportVisitTaskCompletion']"
      class="mb-3"
      type="primary"
      @click="exportExcel(2)"
    >
      导出访视任务完成情况
    </el-button>
    <br>
    <el-button
      v-permission="['manage.home.exportQuestionnaireReview']"
      class="mb-3"
      type="primary"
      @click="exportExcel(3)"
    >
      导出问卷签署&审阅情况
    </el-button>
  </div>
  <h3 v-else class="text-[#777]">暂无权限</h3>
</template>

<script lang="ts">
// import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import {
  defineComponent,
  onMounted,
  onUnmounted,
  reactive,
  toRefs,
  watch,
  // computed,
} from 'vue'
import LoadingPlugin from '@/utils/loading'
import { getQuestExportApply, getQuestExportResult } from '@/api/home'

export default defineComponent({
  name: 'home',
  setup() {
    const route = useRoute()
    const store = useStore()
    // const router = useRouter()
    const state = reactive({
      showModuleFlag:  store.state.menu?.menus?.some(menu => menu.url === '/home'),
      onRefresh: () => {},
      exportExcel: (index) => {
        if (index === 0) {
          if (route.query.exportType) {
            window.open(
              `${window.location.origin}/api/Operator/Report/${store.state.studyItem?.studyId}/DrugQuestReport/Export`
            )
          } else {
            state.startExport(2)
          }
        } else if (index === 1) {
          if (route.query.exportType) {
            window.open(
              `${window.location.origin}/api/Operator/Report/${store.state.studyItem?.studyId}/CustomQuestReport/Export`
            )
          } else {
            state.startExport(1)
          }
        } else if (index === 2) {
          window.open(
            `${window.location.origin}/api/Operator/DoctorPatient/${store.state.studyItem?.studyId}/ExportVisitQuest`
          )
        } else if (index === 3) {
          state.startExport(3)
        }
      },
      // 获取数据
      onLoad: () => {
        state.onLoad()
      },
      intervalId: null,
      timeInterval: 3000,
      startExport: (exportType) => {
        let key
        switch (exportType) {
          case 1:
            key = 'CustomQuestReportKey'
            break
          case 2:
            key = 'DrugQuestReportKey'
            break
          case 3:
            key = 'SignatureReviewKey'
            break
        }
        let exportTaskKey = sessionStorage.removeItem(key)
        LoadingPlugin.open('请稍等，数据导出中...')
        getQuestExportApply(store.state.studyItem?.studyId, exportType)
          .then((res) => {
            sessionStorage.setItem(key, res)
            state.intervalId = setTimeout(
              state.exportReport,
              state.timeInterval,
              exportType
            )
          })
          .catch((error) => {
            console.error('发起导出请求失败:', error)
            LoadingPlugin.close()
          })
      },
      exportReport: (exportType) => {
        let studyId = store.state.studyItem?.studyId
        let key
        switch (exportType) {
          case 1:
            key = 'CustomQuestReportKey'
            break
          case 2:
            key = 'DrugQuestReportKey'
            break
          case 3:
            key = 'SignatureReviewKey'
            break
        }
        let applyCode = sessionStorage.getItem(key)

        getQuestExportResult(applyCode, studyId, exportType)
          .then((res) => {
            if (res.exportStatus === 2) {
              sessionStorage.removeItem(key)
              clearTimeout(state.intervalId)
              LoadingPlugin.close()
              ElMessage.error(`数据导出失败，请联系管理员！`)
            } else if (res.exportStatus === 1) {
              clearTimeout(state.intervalId)
              var blob = new Blob(
                [Uint8Array.from(atob(res.fileBytes), (c) => c.charCodeAt(0))],
                { type: 'application/octet-stream' }
              )
              const url = window.URL.createObjectURL(new Blob([blob]))
              const link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', res.fileDownLoadName)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) // 下载完成移除元素
              window.URL.revokeObjectURL(url) // 释放掉blob对象
              sessionStorage.removeItem(key)
              LoadingPlugin.close()
            } else {
              clearTimeout(state.intervalId)
              state.timeInterval =
                state.timeInterval + 2000 > 10000
                  ? 10000
                  : state.timeInterval + 2000
              state.intervalId = setTimeout(
                state.exportReport,
                state.timeInterval,
                exportType
              )
            }
          })
          .catch((error) => {
            console.error('轮询请求失败:', error)
            clearTimeout(state.intervalId)
            LoadingPlugin.close()
          })
      },
    })
    onMounted(() => {
      state.onLoad()
    })
    onUnmounted(() => {
      if (state.intervalId) {
        clearTimeout(state.intervalId)
      }
    })

    // 监听 store 中菜单的变化，实时更新 showModuleFlag
    watch(
      () => store.state.menu?.menus,
      (newMenus) => {
        if (newMenus && newMenus.length > 0) {
          state.showModuleFlag = newMenus.some(menu => menu.url === '/home')
        } else {
          state.showModuleFlag = false
        }
      },
      { immediate: true, deep: true }
    )

    return {
      ...toRefs(state),
    }
  },
})
</script>
