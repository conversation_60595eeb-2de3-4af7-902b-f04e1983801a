<template>
  <div class="lock-wrap">
    <Unlock />
    <Clock />
    <current-time />
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import Unlock from '@/views/lock/Unlock.vue'
import Clock from '@/views/lock/Clock.vue'
import CurrentTime from '@/views/lock/CurrentTime.vue'

export default defineComponent({
  name: 'Lock',
  components: {
    Unlock,
    Clock,
    CurrentTime
  }
})
</script>

<style lang="scss" scoped>
.lock-wrap {
  background: #222;
  height: 100%;
  min-height: 480px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>
