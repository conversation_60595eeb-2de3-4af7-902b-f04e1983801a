<template>
  <el-dialog
    v-model="ROLE_FORM_DATAS.dialogVisible"
    :title="ROLE_FORM_DATAS.title"
  >
    <el-form ref="roleFormRef" :model="roleForm" :rules="rules">
      <el-form-item
        label="角色名称"
        :label-width="formLabelWidth"
        prop="roleName"
      >
        <el-input
          v-model.trim="roleForm.roleName"
          autocomplete="off"
        />
      </el-form-item>
      <!-- check-strictly 断开和父级关联-->
      <el-tree
        ref="roleTree"
        :props="propsDatas"
        :data="roleOptions"
        show-checkbox
        :default-expand-all="true"
        node-key="title"
        @check-change="handleCheckChange"
      />
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="ROLE_FORM_DATAS.dialogVisible = false"
        >取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  inject,
  onMounted,
  ref,
  nextTick,
} from 'vue'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'EditRole',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    }
  },
  setup(props) {
    const store = useStore()
    const ROLE_FORM_DATAS = inject('ROLE_FORM_DATAS')
    const roleTree = ref<HTMLElement | null>(null)
    // 得到已经勾选的数组
    const CheckedKeyArr = []
    function checkes(oldArr) {
      if (Array.isArray(oldArr)) {
        oldArr.forEach((item) => {
          if (item.children) {
            checkes(item.children)
          }
          if (item.title) {
            CheckedKeyArr.push(item.title)
          }
        })
      }
    }

    const state = reactive({
      // el-tree 默认展示框
      roleOptions: [
        {
          url: '/home',
          title: 'Dashboard',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          // icon: 'home',
          // children: [
          //   {
          //     url: '/home', // '/home/<USER>'
          //     title: '详情',
          //   },
          // ],
        },
        {
          url: '/subjectsList',
          title: '受试者列表',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          // children: [
          //   {
          //     url: '/subjectsList',
          //     title: '详情',
          //   },
          // ],
        },
        {
          url: '/visitInside',
          title: '访视窗口期内',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          // children: [
          //   {
          //     url: '/visitInside',
          //     title: '详情',
          //   },
          // ],
        },
        {
          url: '/visitOutside',
          title: '访视任务超窗',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          // children: [
          //   {
          //     url: '/visitOutside',
          //     title: '详情',
          //   },
          // ],
        },
        {
          url: '/discomfortRecord',
          title: '不适记录',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          // children: [
          //   {
          //     url: '/discomfortRecord',
          //     title: '详情',
          //   },
          // ],
        },
        {
          url: '/concomitantMedication',
          title: '合并用药',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          // children: [
          //   {
          //     url: '/concomitantMedication',
          //     title: '详情',
          //   },
          // ],
        },
        {
          url: '/electronicInfo',
          title: '电子知情',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
        },
        //     children: [
        //       {
        //         url: '/electronicInfo',
        //         title: '详情',
        //       },
        //     ],
        //   },
        {
          url: '/learningAndAssessment',
          title: '学习&测评',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          children: [
            {
              url: '/subjectsLearningInfo',
              title: '受试者学习统计',
            },
            {
              url: '/researchersLearningInfo',
              title: '研究人员学习统计',
            },
            {
              url: '/learningMaterialsInfo',
              title: '学习资料统计',
            }
          ]
        },
        {
          url: '/interactiveCommunication',
          title: '互动沟通',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI'],
          children: [
            {
              url: '/interactiveCommunication',
              title: '受试者沟通统计'
            },
            {
              url: '/researchersCommunicationInfo',
              title: '研究人员沟通统计'
            }
          ]
        },
        {
          url: '/doctorPatientBinding',
          title: '医患绑定',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'PI'],
          children: [
            {
              url: '/doctorPatientBinding',
              title: '医患绑定',
            },
            {
              url: '/doctorList',
              title: '医生列表',
            },
          ],
        },
        {
          url: '/materialsRecycle',
          title: '物资发放回收',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', '物资回收专员'],
        },
        {
          url: '/orderManagement',
          title: '订单管理',
          roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', '物资回收专员'],
          children: [
            {
              url: '/orderManagement',
              title: '物流订单',
            },
            {
              url: '/goodsRepertory',
              title: '物资库存记录',
            },
            {
              url: '/materialOrder',
              title: '物资订单',
            },
          ],
        },
        {
          url: '/dataMigration',
          title: '数据迁移',
          roles: ['admin', 'DM'],
          children: [
            {
              url: '/dataMigration',
              title: '数据迁移',
            },
            {
              url: '/migrationLog',
              title: '迁移记录',
            },
          ],
        },
      ],

      propsDatas: {
        // 勾选展示
        label: 'title',
        children: 'children',
      },
      roleForm: {
        roleName: '',
      },
      roleFormRef: null,
      formLabelWidth: '86px',
      rules: {
        roleName: [
          { required: true, message: '请输入角色名', trigger: 'blur' },
        ],
      },
      loading: false,

      submit: () => {
        state.roleFormRef.validate((valid) => {
          if (valid) {
            state.loading = true
            props.request()
            state.loading = false
            ROLE_FORM_DATAS.dialogVisible = false
          }
        })
      },

      // 选中时的事件
      handleCheckChange: (data, checked, indeterminate) => {
        console.log(roleTree.value.getCheckedKeys()) // 获取到选中的所有key(不带父级的)
        console.log(data) // console.log(checked)console.log(indeterminate)
      },
    })

    onMounted(() => {
      if (ROLE_FORM_DATAS.userFormData) {
        state.roleForm = ROLE_FORM_DATAS.userFormData
      }
      if (store.state.menu.menus && ROLE_FORM_DATAS.userFormData.id) {
        // 当前角色 设置已经勾选的 -> 已有权限
        nextTick(() => {
          // console.log(
          //   store.state.menu.menus.slice(1, store.state.menu.menus.length)
          // )
          checkes(
            store.state.menu.menus
          )
          roleTree.value.setCheckedKeys(CheckedKeyArr)
        })
      }
    })

    return {
      ...toRefs(state),
      ROLE_FORM_DATAS,
      roleTree
    }
  }
})
</script>
