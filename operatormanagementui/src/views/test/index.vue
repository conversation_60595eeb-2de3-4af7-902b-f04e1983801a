<template>
  <div>
    <trial-table
      ref="userTable"
      title="用户列表"
      :request="getList"
      :columns="columns"
      :search="searchConfig"
      :pagination="paginationConfig"
      @selectionChange="handleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <!-- <el-button type="primary" icon="el-icon-delete" @click="batchDelete">
        批量删除
      </el-button> -->
        <el-button type="primary" icon="el-icon-plus" @click="editItem(null)">
          添加用户
        </el-button>

        <el-button type="primary" icon="el-icon-refresh" @click="refresh">
          刷新
        </el-button>
      </template>
      <template #operate="scope">
        <el-button size="small" type="primary" @click="editItem(scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          :disabled="scope.row.nickName === 'admin'"
          @click="deleteItem(scope.row)"
        >删除</el-button>
      </template>
    </trial-table>

    <!--  用户信息编辑表单 -->
    <EditUserForm
      v-if="USER_FORM_DATAS.dialogVisible"
      :request="refresh"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, provide } from 'vue'
import EditUserForm from '@/views/test/Edit.vue'

export default defineComponent({
  name: 'TestList',
  components: {
    EditUserForm
  },
  setup() {
    const USER_FORM_DATAS = reactive({
      userFormData: {},
      dialogVisible: 0
    })
    const state = reactive({
      userTable: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { type: 'selection' },
        { label: '序号', type: 'index' },
        { label: '名称', prop: 'nickName', width: 180 },
        { label: '邮箱', prop: 'userEmail', width: 220 },
        { label: '手机号', prop: 'mobile' },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            label: '账户名称',
            name: 'nickName',
            defaultValue: '',
          },
          {
            type: 'text',
            label: '手机号',
            name: 'mobile',
            defaultValue: '',
          },
          {
            type: 'text',
            label: '邮箱',
            name: 'mailbox',
            defaultValue: '',
          },
          {
            label: '状态',
            name: 'status',
            type: 'select',
            defaultValue: null,
            options: [
              {
                name: '已发布',
                value: 1,
              },
              {
                name: '未发布',
                value: 0,
              },
            ],
          },
          // {
          //   label: '性别',
          //   name: 'sex',
          //   type: 'radio',
          //   options: [
          //     {
          //       name: '男',
          //       value: 1,
          //     },
          //     {
          //       name: '女',
          //       value: 0,
          //     },
          //   ],
          // },
          // {
          // {
          //   label: '日期',
          //   name: 'date',
          //   type: 'date',
          // },
          // {
          //   label: '时间',
          //   name: 'datetime',
          //   type: 'datetime',
          //   defaultValue: '2020-10-10 8:00:00',
          // },
          // {
          //   label: '日期范围',
          //   name: 'daterange',
          //   type: 'daterange',
          //   trueNames: ['startDate', 'endDate'],
          // },
          // {
          //   label: '时间范围',
          //   name: 'datetimerange',
          //   type: 'datetimerange',
          //   trueNames: ['startTime', 'endTime'],
          //   style: { width: '360px' },
          //   defaultValue: ['2020-10-10 9:00:00', '2020-10-11 18:30:00'],
          // },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20, 50],
        style: { textAlign: 'left' },
      },
      selectedItems: [],
      // 批量删除
      // batchDelete() {
      //   console.log(state.selectedItems)
      // },
      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      // 请求函数
      async getList(params) {
        // params是从组件接收的，包含分页和搜索字段。
        try {
          const list = [
            {
              id: 1,
              nickName: 'zhangsan',
              userEmail: '<EMAIL>',
              mobile: '15224646611',
            },
            {
              id: 2,
              nickName: 'admin',
              userEmail: '<EMAIL>',
              mobile: '15224646612',
            },
            {
              id: 3,
              nickName: 'test1',
              userEmail: '<EMAIL>',
              mobile: '15224646613',
            },
            {
              id: 4,
              nickName: 'lisi4',
              userEmail: '<EMAIL>',
              mobile: '15224646614',
            },
            {
              id: 5,
              nickName: 'wanger',
              userEmail: '<EMAIL>',
              mobile: '15224646615',
            },
            {
              id: 6,
              nickName: 'ybule',
              userEmail: '<EMAIL>',
              mobile: '15224646616',
            },
          ]
          const { data } = await new Promise((rs) => {
            setTimeout(() => {
              rs({
                code: 200,
                data: {
                  list,
                  total: list.length,
                },
              })
            }, 1000)
          })

          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: data.list,
            total: +data.total,
          }
        } catch (e) {
          console.log(e)
        }
      },

      // 刷新
      refresh: () => {
        state.userTable.refresh()
      },
      // 新增-编辑
      editItem: (row) => {
        USER_FORM_DATAS.userFormData = { ...row }
        USER_FORM_DATAS.dialogVisible = true
        USER_FORM_DATAS.title = row ? '用户信息修改' : '新增用户信息'
      },
      // 删除
      deleteItem: (row) => {
        ElMessageBox.confirm(
          `您将删除${row.nickName}用户, 是否继续?`,
          '删除提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            ElMessage.success(`${row.nickName}用户删除成功!`)
          })
          .catch(() => {})
      },
    })

    // //传值
    provide('USER_FORM_DATAS', USER_FORM_DATAS)

    return {
      ...toRefs(state),
      USER_FORM_DATAS
    }
  }
})
</script>
