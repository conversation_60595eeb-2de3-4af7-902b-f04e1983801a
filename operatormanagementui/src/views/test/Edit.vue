<template>
  <el-dialog
    v-model="USER_FORM_DATAS.dialogVisible"
    :title="USER_FORM_DATAS.title"
  >
    <el-form ref="userFormRef" :model="userForm" :rules="rules">
      <el-form-item
        label="用户名称"
        :label-width="formLabelWidth"
        prop="nickName"
      >
        <el-input
          v-model.trim="userForm.nickName"
          :disabled="userForm.nickName === 'admin'"
          autocomplete="off"
        />
      </el-form-item>
      <el-form-item
        label="用户邮箱"
        :label-width="formLabelWidth"
        prop="userEmail"
      >
        <el-input
          v-model.trim="userForm.userEmail"
          autocomplete="off"
        />
      </el-form-item>
      <el-form-item
        label="手机号码"
        :label-width="formLabelWidth"
        prop="mobile"
      >
        <el-input v-model.trim="userForm.mobile" autocomplete="off" />
      </el-form-item>
      <!-- <el-form-item label="活动区域" :label-width="formLabelWidth">
      <el-select v-model="form.region" placeholder="请选择活动区域">
        <el-option label="区域一" value="shanghai"></el-option>
        <el-option label="区域二" value="beijing"></el-option>
      </el-select>
    </el-form-item> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="USER_FORM_DATAS.dialogVisible = false"
        >取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'

export default defineComponent({
  name: 'AuditForm',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    }
  },
  setup(props) {
    // setup(props,context){} //let { attrs, emit, expose, slots } = context
    const USER_FORM_DATAS = inject('USER_FORM_DATAS')
    const state = reactive({
      userForm: {
        nickName: '',
        userEmail: '',
        mobile: '',
      },
      formLabelWidth: '120px',
      userFormRef: null,
      loading: false,
      rules: {
        nickName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        userEmail: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            pattern: /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/,
            message: '请输入正确的邮箱',
            trigger: 'blur',
          },
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
      },

      submit: () => {
        state.userFormRef.validate((valid) => {
          if (valid) {
            state.loading = true
            props.request()
            state.loading = false
            USER_FORM_DATAS.dialogVisible = false
          }
        })
      },
    })

    onMounted(() => {
      if (USER_FORM_DATAS.userFormData) {
        state.userForm = USER_FORM_DATAS.userFormData
      }
    })

    return {
      ...toRefs(state),
      USER_FORM_DATAS
    }
  }
})
</script>
