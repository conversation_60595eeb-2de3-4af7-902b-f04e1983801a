<template>
  <div class="cache-container">
    <!-- 取色器 -->
    <div class="color-picker-modules">
      更换背景色 :&nbsp;&nbsp;&nbsp;&nbsp;
      <el-color-picker
        v-model="colorPickerModel"
        show-alpha
        :predefine="predefineColors"
        @change="colorPickerChange"
      />
    </div>

    <!-- 透明度 -->
    <div class="opcity-bg">
      <span>调节背景透明度 :</span>
      <el-slider v-model="opcityValue" :format-tooltip="formatTooltip" />
    </div>

    <!-- 评分 -->
    <div class="score-modules">
      <div class="score-title">评分 :</div>
      <el-rate v-model="score" :colors="rateColors" allow-half />
      <p>{{ score }}分</p>
    </div>
    <!-- 暂无数据 -->
    <!-- <el-empty :image-size="200"></el-empty> -->
    <!-- 上传多文件 -->
    <div class="yy-upload">
      <YyUpload :upload-file-falg="true" />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onActivated,
  reactive,
  toRefs,
  nextTick,
  onDeactivated,
} from 'vue'
import YyUpload from '@/components/Upload/index.vue'
import { throttle } from '@/utils/index'
// import { useStore } from 'vuex'
export default defineComponent({
  name: 'TestCache', // 可缓存页面
  components: {
    YyUpload
  },
  setup() {
    // const store = useStore()
    const state = reactive({
      colorPickerModel: 'rgba(158, 234, 235, 1)',
      score: null,
      opcityValue: 100,
      rateColors: ['#99A9BF', '#F7BA2A', '#FF9900'],
      predefineColors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],

      colorPickerChange: (color) => {
        const main = document.getElementsByClassName('main')[0]
        // let top = document.getElementsByClassName('top')[0]
        // let tagsContainer = document.getElementsByClassName('tags-container')[0] //tags-item
        main.style.background = color
      },

      formatTooltip: (val) => {
        throttle(() => {
          nextTick(() => {
            const main = document.getElementsByClassName('main')[0]
            main.style.opacity = val / 100
          })
          return val // -->   / 100;
        }, 300)()
      },
    })

    onActivated(() => {
      console.log('onActivated,keep-alive 缓存的组件激活时调用')
    })

    onDeactivated(() => {
      // console.log(state.opcityValue);
      console.log('onDeactivated,keep-alive 缓存的组件停用时调用')
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.cache-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  /*取色器*/
  .color-picker-modules {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  /*透明度 条*/
  .opcity-bg {
    width: 50%;
    margin: 20px;
  }
  /*评分*/
  .score-modules {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ff9900;
    margin: 50px 0 0 0;
    .score-title {
      color: #999;
      font-weight: 700;
      margin-right: 20px;
    }
    p {
      min-width: 40px;
      margin-left: 10px;
    }
  }

  .yy-upload {
    margin: 50px 0 0 0;
  }
}
</style>
