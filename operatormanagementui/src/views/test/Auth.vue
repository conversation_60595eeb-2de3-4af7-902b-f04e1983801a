<template>
  <div>
    <trial-table
      ref="authTable"
      title="角色列表"
      :request="getList"
      :columns="columns"
      :search="searchConfig"
      :pagination="paginationConfig"
      @selectionChange="handleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <el-button type="primary" icon="el-icon-plus" @click="editItem(null)">
          添加角色
        </el-button>

        <el-button type="primary" icon="el-icon-refresh" @click="refresh">
          刷新
        </el-button>
      </template>
      <template #operate="scope">
        <el-button size="small" type="primary" @click="editItem(scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          :disabled="scope.row.roleName === '超级管理员'"
          @click="deleteItem(scope.row)"
        >删除</el-button>
      </template>
    </trial-table>

    <!--  用户信息编辑表单 -->
    <EditRoleForm
      v-if="ROLE_FORM_DATAS.dialogVisible"
      :request="refresh"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, provide } from 'vue'
import EditRoleForm from '@/views/test/EditRole.vue'
import { RoleFormInterface } from '@/type/views/auth'
export default defineComponent({
  name: 'TestAuth',
  components: {
    EditRoleForm
  },
  setup() {
    const ROLE_FORM_DATAS: RoleFormInterface = reactive({
      userFormData: {},
      dialogVisible: false,
    })
    const state = reactive({
      authTable: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { type: 'selection' },
        { label: '序号', type: 'index' },
        { label: '角色名称', prop: 'roleName', minWidth: 180 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            label: '角色名称',
            name: 'roleName',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20],
        style: { textAlign: 'left' },
      },
      selectedItems: [],

      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      // 请求函数
      async getList(params) {
        // params是从组件接收的，包含分页和搜索字段。
        try {
          const list = [
            {
              id: 1,
              roleName: '超级管理员',
            },
            {
              id: 2,
              roleName: '测试人员',
            },
            {
              id: 3,
              roleName: '数据管理员',
            },
            {
              id: 4,
              roleName: '库房管理员',
            },
            {
              id: 5,
              roleName: '临床研究协调员',
            },
            {
              id: 6,
              roleName: '药剂师',
            },
          ]
          const { data } = await new Promise((rs) => {
            setTimeout(() => {
              rs({
                code: 200,
                data: {
                  list,
                  total: list.length,
                },
              })
            }, 1000)
          })
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: data.list,
            total: +data.total,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      refresh: () => {
        state.authTable.refresh()
      },
      // 新增-编辑
      editItem: (row) => {
        ROLE_FORM_DATAS.userFormData = { ...row }
        ROLE_FORM_DATAS.dialogVisible = true
        ROLE_FORM_DATAS.title = row ? '角色信息修改' : '新增角色信息'
      },
      // 删除
      deleteItem: (row) => {
        ElMessageBox.confirm(
          `您将删除${row.roleName}角色, 是否继续?`,
          '删除提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            ElMessage.success(`${row.roleName}角色删除成功`)
          })
          .catch(() => {})
      }
    })

    // //传值
    provide('ROLE_FORM_DATAS', ROLE_FORM_DATAS)

    return {
      ...toRefs(state),
      ROLE_FORM_DATAS
    }
  }
})
</script>
