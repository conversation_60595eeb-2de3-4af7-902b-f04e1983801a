<template>
  <div class="cache-container">
    <!-- 拖拽添加单个X轴模块 -->
    <div class="draggable-x" draggable="true" @dragstart="drag($event)">
      <p class="draggable-x-title">添加X(访视期)</p>
      <div class="draggable-x-info">
        <div class="draggable-x-info-inputs">
          <div class="draggable-x-info-inputs-items">
            <p>显示顺序</p>
            <el-input v-model="addCrfDataX.sort" type="text" placeholder="请输入显示顺序" />
          </div>
          <div class="draggable-x-info-inputs-items">
            <p>显示名称</p>
            <el-input v-model="addCrfDataX.Xname" type="text" placeholder="请输入显示名称" />
          </div>
          <div class="draggable-x-info-inputs-items">
            <p>完整名称</p>
            <el-input v-model="addCrfDataX.Crfname" type="text" placeholder="请输入完整名称" />
          </div>
        </div>
        <div class="draggable-x-info-other">
          <div><input v-model="newTabName" type="checkbox">要求分组</div>
          <div>窗口期 ± <el-input v-model="newTabName" style="width:50px;" type="input" />&nbsp;&nbsp;&nbsp;天</div>
          <div>时间周期 <el-input v-model="newTabName" style="width:50px;" type="input" />&nbsp;&nbsp;&nbsp;
            <el-radio v-model="newTabName" label="1">W</el-radio>
            <el-radio v-model="newTabName" label="2">N</el-radio>
          </div>
          <div>
            显示规则
            <el-select v-model="newTabName" placeholder="请选择规则">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
      </div>
      <p class="draggable-x-title">子项</p>
      <div class="draggable-x-subitem">
        <div class="draggable-x-subitem-title">
          <p>显示名称</p>
          <p>完整名称</p>
          <p>显示规则</p>
          <p>时间周期</p>
          <p>删除</p>
        </div>
        <div style="background:#999;height:1px;margin: 0 0 8px 0;" />
        <div class="draggable-x-subitem-info">
          <el-input v-model="newTabName" type="text" />
          <el-input v-model="newTabName" type="text" />
          <el-select v-model="newTabName" placeholder="请选择规则">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-input v-model="newTabName" style="width:50px;" type="input" />&nbsp;&nbsp;&nbsp;
          <el-radio v-model="newTabName" label="1">W</el-radio>
          <el-radio v-model="newTabName" label="2">N</el-radio>
          <i class="el-icon-close" />
        </div>
        <el-button class="draggable-x-subitem-info-add-btn" type="primary">添加子项</el-button>
      </div>
    </div>
    <!-- 拖拽添加单个Y轴模块 -->
    <!-- <div class="draggable-Y" draggable="true" @dragstart="drag($event)">
      <div>y</div>
    </div> -->
    <!-- 表格 -->
    <table border class="table-modules" @drop="drop($event)" @dragover="allowDrop($event)">
      <thead>
        <tr>
          <td />
          <td v-for="(item, index) in theadData" :key="index">
            {{ item.name }}
            <td v-if="index===2">1</td>
          </td>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(itemY, indexY) in tableDataY" :key="indexY">
          <th>{{ itemY.title }}</th>
          <td v-for="(items, indexs) in theadData" :key="indexs" class="check-all">
            <!-- <input type="checkbox"> -->
            <i v-if="itemY.isCheck" class="el-icon-check" />
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'

// import { throttle } from '@/utils/index'
// import { useStore } from 'vuex'
export default defineComponent({
  name: 'TestDragover', // 拖拽测试页面

  setup() {
    // const store = useStore()
    const theadData = reactive([
      {
        name: '筛选期',
      },
      {
        name: '随机分组',
      },
      {
        name: '新辅助化疗(直接手术者无需填写)',
      },
      {
        name: '	基线（术后）',
      }
    ])
    const state = reactive({
      tableDataY: [
        {
          title: '访视日期',
          isCheck: false,
        },
        {
          title: '知情同意书',
          isCheck: true,
        },
        {
          title: '基本信息',
          isCheck: false,
        },
        {
          title: '分组信息',
          isCheck: false,
        },
        {
          title: '入排标准',
          isCheck: false,
        },
      ],
      options: [
        {
          value: '1',
          label: '总是显示',
        },
        {
          value: '2',
          label: '前访视有数据',
        },
        {
          value: '3',
          label: '按公式显示',
        },
        {
          value: '4',
          label: '按公式隐藏',
        },
      ],
      newTabName: '',
      addCrfDataX: {
        sort: theadData.length, // state.theadData.length-排序在最后
        Xname: '',
        Crfname: '',
        Rowspan: 1,
        VersionId: 0,
        Visitend: 0,
        Visitstart: 0,
        Visittimeunit: null,
        Xcode: 'X03',
        Xtype: null,
        Options: {
          BackColor: '#C5D5EC',
          ForeColor: '#000000',
          Icon: '',
          IconAlign: 'none',
          Width: 0
        },
        Children: [
          // {
          //   sort: state.theadData.length,
          //   Xname: '',
          //   Rowspan: 1,
          //   VersionId: 0,
          //   Visitend: 0,
          //   Visitstart: 0,
          //   Visittimeunit: null,
          //   Xcode: 'X03',
          //   Xtype: null,
          //   Options: {
          //     BackColor: '#C5D5EC',
          //     ForeColor: '#000000',
          //     Icon: '',
          //     IconAlign: 'none',
          //     Width: 0
          //   },
          //   Children: []
          // },
        ]
      },

      // 拖拽
      drag: (ev) => {
        console.log('拖动', ev)
        // state.dom = ev.currentTarget.cloneNode(true)
        // console.log(state.dom)
      },
      // 允许放下拖拽
      allowDrop: (ev) => {
        ev.preventDefault()
      },
      // 放下事件
      drop: (ev) => {
        console.log(ev)
        ev.preventDefault()
        if (state.newTabName) {
          state.theadData.push({ name: state.newTabName })
        }
        // const treeNode = ev.target
        // if (treeNode) {
        //   treeNode.appendChild(state.dom)
        // }
      }
    })

    return {
      theadData,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.cache-container {
  background: #f0f0f0;
  .draggable-x {
    width: 700px;
    font-size: 12px;
    margin: 0 0 30px 0;
    border: 1px solid skyblue;
    .draggable-x-title {
      font-weight: 600;
      color: #575765;
      background: #bad9ee;
      padding: 0 0 5px 0;
      margin: 0;
    }
    .draggable-x-info {
      width: 100%;
      display: flex;
      justify-content: space-around;
      .draggable-x-info-inputs {
        width: 35%;
        .el-input {
          margin: 5px 0;
        }
        .draggable-x-info-inputs-items{
          display: flex;
          justify-content: space-around;
          align-items: center;
          p{
            min-width: 60px;
          }
        }
      }
      .draggable-x-info-other {
        div{
          margin: 2px 0;
        }
      }
    }
    .draggable-x-subitem{
      width: 100%;
      padding: 0 10px 10px 10px;
      box-sizing: border-box;
      overflow: hidden;
      .draggable-x-subitem-title{
        display: flex;
        justify-content: space-around;
        font-weight: 700;
        color: skyblue;
      }
      .draggable-x-subitem-info{
        display: flex;
        align-items: center;
        .el-input{
          width: 20%;
          margin-right: 5px;
        }
        .el-select{
          width: 20%;
          margin-right: 5px;
        }
        .el-radio{
          height: 20px;
        }
        .el-icon-close{
          color: red;
          font-size: 20px;
          font-weight: 700;
        }
      }
      .draggable-x-subitem-info-add-btn{
          margin: 6px 0 0 0;
          float: right;
        }
    }
  }
  .dragover {
    width: 30%;
    background: skyblue;
    margin: 0 0 0 400px;
    height: 100px;
  }
  .table-modules {
    background: #f0f0f0;
    font-size: 12px;
    color: #000;
    border-color: #fff;
    border-collapse: collapse;
    border: none;
    thead {
      background: #6bbffb;
      tr td {
        padding: 10px;
        box-sizing: border-box;
      }
    }
    td,
    th {
      border: solid #fff 1px;
      border-radius: 5px;
    }
    th {
      font-weight: 400;
      padding: 10px;
      box-sizing: border-box;
      background: #6bbffb;
    }
    .check-all {
      text-align: center;
    }
  }
}
</style>
