<template>
  <div>
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="receiptsTableRef"
        title=""
        :request="getreceiptsList"
        :columns="receiptsColumns"
        :search="receiptsSearchConfig"
        :pagination="receiptsPaginationConfig"
        :showbtnfalg-excel="showbtnfalgExcel"
        :request-export-excel="requestExportExcel"
      >
        <template #operate="scope">
          <el-button
            v-permission="['manage.compensateReceipts.details']"
            size="small"
            text
            type="primary" 
            @click="receiptsEditItemForm(scope.row)"
          >
            详情
          </el-button>
        </template>
      </trial-table>
    </div>
    <ReceiptsDetails v-if="RESEARCHCENTER_INFOS?.contentVisible" />
  </div>
</template>

<script lang="ts">
import { defineComponent, provide, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import ReceiptsDetails from '@/views/patientCompensate/ReceiptsDetails.vue'
import { getPeriodicCompensationApplyList2 } from '@/api/patientCompensate'

export default defineComponent({
  name: 'CompensateReceipts', // 补偿/报销单据
  components: {
    ReceiptsDetails
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
    })
    const { studyId } = store.state.studyItem
    const state = reactive({
      // manage.compensateReceipts.exportExcel
      showbtnfalgExcel: store.state.studyItem.permissions.includes('manage.compensateReceipts.exportExcel'),
      studyName: store.state.studyItem.studyName,
      receiptsTableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      receiptsColumns: [
        // { type: 'selection' },
        { label: '创建时间', prop: 'createTimeStr', minWidth: 160 },
        { label: '单据号', prop: 'applyNumber', minWidth: 200 },
        { label: '中心', prop: 'siteName', minWidth: 180 },
        { label: '受试者编号', prop: 'patientNumber', minWidth: 130 },
        { label: '关联访视', prop: 'relationVisitName', minWidth: 200 },
        { label: '总金额(元)', prop: 'compensationAmount', minWidth: 150 },
        { label: '单据状态', prop: 'statusStr', minWidth: 130 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      receiptsSearchConfig: {
        // labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'daterange',
            label: '创建日期',
            name: 'dataTime',
            defaultValue: [
              '',
              ''
            ],
            filterable: true,
            clearable: true,
          },
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            // defaultValue: store.state.studyItem.sites[0].siteId,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '状态',
            name: 'Status',
            options: [
              {
                value: 0,
                name: '待确认'
              },
              {
                value: 1,
                name: '处理中'
              },
              {
                value: 2,
                name: '已处理'
              },
              {
                value: 3,
                name: '已拒绝'
              },
              {
                value: 4,
                name: '已退回'
              },
            ],
            filterable: true,
          },
          {
            type: 'text',
            label: '受试者编号',
            name: 'patNumber',
            defaultValue: '',
          },
          {
            type: 'text',
            label: '单号',
            name: 'applyNumber',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      receiptsPaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 请求函数
      async getreceiptsList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          if (params?.dataTime) {
            params.startTime = params.dataTime[0]
            params.endTime = params.dataTime[1]
          }
          if (!params.Status && params.Status !== 0) {
            params.Status = -1
          }
          const data = await getPeriodicCompensationApplyList2(studyId, params)
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data?.items || [],
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      receiptsRefresh: () => {
        state.receiptsTableRef.refresh()
      },
      // 导出表格
      requestExportExcel: (params) => {
        if (params?.dataTime) {
          params.startTime = params.dataTime[0]
          params.endTime = params.dataTime[1]
        }
        if (!params.Status && params.Status !== 0) {
          params.Status = -1
        }
        window.open(
          `${
            window.location.origin
          }/api/Operator/Compensation/${studyId}/GetPeriodicCompensationApplyExport?startTime=${params.startTime || ''}&endTime=${params.endTime || ''}&patNumber=${params.patNumber || ''}&applyNumber=${params.applyNumber || ''}&Status=${params.Status}&siteId=${params.siteId || ''}`
        )
      },
      // 新增-编辑
      receiptsEditItemForm: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.receiptsTableRef
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  }
})
</script>

<style scoped lang="less">
.head {
  width: 100%;
  background: #fff;
  padding: 20px 20px;
  box-sizing: border-box;
  h3 {
    margin: 0;
  }
}
</style>
