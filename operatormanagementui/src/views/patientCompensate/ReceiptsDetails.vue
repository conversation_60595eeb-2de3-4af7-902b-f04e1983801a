<template>
  <div class="receipts-details">
    <div class="receipts-mesg mb-5">
      <div class="receipts-go flex justify-between items-center mb-3">
        <h3>受试者信息</h3>
        <el-button @click="backRefresh">返回</el-button>
      </div>
      <div class="receipts-msg-centent flex">
        <div class="mr-28">
          <span class="mr-3 compensate-color">中心</span>
          <span>{{ detailsData?.siteName }}</span>
        </div>
        <div class="mr-28">
          <span class="mr-3 compensate-color">受试者编号</span>
          <span>{{ detailsData?.patientNumber }}</span>
        </div>
        <div class="mr-28">
          <span class="mr-3 compensate-color">姓名</span>
          <span>{{ detailsData?.patientName }}</span>
        </div>
      </div>
    </div>
    <!-- 表格 -->
    <div class="receipts-mesg mb-5">
      <div class="flex justify-between items-center mb-10">
        <div class="flex">
          <h4>单据号：{{ detailsData?.applyNumber }}</h4>
          <div class="ml-5">
            <span v-if="detailsData?.status === 0" style="color: #fac563;">待确认</span>
            <span v-else-if="detailsData?.status === 1" style="color: #fac563;">处理中</span>
            <span v-else-if="detailsData?.status === 2" style="color: #4b7902;">已处理</span>
            <span v-else-if="detailsData?.status === 3" class="compensate-color">已拒绝</span>
            <span v-else-if="detailsData?.status === 4" class="compensate-color">已退回</span>
          </div>
        </div>
        <div v-if="detailsData?.checkRemark && detailsData?.status === 0" class="compensate-color">
          待{{ detailsData.checkRemark }}确认
        </div>
      </div>
      <div class="receipts-box">
        <div class="flex mb-5">
          <span class="mr-5 compensate-color">关联访视</span>
          <span>{{ detailsData?.relationVisitName }}</span>
        </div>
        <div class="flex">
          <span class="mr-5 whitespace-nowrap compensate-color">费用明细</span>
          <div class="overflow-hidden w-full">
            <trial-table
              ref="receiptsDetailsTableRef"
              border
              title=""
              class="receiptsDetails-table"
              :request="getReceiptsDetailsList"
              :columns="receiptsDetailsColumns"
              :pagination="false"
              :cell-style="cellStyle"
            >
              <template #operate="scope">
                <div class="editBtnBlue" @click="operateFileClick(scope.row)"><span class="underline">{{ scope.row?.attachmentEditViewModels?.length || '' }}</span></div>
              </template>
            </trial-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 流程 -->
    <div class="receipts-mesg">
      <div class="font-semibold mb-3">流程</div>
      <!-- :active="2" -->
      <el-steps direction="vertical" space="120px">
        <el-step v-for="item in detailsData.runtimeLog" :key="item.detailCode">
          <template #title>
            <div class="flex justify-between font-normal compensate-color mb-3">
              <div class="flex-1">
                <div class="font-color-000">{{ item?.detailName }}</div>
                <div>{{ item?.remark }}</div>
                <div>{{ item?.comment }}</div>
              </div>
              <div class="auditor-all text-right">
                <div v-if="(!item?.aprStatus && item?.detailName !== '发起人') || (item?.aprStatus && item?.aprStatus !== 'Processing')">
                  <span v-if="item?.aprStatus">
                    {{ item?.requestDate }}
                  </span>
                  <div v-else-if="item?.approver" class="flex flex-col items-end">
                    <div class="auditor-box flex items-center justify-center mr-3">
                      <img src="@/assets/svg/auditor.svg" alt="">
                    </div>
                    <div>{{ item?.approver }}</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template #icon>
            <div class="icon-sty" :class="{'icon-back': (!item?.aprStatus && item?.detailName === '发起人') || item?.aprStatus }">
              <div class="icon-sty-in">
                <img v-if="item?.aprStatus === 'Processing'" src="@/assets/svg/pendingApprover.svg" alt="">
                <img v-else-if="item?.aprStatus === 'Approved'" src="@/assets/svg/passApprover.svg" alt="">
                <img v-else-if="item?.aprStatus === 'Rejected' && item?.aprBizStatus === 'Returned'" src="@/assets/svg/sendBackApprover.svg" alt="">
                <img v-else-if="item?.aprStatus === 'Rejected' && item?.aprBizStatus === 'Rejected'" src="@/assets/svg/rejectApprover.svg" alt="">
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>

    <el-dialog
      v-model="imgDialogShow"
      title="附件"
      width="60%"
    >
      <!-- 图片的显示 -->
      <el-image
        v-for="(item, index) in srcList"
        :key="index"
        class="img-size mr-3"
        hide-on-click-modal
        lazy
        :src="item"
        :preview-src-list="srcList"
        fit="cover"
        :initial-index="initialIndex"
        @click="imageInitialClick(index)"
      />
      <template #footer>
        <div class="flex justify-center">
          <el-button @click="imgDialogShow = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang='ts'>
import { getPeriodicCompensationApply } from '@/api/patientCompensate'
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'ReceiptsDetails', // 补偿/报销单据详情页
  setup() {
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      initialIndex: 0,
      imgDialogShow: false,
      detailsData: {},
      receiptsDetailsColumns: [
        // { type: 'selection' },
        { label: '', prop: 'serial', minWidth: 130 },
        { label: '科目', prop: 'subjectName', minWidth: 180 },
        { label: '金额(元)', prop: 'amount', minWidth: 160 },
        { label: '备注', prop: 'remark', minWidth: 300 },
        {
          label: '附件',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      receiptsDetailsTableRef: null,
      srcList: [],
      imageInitialClick: (index) => {
        state.initialIndex = index
      },
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.refresh()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      async getReceiptsDetailsList() {
        return {
          data: []
        }
      },
      operateFileClick: (row) => {
        state.imgDialogShow = true
        if (row?.attachmentEditViewModels) {
          state.srcList = row.attachmentEditViewModels.map(item => item.fileUrl)
        }
      },
      cellStyle: ({ row }) => {
        if (row?.serial === '合计') {
          return {
            'background-color': '#e6f6fe',
          }
        }
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        getPeriodicCompensationApply(RESEARCHCENTER_INFOS.researchContent?.patientId || '', { ApplyId: RESEARCHCENTER_INFOS.researchContent?.id || '' }).then((res) => {
          if (res?.users) {
            const usersArr = res.users.map(item => item?.userName)
            res.checkRemark = usersArr + ''
          }
          // 处理列表
          if (res?.applySubjects) {
            res.applySubjects.forEach((item, index) => {
              item.serial = index + 1
            })
            state.receiptsDetailsTableRef.tableData = res.applySubjects
          }
          state.receiptsDetailsTableRef.tableData.push({
            serial: '合计',
            amount: res?.compensationAmount
          })
          state.detailsData = res
        })
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.compensate-color {
  color: #9c9898;
}
.receipts-mesg {
  padding: 20px;
  background: #fff;
  h3 {
    margin: 0;
  }
  h4 {
    margin: 0;
  }
  .receipts-msg-centent {
    padding: 0 20px;
  }
  .receipts-box {
    padding: 0 20px;
  }
  // 流程
  .icon-sty {
    width: 24px;
    height: 24px;
    background: #d7d7d7;
    border-radius: 50%;
    position: relative;
    .icon-sty-in {
      width: 16px;
      height: 16px;
      position: absolute;
      bottom: -2px;
      right: -2px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .icon-back {
    background: #5995FF;
  }
}
.img-size {
  width: 150px;
  height: 150px;
  border-radius: 5px;
}
.auditor-all {
  width: 30%;
  margin-left: 20px;
  .auditor-box {
    width: 40px;
    height: 40px;
    border-radius: 5px;
    background: #B5D0FF;
    img {
      width: 20px;
      height: 20px;
    }
  }
}
.receiptsDetails-table {
  :deep(.my-el-table),
  :deep(.head) {
    padding: 0 !important;
  }
}
</style>
