<template>
  <div v-if="false" class="PatientCompensate">
    <div class="head">
      <h3 v-html="studyName" />
    </div>
    <div class="compensate-stat mb-5">
      <div class="mb-5 font-semibold">
        受试者补偿/报销统计
      </div>
      <div class="flex compensate-data">
        <div class="w-1/4">
          <div class="mb-5">
            <span class="mr-5 compensate-color">已处理单据</span>
            <span class="font-semibold">33</span>
          </div>
          <div>
            <span class="mr-5 compensate-color">金额</span>
            <span class="font-semibold">100000</span>
          </div>
        </div>
        <div class="w-1/4">
          <div class="mb-5">
            <span class="mr-5 compensate-color">申请中单据</span>
            <span class="font-semibold">33</span>
          </div>
          <div>
            <span class="mr-5 compensate-color">金额</span>
            <span class="font-semibold">100000</span>
          </div>
        </div>
        <div class="w-1/4">
          <div class="mb-5">
            <span class="mr-5 compensate-color">待确认单据</span>
            <span class="font-semibold">33</span>
          </div>
          <div>
            <span class="mr-5 compensate-color">金额</span>
            <span class="font-semibold">100000</span>
          </div>
        </div>
        <div class="w-1/4">
          <div class="mb-5">
            <span class="mr-5 compensate-color">已退回单据</span>
            <span class="font-semibold">33</span>
          </div>
          <div>
            <span class="mr-5 compensate-color">金额</span>
            <span class="font-semibold">100000</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 图 -->
    <!-- 表格 -->
    <div class="compensate-search mb-3 flex items-center justify-between">
      <div class="flex">
        <div :class="{'compensate-btn-back' : btnListShowColor}" class="mr-5 compensate-btn" @click="btnListShow(0)">中心列表</div>
        <div :class="{'compensate-btn-back' : !btnListShowColor}" class="compensate-btn" @click="btnListShow(1)">受试者列表</div>
      </div>
      <el-button type="primary" @click="exportList">导出</el-button>
    </div>
    <trial-table
      ref="patientCompensateTableRef"
      title=""
      :request="getpatientCompensateList"
      :columns="patientCompensateColumns"
      :pagination="false"
      :cell-style="cellStyle"
      :header-cell-style="headerCellStyle"
    />
  </div>
</template>

<script lang='ts'>
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'PatientCompensate', // 补偿/报销统计
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    // const { studyId, studyName } = store.state.studyItem
    const state = reactive({
      studyName: store.state.studyItem.studyName,
      patientCompensateTableRef: null,
      listType: 0,
      btnListShowColor: true, // 列表按钮的颜色
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      patientCompensateColumns: [
        // { type: 'selection' },
        { label: '中心', prop: 'siteName', minWidth: 180, fixed: 'left' },
        { label: '受试者数量', prop: 'patientNum', minWidth: 120 },
        { label: '已处理单据', prop: 'patientNum1', minWidth: 120 },
        { label: '金额(元)', prop: 'patientNum2', minWidth: 120 },
        { label: '申请中单据', prop: 'patientNum3', minWidth: 120 },
        { label: '金额(元)', prop: 'patientNum4', minWidth: 120 },
        { label: '待确认单据', prop: 'patientNum5', minWidth: 120 },
        { label: '金额(元)', prop: 'patientNum6', minWidth: 120 },
        { label: '已退回单据', prop: 'patientNum7', minWidth: 120 },
        { label: '金额(元)', prop: 'name', minWidth: 120 },
      ],
      // 请求函数
      async getpatientCompensateList(params) {
        params.listType = state.listType
        console.log(params)
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const list = [
            {
              id: 1,
              name: '超级管理员',
              patientNo: '1'
            },
            {
              id: 2,
              name: '测试人员',
              patientNo: '2'
            },
            {
              id: 3,
              name: '数据管理员',
              patientNo: '3'
            }
          ]
          const { data } = await new Promise((rs) => {
            setTimeout(() => {
              rs({
                code: 200,
                data: {
                  list: [
                    {
                      id: 1,
                      name: '超级管理员',
                      patientNo: '1'
                    },
                    {
                      id: 2,
                      name: '测试人员',
                      patientNo: '2'
                    },
                    {
                      id: 3,
                      name: '数据管理员',
                      patientNo: '3'
                    }
                  ],
                  total: list.length,
                },
              })
            }, 1000)
          })
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data.list,
            total: +data.total,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 修改颜色
      cellStyle: ({ columnIndex }) => {
        if (columnIndex === 2 || columnIndex === 3) {
          return { background: '#eaf4dc' }
        } else if (columnIndex === 4 || columnIndex === 5 || columnIndex === 6 || columnIndex === 7) {
          return { background: '#f4ebdf' }
        }
      },
      // 修改颜色
      headerCellStyle: ({ columnIndex }) => {
        if (columnIndex === 2 || columnIndex === 3) {
          return { background: '#eaf4dc !important' }
        } else if (columnIndex === 4 || columnIndex === 5 || columnIndex === 6 || columnIndex === 7) {
          return { background: '#f4ebdf !important' }
        }
      },
      btnListShow: (index) => {
        state.listType = index
        if (index === 0) {
          state.btnListShowColor = true
          state.patientCompensateColumns[1].label = '受试者数量'
          state.patientCompensateColumns[1].prop = 'patientNum'
        } else if (index === 1) {
          state.btnListShowColor = false
          state.patientCompensateColumns[1].label = '受试者编号'
          state.patientCompensateColumns[1].prop = 'patientNo'
        }
        state.patientCompensateRefresh()
      },
      // 导出ex
      exportList: () => {
        if (state.listType === 0) {
          console.log(0)
        } else if (state.listType === 1) {
          console.log(1)
        }
      },
      // 刷新
      patientCompensateRefresh: () => {
        state.patientCompensateTableRef.refresh()
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
      // 
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.head {
  width: 100%;
  background: #fff;
  padding: 20px 20px;
  box-sizing: border-box;
  margin: 10px 0 20px 0;
  h3 {
    margin: 0;
  }
}
.compensate-color {
  color: #9c9898;
}
.compensate-stat {
  padding: 20px;
  background: #fff;
}
.compensate-search {
  padding: 10px;
  .compensate-btn {
    font-size: 14px;
    padding: 5px 15px;
    background: #fff;
    border-radius: 2px;
    cursor: pointer;
  }
  .compensate-btn-back {
    background: #304156;
    color: #fff;
  }
}
.compensate-data {
  padding: 0 15px;
}
</style>
