<template>
  <trial-table
    ref="noteTableRef"
    title="短信查看列表"
    :request="getList"
    :columns="columns"
    :search="searchConfig"
    :pagination="paginationConfig"
    @selectionChange="handleSelectionChange"
  />
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
// import { getNoteList } from "@/api/note";

export default defineComponent({
  name: 'NoteList',

  setup() {
    const state = reactive({
      noteTableRef: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { type: 'selection' },
        { label: 'ID', prop: 'id', width: 80 },
        { label: '系统', prop: 'system', width: 100 },
        { label: '发送状态', prop: 'status', width: 100 },
        { label: '发送时间', prop: 'timeDeparture', width: 180 },
        { label: '签名', prop: 'nickName', width: 100 },
        { label: '短信类型', prop: 'messageType', width: 100 },
        { label: '接收人', prop: 'mobile', width: 120 },
        { label: '短信内容', prop: 'messageContent', width: 200 },
        { label: '失败详情', prop: 'failureDetails', minWidth: 180 },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            label: '系统',
            name: 'system',
            defaultValue: '',
          },
          {
            type: 'text',
            label: '签名',
            name: 'nickName',
            defaultValue: '',
          },
          {
            type: 'text',
            label: '短信类型',
            name: 'messageType',
            defaultValue: '',
          },
          {
            type: 'text',
            label: '接收人',
            name: 'mobile',
            defaultValue: '',
          },
          /*
          {
            label: "发送状态",
            name: "status",
            type: "select",
            defaultValue: null,
            options: [
              {
                name: "发送成功",
                value: 1,
              },
              {
                name: "发送失败",
                value: 0,
              },
            ],
          },
          */
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20, 50],
        style: { textAlign: 'left' },
      },
      selectedItems: [],

      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      // 请求短信列表函数
      async getList(params) {
        // params包含pageNum: 1, pageSize: 5,分页和搜索等字段。
        try {
          // const { data } = await getNoteList(params)
          const list = [
            {
              id: 1,
              system: 'EDC',
              nickName: 'zhangsan',
              status: '发送成功',
              timeDeparture: '2021-06-06 16:16:16',
              messageContent: '短信内容短信内容短信内容',
              messageType: '找回密码',
              mobile: '15224646611',
              failureDetails: '无',
            },
            {
              id: 2,
              system: 'EDC',
              status: '发送成功',
              nickName: 'admin',
              timeDeparture: '2021-06-06 16:16:16',
              messageContent: '短信内容短信内容短信内容',
              messageType: '找回密码',
              mobile: '15224646612',
              failureDetails: 'xxxx',
            },
            {
              id: 3,
              system: 'EDC',
              status: '发送成功',
              nickName: 'test1',
              timeDeparture: '2021-06-06 16:16:16',
              messageContent: '短信内容短信内容短信内容',
              messageType: '找回密码',
              mobile: '15224646613',
              failureDetails: '无',
            },
            {
              id: 4,
              system: 'EDC',
              status: '发送成功',
              nickName: 'lisi4',
              timeDeparture: '2021-06-06 16:16:16',
              messageContent: '短信内容短信内容短信内容',
              messageType: '找回密码',
              mobile: '15224646614',
              failureDetails: '无',
            },
            {
              id: 5,
              system: 'EDC',
              status: '发送成功',
              nickName: 'wanger',
              timeDeparture: '2021-06-06 16:16:16',
              messageContent: '短信内容短信内容短信内容',
              messageType: '找回密码',
              mobile: '15224646615',
              failureDetails: '无',
            },
            {
              id: 6,
              system: 'EDC',
              status: '发送成功',
              nickName: 'ybule',
              timeDeparture: '2021-06-06 16:16:16',
              messageContent: '短信内容短信内容短信内容',
              messageType: '找回密码',
              mobile: '15224646616',
              failureDetails: '无',
            },
          ]
          const { data } = await new Promise((rs) => {
            setTimeout(() => {
              rs({
                code: 200,
                data: {
                  list,
                  total: list.length,
                },
              })
            }, 500)
          })

          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: data.list,
            total: +data.total,
          }
        } catch (e) {
          console.log(e)
        }
      }
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>
