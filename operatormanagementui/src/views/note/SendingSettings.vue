<template>
  <div class="sending-settings">
    <div class="settings-btns">
      <el-button type="info" @click="backSendingSettings">取消</el-button>
      <el-button type="primary" :loading="loading" @click="saveSendingSettings">保存</el-button>
    </div>

    <div class="sending-module">
      <div class="sending-title">
        <h3>短信发送设置</h3>
        <p>(邮箱丶手机号可配置多个, 用英文逗号隔开)</p>
      </div>

      <el-form
        ref="sendingSettingsFormRef"
        :model="sendingSettingsForm"
        :rules="rules"
        label-width="180px"
      >
        <el-form-item label="后台服务地址：" prop="backgroundServiceAddress">
          <el-input
            v-model.trim="sendingSettingsForm.backgroundServiceAddress"
            maxlength="1000"
            placeholder="请输入后台服务地址"
            :disabled="disabledFalg"
          />
        </el-form-item>

        <el-form-item label="后台服务API地址：" prop="backgroundServiceAPI">
          <el-input
            v-model.trim="sendingSettingsForm.backgroundServiceAPI"
            maxlength="1000"
            placeholder="请输入后台服务API地址"
            :disabled="disabledFalg"
          />
        </el-form-item>

        <el-form-item label="测试短信模式：" prop="testingSmsMode">
          <el-radio-group
            v-model="sendingSettingsForm.testingSmsMode"
            :disabled="disabledFalg"
          >
            <el-radio label="1">不发送短信</el-radio>
            <el-radio label="2">发送给测试人员</el-radio>
            <el-radio label="3">正常发送短信(有风险，客户会收到短信)</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="测试人员手机号：" prop="mobile">
          <el-input
            v-model.trim="sendingSettingsForm.mobile"
            maxlength="1000"
            placeholder="请输入手机号"
            :disabled="disabledFalg"
          />
        </el-form-item>

        <el-form-item label="失败时发送邮件：" prop="mailbox">
          <el-input
            v-model.trim="sendingSettingsForm.mailbox"
            maxlength="1000"
            placeholder="请输入邮件"
            :disabled="disabledFalg"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from 'vue'
// import { useRouter } from 'vue-router';
import { uniqueArr } from '@/utils/index'
// import { setSendingSettings, getSendingSettings } from "@/api/note";

export default defineComponent({
  name: 'SendingSettings',

  setup() {
    // const router = useRouter();
    const MOBILE_REQUIRED = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else if (
        value.indexOf(',') === -1 &&
        !value.match(/^1[3-9][0-9]\d{8}$/)
      ) {
        callback(new Error('请输入正确的手机号'))
      } else if (value.indexOf(',') !== -1) {
        value.split(',').forEach((item) => {
          if (!item.match(/^1[3-9][0-9]\d{8}$/)) {
            callback(new Error('请输入正确的手机号'))
          }
        })
        if (uniqueArr(value.split(',')).length !== value.split(',').length) {
          callback(new Error('请不要重复输入手机号'))
        }
      } else {
        callback()
      }
    }
    const MAILBOX_REQUIRED = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入邮件'))
      } else if (
        value.indexOf(',') === -1 &&
        !value.match(/^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/)
      ) {
        callback(new Error('请输入正确的邮件'))
      } else if (value.indexOf(',') !== -1) {
        value.split(',').forEach((item) => {
          if (
            !item.match(
              /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
            )
          ) {
            callback(new Error('请输入正确的邮件'))
          }
        })
        if (uniqueArr(value.split(',')).length !== value.split(',').length) {
          callback(new Error('请不要重复输入邮件'))
        }
      } else {
        callback()
      }
    }
    const state = reactive({
      sendingSettingsFormRef: null,
      sendingSettingsForm: {
        backgroundServiceAddress: '',
        backgroundServiceAPI: '',
        testingSmsMode: '',
        mobile: '',
        mailbox: '',
      },
      rules: {
        backgroundServiceAddress: [
          { required: true, message: '请输入后台服务地址', trigger: 'blur' },
          {
            pattern: /(http|https):\/\/([\w.]+\/?)\S*/,
            message: '请输入正确服务地址',
            trigger: 'blur',
          },
        ],
        backgroundServiceAPI: [
          { required: true, message: '请输入后台服务API地址', trigger: 'blur' },
          {
            pattern: /(http|https):\/\/([\w.]+\/?)\S*/,
            message: '请输入正确服务API地址',
            trigger: 'blur',
          },
        ],
        testingSmsMode: [
          { required: true, message: '请选择模式', trigger: 'blur' },
        ],
        mobile: [
          { required: true, validator: MOBILE_REQUIRED, trigger: 'blur' },
        ],
        mailbox: [
          { required: true, validator: MAILBOX_REQUIRED, trigger: 'blur' },
        ],
      },
      // 根据环境决定->是否禁用
      disabledFalg: process.env.NODE_ENV !== 'development',
      loading: false,
      // 获取短信发送设置的数据
      getSendingSettingData: () => {
        // getSendingSettings().then((res)=>{
        //   state.sendingSettingsForm = res;
        // }).catch((e) => {console.log(e)})
      },
      // 保存时-->
      saveSendingSettings: () => {
        if (state.disabledFalg) return
        state.sendingSettingsFormRef.validate((valid) => {
          if (valid) {
            // const { backgroundServiceAddress, backgroundServiceAPI, testingSmsMode, mobile, mailbox, } = state.sendingSettingsForm
            // console.log(state.sendingSettingsForm)
            state.loading = true
            ElMessage.success('保存成功')
            state.loading = false
            state.getSendingSettingData() // 再次获取短信发送设置的数据
          }
        })
      },
      // 取消
      backSendingSettings: () => {
        if (state.disabledFalg) return
        state.getSendingSettingData()
        // router.go(0);
      }
    })

    onBeforeMount(() => {
      state.getSendingSettingData()
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.sending-settings {
  .sending-module {
    width: 100%;
    height: 500px;
    overflow: auto;
    background: #fff;
    margin: 10px 0 0 0;
    .sending-title {
      display: flex;
      align-items: center;
      margin: 0 0 30px 0;
      h3 {
        margin: 0 30px;
      }
      p {
        color: rgba(109, 115, 115, 1);
      }
    }
    .el-input {
      max-width: 500px;
    }
    &:deep(.el-form-item.is-required:not(.is-no-asterisk)
        > .el-form-item__label:before) {
      content: '';
    }
  }
}
</style>
