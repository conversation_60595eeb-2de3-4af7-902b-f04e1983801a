<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
        :showbtnfalg-excel="showbtnfalgExcel"
        :request-export-excel="getResearchCenterExport"
      >
        <template #operate="scope">
          <span
            v-permission="['manage.visitOutside.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <Details :request="onLoad" />
    </div>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import Details from '@/views/visitOutside/VisitOutsideDetails.vue'
import {
  getStudyPatinetOverVisit, // 获取某课题下访视任务超窗
  getStudyPatinetOverVisitExport, // 导出某课题下访视任务超窗
} from '@/api/visitOutside'

export default defineComponent({
  name: 'VisitOutside', // 访视任务超窗
  components: {
    Details,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      // manage.visitOutside.exportExcel
      showbtnfalgExcel: store.state.studyItem.permissions.includes('manage.visitOutside.exportExcel'),
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        // doctorInfo.hospital + doctorInfo.department
        { label: '中心名称', prop: 'siteName', width: 250 },
        { label: '病例编号', prop: 'patientNo', width: 100 },
        { label: '性别', prop: 'gender', width: 80 },
        { label: '入组日期', prop: 'inGroupDate', width: 180 },
        { label: '组别', prop: 'group', width: 100 },
        { label: '访视名称', prop: 'visitName', width: 180 },
        { label: '缺失天数', prop: 'overVisitDay', width: 100 },
        { label: '未完成任务', prop: 'unFinishQuestStr', width: 300 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            label: '病例编号',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 导出Excel方法
      getResearchCenterExport: async (params) => {
        const myParams = { ...params }
        if (myParams.siteId == null) {
          myParams.siteId = ''
        }
        if (myParams.patNumber == null) {
          myParams.patNumber = ''
        }
        getStudyPatinetOverVisitExport(studyId, myParams)
        window.open(
          `${window.location.origin
          }/api/Operator/DoctorPatient/${studyId}/PatientOverVisit/Export?siteId=${myParams.siteId
          }&patNumber=${myParams.patNumber}`
        )
      },
      // 获取列表数据方法
      getResearchCenterList: async (params) => {
        try {
          const rest = await getStudyPatinetOverVisit(studyId, params)
          rest.items.forEach((item, idx) => {
            item.id = idx + 1
            item.visitName = 'V' + item.versionNumber + '-' + item.visitName
            item.overVisitDay = item.overVisitDay + '天'
          })
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      // 编辑某项中心
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      onLoad: async () => {
        //
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    h3 {
      margin: 0;
    }
  }
}
</style>
