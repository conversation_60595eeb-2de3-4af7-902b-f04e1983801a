<template>
  <div class="subjectsListDetails">
    <div>
      <div class="infos-body-title">
        <div>
          <h4>
            {{ studyName }}
          </h4>
        </div>
        <div>
          <el-button @click="backRefresh">返回</el-button>
          <el-button
            v-show="resCreenStatusMap.indexOf(DetailsList?.patientStatus) > -1"
            v-permission="['manage.subjectsList.reFilter']"
            type="warning"
            @click="rescreen"
            >重新筛选</el-button
          >
        </div>
      </div>
      <div class="infos-body">
        <div class="head">
          <div class="head-boxTop">
            <div>
              <span>中心：</span>
              <span>{{ DetailsList?.siteName }}</span>
            </div>
            <div>
              <!-- <span>编号：</span>
              <span>{{ DetailsList?.patientNo }}</span> -->
              <span>姓名：</span>
              <span>{{ DetailsList?.realName }}</span>
            </div>
            <div>
              <span>性别：</span>
              <span>{{ DetailsList?.sex }}</span>
            </div>
          </div>
          <div class="head-boxTop">
            <div>
              <span>受试者编号：</span>
              <span>{{ DetailsList?.patientNo }}</span>
            </div>
            <div>
              <span>状态：</span>
              <span>{{ DetailsList?.groupStatus }}</span>
            </div>
            <div>
              <span>CRF版本：</span>
              <span>{{ DetailsList?.versionNumber }}</span>
            </div>
          </div>
          <div class="head-boxCenter">
            <div>
              <span>入组日期：</span>
              <span>{{ DetailsList?.inGroupDate }}</span>
            </div>
            <div>
              <span>组别：</span>
              <span>{{ DetailsList?.group }}</span>
            </div>
            <div></div>
          </div>
          <div class="head-boxCenter">
            <div class="!w-full">
              <span>标签：</span>
              <span v-if="DetailsList?.patientTags?.length">{{
                DetailsList.patientTags.join('，')
              }}</span>
              <span v-else>&nbsp;</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 信息区 -->
      <el-tabs
        v-model="activeName"
        class="demo-tabs"
        @tab-change="tabsHandleChange"
      >
        <el-tab-pane
          v-permission="['manage.subjectsList.eventInfo']"
          label="事件信息"
          name="eventInformation"
        >
        <div v-show="activeName === 'eventInformation'">
          <div class="centerflex-h justify-between">
            <h4>事件信息</h4>
            <el-button
              v-permission="['manage.eventInfo.exportPDF']"
              :loading="loading"
              type="primary"
              class="randomUpside-export"
              @click="exportRescreenInfo"
              >导出PDF</el-button
            >
          </div>
          <div class="px-[20px]">
            <div v-for="(item, index) in DetailsList.eventsInfos" :key="index">
              <div class="mb-[30px]">
                <div class="flex">
                  <div>时间：</div>
                  <div class="flex-1">{{ item?.operateTime }}</div>
                </div>
                <div class="flex mt-[10px]">
                  <div>事件：</div>
                  <div class="flex-1">{{ item?.eventName }}</div>
                </div>
                <div class="flex mt-[10px]">
                  <div>操作人：</div>
                  <div class="flex-1">{{ item?.operateUser }}</div>
                </div>

                <!-- 分层因素：-->
                <div v-if="item?.factors?.length" class="flex mt-[10px]">
                  <div>分层因素：</div>
                  <div
                    v-for="(clItem, clIndex) in item?.factors"
                    :key="clIndex"
                    class="flex-1"
                  >
                    <div
                      class="htmls-m-0 text-[#7F7F7F]"
                      v-html="clItem?.fieldLabel"
                    ></div>
                    <div class="mb-[10px]">{{ clItem?.fieldData }}</div>
                  </div>
                </div>
                <div v-if="item?.randomDate" class="flex mt-[10px]">
                  <div v-if="item?.eventName === '随机'">随机日期：</div>
                  <div v-else>入组日期：</div>
                  <div class="flex-1">{{ item.randomDate }}</div>
                </div>
                <div v-if="item?.randomNumber" class="flex mt-[10px]">
                  <div>随机数：</div>
                  <div class="flex-1">{{ item.randomNumber }}</div>
                </div>
                <div v-if="item?.armName" class="flex mt-[10px]">
                  <div>组别：</div>
                  <div class="flex-1">{{ item.armName }}</div>
                </div>
                <div v-if="item?.remark" class="flex mt-[10px]">
                  <div>说明：</div>
                  <div class="flex-1" v-html="item.remark"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </el-tab-pane>
        <!-- <el-tab-pane
          v-if="DetailsList?.rescreeningInfo"
          label="重筛信息"
          name="rescreeningInfo"
        >
          <div class="random">
            <el-button
              :loading="loading"
              type="primary"
              size
              class="randomUpside-export"
              @click="exportRescreenInfo"
              >导出PDF</el-button
            >
            <div v-if="DetailsList?.rescreeningInfo" class="randomBottom">
              <h3>重筛信息</h3>
              <div class="randomBottom-content-box" style="width: 50%">
                <div>
                  <span>操作时间：</span>
                  <span>{{ DetailsList?.rescreeningInfo?.operationTime }}</span>
                </div>
                <div>
                  <span>操作人：</span>
                  <span>{{ DetailsList?.rescreeningInfo?.operatorName }}</span>
                </div>
              </div>
              <div class="rescreenRemark">
                <span>说明：</span>
                <span>{{ DetailsList?.rescreeningInfo?.remark }}</span>
              </div>
            </div>
          </div>
        </el-tab-pane> -->
        <el-tab-pane
          v-show="
            DetailsList?.ieQuestCrfInfo?.ieQuestCrfItems &&
            DetailsList?.ieQuestCrfInfo?.ieQuestCrfItems.length
          "
          v-permission="['manage.subjectsList.initialScreeningInfo']"
          label="初筛信息"
          name="filtrateMessage"
        >
          <div v-show="activeName === 'filtrateMessage'" class="filtrate-message">
            <div>
              <div v-if="DetailsList?.ieQuestCrfInfo?.lastPatientStatus">
                <h3 class="content-box">
                  审核结果: {{ DetailsList?.ieQuestCrfInfo?.lastPatientStatus }}
                </h3>
              </div>
              <div
                v-for="(item, idx) in DetailsList?.ieQuestCrfInfo
                  ?.ieQuestCrfItems"
                :key="idx"
                class="content-box"
              >
                <div class="top">
                  <!-- <span>({{ idx + 1 }})</span> -->
                  <span v-html="item?.fieldLabel" />
                </div>
                <div class="bottom">
                  <div>
                    <div
                      v-if="
                        (item?.crfFieldType === 1 ||
                          item?.crfFieldControl === 2) &&
                        item?.fieldValue
                      "
                      class="el-image-box"
                    >
                      <div
                        v-for="(iteImg, index) in item?.fieldValue.split(',')"
                        :key="index"
                      >
                        <el-image
                          v-if="iteImg"
                          style="width: 100px; height: 100px"
                          class="el-image-more"
                          :src="iteImg"
                          :preview-src-list="previewUrllist"
                          @click="elimgclick(item, index)"
                        />
                      </div>
                    </div>
                    <div v-else>
                      <span v-if="item?.fieldDisplayValue">
                        {{ item?.fieldDisplayValue }}
                        {{ item.dctQuestUnit ? item.dctQuestUnit : '' }}
                      </span>
                    </div>
                    <div
                      v-if="item.children && item.children.length"
                      class="bottomChildern"
                    >
                      <div
                        v-for="(itemChildern, index) in item.children"
                        :key="index"
                      >
                        <div class="bottom-childern-box">
                          <div
                            class="bottomChildern-son"
                            v-html="itemChildern?.fieldLabel"
                          />
                          <div
                            v-if="
                              !(item.crfFieldControl === 2) && !item.fieldValue
                            "
                            class="el-image-box"
                          >
                            <div
                              v-for="(iteImg, inx) in item?.fieldValue.split(
                                ','
                              )"
                              :key="inx"
                            >
                              <el-image
                                v-if="iteImg"
                                style="width: 100px; height: 100px"
                                class="el-image-more"
                                :src="iteImg"
                                :preview-src-list="item?.fieldValue.split(',')"
                              />
                            </div>
                          </div>
                          <div v-else class="bottomChildern-son">
                            <span v-if="itemChildern.fieldDisplayValue">
                              {{ itemChildern.fieldDisplayValue }}
                              {{
                                itemChildern.dctQuestUnit
                                  ? itemChildern.dctQuestUnit
                                  : ''
                              }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="DetailsList?.ieQuestCrfInfo?.remaks.length">
                <h3 class="content-box">轨迹</h3>
                <div>
                  <div class="content-box center-box-bottom">
                    <div
                      v-for="(item, index) in DetailsList?.ieQuestCrfInfo
                        .remaks"
                      :key="index"
                    >
                      <div class="track-details">
                        <span class="time">{{ item.lastUpdate }}</span>
                        <!-- 6待审核 7审核失败 8审核信息不足 其余已入组 -->
                        <div v-if="item.status === 6">
                          <span>受试者提交</span>
                        </div>
                        <div v-else-if="item.status === 7">
                          <span
                            >研究人员：{{
                              item.doctorName ? item.doctorName : ''
                            }}，审核结果：筛选失败，</span
                          >
                          <span v-if="item.remark"
                            >原因：{{ item.remark }}</span
                          >
                        </div>
                        <div v-else-if="item.status === 8">
                          <span
                            >研究人员：{{
                              item.doctorName ? item.doctorName : ''
                            }}，审核结果：需完善，</span
                          >
                          <span v-if="item.remark"
                            >原因：{{ item.remark }}</span
                          >
                        </div>
                        <div v-else>
                          <span
                            >研究人员：{{
                              item.doctorName ? item.doctorName : '无'
                            }}，审核结果：通过</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <el-button
                v-permission="['manage.initialScreeningInfo.exportPDF']"
                :loading="loading"
                type="primary"
                size
                class="filtrate-message-export"
                @click="exportScreen"
                >导出PDF</el-button
              >
            </div>
          </div>
        </el-tab-pane>
        <!-- <el-tab-pane
          v-if="DetailsList?.randomQuestCrfInfo?.randomNum"
          label="随机信息"
          name="randomMessage"
        >
          <div class="random">
            <el-button
              :loading="loading"
              type="primary"
              size
              class="randomUpside-export"
              @click="exportRandom"
              >导出PDF</el-button
            >
            <div
              v-if="DetailsList?.randomQuestCrfInfo?.randomQuestCrfItems.length"
              class="randomTop"
            >
              <div class="randomBottom">
                <h3>分层因素</h3>
              </div>
              <div
                v-for="(item, index) in DetailsList?.randomQuestCrfInfo
                  ?.randomQuestCrfItems"
                :key="index"
                class="randomTop-content-box"
              >
                <div class="top">
                  <span v-html="item?.fieldLabel" />
                </div>
                <div class="bottom">
                  <span v-html="item?.fieldDisplayValue" />
                  <div class="bottomChildern">
                    <div
                      v-for="(itemChildern, idx) in item.children"
                      :key="idx"
                    >
                      <span v-html="itemChildern?.fieldLabel" />
                      <span v-html="itemChildern?.fieldDisplayValue" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="DetailsList?.randomQuestCrfInfo" class="randomBottom">
              <h3>随机信息</h3>
              <div class="randomBottom-content-box">
                <div>
                  <span>随机日期：</span>
                  <span>{{ DetailsList?.randomQuestCrfInfo?.randomDate }}</span>
                </div>
                <div>
                  <span>随机人员：</span>
                  <span>{{ DetailsList?.randomQuestCrfInfo?.randomName }}</span>
                </div>
                <div>
                  <span>随机数：</span>
                  <span>{{ DetailsList?.randomQuestCrfInfo?.randomNum }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane> -->
        <el-tab-pane
          v-show="treeData && treeData.length > 0 && crfShow"
          v-permission="['manage.subjectsList.questionnaireInfo']"
          label="问卷信息"
          name="CRFQuestionnaireMessage"
        >
          <div v-show="activeName === 'CRFQuestionnaireMessage'" class="randomCRF">
            <el-button
              v-permission="['manage.questionnaireInfo.exportPDF']"
              :loading="loading"
              type="primary"
              class="randomUpside-export-CRF"
              @click="exportCRFRandom"
              >导出PDF</el-button
            >
            <div class="CRFQuestionnaireMessage-box">
              <!-- 左边tree -->
              <div class="CRFQuestionnaireMessage-left">
                <!-- highlight-current -->
                <el-tree
                  ref="SubjectsListDetailsTreeRef"
                  :data="treeData"
                  :props="defaultProps"
                  show-checkbox
                  node-key="id"
                  :current-node-key="currentNodeKey"
                  highlight-current
                  @check="check"
                  @node-click="handleNodeClick"
                >
                  <!-- :render-content="renderContent" -->
                  <template #default="{ node, data }">
                    <div v-if="data.treeParentNode" class="custom-tree-node">
                      <div class="custom-tree-node-title-box">
                        <span class="custom-tree-node-title">{{
                          node.label
                        }}</span>
                        <span
                          v-if="data.beginDate || data.endDate"
                          class="custom-tree-node-date"
                          >{{ data.beginDate }} ~ {{ data.endDate }}</span
                        >
                      </div>
                    </div>
                    <!--  @click="handleNodeClick(data)" -->
                    <div v-else class="custom-tree-node">
                      <div class="custom-tree-node-title-box">
                        <span class="custom-tree-node-title">{{
                          node.label
                        }}</span>
                      </div>
                      <div class="custom-tree-node-right">
                        <span
                          v-if="data?.questSigntrueStatus === 3"
                          class="custom-tree-node-right-doctorWhite !bg-[#7275FA] !text-white"
                          >已签署</span
                        >
                        <span
                          v-if="data?.reviewStatus === 1"
                          class="custom-tree-node-right-doctorWhite !bg-[#3D6D17] !text-white"
                          >已审阅</span
                        >
                        <span
                          v-if="data?.questTemplateTypeTxt"
                          class="custom-tree-node-right-doctorWhite"
                          :class="{
                            'custom-tree-node-right-state-questTemplateType-color':
                              data.questTemplateType === 2 ||
                              data?.questTemplateTypeTxt === '研究人员',
                          }"
                          >{{ data.questTemplateTypeTxt }}</span
                        >
                        <span
                          v-if="!data.isSpecialQuest && data?.finishStatusText"
                          class="custom-tree-node-right-state"
                          :class="{
                            'custom-tree-node-right-state-unfinished-color':
                              data.finishStatus === 1,
                          }"
                          >{{ data.finishStatusText }}</span
                        >
                        <span
                          v-if="
                            data.reviewStatus &&
                            data.dataReview &&
                            data.finishStatus == 2 &&
                            data.dctQuestPatientTemplateType !== 4
                          "
                          class="custom-tree-node-right-state-review ml-[8px]"
                          :class="{
                            'custom-tree-node-right-state-unfinished-color-review':
                              data.reviewStatus != 1,
                          }"
                          >{{ data.reviewStatusText }}</span
                        >
                      </div>
                    </div>
                  </template>
                </el-tree>
              </div>
              <!-- 右边详情 -->
              <div class="CRFQuestionnaireMessage-right">
                <div
                  v-loading="loadingData"
                  element-loading-text="Loading..."
                  class="w-full"
                >
                  <div class="px-[20px] box-border">
                    <div class="flex justify-between items-center mt-6">
                      <div
                        v-if="CrfvisitName || CrfquestName"
                        class="CRFQuestionnaireMessage-title"
                      >
                        {{ CrfvisitName || '' }} {{ CrfquestName ? '——' : '' }}
                        {{ CrfquestName || '' }}
                      </div>
                      <div class="flex">
                        <div>
                          <span
                            v-show="CRFDetailsAllMsg?.crfQuestItemAndFieldLogs?.length && CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.toBeOpenUnplannedWindow && !CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.unplannedWindowId"
                            v-permission="['manage.questionnaireInfo.openPlanChange']"
                            class="editBtnBlue underline mr-[20px]"
                            @click="openPlanChangeWindow"
                          >打开计划外修改窗口</span>
                          <span
                            v-show="CRFDetailsAllMsg?.crfQuestItemAndFieldLogs?.length && CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.toBeOpenUnplannedWindow && CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.unplannedWindowId"
                            v-permission="['manage.questionnaireInfo.viewPlanChange']"
                            class="text-yellow-400 underline mr-[20px]"
                            @click="openPlanChangeWindow"
                          >已打开计划外修改窗口</span>
                        </div>
                        <div
                          v-if="CRFRightMsgShow === 1 && !dataObj?.isOffline"
                          class="editBtnBlue"
                        >
                          <span v-if="trackShow" @click="trackShowClick(1)"
                            >显示稽查轨迹</span
                          >
                          <span v-else @click="trackShowClick(2)"
                            >隐藏稽查轨迹</span
                          >
                        </div>
                      </div>
                    </div>
                    <el-divider
                      class="!mb-[0]"
                      v-if="CrfvisitName || CrfquestName"
                    />
                  </div>
                  <div
                    v-if="CRFRightMsgShow === 1 && dataObj?.isOffline"
                    class="CRFQuestionnaireMessage-right-scoll"
                  >
                    <!-- 转线下的走这里了 -->
                    <div v-if="CRFDetailsAllMsg?.crfQuestItemAndFieldLogs">
                      <div
                        v-for="(item, index) in CRFDetailsAllMsg
                          ?.crfQuestItemAndFieldLogs[0]?.records"
                        :key="index"
                        class="mb-[30px]"
                      >
                        <div class="flex items-center mb-[10px]">
                          <div>{{ item.createTime }}</div>
                          <div class="mx-[30px]">{{ item.userName }}</div>
                          <div>{{ item.patientQuestRecordStatus }}</div>
                        </div>
                        <div>备注：{{ item?.remark ? item.remark : '无' }}</div>
                      </div>
                    </div>
                  </div>
                  <!-- 数组和对象 -->
                  <!-- 添加一层循环 -->
                  <div
                    v-else-if="CRFRightMsgShow === 1 || CRFRightMsgShow === 4"
                    ref="CRFQuestionnaireMessageRef"
                    class="CRFQuestionnaireMessage-right-scoll"
                  >
                    <div
                      v-for="(
                        it, idn
                      ) in CRFDetailsAllMsg?.crfQuestItemAndFieldLogs"
                      :key="idn"
                    >
                      <div
                        v-if="CrfdctQuestPatientTemplateType === 12"
                        class="mb-5 font-semibold"
                      >
                        <div
                          v-if="it?.dctQuestPatientTemplateType !== 12"
                          class="mb-3"
                        >
                          <span>{{ it?.taskName }}</span>
                        </div>
                        <span class="mr-3">推送日期：{{ it?.startTime }}</span>
                        <span
                          v-if="it?.dctQuestPatientTemplateType !== 12"
                          class="mr-3"
                          >截至日期：{{ it?.endTime }}</span
                        >
                      </div>
                      <div
                        v-else-if="CrfdctQuestPatientTemplateType === 11"
                        class="mb-5 font-semibold"
                      >
                        <span class="mr-3"
                          >{{ it?.displayTime }}{{ it?.modifier }}</span
                        >
                        <span v-if="!it?.isUserAdd">{{ it?.drugName }}</span>
                      </div>
                      <div v-if="it?.crfQuestItems?.length ">
                        <div v-if="CRFRightMsgShow === 1 && trackShow">
                          <div
                            v-if="
                              !it?.questFieldDataLogs?.length &&
                              (CrfdctQuestPatientTemplateType === 11 ||
                                CrfdctQuestPatientTemplateType === 12)
                            "
                            style="color: #666"
                          >
                            未填写
                          </div>
                          <div v-else>
                            <div
                              v-for="(item, idx) in it.crfQuestItems"
                              :key="idx"
                              class="content-box"
                            >
                              <div v-if="!item.isDeleted">
                                <div class="top">
                                  <span
                                    v-if="
                                      item.fieldDataValidationStatus !== 2 &&
                                      item.fieldDataValidationStatus !== 3
                                    "
                                    v-html="item?.fieldLabel"
                                  />
                                  <span
                                    class="centerflex"
                                    v-if="
                                      item.fieldDataValidationStatus !== 2 &&
                                      item.fieldDataValidationStatus !== 3
                                    "
                                  >
                                    <span
                                      v-show="
                                        item.crfFieldType !== 3 &&
                                        (it?.dctQuestPatientTemplateType ===
                                          11 ||
                                          it?.dctQuestPatientTemplateType ===
                                            4 ||
                                          it?.dctQuestPatientTemplateType ===
                                            12 ||
                                          it?.dctQuestPatientTemplateType ===
                                            2 ||
                                          it?.dctQuestPatientTemplateType === 3
                                          ||
                                          it?.dctQuestPatientTemplateType === 1 ||
                                            it?.dctQuestPatientTemplateType ===
                                            9
                                          )
                                      "
                                      v-permission="['manage.questionnaireInfo.modifyQuestionnaireData']"
                                      class="text-nowrap mr-[10px] text-[#D9001B] underline cursor-pointer"
                                      @click="editData(item, it)"
                                      >修改数据</span
                                    >
                                    <span
                                      class="clarifyBtn"
                                      @click="showClarify(item)"
                                      v-if="
                                        item?.dataClarification &&
                                        item?.crfFieldType != 3 &&
                                        [1,2,3,9,11,12].includes(
                                          it?.dctQuestPatientTemplateType
                                        )
                                      "
                                      ><p>
                                        数据澄清{{
                                          item?.clarifyCount == 0
                                            ? ''
                                            : '（关闭' +
                                              item?.closeClarifyCount +
                                              '/' +
                                              item?.clarifyCount +
                                              '）'
                                        }}
                                      </p>
                                    </span>
                                  </span>
                                </div>
                                <div
                                  class="bottom CRFQuestionnaireMessage-right-box-color"
                                >
                                  <div>
                                    <div
                                      v-if="
                                        item.crfFieldControl === 2 &&
                                        item.fieldValue
                                      "
                                      class="el-image-box"
                                    >
                                      <div
                                        v-for="(
                                          iteImg, index
                                        ) in item?.fieldValue.split(',')"
                                        :key="index"
                                      >
                                        <el-image
                                          v-if="iteImg"
                                          style="width: 100px; height: 100px"
                                          class="el-image-more"
                                          :src="iteImg"
                                          :preview-src-list="previewUrllist"
                                          @click="elimgclick(item, index)"
                                        />
                                      </div>
                                    </div>
                                    <div v-else>
                                      <span v-if="item?.fieldDisplayValue">
                                        {{ item?.fieldDisplayValue }}
                                        {{
                                          item.dctQuestUnit
                                            ? item.dctQuestUnit
                                            : ''
                                        }}
                                      </span>
                                    </div>
                                    <div
                                      v-if="
                                        item.children &&
                                        item.children.length &&
                                        item.fieldDataValidationStatus !== 2 &&
                                        item.fieldDataValidationStatus !== 3
                                      "
                                      class="bottomChildern"
                                    >
                                      <div
                                        v-for="(
                                          itemChildern, indexn
                                        ) in item.children"
                                        :key="indexn"
                                      >
                                        <div
                                          v-if="!itemChildern.isDeleted"
                                          class="bottom-childern-box"
                                        >
                                          <div
                                            class="bottomChildern-son"
                                            v-html="itemChildern?.fieldLabel"
                                          />
                                          <div
                                            v-if="
                                              itemChildern.crfFieldControl ===
                                                2 && itemChildern.fieldValue
                                            "
                                            class="el-image-box"
                                          >
                                            <div
                                              v-for="(
                                                iteImgChildren, inx
                                              ) in itemChildern?.fieldValue.split(
                                                ','
                                              )"
                                              :key="inx"
                                            >
                                              <el-image
                                                v-if="iteImgChildren"
                                                style="
                                                  width: 100px;
                                                  height: 100px;
                                                "
                                                class="el-image-more"
                                                :src="iteImgChildren"
                                                :preview-src-list="
                                                  itemChildern?.fieldValue.split(
                                                    ','
                                                  )
                                                "
                                              />
                                            </div>
                                          </div>
                                          <div
                                            v-else
                                            class="flex-1 bottomChildern-son"
                                          >
                                            <span
                                              v-if="
                                                itemChildern.fieldDisplayValue
                                              "
                                            >
                                              {{
                                                itemChildern.fieldDisplayValue
                                              }}
                                              {{
                                                itemChildern.dctQuestUnit
                                                  ? itemChildern.dctQuestUnit
                                                  : ''
                                              }}
                                            </span>
                                          </div>
                                          <div
                                            class="clarifyBtnOuter flex justify-end"
                                          >
                                            <span class="centerflex-w">
                                              <span
                                                v-show="
                                                  itemChildern?.crfFieldType !==
                                                    3 &&
                                                  (it?.dctQuestPatientTemplateType ===
                                                    11 ||
                                                    it?.dctQuestPatientTemplateType ===
                                                      4 ||
                                                    it?.dctQuestPatientTemplateType ===
                                                      12 ||
                                                    it?.dctQuestPatientTemplateType ===
                                                      2 ||
                                                    it?.dctQuestPatientTemplateType ===
                                                      3 ||
                                                      it?.dctQuestPatientTemplateType ===
                                                      1 ||
                                                      it?.dctQuestPatientTemplateType ===
                                                      9
                                                    )
                                                "
                                                v-permission="['manage.questionnaireInfo.modifyQuestionnaireData']"
                                                class="mr-[10px] text-[#D9001B] underline cursor-pointer"
                                                @click="
                                                  editData(itemChildern, it)
                                                "
                                                >修改数据</span
                                              >
                                              <span
                                                class="clarifyBtn"
                                                @click="
                                                  showClarify(itemChildern)
                                                "
                                                v-if="
                                                  itemChildern?.dataClarification &&
                                                  itemChildern?.crfFieldType !=
                                                    3 &&
                                                    [1,2,3,9,11,12].includes(
                                                      it?.dctQuestPatientTemplateType
                                                    )
                                                "
                                                ><span>
                                                  数据澄清{{
                                                    itemChildern?.clarifyCount ==
                                                    0
                                                      ? ''
                                                      : '（关闭' +
                                                        itemChildern?.closeClarifyCount +
                                                        '/' +
                                                        itemChildern?.clarifyCount +
                                                        '）'
                                                  }}
                                                </span>
                                              </span>
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- 轨迹 -->
                        <div v-else-if="CRFRightMsgShow === 1 && !trackShow">
                          <div v-if="!CRFDetailsAllMsg?.LogDataType">
                            <div
                              v-if="
                                !it?.questFieldDataLogs?.length &&
                                (CrfdctQuestPatientTemplateType === 11 ||
                                  CrfdctQuestPatientTemplateType === 12)
                              "
                              style="color: #666"
                            >
                              未填写
                            </div>
                            <div v-else>
                              <div
                                v-for="(item, index) in it.crfQuestItems"
                                :key="index"
                              >
                                <div v-if="item.crfFieldType !== 3">
                                  <div style="display: flex">
                                    <div
                                      class="fontColor my-3"
                                      v-html="item.fieldLabel"
                                      style="width: calc(100% - 200px)"
                                    ></div>
                                    <span
                                      class="clarifyShowTrack"
                                      @click="showClarify(item)"
                                      v-if="
                                        item?.dataClarification &&
                                        item?.crfFieldType != 3 &&
                                        [1,2,3,9,11,12].includes(
                                          it?.dctQuestPatientTemplateType
                                        )
                                      "
                                      ><p>
                                        数据澄清{{
                                          item?.clarifyCount == 0
                                            ? ''
                                            : '（关闭' +
                                              item?.closeClarifyCount +
                                              '/' +
                                              item?.clarifyCount +
                                              '）'
                                        }}
                                      </p>
                                    </span>
                                  </div>
                                  <div
                                    v-if="
                                      item.crfFieldControl === 2 &&
                                      item.fieldValue
                                    "
                                    class="el-image-box"
                                  >
                                    <div
                                      v-for="(
                                        iteImg, idx
                                      ) in item?.fieldValue.split(',')"
                                      :key="idx"
                                    >
                                      <el-image
                                        v-if="iteImg"
                                        style="width: 100px; height: 100px"
                                        class="el-image-more"
                                        :src="iteImg"
                                        :preview-src-list="previewUrllist"
                                        @click="elimgclick(item, idx)"
                                      />
                                    </div>
                                  </div>
                                  <div v-else class="mb-3">
                                    {{ item.fieldDisplayValue }}
                                    {{
                                      item.dctQuestUnit ? item.dctQuestUnit : ''
                                    }}
                                  </div>
                                  <el-table
                                    :data="item.dataLogs"
                                    border
                                    style="width: 100%"
                                  >
                                    <el-table-column
                                      v-if="dataIsSpecialQuest"
                                      label="序号"
                                      width="100"
                                    >
                                      <template #default="scope">
                                        <span>
                                          {{ idn }}
                                        </span>
                                      </template>
                                    </el-table-column>
                                    <el-table-column
                                      v-else
                                      prop="rowNum"
                                      label="序号"
                                      width="60"
                                    />
                                    <el-table-column
                                      prop="oprtTime"
                                      label="操作时间"
                                      width="180"
                                    />
                                    <el-table-column
                                      prop="oprtUserType"
                                      label="操作人"
                                      width="90"
                                    >
                                      <template #default="scope">
                                        <span>
                                          {{ scope.row.oprtUserType
                                          }}{{
                                            scope.row?.participantRelation
                                              ? `（${scope.row.participantRelation}）`
                                              : ''
                                          }}
                                        </span>
                                      </template>
                                    </el-table-column>
                                    <el-table-column
                                      prop="oprtTypeStr"
                                      label="操作类型"
                                      width="90"
                                    />
                                    <el-table-column
                                      prop="oldValue"
                                      label="变更前值"
                                      width="180"
                                    />
                                    <el-table-column
                                      prop="newValue"
                                      label="变更后值"
                                      width="180"
                                    />
                                    <el-table-column
                                      prop="reason"
                                      label="变更原因"
                                      width="150"
                                    />
                                    <el-table-column
                                      prop="remark"
                                      label="备注"
                                    />
                                  </el-table>
                                </div>
                                <div v-if="item.crfFieldType === 3">
                                  <div
                                    v-if="item.showType"
                                    class="fontColor my-3 flex"
                                  >
                                    <span v-html="item.fieldLabel"></span>
                                  </div>
                                  <div
                                    v-for="(ite, idx) in item.children"
                                    :key="idx"
                                  >
                                    <div style="display: flex">
                                      <div
                                        style="width: calc(100% - 200px)"
                                        class="my-3 flex items-center"
                                        :class="{
                                          'line-through':
                                            item?.fieldDataValidationStatus ===
                                            2,
                                        }"
                                      >
                                        <span
                                          class="fontColor mr-3"
                                          v-html="ite.fieldLabel"
                                        />
                                        <span>{{ ite.fieldDisplayValue }}</span>
                                      </div>
                                      <span
                                        class="clarifyShowTrack"
                                        @click="showClarify(ite)"
                                        v-if="
                                          ite?.dataClarification &&
                                          ite?.crfFieldType != 3 &&
                                          [1,2,3,9,11,12].includes(
                                            it?.dctQuestPatientTemplateType
                                          )
                                        "
                                        ><span>
                                          数据澄清{{
                                            ite?.clarifyCount == 0
                                              ? ''
                                              : '（关闭' +
                                                ite?.closeClarifyCount +
                                                '/' +
                                                ite?.clarifyCount +
                                                '）'
                                          }}
                                        </span>
                                      </span>
                                    </div>
                                    <el-table
                                      :data="ite.dataLogs"
                                      border
                                      style="width: 100%"
                                      show-overflow-tooltip
                                    >
                                      <el-table-column label="序号" width="60">
                                        <template #default="scope">
                                          <span>
                                            {{ scope.row.rowNum }}
                                          </span>
                                        </template>
                                      </el-table-column>
                                      <el-table-column
                                        prop="oprtTime"
                                        label="操作时间"
                                        width="180"
                                      />
                                      <el-table-column
                                        prop="oprtUserType"
                                        label="操作人"
                                        width="90"
                                      >
                                        <template #default="scope">
                                          <span>
                                            {{ scope.row.oprtUserType
                                            }}{{
                                              scope.row?.participantRelation
                                                ? `（${scope.row.participantRelation}）`
                                                : ''
                                            }}
                                          </span>
                                        </template>
                                      </el-table-column>
                                      <el-table-column
                                        prop="oprtTypeStr"
                                        label="操作类型"
                                        width="90"
                                      />
                                      <el-table-column
                                        prop="oldValue"
                                        label="变更前值"
                                        width="180"
                                      />
                                      <el-table-column
                                        prop="newValue"
                                        label="变更后值"
                                        width="180"
                                      />
                                      <el-table-column
                                        prop="reason"
                                        label="变更原因"
                                        width="150"
                                      />
                                      <el-table-column
                                        prop="remark"
                                        label="备注"
                                      />
                                    </el-table>
                                  </div>
                                  <el-divider
                                    v-if="it.crfQuestItems.length !== index + 1"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- 事件 -->
                        <!-- <div v-if="DetailsList.questEventRecords?.length">
                          <div class="font-bold my-[15px]">事件</div>
                          <div
                            v-for="(
                              item, index
                            ) in DetailsList.questEventRecords"
                            :key="index"
                          >
                            <div class="mb-[30px]">
                              <div class="flex">
                                <div>时间：</div>
                                <div class="flex-1">
                                  {{ item?.operateTime }}
                                </div>
                              </div>
                              <div class="flex mt-[10px]">
                                <div>事件：</div>
                                <div class="flex-1">
                                  {{ item?.eventTypeText }}
                                </div>
                              </div>
                              <div class="flex mt-[10px]">
                                <div>操作人：</div>
                                <img
                                  v-if="item?.signtrueFileUrl"
                                  :src="item?.signtrueFileUrl"
                                  class="w-[100px]"
                                  alt=""
                                />
                                <div
                                  v-else-if="item?.operateUser"
                                  class="flex-1"
                                >
                                  {{ item?.operateUser }}
                                </div>
                              </div>
                              <div
                                v-if="item?.eventType === 2 && item?.remark"
                                class="flex mt-[10px]"
                              >
                                <div>声明：</div>
                                <div class="flex-1" v-html="item.remark"></div>
                              </div>
                              <div
                                v-else-if="
                                  (item?.eventType === 3 ||
                                    item?.eventType === 6) &&
                                  item?.remark
                                "
                                class="flex mt-[10px]"
                              >
                                <div>原因：</div>
                                <div class="flex-1" v-html="item.remark"></div>
                              </div>
                              <div
                                v-else-if="item?.remark"
                                class="flex mt-[10px]"
                              >
                                <div>备注：</div>
                                <div class="flex-1" v-html="item.remark"></div>
                              </div>
                            </div>
                          </div>
                        </div>-->
                        <!-- 更新时间 去除 ，更新为事件轨迹 -->
                        <!-- <div
                          v-if="
                            it?.questFieldDataLogs?.length ||
                            (CrfdctQuestPatientTemplateType !== 11 &&
                              CrfdctQuestPatientTemplateType !== 12)
                          "
                          class="CRFQuestionnaireMessage-right-box-track"
                        >
                          <h3 v-if="!loadingData" class="mb-5">更新时间</h3>
                          <div
                            v-for="item in it?.questFieldDataLogs"
                            :key="item.id"
                            class="mb-2"
                          >
                            <span class="mr-7">{{ item.oprtTime }}</span>
                            <span v-if="item.oprtUserType === '研究人员'"
                              >{{ item.oprtUserType }}:{{
                                item.oprtUser
                              }}提交</span
                            >
                            <span v-else-if="item.oprtUserType === '参与者'"
                              >{{ item.oprtUserType }}:{{
                                item.patNumber
                              }}提交</span
                            >
                            <span v-else>{{ item.oprtUserType }}提交</span>
                          </div>
                        </div> -->
                        <!-- 事件轨迹 -->
                        <div
                          v-if="it?.questEventRecords?.length > 0"
                          class="CRFQuestionnaireMessage-right-box-track"
                        >
                          <h3 style="margin-bottom: 0">事件</h3>
                          <div
                            v-for="record in it?.questEventRecords"
                            :key="record.id"
                            class="recordBox"
                          >
                            <span class="recordLine"
                              >时间：{{ record.oprtTime }}</span
                            >
                            <span class="recordLine"
                              >事件：{{ record.eventTypeText }}</span
                            >
                            <span
                              style="
                                display: inline-flex;
                                margin-bottom: 0.5rem;
                              "
                              class="recordLine"
                              v-if="[2, 5].indexOf(record.eventType) > -1"
                            >
                              <div>操作人：</div>
                              <!-- <img
                                v-if="
                                  !imgSignatureShow && record.eventType != 5
                                "
                                style="width: 100px; height: 100px"
                                class="el-image-more"
                                src="@/assets/img/signature.png"
                              /> -->
                              <el-image
                                v-if="record?.signtrueFileUrl"
                                style="width: 100px; height: 100px"
                                class="el-image-more"
                                :src="record.signtrueFileUrl"
                                :preview-src-list="[record.signtrueFileUrl]"
                              />
                            </span>
                            <span v-else class="recordLine"
                              >操作人：{{ record.oprtUserTypeName }}</span
                            >
                            <span
                              v-if="record.eventType === 2"
                              class="recordLine"
                              >声明：{{ record.remark }}</span
                            >
                            <span
                              v-else-if="
                                [3, 4, 6, 9, 10, 11].indexOf(record.eventType) > -1
                              "
                              class="recordLine"
                              >原因：{{ record.remark }}</span
                            >
                            <span
                              v-else-if="[5].indexOf(record.eventType) > -1"
                              class="recordLine"
                              >备注：{{
                                record.remark == null || record.remark == ''
                                  ? '无'
                                  : record.remark
                              }}</span
                            >
                            <!-- 计划外时：打开计划外修改窗口的操作时间 -->
                            <span
                              v-if="record.eventType === 10"
                              class="recordLine"
                              >附件：{{ record?.fileUrl || '无' }}</span
                            >
                            <span
                              v-if="record.eventType === 10"
                              class="recordLine"
                              >修改人：{{ record?.updateUserName }}</span
                            >
                            <span
                              v-if="record.eventType === 10"
                              class="recordLine"
                              >反馈：{{ record?.feedBack }}</span
                            >
                            <span
                              v-if="record.eventType === 10"
                              class="recordLine"
                              >反馈时间：{{ record?.feedBackTime || record?.feedBackTimeStr }}</span
                            >
                            <el-divider />
                          </div>
                          <!-- </div> -->
                        </div>
                      </div>
                      <el-divider
                        v-if="
                          CRFDetailsAllMsg?.crfQuestItemAndFieldLogs?.length !==
                          idn + 1
                        "
                      />
                    </div>
                  </div>
                  <div
                    v-else-if="
                      CRFRightMsgShow === 2 &&
                      visitPlanObj &&
                      visitPlanObjShow === 0
                    "
                    class="p-[20px] box-border"
                  >
                    <div class="mb-5">
                      计划随访日期：{{ visitPlanObj.expectedDate }}
                    </div>
                    <div class="mb-5">
                      窗口期：{{ visitPlanObj.beginDate
                      }}{{
                        visitPlanObj.beginDate && visitPlanObj.endDate
                          ? '~'
                          : ''
                      }}{{ visitPlanObj.endDate }}
                    </div>
                    <!-- <div class="mb-5">
                      {{ visitPlanObj.actualMeetingDate ? `实际随访日期：${visitPlanObj.actualMeetingDate}` : `计划随访日期：${visitPlanObj.meetingDateTime}`}}
                    </div> -->
                    <div class="mb-5">
                      实际随访日期：{{ visitPlanObj.actualMeetingDate || '' }}
                    </div>
                  </div>
                  <div v-else class="CRFQuestionnaireMessage-right-no-data">
                    暂无数据
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <trial-dialog
      v-model="popupshow"
      :my-dialog-body-style="myDialogBodyStyle"
      title="问卷导出"
    >
      <template #DialogBody>
        <el-form>
          <el-form-item label="事件：" label-width="120px">
            <el-radio-group
              v-model="exportPdfData.lastUpdate"
              @change="lastUpdateChange"
            >
              <el-radio :label="true">需要</el-radio>
              <el-radio :label="false">不需要</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label-width="120px">
            <el-checkbox
              v-model="exportPdfData.patientNameShow"
              :disabled="!exportPdfData.lastUpdate"
              class="checkbox-margin"
              :label="0"
              >操作人姓名</el-checkbox
            >
          </el-form-item>
          <el-form-item
            label="稽查轨迹："
            label-width="120px"
            @change="hasTarckChange"
          >
            <el-radio-group v-model="exportPdfData.hasTarck">
              <el-radio :label="true">需要</el-radio>
              <el-radio :label="false">不需要</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label-width="120px">
            <el-checkbox
              v-model="exportPdfData.hasParticipantRelation"
              :disabled="!exportPdfData.hasTarck"
              class="checkbox-margin"
              >与参与者关系（如有）</el-checkbox
            >
          </el-form-item> -->
          <el-form-item label="导出人：" label-width="120px">
            <el-radio-group v-model="exportPdfData.hasOperator">
              <el-radio :label="true">需要</el-radio>
              <el-radio :label="false">不需要</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="导出时间：" label-width="120px">
            <el-radio-group v-model="exportPdfData.hasExportTime">
              <el-radio :label="true">需要</el-radio>
              <el-radio :label="false">不需要</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="mt-16" style="text-align: center">
          <el-button :loading="loading" @click="cancelExportPdf"
            >取消</el-button
          >
          <el-button :loading="loading" type="primary" @click="exportPdf">
            确定
          </el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog
      v-model="dialogVisible"
      :my-dialog-body-style="{ width: '40%' }"
      class="rescreenDialog"
    >
      <template #DialogBody>
        <div class="mb-5">
          <span class="mr-5 ft-18-px font-semibold">重新筛选</span>
          <span class="rescreen-tips"
            >将标记该病例状态为"重新筛选"，可引导用户打开受试者端重新开始流程</span
          >
        </div>
        <el-form
          ref="rescreenRef"
          :model="rescreenFormData"
          :rules="rules"
          label-position="top"
        >
          <el-form-item label="说明" prop="remark">
            <el-input
              v-model="rescreenFormData.remark"
              type="textarea"
              maxlength="200"
              rows="8"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="centerflex">
          <el-button :loading="loading" @click="dialogVisible = false"
            >取 消</el-button
          >
          <el-button type="primary" :loading="loading" @click="submitRescreen"
            >确 定</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <trial-dialog
      v-model="clarifyDialogVisible"
      :my-dialog-body-style="{
        width: '60%',
        height: '80vh',
        'margin-top': '50px',
        'margin-bottom': 'unset',
      }"
      class="rescreenDialog"
    >
      <template #DialogBody>
        <div class="mb-5" style="height: inherit">
          <div class="clarifyTitle">
            <span class="mr-5 ft-18-px font-semibold">数据澄清</span>
            <el-button size="default" @click="closeClarifyDialog"
              >关闭</el-button
            >
          </div>
          <div class="clarifyBody">
            <div class="clarifyList">
              <div
                @click="selectClarify(item, index)"
                v-for="(item, index) in clarifyList"
                class="clarifyItem"
                :style="{
                  backgroundColor:
                    tmpContentIndex == index ? '#F3F3F3' : '#ffffff',
                }"
              >
                <div class="firstLevel">
                  <span>{{ '澄清：' + item.id }}</span>
                  <span
                    :style="{
                      color:
                        item.clarifyTypeStatusStr == '未关闭'
                          ? '#f59a23'
                          : '#666666',
                    }"
                    >{{ item.clarifyTypeStatusStr }}</span
                  >
                </div>
                <div class="secLevel">
                  <span>{{ item.oprtTime }}</span>
                </div>
                <div class="thirdLevel">
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="item.question"
                    placement="top-start"
                  >
                    {{ item.question }}
                  </el-tooltip>
                </div>
              </div>
              <div v-if="clarifyList?.length === 0" class="noData">
                <span>暂无数据</span>
              </div>
            </div>
            <el-divider direction="vertical" class="verDivider" />
            <div class="clarifyContent">
              <div>
                <div class="fieldLineLeft" v-if="tmpContent != null">
                  <div style="display: flex">
                    <span>题目</span>
                    <span
                      class="showHtml"
                      v-html="tmpContent?.fieldLabel"
                    ></span>
                  </div>
                  <div style="display: flex">
                    <span>结果</span>
                    <span v-if="tmpContent?.crfFieldControl != 2">{{
                      tmpContent?.fieldValue
                    }}</span>
                    <div
                      style="width: 90%; margin-left: 20px"
                      class="el-image-box"
                      v-if="
                        tmpContent?.crfFieldControl === 2 &&
                        tmpContent?.fieldValue
                      "
                    >
                      <div
                        v-for="(
                          iteImgChildren, inx
                        ) in tmpContent?.fieldValue.split(',')"
                        :key="inx"
                      >
                        <el-image
                          v-if="iteImgChildren"
                          style="width: 100px; height: 100px"
                          class="el-image-more"
                          :src="iteImgChildren"
                          :preview-src-list="previewUrllist"
                          @click="elimgclick(tmpContent, inx)"
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <span>填写人</span>
                    <span>{{ tmpContent?.oprtUserTypeName }}</span>
                  </div>
                  <el-divider
                    v-if="tmpContent?.clarifyReply.length > 0"
                    class="divider"
                  />
                </div>
                <div
                  class="fieldLineLeft"
                  v-for="(question, index) in tmpContent?.clarifyReply"
                >
                  <div>
                    <span>{{
                      question?.contentType == 1
                        ? '提问'
                        : question?.contentType == 2
                        ? '回复'
                        : '关闭澄清'
                    }}</span>
                    <span>{{ question?.content }}</span>
                  </div>
                  <div>
                    <span>操作人</span>
                    <span>{{ question?.oprtUserName }}</span>
                  </div>
                  <div>
                    <span>操作时间</span>
                    <span>{{ question?.oprtTime }}</span>
                  </div>
                  <el-divider
                    v-if="index != tmpContent?.clarifyReply.length - 1"
                    class="divider"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </trial-dialog>
    <!-- editDataFlag 修改数据-->
    <trial-dialog
      v-model="editDataFlag"
      :my-dialog-body-style="{ width: '46%' }"
      class="!z-[2000]"
    >
      <template #DialogBody>
        <div class="mb-[20px]">
          <span class="mr-5 ft-18-px font-semibold">修改数据</span>
        </div>
        <div class="flex mb-[15px] no-styles">
          <span class="w-[130px] text-right text-[#7F7F7F]">题目：</span>
          <span
            v-if="questionEidtObj?.fieldLabel"
            v-html="questionEidtObj.fieldLabel"
          ></span>
        </div>

        <div class="flex mb-[15px]">
          <span class="w-[130px] text-right text-[#7F7F7F]">结果-修改前：</span>
          <!-- <span>{{ questionEidtObj?.fieldDisplayValue }}</span> -->
          <div
            v-if="
              (questionEidtObj?.crfFieldType === 1 ||
                questionEidtObj?.crfFieldControl === 2) &&
              questionEidtObj?.fieldValue
            "
            class="flex-1 flex flex-wrap"
          >
            <div
              v-for="(e, i) in questionEidtObj?.fieldValue.split(',')"
              :key="i"
            >
              <img
                v-if="e"
                class="mr-[10ox] mb-[15px]"
                style="width: 70px; height: 70px"
                :src="e"
                alt=""
              />
            </div>
          </div>
          <div v-else class="font-color-333 flex-1 max-h-[220px] overflow-auto">
            {{ questionEidtObj?.fieldDisplayValue }}
          </div>
          <!-- 图片控件 -->
        </div>

        <div class="flex mb-[15px]">
          <span class="min-w-[130px] text-right text-[#7F7F7F]"
            >结果-修改后：</span
          >
          <Question ref="questionEidtRef" />
        </div>

        <div class="flex flex-1">
          <span class="w-[130px] text-right text-[#7F7F7F]">修改原因：</span>
          <el-input
            class="flex-1"
            type="textarea"
            v-model.trim="questionEidtObj.remarks"
            placeholder="请输入内容"
            maxlength="200"
            show-word-limit
          />
        </div>
        <div
          v-if="!questionEidtObj?.remarks"
          class="text-[12px] ml-[130px] text-[#D9001B]"
        >
          请输入
        </div>

        <div class="flex my-[15px]">
          <span class="w-[130px] text-right text-[#7F7F7F]"
            >附件 &nbsp;&nbsp;</span
          >
          <span class="text-[#7F7F7F]"
            >若是线下澄清，请上传证据，支持pdf格式，大小不超过10M</span
          >
        </div>
        <!-- patientId -->
        <MyUpload
          class="ml-[116px]"
          ref="editUploadRef"
          upload-file-falg
          customFlag
          :limit="1"
          :custom-information-form-data="customInformationFormData"
          :request-fun="fileFun"
          :deletefile="deletefile"
          :file-list="fileList"
          :file-size="10"
          :before-file-upload-type="['.pdf']"
        >
          <template #uploadBtn="row">
            <el-button type="primary" text class="el-button-primary-small"
              >上传</el-button
            >
          </template>
        </MyUpload>
      </template>
      <template #footer>
        <div class="centerflex mt-[40px]">
          <el-button @click="editDataFlag = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="hanleEditData"
            >确认</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <!-- 数据变动 -->
    <trial-dialog
      v-model="changeDataFlag"
      :my-dialog-body-style="{ width: '50%', overflow: 'auto' }"
    >
      <template #DialogBody>
        <div class="mb-[20px]">
          <span class="mr-5 ft-18-px font-semibold">数据变动</span>
        </div>
        <div
          v-for="(item, index) in questChangeDataObj.questCrfItemes.filter(
            (e) => e?.isDisplay
          )"
          :key="index"
        >
          <div class="flex mb-[15px] no-styles">
            <span class="w-[130px] text-right text-[#7F7F7F]">题目：</span>
            <span v-if="item?.fieldLabel" v-html="item.fieldLabel"></span>
          </div>

          <div class="flex mb-[15px]">
            <span class="w-[130px] text-right text-[#7F7F7F]">操作类型：</span>
            <span>{{ oprtTypeList[item?.oprtType] }}</span>
          </div>

          <div class="flex mb-[15px]">
            <span class="w-[130px] text-right text-[#7F7F7F]"
              >结果-修改前：</span
            >
            <div
              v-if="
                (item?.crfFieldType === 1 || item?.crfFieldControl === 2) &&
                item?.fielOriginalUrl
              "
              class="flex-1 flex flex-wrap"
            >
              <div v-for="(e, i) in item?.fielOriginalUrl.split(',')" :key="i">
                <img
                  v-if="e"
                  class="mr-[10px] mb-[15px]"
                  style="width: 70px; height: 70px"
                  :src="e"
                  alt=""
                />
              </div>
            </div>
            <div
              v-else
              class="font-color-333 flex-1 max-h-[220px] overflow-auto"
            >
              {{ item?.fieldOriginalValueStr }}
            </div>
          </div>

          <div class="flex mb-[15px]">
            <span class="w-[130px] text-right text-[#7F7F7F]"
              >结果-修改后：</span
            >
            <div
              v-if="
                (item?.crfFieldType === 1 || item?.crfFieldControl === 2) &&
                item?.fielUrl
              "
              class="flex-1 flex flex-wrap"
            >
              <div v-for="(e, i) in item?.fielUrl.split(',')" :key="i">
                <img
                  v-if="e"
                  class="mr-[10px] mb-[15px]"
                  style="width: 70px; height: 70px"
                  :src="e"
                  alt=""
                />
              </div>
            </div>
            <div
              v-else
              class="font-color-333 flex-1 max-h-[220px] overflow-auto"
            >
              {{ item?.fieldValueStr }}
            </div>
          </div>

          <div class="flex mb-[15px]">
            <span class="w-[130px] text-right text-[#7F7F7F]">修改原因：</span>
            <span class="flex-1">{{ item?.remarksStr }}</span>
          </div>

          <div
            v-if="
              index !==
              questChangeDataObj.questCrfItemes.filter((e) => e?.isDisplay)
                .length -
                1
            "
            class="w-full h-[1px] bg-[#E5E5E5]"
          />
        </div>
      </template>
      <template #footer>
        <div class="centerflex mt-[40px]">
          <el-button @click="changeDataFlag = false">取消</el-button>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSaveChangeData"
            >确认提交</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <!-- 打开计划外修改窗口 -->
    <OpenPlanChangeDialog
      v-model="openDialogVisible"
      ref="openFormRef"
      @confirm="handleConfirmOpen"
      @cancel="handleCancelOpen"
    />
    <!-- 已打开计划外修改窗口 -->
    <trial-dialog
      v-model="openInDialogVisible"
      :my-dialog-body-style="{ width: '40%' }"
      class="open-in-form-module !z-[2000]"
    >
      <template #DialogBody>
        <div class="font-bold mb-[20px]">打开计划外修改窗口</div>
        <el-form label-width="100px">
          <el-form-item label="创建时间">
            <div>{{ openInDialogObj?.oprtTimeStr }}</div>
          </el-form-item>
          <el-form-item label="创建人">
            <div>{{ openInDialogObj?.oprtUserName }}</div>
          </el-form-item>
          <el-form-item label="修改窗口" class="mt-[25px]">
            <div>{{ openInDialogObj?.windowStartTimeStr }} ~ {{ openInDialogObj?.windowEndTimeStr }}</div>
          </el-form-item>
          <!-- <el-form-item label="状态">
            <div>{{ openInDialogObj?.questWindowStatus }}</div>
          </el-form-item> -->
          <el-form-item label="原因">
            <div>{{ openInDialogObj?.openWindowReason }}</div>
          </el-form-item>
          <!-- <el-form-item label="关闭窗口原因">
            <div>{{ openInDialogObj?.closeWindowReason }}</div>
          </el-form-item> -->
          <el-form-item label="附件">
            <!-- css文字换行 -->
            <span
              v-if="openInDialogObj?.fileUrl"
              v-text="openInDialogObj.fileUrl" 
              class="whitespace-pre-wrap break-all"
            />
            <span v-else>无</span>
          </el-form-item>
          <el-form-item label="修改人">
            <div>{{ openInDialogObj?.updateUserName }}</div>
          </el-form-item>
          <el-form-item label="反馈">
            <div>{{ openInDialogObj?.feedBack }}</div>
          </el-form-item>
          <el-form-item label="反馈时间">
            <div>{{ openInDialogObj?.feedBackTimeStr }}</div>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="centerflex mt-[40px]">
          <el-button @click="openInDialogVisible = false">取消</el-button>
          <el-button
           v-permission="['manage.questionnaireInfo.closePlanChange']"
           type="danger"
           @click="openClosePlanChangeWindow"
          >关闭计划外修改窗口</el-button>
        </div>
      </template>
    </trial-dialog>
    <!-- 关闭计划外修改窗口 -->
    <ClosePlanChangeDialog
      v-model="closeDialogVisible"
      ref="closePlanChangeDialogRef"
      @confirm="handleCloseConfirm"
      @cancel="handleCloseCancel"
    />
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  inject,
  nextTick,
  ref,
} from 'vue'
import {
  getQuestDetailsMessage,
  getQuestDetailsMessagePdf,
  getQuestFileImage,
  getCrfVisitInfo,
  getCrfQuestInfo,
  postCrfQuestInfoExportPDF,
  patientRescreening,
  exportPatRescreeningInfo,
  // 修改数据
  putPatientQuestChangeData,
  // 数据变动
  putUpdatePatientQuestChangeData,
  // getExportEventInformationPDF,
} from '@/api/subjectsList'
import { getQuestClarifyListInfo } from '@/api/clarify'
import Question from '@/components/Question.vue'
import MyUpload from '@/components/Upload/index.vue'
import { deepClone, parseTime } from '@/utils'
import { postPublicDocumentFile } from '@/api/home'
import { ElMessage, ElLoading } from 'element-plus'
import { saveAs } from 'file-saver'
import OpenPlanChangeDialog from '@/views/subjectsList/OpenPlanChangeDialog.vue' // 引入新的弹窗组件
import ClosePlanChangeDialog from '@/views/planChange/ClosePlanChangeDialog.vue' // 引入关闭计划外修改窗口弹窗组件
import { getQuestUnPlannedWindowDetail2, getUnplannedSelectUsers } from '@/api/planChange'

// Define interfaces based on usage
interface DetailsList {
  patientStatus?: number;
  siteName?: string;
  patientNo?: string;
  realName?: string;
  sex?: string;
  groupStatus?: string;
  versionNumber?: string;
  inGroupDate?: string;
  group?: string;
  patientTags?: string[];
  eventsInfos?: {
    operateTime?: string;
    eventName?: string;
    operateUser?: string;
    factors?: { fieldLabel?: string; fieldData?: string }[];
    randomDate?: string;
    randomNumber?: string;
    armName?: string;
    remark?: string;
  }[];
  rescreeningInfo?: {
    operationTime?: string;
    operatorName?: string;
    remark?: string;
  };
  ieQuestCrfInfo?: {
    lastPatientStatus?: string;
    ieQuestCrfItems?: CrfQuestItem[];
    remaks?: { lastUpdate?: string; status?: number; doctorName?: string; remark?: string }[];
  };
  randomQuestCrfInfo?: any; // To be refined if needed
  questEventRecords?: any[]; // For the commented out section, keep as any for now
}

interface CrfQuestItem {
  fieldLabel?: string;
  crfFieldType?: number;
  crfFieldControl?: number;
  fieldValue?: string;
  fieldDisplayValue?: string;
  dctQuestUnit?: string;
  children?: CrfQuestItem[];
  isDeleted?: boolean;
  dataClarification?: any;
  clarifyCount?: number;
  closeClarifyCount?: number;
  showType?: boolean;
}

interface CrfQuestItemAndFieldLog {
  isOffline?: boolean;
  records?: { createTime?: string; userName?: string; patientQuestRecordStatus?: string; remark?: string }[];
  crfQuestItems?: CrfQuestItem[];
  taskName?: string;
  startTime?: string;
  endTime?: string;
  displayTime?: string;
  modifier?: string;
  isUserAdd?: boolean;
  drugName?: string;
  questEventRecords?: {
    oprtTime?: string;
    eventTypeText?: string;
    signtrueFileUrl?: string;
    oprtUserTypeName?: string;
    eventType?: number;
    remark?: string;
    id?: string;
  }[];
  visitName?: string;
  questName?: string;
  LogDataType?: any;
  dctQuestPatientTemplateType?: number;
}

interface RESEARCHCENTER_INFOS_TYPE {
  resetMethod: { handleReset: () => void };
  contentVisible: boolean;
  researchContent: { patientId: string };
}

interface ClarifyItem {
  id: number;
  clarifyTypeStatusStr: string;
  oprtTime: string;
  question: string;
  fieldLabel?: string;
  crfFieldControl?: number;
  fieldValue?: string;
  oprtUserTypeName?: string;
  clarifyReply: ClarifyReplyItem[];
}

interface ClarifyReplyItem {
  contentType: number;
  content: string;
  oprtUserName: string;
  oprtTime: string;
}

interface QuestionEditObj {
  fieldLabel?: string;
  crfFieldType?: number;
  crfFieldControl?: number;
  fieldValue?: string;
  fieldDisplayValue?: string;
  remarks?: string;
  remarksStr?: string;
  questId?: string;
  rowNum?: number;
  isRequired?: boolean;
}

interface QuestCrfItemForChange {
  isDisplay?: boolean;
  fieldLabel?: string;
  oprtType?: number;
  fielOriginalUrl?: string;
  fieldOriginalValueStr?: string;
  fielUrl?: string;
  fieldValueStr?: string;
  remarksStr?: string;
  crfFieldType?: number;
  crfFieldControl?: number;
  rowNum?: number;
}

interface QuestChangeDataObj {
  questCrfItemes: QuestCrfItemForChange[];
  rowNum?: number;
}

export default defineComponent({
  name: 'SubjectsListDetails', // 受试者列表详情页
  components: {
    Question,
    MyUpload,
    OpenPlanChangeDialog,
    ClosePlanChangeDialog,
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS') as RESEARCHCENTER_INFOS_TYPE
    const state = reactive({
      // 弹窗内obj
      openInDialogObj: {},
      //
      imgSignatureShow: false,
      oprtTypeList: [
        '未知',
        '新增',
        '修改',
        '删除',
        '迁移',
        '新增澄清',
        '回复澄清',
        '关闭澄清',
      ],
      tmpQuestId: '',
      tmpContentIndex: 0,
      tmpContent: null as ClarifyItem | null,
      clarifyList: [] as ClarifyItem[],
      clarifyQuestionList: [],
      clarifyDialogVisible: false,
      resCreenStatusMap: [7, 16],
      btnRescreenVisible: false,
      rescreenRef: null as FormInstance | null,
      rules: {
        remark: [{ required: true, message: '请输入', trigger: 'blur' }],
      },
      dialogVisible: false,
      rescreenFormData: {
        remark: '',
      },
      cause: '',
      activeName: 'eventInformation',
      DetailsList: {} as DetailsList,
      studyName: store.state.studyItem.studyName,
      previewUrllist: [] as string[],
      loading: false,
      defaultProps: {
        // 规定
        children: 'childrens',
        label: 'label',
        disabled: 'disabled',
      },
      SubjectsListDetailsTreeRef: null as any, // Refine later if needed
      // 数据
      treeData: [] as any[],
      popupshow: false, // 弹窗
      exportPdfData: {
        hasTarck: false, // 轨迹的
        patientNameShow: false, // 是否显示姓名的
        lastUpdate: true, // 最后更新时间的
        hasOperator: true,
        hasExportTime: true,
        hasParticipantRelation: false,
      }, // 导出pdf
      myDialogBodyStyle: {
        width: '30%',
        minHeight: '200px',
      }, // 修改弹窗的大小
      currentNodeKey: '',
      crfShow: false,
      crfCheckedData: [] as string[],
      crfCheckedType: [] as number[],
      aeCheckedRowNums: [] as number[],
      cmCheckedRowNums: [] as number[],
      CRFDetailsAllMsg: {} as { crfQuestItemAndFieldLogs?: CrfQuestItemAndFieldLog[] },
      CRFRightMsgShow: 0, // 右侧是否有值
      CrfvisitName: '', // 右侧的名字 -
      CrfquestName: '', // 右侧的名字
      loadingData: false,
      visitPlanObj: {} as { expectedDate?: string; beginDate?: string; endDate?: string; actualMeetingDate?: string },
      trackShow: true, // 是否显示轨迹
      SubjectsListDetailsRef: null as any, // 轨迹表格的ref
      dataIsSpecialQuest: false,
      visitPlanObjShow: 0, // 控制是否显示窗口期
      CrfdctQuestPatientTemplateType: -1,
      CRFQuestionnaireMessageRef: null as any, // Refine later if needed
      dataObj: {} as { rowNum?: number; isOffline?: boolean; questId?: string },
      // 数据变动原因展示的
      questChangeDataObj: {
        questCrfItemes: [],
      } as QuestChangeDataObj,
      // 保存数据变动
      handleSaveChangeData: () => {
        state.loading = true
        state.changeDataFlag = false
        state.questChangeDataObj.rowNum = state.dataObj.rowNum
        putUpdatePatientQuestChangeData(state.questChangeDataObj)
          .then(() => {
            const { data, node } = state.lastTimeObj
            state.handleNodeClick(data, node)
            state.loading = false
          })
          .catch(() => {
            state.loading = false
          })
      },
      // 保存修改数据
      hanleEditData: async () => {
        if (!state.questionEidtObj?.remarks) {
          return
        }
        // console.log(state.questionEidtRef?.item?.isRequired ,state.questionEidtRef?.item?.fieldValue);
        const res =
          state.questionEidtRef?.validateDateType &&
          state.questionEidtRef?.checkForm
            ? await state.questionEidtRef.validateDateType()
            : true
        if (res) {
          // console.log(state.questionEidtRef);
          // 必填
          // let isRequiredFlag = false
          // if (state.questionEidtRef?.item?.isRequired && !state.questionEidtRef?.item?.fieldValue) {
          //   isRequiredFlag = true
          // } else if (state.questionEidtRef?.item?.isRequired &&
          //   state.questionEidtRef?.item?.fieldValue &&
          //   state.questionEidtRef?.item?.crfFieldControl === 8) {
          //   const fieldValues = state.questionEidtRef.item.fieldValue.split(",")
          //   if (!fieldValues[0] || !fieldValues[1]) {
          //     isRequiredFlag = true
          //   }
          // }
          // if (isRequiredFlag) {
          //   ElMessage.warning('请填写完修改后的结果')
          //   return
          // }
          if (state.questionEidtRef?.item?.questId) {
            state.loading = true
            state.questChangeDataObj.questCrfItemes = []
            state.editDataFlag = false
            state.changeDataFlag = true
            const data = { ...state.questionEidtObj } as QuestionEditObj
            data.fieldValue = state.questionEidtRef.item.fieldValue
            data.fieldValueStr = state.questionEidtRef.item.fieldValueStr
            // console.log(data);
            data.remarks = state.questionEidtObj?.remarksStr
            data.remarksStr = state.questionEidtObj?.remarks
            // return
            data.rowNum = state.questionEidtObj?.rowNum || state.dataObj?.rowNum
            putPatientQuestChangeData(state.questionEidtRef.item?.questId, data)
              .then((res: any) => {
                state.questChangeDataObj = res
                // 直接提交 数据变动
                if (!res.questCrfItemes.filter((e: any) => e?.isDisplay).length) {
                  // state.handleSaveChangeData()
                  state.loading = false
                  state.changeDataFlag = false
                  return
                }
                state.loading = false
                state.changeDataFlag = true
              })
              .catch(() => {
                state.loading = false
              })
          }
        }
      },
      fileList: [] as any[],
      editUploadRef: null as any,
      // 上传文件
      customInformationFormData: {
        customInformationFormDataObj: {
          icfFilePath: '', // 文件声明路径
          icfFileName: '', // 文件名
          icfFileUrl: '', // 文件访问路径
        },
      },
      // 上传文件
      fileFun: (fileObj: any) => {
        const myFormDataObj = new FormData()
        const fileName = fileObj.file.name
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        postPublicDocumentFile(
          store.state.studyItem.studyId,
          14,
          3,
          myFormDataObj
        )
          .then((res: any) => {
            loading.close()
            ElMessage.success('上传成功')
            state.questionEidtObj.remarksStr = res?.fileUrl || res?.thumbnailUrl
            if (state.editUploadRef?.fileList?.length === 0) {
              state.editUploadRef.fileList.push({
                name: fileName,
                url: res?.fileUrl || res?.thumbnailUrl,
              })
            }
          })
          .catch(() => {
            if (state.editUploadRef?.fileList) {
              state.editUploadRef.fileList.length = 0
            }
            state.questionEidtObj.remarksStr = ''
            loading.close()
          })
      },
      // 删除文件
      deletefile: () => {
        if (state.editUploadRef?.fileList) {
          state.editUploadRef.fileList.length = 0
        }
        state.questionEidtObj.remarksStr = ''
      },
      //
      backRefresh: () => {
        if (RESEARCHCENTER_INFOS) {
          RESEARCHCENTER_INFOS.resetMethod.handleReset()
          RESEARCHCENTER_INFOS.contentVisible = false
        }
      },
      // 点击切换
      tabsHandleChange: (name: string) => {
        switch (name) {
          case 'filtrateMessage':
            break
          case 'randomMessage':
            break
          case 'CRFQuestionnaireMessage':
            // state.crfQuestionnaireMessage()
            break
          // default: '0' //
        }
      },
      elimgclick: (item: any, index: number) => {
        state.previewUrllist = []
        // console.log(item.fieldDisplayValue.split(',')[index])
        // const fileIdList = item.fieldDisplayValue.split(',')
        // fileIdList.forEach((item) => {
        //   console.log(item)
        //   getQuestFileImage(item).then((res: any) => {
        //     state.previewUrllist.push(res?.url)
        //   })
        // })
        const fileId = item.fieldDisplayValue.split(',')[index]
        getQuestFileImage(fileId).then((res: any) => {
          state.previewUrllist.push(res?.url)
        })
      },
      // 导出筛选信息PDF
      exportScreen: () => {
        const data = {
          questCrfType: 1,
        }
        getQuestDetailsMessagePdf(
          RESEARCHCENTER_INFOS.researchContent.patientId,
          data
        )
        window.open(
          `${window.location.origin}/api/Operator/DoctorPatient/${RESEARCHCENTER_INFOS.researchContent.patientId}/BGQuest/Export?questCrfType=1`
        )
      },
      // 导出事件信息pdf
      exportEventInformation: () => {
        // const data = {
        //   questCrfType: 2,
        // }
        // getExportEventInformationPDF(
        //   RESEARCHCENTER_INFOS.researchContent.patientId,
        //   data
        // ).then((res) => {
        //   saveAs(new Blob([res.data]), res.headers?.filedownloadname)
        //   state.popupshow = false
        //   state.loading = false
        // }).catch(() => {
        //   state.loading = false
        // })
      },
      // 导出随机信息PDF
      exportRandom: () => {
        const data = {
          questCrfType: 2,
        }
        getQuestDetailsMessagePdf(
          RESEARCHCENTER_INFOS.researchContent.patientId,
          data
        )
        window.open(
          `${window.location.origin}/api/Operator/DoctorPatient/${RESEARCHCENTER_INFOS.researchContent.patientId}/Quest/Export?questCrfType=2`
        )
      },
      // 导出重筛信息
      exportRescreenInfo: () => {
        state.loading = true
        exportPatRescreeningInfo(RESEARCHCENTER_INFOS.researchContent.patientId)
          .then(() => {
            state.loading = false
          })
          .catch(() => {
            state.loading = false
          })
        window.open(
          `${window.location.origin}/api/Operator/DoctorPatient/${RESEARCHCENTER_INFOS.researchContent.patientId}/Rescreening`
        )
      },
      // 重新筛选按钮
      rescreen: () => {
        state.dialogVisible = true
      },
      // 提交重筛
      submitRescreen: () => {
        if (state.rescreenRef) {
          state.rescreenRef.validate((valid) => {
            if (valid) {
              patientRescreening(RESEARCHCENTER_INFOS.researchContent.patientId, {
                Remark: state.rescreenFormData.remark,
              }).then((res: any) => {
                if (res) {
                  ElMessage.success('重新筛选成功')
                  RESEARCHCENTER_INFOS.resetMethod.handleReset()
                  RESEARCHCENTER_INFOS.contentVisible = false
                }
              })
            }
          })
        }
      },
      // 点击pdf按钮
      exportCRFRandom: () => {
        // 没有勾选的提示
        if (state.SubjectsListDetailsTreeRef.getCheckedNodes().length === 0) {
          ElMessage({
            showClose: true,
            message: '请选择要导出的内容',
            type: 'warning',
          })
          return
        }
        state.exportPdfData = {
          hasTarck: false, // 轨迹的
          patientNameShow: false, // 是否显示姓名的
          lastUpdate: true, // 最后更新时间的
          hasOperator: true,
          hasExportTime: true,
          hasParticipantRelation: false,
        }
        // 展示弹窗
        state.popupshow = true
      },
      // 导出
      exportPdf: () => {
        const data = {
          ...state.exportPdfData,
          patientQuestIds: state.crfCheckedData + '',
          dctQuestPatientTemplateTypes: state.crfCheckedType + '',
          aeRowNums: state.aeCheckedRowNums + '',
          cmRowNums: state.cmCheckedRowNums + '',
        }
        state.loading = true
        // 最新导出方式（2024年12月27日）写的
        postCrfQuestInfoExportPDF(
          RESEARCHCENTER_INFOS.researchContent.patientId,
          data
        )
          .then((res: any) => {
            saveAs(new Blob([res.data]), res.headers?.filedownloadname)
            // downLoad(res.data, res.headers?.filedownloadname,{ type: 'application/pdf' })
            // downLoad(res.data, res.headers?.filedownloadname)
            state.popupshow = false
            state.loading = false // 加载完 -- false
          })
          .catch(() => {
            state.loading = false
          })
        // window.open(
        //   `${window.location.origin}/api/Operator/DoctorPatient/${RESEARCHCENTER_INFOS.researchContent.patientId}/Quest/Export?hasTarck=${state.exportPdfData.hasTarck}&patientQuestIds=${state.crfCheckedData}&dctQuestPatientTemplateTypes=${state.crfCheckedType}&patientNameShow=${state.exportPdfData.patientNameShow}&lastUpdate=${state.exportPdfData.lastUpdate}&hasOperator=${state.exportPdfData.hasOperator}&hasExportTime=${state.exportPdfData.hasExportTime}&hasParticipantRelation=${state.exportPdfData.hasParticipantRelation}`
        // )
      },
      // 取消导出
      cancelExportPdf: () => {
        state.popupshow = false
      },
      // 上次点击节点的时候 的参数
      lastTimeObj: {
        data: null as any,
        node: null as any,
      },
      dataNodeClickObj: {},
      // 点击节点的时候
      handleNodeClick: (data: any, node: any, item: any) => {
        // console.log(data);
        if (!data?.questId) {
          return
        }
        state.lastTimeObj = {
          data,
          node,
        }
        state.tmpQuestId = data.questId
        if (data.isSpecialQuest) {
          state.dataIsSpecialQuest = true
        } else {
          state.dataIsSpecialQuest = false
        }
        // console.log(node.parent.data.label)
        // console.log(item)
        // 对象和数组全部清空完事
        if (data.visitId === '1') {
          // 合并用药和不适记录
          state.visitPlanObjShow = 1
        } else {
          state.visitPlanObjShow = 0
        }
        state.CRFDetailsAllMsg = {} as { crfQuestItemAndFieldLogs?: CrfQuestItemAndFieldLog[] }
        state.CrfvisitName = data.visitName || node.parent.data.label
        state.CrfquestName = data.questName || ''
        if (!data.treeParentNode && !data.disabled) {
          state.CrfdctQuestPatientTemplateType =
            data?.dctQuestPatientTemplateType || ''
          state.CRFRightMsgShow = 1
          state.trackShow = true
          state.dataObj = data
          state.crfQuestionnaireMessageDetails(
            data.questId,
            data.dctQuestPatientTemplateType,
            data.rowNum
          )
        } else if (data.treeParentNode && !data.disabled) {
          state.getPeriodDate(data)
          state.CRFRightMsgShow = 2
        } else {
          // console.log(`state.CRFRightMsgShow = 0 需要接口获取计划外按钮是否显示`)
          state.crfQuestionnaireMessageDetails(
            data.questId,
            data.dctQuestPatientTemplateType,
            data.rowNum
          )
          // 事件 计划外时显示
          state.CRFRightMsgShow = 4
          // 这里是禁用的情况下 需要接口获取计划外按钮是否显示
          // state.CRFRightMsgShow = 0
        }
      },
      // 点击节点之后触发 -- 打印用
      check: (data: any, checkedData: any) => {
        state.crfCheckedData = []
        state.crfCheckedType = []
        state.aeCheckedRowNums = []
        state.cmCheckedRowNums = []
        checkedData.checkedKeys.forEach((item: any) => {
          // 通过item不是unde 拿到子节点的questId
          if (item) {
            if (item.length > 5) {
              state.crfCheckedData.push(item.split('|')[0])
            }
            checkedData.checkedNodes.forEach((ite: any) => {
              let curId = ite?.questId + '|' + ite?.rowNum
              if (
                item === ite?.questId &&
                ite?.dctQuestPatientTemplateType !== 2 &&
                ite?.dctQuestPatientTemplateType !== 3
              ) {
                state.crfCheckedType.push(ite.dctQuestPatientTemplateType)
              }
              if (
                item === curId &&
                ite?.dctQuestPatientTemplateType === 2 &&
                ite?.rowNum
              ) {
                state.crfCheckedType.push(ite.dctQuestPatientTemplateType)
                state.aeCheckedRowNums.push(ite.rowNum)
              }
              if (
                item === curId &&
                ite?.dctQuestPatientTemplateType === 3 &&
                ite?.rowNum
              ) {
                state.crfCheckedType.push(ite.dctQuestPatientTemplateType)
                state.cmCheckedRowNums.push(ite.rowNum)
              }
            })
          }
        })
      },
      // 获取crf问卷信息详情 -- 右边的
      crfQuestionnaireMessageDetails: (
        questId: string,
        dctQuestPatientTemplateType: number,
        rowNum: number
      ) => {
        state.dataNodeClickObj = deepClone({
          questId,
          dctQuestPatientTemplateType,
          rowNum
        })
        state.loadingData = true
        const data = {
          WithLog: 1,
          DctQuestPatientTemplateType: dctQuestPatientTemplateType,
          RowNum: rowNum,
        }
        getCrfQuestInfo(
          RESEARCHCENTER_INFOS.researchContent.patientId,
          questId,
          data
        ).then((res: any) => {
          if (res?.crfQuestItemAndFieldLogs && res.crfQuestItemAndFieldLogs.length > 0) {
            state.dataObj.isOffline = res.crfQuestItemAndFieldLogs[0]?.isOffline
          }
          state.CRFDetailsAllMsg = res
          state.loadingData = false
          if (res?.crfQuestItemAndFieldLogs && res.crfQuestItemAndFieldLogs.length > 0) {
            state.CrfvisitName = res.crfQuestItemAndFieldLogs[0]?.visitName
            state.CrfquestName = res.crfQuestItemAndFieldLogs[0]?.questName
          }
          if (state?.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs?.length) {
            state.CRFDetailsAllMsg.crfQuestItemAndFieldLogs.forEach((item: any) => {
              let addshow = false
              item.crfQuestItems.forEach((ite: any) => {
                if (ite.crfFieldType === 3 && !addshow) {
                  ite.showType = true
                  addshow = true
                }
              })
            })
          }
        })
      },
      // 获取周期下的数据
      getPeriodDate: (data: any) => {
        state.visitPlanObj = data
      },
      // 轨迹显示隐藏
      trackShowClick: (index: number) => {
        if (state.CRFQuestionnaireMessageRef) {
          state.CRFQuestionnaireMessageRef.scrollTop = 0
        }
        if (index === 1) {
          state.trackShow = false
        } else if (index === 2) {
          state.trackShow = true
        }
      },
      lastUpdateChange: () => {
        if (!state.exportPdfData.lastUpdate) {
          state.exportPdfData.patientNameShow = false
        }
      },
      hasTarckChange: () => {
        if (!state.exportPdfData.hasTarck) {
          state.exportPdfData.hasParticipantRelation = false
        }
      },
      async getResearchCenterList() {
        return {
          data: [],
        }
      },
      // 获取数据
      onLoad: () => {
        // 重新筛选权限
        // const rescreenRoles = ['admin', 'PI', 'SUBI']
        // const role = store.state.account.userinfo?.roles || []
        // state.editDataBtnFlag = ['admin', 'DC'].some((item) =>
        //   role.includes(item)
        // )
        // state.btnRescreenVisible = rescreenRoles.some((item) =>
        //   role.includes(item)
        // )
        // 手写签名权限
        // const signatureRoles = ['admin', 'PI', 'SUBI', 'CRC']
        // state.imgSignatureShow = signatureRoles.some((item) =>
        //   role.includes(item)
        // )
        getQuestDetailsMessage(
          RESEARCHCENTER_INFOS.researchContent.patientId
        ).then((res: DetailsList) => {
          state.DetailsList = res
          // 受试者id
          // state.DetailsList.patientId
          state.crfShow = true
          if (state.DetailsList?.ieQuestCrfInfo?.ieQuestCrfItems?.length === 0) {
            // state.activeName = 'CRFQuestionnaireMessage'
            // 事件 eventInformation
          }
          // if (res.rescreeningInfo != null) {
          //   state.activeName = 'rescreeningInfo'
          // }
        })
        // 获取crf问卷信息左边的
        getCrfVisitInfo(RESEARCHCENTER_INFOS.researchContent.patientId).then(
          (res: any[]) => {
            if (res && res.length > 0) {
              res.forEach((item: any) => {
                item.label = item.visitName
                item.treeParentNode = true
                item.childrens = item?.quests ? deepClone(item?.quests) : []
                if (Array.isArray(item?.lists) && item?.lists?.length) {
                  item.childrens = item?.lists
                }
                if (Array.isArray(item.childrens)) {
                  item.childrens.forEach((clItem: any) => {
                    clItem.childrens = clItem?.quests
                      ? deepClone(clItem?.quests)
                      : []
                    if (Array.isArray(item?.lists) && item?.lists?.length) {
                      clItem.label = clItem.visitName
                      clItem.id = clItem.visitId
                    } else {
                      clItem.label = clItem.questName
                      clItem.id = clItem.questId
                    }

                    if (clItem.questTemplateType === 1) {
                      clItem.questTemplateTypeTxt = '受试者'
                    } else if (
                      clItem.questTemplateType === 2 ||
                      clItem.dctQuestPatientTemplateType === 4
                    ) {
                      clItem.questTemplateTypeTxt = '研究人员'
                    }
                    if (clItem.finishStatus === 1) {
                      clItem.disabled = true
                    }
                    if (Array.isArray(clItem.childrens)) {
                      clItem.childrens.forEach((ite: any) => {
                        ite.label = ite.questName
                        //不适记录和合并用药是同一个问卷,不同RowNum,特殊处理
                        if (
                          ite.dctQuestPatientTemplateType === 2 ||
                          ite.dctQuestPatientTemplateType === 3
                        ) {
                          ite.id = ite.questId + '|' + (ite.rowNum ?? 0)
                        } else {
                          ite.id = ite.questId
                        }
                        // if (ite.questTemplateType === 1) {
                        //   ite.questTemplateTypeTxt = '受试者'
                        // } else if (ite.questTemplateType === 2) {
                        //   ite.questTemplateTypeTxt = '研究人员'
                        // }
                        if (ite.finishStatus === 1) {
                          ite.disabled = true
                        }
                      })
                    }
                  })
                } else {
                  item.disabled = true
                }
              })
              state.treeData = res
            }
          }
        )
      },
      questionEidtRef: null as any,
      // 修改数据弹框-展示用的数据
      questionEidtObj: {} as QuestionEditObj,
      // 修改数据按钮权限
      editDataBtnFlag: false,
      // 修改数据弹框
      editDataFlag: false,
      // 显示修改数据弹框
      editData: (item: any, it: any) => {
        // console.log(item, it, 'editData');
        //isReadOnly: 0
        // isRequired: 0
        if (item.isReadOnly) {
          ElMessage.warning('该题目只读')
          return
        }
        item.questId = it.questFieldDataLogs?.[0]?.patientQuestId || item.questId
        if (item?.crfFieldControl && item?.crfFieldType) {
          state.editDataFlag = true
          state.questionEidtObj = deepClone(item)
          // remarks：修改原因   remarksStr：pdf的url地址
          state.questionEidtObj.remarks = ''
          state.questionEidtObj.remarksStr = ''
          // state.questionEidtObj.crfFieldControl = 6
          nextTick(() => {
            // console.log(state.questionEidtRef?.item,'state.questionEidtRef.item');
            const questionItem = deepClone(state.questionEidtRef?.item)
            // state.questionEidtObj
            state.questionEidtRef.item = {
              ...questionItem,
              ...deepClone(item),
              // fieldValue: '',
              fieldValueStr: item?.fieldDisplayValue,
            }
            state.questionEidtRef?.onLoad()
          })
        }
      },
      // 数据变动弹框
      changeDataFlag: false,
      // 数据澄清
      showClarify: (item: any) => {
        state.tmpContent = null
        state.clarifyList = []
        // 获取澄清列表
        getQuestClarifyListInfo(item.questDataId, state.tmpQuestId).then(
          (res: ClarifyItem[]) => {
            state.clarifyList = res
            if (res.length > 0) {
              state.tmpContent = res[0]
            }
            state.clarifyDialogVisible = true
          }
        )
      },
      closeClarifyDialog: () => {
        state.tmpContent = null
        state.clarifyList = []
        state.tmpContentIndex = 0
        state.clarifyDialogVisible = false
      },
      selectClarify: (item: ClarifyItem, index: number) => {
        state.tmpContent = item
        state.tmpContentIndex = index
      },
    })
    onMounted(() => {
      state.onLoad()
    })
    const openDialogVisible = ref(false)
    const openFormRef = ref()
    const openInDialogVisible = ref(false)
    const closeDialogVisible = ref(false)
    const closePlanChangeDialogRef = ref(null)

    const openPlanChangeWindow = () => {
      getQuestUnPlannedWindowDetail2(state.tmpQuestId,{
        windowId: state.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs?.length && state.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.unplannedWindowId ? state.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.unplannedWindowId : '',
        rowNum: state.dataObj.rowNum || '0'
      }).then((res: any) => {
        if (state.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs?.length && state.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.unplannedWindowId) {
          state.openInDialogObj = res
        } else {
          if (res?.updateUserName === '研究人员') {
            getUnplannedSelectUsers(state.tmpQuestId).then((personnelList) => {
              openFormRef.value.personnelList = personnelList || []
            })
          }
          res.modifyWindow = []
          // 生成当前时间 格式是YYYY-MM-DD HH:mm:ss
          // 获取当前时间
          const currentDate = new Date();
          const seventyTwoHoursLater = new Date(currentDate.getTime() + 72 * 60 * 60 * 1000); // 72小时转换为毫秒
          // res.modifyWindow = [parseTime(new Date()), parseTime(seventyTwoHoursLater)]
          // console.log('res modifyWindow',res);
          // res.modifyWindow = ['2025-06-23 09:30:00'
          // ,'2025-06-26 09:30:00']
          // res.modifyWindow = openFormRef.value.modifyWindow
          res.windowStartTime = parseTime(new Date()),
          res.windowEndTime = parseTime(seventyTwoHoursLater),
          openFormRef.value.openForm = res
        }
      })
      if (state.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs?.length && state.CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.unplannedWindowId) {
          // 已经打开
          // CRFDetailsAllMsg?.crfQuestItemAndFieldLogs[0]?.unplannedWindowId
          openInDialogVisible.value = true
        } else {
          // 未打开
          openDialogVisible.value = true
        }
    }

    const openClosePlanChangeWindow = () => {
      openInDialogVisible.value = false
      closeDialogVisible.value = true
      if (closePlanChangeDialogRef.value) {
        closePlanChangeDialogRef.value.closeForm = deepClone(state.openInDialogObj)
      }
    }

    const handleConfirmOpen = (formData: any) => {
      // console.log('Dialog confirmed with data:', formData,'dataNodeClickObj',state.dataNodeClickObj)
      state.crfQuestionnaireMessageDetails(
        state.dataNodeClickObj.questId,
        state.dataNodeClickObj.dctQuestPatientTemplateType,
        state.dataNodeClickObj.rowNum
      )
      // TODO: 调用打开计划外修改窗口接口
    }

    const handleCancelOpen = () => {
      // console.log('Dialog cancelled')
    }
    const handleCloseConfirm = (formData: any) => {
      // console.log('Close Dialog confirmed with data:', formData)
      // TODO: 调用关闭计划外修改窗口接口
      state.crfQuestionnaireMessageDetails(
        state.dataNodeClickObj.questId,
        state.dataNodeClickObj.dctQuestPatientTemplateType,
        state.dataNodeClickObj.rowNum
      )
    }

    const handleCloseCancel = () => {
      // console.log('Close Dialog cancelled')
    }

    return {
      openInDialogVisible,
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
      openDialogVisible,
      openFormRef,
      openPlanChangeWindow,
      handleConfirmOpen,
      handleCancelOpen,
      closeDialogVisible,
      closePlanChangeDialogRef,
      openClosePlanChangeWindow,
      handleCloseConfirm,
      handleCloseCancel,
    }
  },
})
</script>

<style lang="less" scoped>
:deep(.el-upload-list__item .el-icon--close-tip) {
  display: none !important;
  top: 27px;
  min-width: 110px;
}
.open-in-form-module  {
  .el-form-item {
    margin-bottom: 0px
  }
}
.htmls-m-0 {
  :deep(p) {
    margin: 0;
  }
}
.no-styles {
  :deep(p) {
    margin: 0;
  }
}
:deep(.el-upload-list.el-upload-list--text) {
  position: relative;
  top: -35px;
  left: 50px;
  width: calc(100% - 60px);
}
.subjectsListDetails {
  min-width: 990px;
}
.el-image-box {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  .el-image-more {
    margin-right: 0.2rem;
  }
}
.randomUpside {
  display: flex;
  justify-content: space-between;
}
.random {
  width: 100%;
  .randomUpside-export {
    float: right;
  }
}
.filtrate-message {
  display: flex;
  justify-content: space-between;
  .filtrate-message-export {
    margin-top: 20px;
  }
}
.infos-body {
  width: 100%;
  margin: 20px 0;
  padding: 0 20px 10px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 5px;
  .head {
    width: 100%;
    background: #fff;
    padding: 5px 10px 20px;
    box-sizing: border-box;
    .head-boxTop {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      div {
        width: 33%;
        display: flex;
        span {
          font-weight: normal;
          &:first-child {
            width: 100px;
            text-align: right;
            color: rgb(156, 152, 152);
          }
          &:last-child {
            flex: 1;
          }
        }
      }
    }
    .head-boxCenter {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      div {
        width: 33%;
        display: flex;
        span {
          font-weight: normal;
          &:first-child {
            width: 100px;
            text-align: right;
            color: rgb(156, 152, 152);
          }
          &:last-child {
            flex: 1;
          }
        }
      }
    }
  }
}
.center-box-bottom {
  font-size: 14px;
  div {
    .track-details {
      display: flex;
      .time {
        display: inline-block;
        width: 160px;
      }
    }
  }
}
.content-box {
  margin-top: 20px;
  .top {
    width: 100%;
    display: inline-flex;
    justify-content: space-between;
    .clarifyBtn {
      color: #f59a23;
      text-decoration: underline;
      cursor: pointer;
    }
  }
  .bottom {
    width: 100%;
    margin-top: 10px;
    color: #666666;
    .bottomChildern {
      width: 100%;
      margin-left: 10px;
      .bottom-childern-box {
        display: flex;
        margin: 10px 0 0 0;
      }
      .bottomChildern-son {
        display: block;
        margin-right: 20px;
        :deep(p) {
          margin: 0;
        }
        &:nth-child(1) {
          color: black;
        }
      }
    }
  }
}
.randomTop-content-box {
  margin-top: 20px;
  .top {
    width: 100%;
    display: flex;
  }
  .bottom {
    width: 100%;
    margin-left: 5px;
    margin-top: 10px;
    color: #666666;
    .bottomChildern {
      width: 100%;
      span {
        display: block;
        &:nth-child(1) {
          color: black;
        }
      }
    }
  }
}
.randomBottom-content-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  div {
    width: 33%;
    display: flex;
    span {
      font-weight: normal;
      &:first-child {
        text-align: right;
      }
      &:last-child {
        flex: 1;
        color: #666666;
      }
    }
  }
}
.randomCRF {
  // width: 100%;
  min-width: 910px;
  .randomUpside-export-CRF {
    margin: 20px 0;
  }
  .CRFQuestionnaireMessage-box {
    display: flex;
    .CRFQuestionnaireMessage-left,
    .CRFQuestionnaireMessage-right-scoll {
      // display: none;
      &::-webkit-scrollbar {
        // display: none;
        width: 6px;
      }
      /* 修改滚动条的轨道背景颜色 */
      &::-webkit-scrollbar-track {
        // background-color: #ffffff;
      }

      /* 修改滚动条的滑块颜色 */
      &::-webkit-scrollbar-thumb {
        background-color: #e9e9e9;
        border-radius: 5px;
      }

      /* 修改滚动条的滑块悬停时的颜色 */
      &::-webkit-scrollbar-thumb:hover {
        background-color: #c7c7c7;
        border-radius: 5px;
      }
    }
    .CRFQuestionnaireMessage-left {
      height: 700px;
      // min-height: 500px;
      // max-height: 600px;
      width: 26%;
      min-width: 520px;
      padding: 0 5px;
      border: 1px solid #cccccc;
      margin-right: 60px;
      overflow: hidden;
      border-radius: 5px;
      // overflow-y: auto;
      overflow: scroll;
      .custom-tree-node-right-doctorWhite {
        padding: 4px 8px;
        font-size: 12px;
        background: #d7d7d7;
        color: #7a7a7a;
        border-radius: 4px;
        box-sizing: border-box;
        margin-right: 8px;
      }
      .custom-tree-node-right-state-questTemplateType-color {
        padding: 3px 7px;
        background: #fff;
        color: #7a7a7a;
        border: 1px solid #7a7a7a;
      }
      .custom-tree-node-right-state {
        padding: 3px 8px;
        font-size: 12px;
        border-radius: 4px;
        background: rgba(103, 194, 58, 0.1);
        color: #67c23a;
        border: 1px solid #67c23a;
        box-sizing: border-box;
      }
      .custom-tree-node-right-state-review {
        padding: 3px 8px;
        font-size: 12px;
        border-radius: 4px;
        background: #4b7902;
        color: #ffffff;
        border: 1px solid #4b7902;
        box-sizing: border-box;
      }
      .custom-tree-node-right-state-unfinished-color {
        background: rgba(245, 108, 108, 0.1);
        color: #f56c6c;
        border: 1px solid #f56c6c;
      }
      .custom-tree-node-right-state-unfinished-color-review {
        background: #aaaaaa;
        color: #ffffff;
        border: 1px solid #aaaaaa;
      }
    }
    .CRFQuestionnaireMessage-right-scoll {
      padding: 0 20px;
      box-sizing: border-box;
      height: 600px;
      overflow-y: auto;
    }
    .CRFQuestionnaireMessage-right {
      // min-height: 600px;
      width: calc(74% - 72px);
      min-width: 438px;
      // height: 700px;
      border: 1px solid #cccccc;
      // padding: 0 20px;
      font-size: 16px;
      border-radius: 5px;
      // overflow-y: auto;
      .CRFQuestionnaireMessage-title {
        font-size: 18px;
        font-weight: 700;
      }
      .CRFQuestionnaireMessage-right-no-data {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 30px;
      }
      .CRFQuestionnaireMessage-right-box-color {
        color: #666666;
        font-size: 15px;
      }
      .CRFQuestionnaireMessage-right-box-track {
        font-size: 14px;
      }
    }
  }
}
.custom-tree-node {
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  margin: 6px 0;
  .custom-tree-node-title-box {
    display: flex;
    flex: 1;
    justify-content: space-between;
    align-items: center;
    .custom-tree-node-date {
      padding: 3px 8px;
      font-size: 12px;
      border-radius: 4px;
      background: rgba(236, 245, 255, 0.1);
      color: #409eff;
      border: 1px solid #b3d8ff;
    }
    .custom-tree-node-title {
      max-width: 180px;
      overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }
  }
}
:deep(.el-tree-node) {
  margin: 6px 0;
}
:deep(.el-tree-node__label) {
  flex: 1;
}
:deep(.my-dialog-body h4) {
  margin: 10px 0 20px 0;
  text-align: center;
  font-size: 18px;
  font-weight: 400;
}
:deep(.el-tabs) {
  min-height: calc(100vh - 120px);
  background: #fff;
}
:deep(.el-tabs__header) {
  margin-bottom: 0;
}
:deep(.el-tabs__content) {
  height: 100%;
  padding: 0 20px 20px;
}
:deep(.el-tabs__nav-scroll) {
  background: #f0f2f5;
}
:deep(.el-tabs__nav) {
  background: #fff;
}
:deep(.el-tabs__item) {
  padding: 0;
}
:deep(.el-tabs__item, .is-top) {
  min-width: 97px;
  text-align: center;
}
:deep(.el-tabs__nav-wrap::after) {
  height: 0;
}
:deep(.el-tabs__active-bar) {
  height: 0;
}
:deep(.el-tabs__item.is-active) {
  color: #fff;
  background-color: #304156;
  border-radius: 5px;
}
:deep(.el-tabs__item) {
  color: #000;
}
.fontColor {
  color: #7f7f7f;
  :deep(p) {
    margin: 0;
  }
}
.infos-body-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  & > div {
    width: 80%;
    & > h4 {
      margin: 0;
      height: 100%;
      padding-top: 7px;
    }
  }
  & > div:nth-child(2) {
    width: 20%;
    text-align: right;
  }
}
.rescreenDialog {
  :deep(.my-dialog-body) {
    min-height: auto;
    h4 {
      margin: 0;
    }
  }
  .rescreen-tips {
    color: #f59a23;
  }
}
.rescreenRemark {
  margin-top: 1em;
  display: block;
  & > span:nth-child(2) {
    color: #666666;
  }
}
:deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
}
:deep(.el-dialog) {
  padding: 30px;
}
.clarifyBody {
  font-size: 16px;
  height: calc(100% - 20px);
  display: flex;
  margin-top: 10px;
  & > .verDivider {
    height: 100%;
    margin-left: 10px;
  }
  & > .clarifyList {
    width: 20%;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #e9e9e9;
      border-radius: 5px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: #c7c7c7;
      border-radius: 5px;
    }
    & > .clarifyItem {
      cursor: pointer;
      padding: 10px;
    }
    & > .noData {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      & > span {
        color: #666666;
      }
    }
    & > div {
      & > .firstLevel {
        display: flex;
        justify-content: space-between;
        // margin-top: 20px;
        & > span:first-child {
          color: #000000;
        }
      }
      & > .secLevel {
        margin-top: 10px;
        & > span {
          color: #666666;
        }
      }
      & > .thirdLevel {
        margin-top: 10px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  & > .clarifyContent {
    width: 80%;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #e9e9e9;
      border-radius: 5px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: #c7c7c7;
      border-radius: 5px;
    }
  }
}
.clarifyTitle {
  justify-content: space-between;
  display: flex;
}
.fieldLineLeft {
  width: calc(100% - 40px);
  display: block;
  margin: 10px 0 0 0px;
  & > div {
    margin: 10px;
    display: block;
    & > span:nth-child(odd) {
      width: 10%;
      text-align: right;
      color: rgb(156, 152, 152);
      display: inline-block;
    }
    & > span:nth-child(even) {
      width: 90%;
      margin-left: 20px;
    }
  }
  & > .divider {
    width: calc(100% - 40px);
  }
}
.clarifyBtnOuter {
  // flex-grow: 1;
  max-width: 230px;
  margin-right: 10px;
  text-align: right;
  font-size: 16px;
  .clarifyBtn {
    color: #f59a23;
    text-decoration: underline;
    cursor: pointer;
    font-size: 16px;
  }
}
.clarifyShowTrack {
  width: 200px;
  display: block;
  text-align: right;
  color: #f59a23;
  text-decoration: underline;
  cursor: pointer;
}
.recordBox {
  margin-top: 1rem;
  & .recordLine {
    display: block;
    margin-bottom: 0.5rem;
  }
}
</style>
<style>
.showHtml {
  display: inline-flex;
  & > * {
    margin: 0;
  }
}
</style>
