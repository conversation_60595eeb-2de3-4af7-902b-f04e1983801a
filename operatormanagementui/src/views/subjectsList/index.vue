<template>
  <div>
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="p-[20px] mt-[10px] mb-[20px] bg-white box-border">
        <h3 class="m-0" v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
        :showbtnfalg="true"
      >
        <!-- :requestExport="getResearchCenterExport" -->
        <template #patientTagsStr="scope">
          <span v-if="scope.row.patientTags?.length">
            {{ scope.row.patientTags.join('，') }}
          </span>
        </template>
        <template #operate="scope">
          <span
            v-permission="['manage.subjectsList.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <Details />
    </div>
  </div>
</template>
<script lang="ts">
// import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { defineComponent, reactive, toRefs, provide } from 'vue'
import Details from '@/views/subjectsList/SubjectsListDetails.vue'
import { getPatientList, getPatientTagList } from '@/api/subjectsList'

export default defineComponent({
  name: 'SubjectsList', // 受试者列表
  components: {
    Details,
  },
  setup() {
    const store = useStore()
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
    })
    const getPatientTagListFun = () => {
      getPatientTagList(store.state.studyItem.studyId)
        .then((res) => {
          if (res?.length) {
            state.searchConfig.fields[2].options = res.map((e) => {
              return {
                value: e.id,
                name: e.tagName
              }
            })
          }
        })
    }
    getPatientTagListFun()
    const state = reactive({
      studyName: store.state.studyItem.studyName,
      researchCenterRef: null,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        { label: '中心名称', prop: 'siteName', width: 200 },
        { label: '姓名', prop: 'realName', width: 100 },
        { label: '受试者编号', prop: 'patientNo', width: 100 },
        { label: '性别', prop: 'sex', width: 100 },
        { label: '状态', prop: 'groupStatus', width: 140 },
        { label: '组别', prop: 'group', width: 100 },
        { label: '入组日期', prop: 'inGroupDate', width: 120 },
        { label: '标签', tdSlot: 'patientTagsStr', minWidth: 120 },
        {
          width: 120,
          label: '操作',
          fixed: 'right',
          align: 'left',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        // labelWidth: '90px', // 必须带上单位
        // inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          // {
          //   label: '状态',
          //   name: 'status',
          //   type: 'select',
          //   defaultValue: null,
          //   tdSlot: 'operateVal',
          //   options: [
          //     {
          //       value: 1,
          //       name: '待入组',
          //     },
          //     {
          //       value: 2,
          //       name: '已入组',
          //     },
          //     {
          //       value: 3,
          //       name: '已出组'
          //     }
          //   ],
          // },
          {
            label: '受试者信息',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
          {
            label: '标签',
            name: 'patTagIds',
            // type: 'input',
            // 多选
            type: 'select',
            multiple: true,
            options: [],
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 受试者列表
      async getResearchCenterList(params) {
        const { studyId } = store.state.studyItem
        try {
          if (!params.status) {
            params.status = 0
          }
          if (params?.patTagIds?.length) {
            params.patTagIds += ''
          }
          const rest = await getPatientList(studyId, params)
          rest.items.forEach((item, idx) => {
            item.id = idx + 1
          })
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      onRefresh: () => { },
      // 获取数据
      // onLoad: async() => {
      //   //
      // },
    })

    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.page-box {
  :deep(.el-select) {
    min-width: 200px;
  }
  :deep(.el-select .el-select__tags-text) {
    display: block !important;
  }
}
</style>