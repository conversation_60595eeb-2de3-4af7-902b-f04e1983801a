<template>
  <trial-dialog
    v-model="dialogVisible"
    :my-dialog-body-style="{ width: '60%' }"
    class="!z-[2000]"
    @close="handleClose"
  >
    <template #DialogBody>
      <div class="flex">
        <div class="font-bold">打开计划外修改窗口</div>
        <div class="text-[#E6A23C] ml-[20px] mb-[10px]"
            >数据修改请确保有客观依据，并符合项目组既定的标准。</div
        >
      </div>
      <el-form
        ref="openFormRef"
        :model="openForm"
        :rules="openRules"
        label-position="right"
        label-width="80px"
      >
        <el-form-item label="修改人" prop="updateUserId">
          <div class="flex items-center">
            <div class="mr-[20px] whitespace-nowrap">{{ openForm?.updateUserName }}</div>
            <el-autocomplete
              v-if="openForm?.updateUserName === '研究人员' && personnelList?.length"
              v-model.trim="openForm.updateUserNewName"
              :fetch-suggestions="querySearch"
              placeholder="请输入"
              class="min-w-[260px]"
              value-key="userName"
              clearable
              @select="handleSelect"
            />
          </div>
        </el-form-item>
        <el-form-item label="修改窗口" prop="windowEndTime">
          <div class="flex">
          <el-date-picker
            v-model="openForm.windowStartTime"
            type="datetime"
            placeholder="年/月/日 --:--"
            format="YYYY/MM/DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%;"
            :readonly="true"
            :disabled="true"
          />
         <div class="mx-[20px]"> - </div>
          <el-date-picker
            v-model="openForm.windowEndTime"
            type="datetime"
            placeholder="年/月/日 --:--"
            format="YYYY/MM/DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%;"
            :disabled-date="disabledDateFun"
          />
        </div>
        </el-form-item>
        <el-form-item label="原因" prop="openWindowReason">
          <el-input
            v-model="openForm.openWindowReason"
            type="textarea"
            :rows="6"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="附件" prop="attaId">
          <div class="flex flex-col items-start w-full">
            <span>按需上传证据，支持pdf格式，大小不超过10M</span>
            <Upload
              ref="OpenPlanChangeUploadRef"
              upload-file-falg
              customFlag
              :request-fun="fileFun"
              :deletefile="deletefile"
              :file-list="fileList"
              :file-size="10"
              accept=".pdf"
              :before-file-upload-type="['.pdf']"
            >
            <template #uploadBtn="row">
              <el-button type="primary" link class="el-button-primary-small"
                >上传</el-button
              >
            </template>
          </Upload>
          </div>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="centerflex mt-[60px]">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import Upload from '@/components/Upload/index.vue'
import { saveOrUpdateQuestUnplannedWindow } from '@/api/planChange'
import { deepClone, parseTime } from '@/utils'
import { postPublicDocumentFile } from '@/api/home'
import { ElMessage, ElLoading } from 'element-plus'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'OpenPlanChangeDialog',
  components: {
    Upload
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const dialogVisible = ref(props.modelValue)
    const openFormRef = ref<FormInstance>()
    // 获取当前时间
    const currentDate = new Date();
    const seventyTwoHoursLater = new Date(currentDate.getTime() + 72 * 60 * 60 * 1000); // 72小时转换为毫秒
    
    const openForm = ref({
      updateUserName: '',
      updateUserNewName: '',
      updateUserId: '',
      windowStartTime: parseTime(new Date()),
      windowEndTime: parseTime(seventyTwoHoursLater),
      openWindowReason: '',
      attaId: '',
      fileUrl: '',
    })

    // 模拟人员数据，实际项目中应该从API获取
    const personnelList = ref([])
    const querySearchList = ref([])

    const querySearch = (queryString: string, cb: (arg: any[]) => void) => {
      let results: any[] = []
      querySearchList.value = deepClone(personnelList.value)
      // console.log(personnelList.value,'personnelList.value22')
      if (queryString) {
        results = Array.isArray(querySearchList.value)
          ? querySearchList.value.filter(person =>
              person.userName && person.userName.toLowerCase().includes(queryString.toLowerCase())
            )
          : []
      } else {
        results = Array.isArray(querySearchList.value) ? querySearchList.value : []
      }
      // console.log(results, querySearchList.value)  
      cb(results)
    }

    const handleSelect = (item: any) => {
      openForm.value.updateUserNewName = item.userName
      // openForm.value.updateUserName = item.value
      openForm.value.updateUserId = item.userId
    }

    // const handleInput = (value: string) => {
    //   // 如果输入的值不在选项列表中，则清空
    //   if (!personnelList.some(person => person.value === value)) {
    //     openForm.updateUserName = ''
    //   }
    // }

    const openRules = reactive<FormRules>({
      updateUserId: [{
        required: true, message: '请输入', trigger: 'blur'
      }],
      windowStartTime: [{
        required: true, message: '请选择', trigger: 'change'
      }],
      windowEndTime: [{
        required: true, message: '请选择', trigger: 'change'
      }],
      openWindowReason: [{
        required: true, message: '请输入', trigger: 'blur'
      }],
    })

    watch(() => props.modelValue, (newVal) => {
      dialogVisible.value = newVal
      if (!newVal && openFormRef.value) {
        openFormRef.value.resetFields()
        openForm.value.attaId = '' // 清空附件
        openForm.value.windowStartTime = parseTime(new Date())
        openForm.value.windowEndTime = ''
      }
    })
    const handleClose = () => {
      emit('update:modelValue', false)
    }

    const OpenPlanChangeUploadRef = ref()
    const fileList = ref<any[]>([])

    // 上传文件
    const fileFun = async (fileObj: any) => {
      const myFormDataObj = new FormData()
      const fileName = fileObj.file.name
      const loading = ElLoading.service({
        lock: true,
        text: '上传中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      myFormDataObj.append('CheckImageFiles', fileObj.file)
      try {
        // TODO: 传实际studyId，或从props等获取
        const res: any = await postPublicDocumentFile(studyId, 14, 3, myFormDataObj)
        loading.close()
        ElMessage.success('上传成功')
        openForm.value.attaId = res?.id || ''
        openForm.value.fileUrl = res?.fileUrl || res?.thumbnailUrl || ''
        fileList.value.length = 0
        fileList.value.push({
          name: fileName,
          url: openForm.value.fileUrl,
        })
      } catch (e) {
        loading.close()
        ElMessage.error('上传失败')
        fileList.value.length = 0
        openForm.value.attaId = ''
        openForm.value.fileUrl = ''
      }
    }

    // 删除文件
    const deletefile = () => {
      fileList.value.length = 0
      openForm.value.attaId = ''
      openForm.value.fileUrl = ''
    }

    const loading = ref(false)

    const handleConfirm = async () => {
      if (!openFormRef.value) return
      loading.value = true
      try {
        await openFormRef.value.validate()
        const openFormDatas: any = deepClone(openForm.value)
        if (openForm.value?.updateUserName === '研究人员' && openForm.value?.updateUserNewName) {
          const flag = personnelList.value.some(e => {
            if (openForm.value.updateUserNewName === e.userName) {
              openFormDatas.updateUserId = e.userId
              return true
            } else {
              return false
            }
          })
          if (flag) {
            openFormDatas.updateUserName = openForm.value.updateUserNewName
          } else {
            ElMessage.error('请输入正确的研究人员')
            return
          }
        }
        // openFormDatas.windowEndTime 转成时间戳 如果小于 openFormDatas.windowStartTime 提示结束时间不可以早于开始时间
        const startTimestamp = new Date(openFormDatas.windowStartTime).getTime();
        const endTimestamp = new Date(openFormDatas.windowEndTime).getTime();
        if (endTimestamp <= startTimestamp) {
          ElMessage.error('结束时间不可以早于等于开始时间');
          loading.value = false;
          return;
        }
        // console.log('endTimestamp <= startTimestamp',endTimestamp , startTimestamp,endTimestamp <= startTimestamp);
        await saveOrUpdateQuestUnplannedWindow(openFormDatas)
        emit('confirm', openFormDatas)
        emit('update:modelValue', false)
        ElMessage.success('保存成功')
      } catch {
        // 校验失败或接口异常都会进这里
        // 可根据err内容区分
        // ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }

    const handleCancel = () => {
      emit('cancel')
      emit('update:modelValue', false)
    }

    const disabledDateFun = (date: any) => {
      // 如果 date 小于当前时间则禁用，返回 true，否则返回 false
      const midnightToday = new Date();
      midnightToday.setHours(0, 0, 0, 0);
      // 获取时间戳
      const timestamp = midnightToday.getTime();
      // if (date.getTime() < timestamp) {
      //   console.log('true',date.getTime(), timestamp);
      // }
      // console.log(date.getTime(), timestamp);
      return date.getTime() < timestamp;
    }

    return {
      disabledDateFun,
      querySearchList,
      personnelList,
      dialogVisible,
      openFormRef,
      openForm,
      openRules,
      handleClose,
      handleConfirm,
      handleCancel,
      querySearch,
      handleSelect,
      OpenPlanChangeUploadRef,
      fileList,
      fileFun,
      deletefile,
      loading,
    }
  },
})
</script>

<style lang="less" scoped>
/*调整上传按钮的位置*/
:deep(.el-upload-list.el-upload-list--text) {
  top: -40px !important;
}
// 调整pdf上传字样
:deep(.el-upload-list__item-file-name) {
  width: auto !important;
  // min-width: 40px;
  // width: 20px;
  display: inline-block;
  overflow: visible;
}
:deep(.el-upload-list.el-upload-list--text) {
  width: 100% !important;
}
:deep(.el-upload-list__item-status-label) {
  right: -40px;
}
:deep(.el-upload-list__item .el-icon.el-icon--close) {
  right: -40px;
}
</style>