<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
        :showbtnfalg-excel="showbtnfalgExcel"
        :request-export-excel="getResearchCenterExport"
      >
        <template #operate="scope">
          <span
            v-permission="['manage.concomitantMedication.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <Details />
    </div>
  </div>
</template>

<script lang="ts">
// import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { defineComponent, provide, reactive, toRefs } from 'vue'
import Details from '@/views/concomitantMedication/ConcomitantMedicationDetails.vue'
import {
  getStudyCMQuest, // 获取某课题下合并用药问卷
  getStudyCMQuestExport, // 导出某课题下合并用药问卷
} from '@/api/concomitantMedication'

export default defineComponent({
  name: 'ConcomitantMedication', // 合并用药
  components: {
    Details,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      showbtnfalgExcel: store.state.studyItem.permissions.includes('manage.concomitantMedication.exportExcel'),
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        // doctorInfo.hospital + doctorInfo.department
        { label: '中心名称', prop: 'siteName', width: 250 },
        { label: '病例编号', prop: 'patNumber', width: 100 },
        { label: '药物名称', prop: 'title', width: 250 },
        { label: '开始日期', prop: 'happenDate', width: 180 },
        { label: '创建时间', prop: 'createDate', width: 180 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            label: '病例编号',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 导出Excel方法
      getResearchCenterExport: async (params) => {
        const myParams = { ...params }
        if (myParams.siteId == null) {
          myParams.siteId = ''
        }
        if (myParams.patNumber == null) {
          myParams.patNumber = ''
        }
        getStudyCMQuestExport(studyId, myParams)
        window.open(
          `${window.location.origin
          }/api/Operator/DoctorPatient/${studyId}/CMQuest/Export?siteId=${myParams.siteId
          }&patNumber=${myParams.patNumber}`
        )
      },
      // 获取列表数据方法
      getResearchCenterList: async (params) => {
        try {
          const rest = await getStudyCMQuest(studyId, params)
          rest.items.forEach((item, idx) => {
            item.id = idx + 1
          })
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      // 编辑某项中心
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    h3 {
      margin: 0;
    }
  }
}
</style>

