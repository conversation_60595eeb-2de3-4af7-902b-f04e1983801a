<template>
  <div class="details">
    <div class="content-top">
      <div class="top">
        <h3 v-html="studyName" />
        <el-button size="large" @click="backRefresh">返回</el-button>
      </div>
      <div class="bottom">
        <div>
          <span>中心名称:</span>
          <span>{{ DetailsList?.siteName }}</span>
        </div>
        <div>
          <span>病例编号:</span>
          <span>{{ DetailsList?.patNumber }}</span>
        </div>
        <div>
          <span>创建时间:</span>
          <span>{{ DetailsList?.createDate }}</span>
        </div>
      </div>
    </div>
    <div class="content">
      <h3 class="content-title">V{{ DetailsList?.verisonNumber }}</h3>
      <div
        v-for="(item, idx) in DetailsList?.questItems"
        :key="idx"
        class="content-box"
      >
        <div class="top">
          <span v-html="item?.fieldLabel" />
        </div>
        <div class="bottom">
          <div v-if="item.children.length == 0">
            <el-image
              v-if="item?.crfFieldType === 1 && item?.fieldDisplayValue && item?.fieldDisplayValue.indexOf(',') === -1"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[item?.fieldValue || item?.fieldDisplayValue]"
              :initial-index="0"
              fit="cover"
              :src="item.fieldDisplayValue"
              class="w-[100px]"
              alt=""
            />
            <div v-if="item?.crfFieldType === 1 && item?.fieldDisplayValue">
              <el-image
                v-for="(e, index) in item.fieldDisplayValue.split(',')"
                :key="e"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="[item?.fieldValue?.split(',')[index] || e]"
                :initial-index="0"
                fit="cover"
                :src="e"
                class="w-[100px] mr-[20px]"
                alt=""
              />
            </div>
            <span v-else> {{ item?.fieldDisplayValue }} {{ item.dctQuestUnit ? item.dctQuestUnit : '' }}</span>
          </div>
          <div v-else class="bottomChildern">
            <div v-for="(itemChildern, index) in item.children" :key="index">
              <span v-html="itemChildern?.fieldLabel" />
              <img v-if="itemChildern?.crfFieldType === 1 && itemChildern?.fieldDisplayValue" :src="itemChildern.fieldDisplayValue" alt="" />
              <span v-else-if="itemChildern?.fieldDisplayValue"> {{ itemChildern?.fieldDisplayValue }} {{ itemChildern.dctQuestUnit ? itemChildern.dctQuestUnit : '' }} </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  getStudyCMQuestDetails, // 获取某个合并用药问卷详情
} from '@/api/concomitantMedication'
import { useStore } from 'vuex'
export default defineComponent({
  name: 'ConcomitantMedicationDetails', // 合并用药详情
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      studyName: store.state.studyItem.studyName,
      DetailsList: [],
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      onLoad: () => {
        // 获取某个合并用药问卷详情
        getStudyCMQuestDetails(
          RESEARCHCENTER_INFOS.researchContent.patientId,
          RESEARCHCENTER_INFOS.researchContent.rowId
        ).then((res) => {
          state.DetailsList = res
        })
      },
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.details {
  width: 100%;
  .content-top {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    .top {
      display: flex;
      justify-content: space-between;
      h3 {
        margin: 0;
        line-height: 40px;
      }
    }
    .bottom {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      div {
        width: 33%;
        display: flex;
        span {
          font-weight: normal;
          &:last-child {
            margin-left: 10px;
            color: rgb(156, 152, 152);
          }
        }
      }
    }
  }
  .content {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    .content-title {
      margin: 0;
    }
    .content-box {
      margin-top: 20px;
      .top {
        width: 100%;
      }
      .bottom {
        width: 100%;
        margin-top: 10px;
        color: rgb(156, 152, 152);
        .bottomChildern {
          width: 100%;
          span {
            display: block;
            &:nth-child(1) {
              color: black;
            }
          }
        }
      }
    }
  }
}
</style>
