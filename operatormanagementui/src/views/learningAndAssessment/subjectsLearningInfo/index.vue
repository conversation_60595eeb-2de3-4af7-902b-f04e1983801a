<template>
  <div>
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="subjectsLearningInfoRef"
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
        :showbtnfalg="true"
        :showbtnfalg-excel="showbtnfalgExcel"
        :request-export-excel="getResearchCenterExport"
        :default-sort="defaultSort"
      >
        <template #averageTimeOfTraining="scope">
          <span>
            {{ scope.row.averageTimeOfTrainingText }}
          </span>
        </template>
        <template #operate="scope">
          <span
            v-permission="['manage.subjectsLearningInfo.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <Details :request="onLoad" :dateTime="dateTime" />
    </div>
  </div>
</template>
<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, reactive, toRefs, onBeforeMount, provide } from 'vue'
import Details from '@/views/learningAndAssessment/subjectsLearningInfo/SubjectsLearningInfoDetails.vue'
import { getPatientTrainingList, getPatientTrainingExport } from '@/api/learningAndAssessment'
import { parseTime } from '@/utils'
export default defineComponent({
  name: 'SubjectsLearningInfo', // 受试者学习统计
  components: {
    Details,
  },
  setup() {
    const store = useStore()
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
    })
    const state = reactive({
      // manage.subjectsLearningInfo.exportExcel
      showbtnfalgExcel: store.state.studyItem.permissions.includes('manage.subjectsLearningInfo.exportExcel'),
      studyName: store.state.studyItem.studyName,
      subjectsLearningInfoRef: null,
      tyPingList: [],
      dateTime: {
        startTime: '',
        endTime: ''
      },
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        // { label: '中心编号', prop: 'siteNumber', width: 100 },
        { label: '中心名称', prop: 'siteName', width: 200 },
        { label: '受试者编号', prop: 'patNumber', width: 120 },
        { label: '入组日期', prop: 'inGroup', width: 180 },
        { label: '学习次数', prop: 'numberOfTraining', width: 180, sortable: true },
        {
          label: '平均学习时长',
          prop: 'averageTimeOfTraining',
          width: 160,
          sortable: true,
          numberToStingSort: 'averageTimeOfTraining', // 自定义单元格内容的插槽名称
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            // defaultValue: store.state.studyItem.sites[0].siteId,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'daterange',
            label: '日期',
            name: 'dataTime',
            defaultValue: [
              new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
              new Date()
            ],
            filterable: true,
            clearable: false,
          },
          {
            label: '受试者编号',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      defaultSort: { prop: 'averageTimeOfTraining', order: 'descending' },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 受试者列表
      async getResearchCenterList(params) {
        const { studyId } = store.state.studyItem

        const myParams = {
          pageIndex: params?.pageIndex || 1,
          pageSize: params?.pageSize,
          siteId: params?.siteId || '',
          patNumber: params?.patNumber || '',
          startTime: '',
          endTime: ''
        }
        if (params.dataTime) {
          myParams.startTime = parseTime(new Date(params.dataTime[0]), '{y}-{m}-{d}')
          myParams.endTime = parseTime(new Date(params.dataTime[1]), '{y}-{m}-{d}')
        } else {
          myParams.startTime = parseTime(new Date(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30)), '{y}-{m}-{d}')
          myParams.endTime = parseTime(new Date(), '{y}-{m}-{d}')
        }
        try {
          // 刚进入走一边，传入默认的
          // if (!myParams.siteId) {
          //   myParams.siteId = store.state.studyItem.sites[0].siteId
          // }
          const rest = await getPatientTrainingList(studyId, myParams)
          if (rest?.items && rest?.items.length) {
            rest.items.forEach((item) => {
              if (item.inGroup) {
                item.inGroup = parseTime(new Date(item.inGroup), '{y}-{m}-{d}')
              }
            })
          }
          state.dateTime.startTime = myParams.startTime
          state.dateTime.endTime = myParams.endTime
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      // 导出Excel方法
      getResearchCenterExport: (params) => {
        const { studyId } = store.state.studyItem
        const myParams = { ...params }
        if (!myParams.siteId) {
          myParams.siteId = ''
        }
        if (myParams.dataTime) {
          myParams.startTime = myParams.dataTime[0]
          myParams.endTime = myParams.dataTime[1]
        }
        delete myParams.dataTime
        getPatientTrainingExport(studyId, myParams)
        window.open(
          `${
            window.location.origin
          }/api/Operator/DoctorPatient/${studyId}/Training/Patient/Export?siteId=${
            myParams.siteId
          }&patNumber=${myParams?.patNumber || ''}&startTime=${myParams?.startTime || ''}&endTime=${myParams?.endTime || ''}`
        )
      },
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.subjectsLearningInfoRef
      },
      onRefresh: () => {},
      // 获取数据
      onLoad: async() => {
        //
      },
    })

    onBeforeMount(() => {
      state.onLoad()
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="scss" scoped>
.head {
  width: 100%;
  background: #fff;
  margin-top: 10px;
  padding: 20px 20px 0 20px;
  box-sizing: border-box;
  h3 {
    margin: 0;
  }
}
</style>
