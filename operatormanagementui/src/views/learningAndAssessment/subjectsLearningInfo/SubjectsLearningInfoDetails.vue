<template>
  <div>
    <div class="subjects-learning-info">
      <div class="infos-body">
        <div class="infos-body-title">
          <h4>
            学习详情
          </h4>
          <el-button size="large" @click="backRefresh">返回</el-button>
        </div>
        <div class="head">
          <div class="head-boxTop">
            <div>
              <span>中心名称：</span>
              <span>{{ DetailsList?.siteName }}</span>
            </div>
            <div>
              <span>受试者编号：</span>
              <span>{{ DetailsList?.patNumber }}</span>
            </div>
            <div>
              <span>入组日期：</span>
              <span>{{ DetailsList?.inGroup }}</span>
            </div>
          </div>
        </div>
        <div class="subjectsLearningInfoDetails-table">
          <trial-table
            ref="subjectsLearningInfoDetailsRef"
            title=""
            :request="getResearchCenterList"
            :columns="columns"
            :pagination="false"
            :search="searchConfig"
            :rightBtnhide="false"
            :pickerChangeUse="true"
            :defaultSort="defaultSort"
          >
            <template #averageTimeOfTraining="scope">
              <span>
                {{ scope.row.averageTimeOfTrainingText }}
              </span>
            </template>
          </trial-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, reactive, toRefs, inject, nextTick, onBeforeUpdate } from 'vue'
import { getCertainPatientTraining } from '@/api/learningAndAssessment'
import { parseTime } from '@/utils'
export default defineComponent({
  name: 'SubjectsLearningInfoDetails', // 受试者学习统计详情页
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
    dateTime: {
      type: Object,
      default: null
    }
  },
  setup({ request,dateTime }) {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      subjectsLearningInfoDetailsRef: null,
      dateValue: [],
      DetailsList: [],
      firstEnterNumber: 0,
      // studyName: store.state.studyItem.studyName,
      loading: false,
      columns: [
        // { type: 'selection' }, // table勾选框
        { label: '标题', prop: 'trianingTitle', width: 300 },
        { label: '下发规则', prop: 'distributionRuleString', width: 260 },
        { label: '首次学习', prop: 'firstTrainingTime', width: 260 },
        { label: '学习次数', prop: 'numberOfTraining', width: 260, sortable: true },
        {
          label: '平均学习时长',
          prop: 'averageTimeOfTraining',
          sortable: true,
          numberToStingSort: 'averageTimeOfTraining', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'daterange',
            label: '日期：',
            name: 'dataTime',
            defaultValue: [
              dateTime.startTime,
              dateTime.endTime
            ],
            filterable: true,
            clearable: false,
          },
        ],
      },
      defaultSort: { prop: 'averageTimeOfTraining', order: 'descending' },
      async getResearchCenterList(params) {
        const { studyId } = store.state.studyItem
        const myParams = {
          startTime: '',
          endTime: ''
        }
        if (params.dataTime) {
          myParams.startTime = params.dataTime[0]
          myParams.endTime = params.dataTime[1]
        }
        if (!params.dataTime && state.firstEnterNumber === 0) {
          myParams.startTime = dateTime.startTime
          myParams.endTime = dateTime.endTime
          nextTick(() => {
            if (state.subjectsLearningInfoDetailsRef) {
              state.subjectsLearningInfoDetailsRef.oldSearchModel.startTime = parseTime(new Date(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30)), '{y}-{m}-{d}')
              state.subjectsLearningInfoDetailsRef.oldSearchModel.endTime = parseTime(new Date(), '{y}-{m}-{d}')
            }
          })
          state.firstEnterNumber = 1
        }
        try {
          const rest = await getCertainPatientTraining(studyId, RESEARCHCENTER_INFOS.researchContent.userId, myParams)
          state.DetailsList = rest
          if (state.DetailsList?.inGroup) {
            state.DetailsList.inGroup = parseTime(new Date(state.DetailsList?.inGroup), '{y}-{m}-{d}')
          }
          return {
            data: rest.trainings,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
    })
    onBeforeUpdate(() => {
      state.firstEnterNumber = 0
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="scss" scoped>
.infos-body {
  width: 100%;
  margin: 20px 0;
  padding: 0 20px 10px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 5px;
  .infos-body-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .head {
    width: 100%;
    background: #fff;
    padding: 5px 20px 20px;
    box-sizing: border-box;
    .head-boxTop {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      div {
        width: 33%;
        display: flex;
        span {
          font-weight: normal;
          &:first-child {
            // max-width: 130px;
            text-align: right;
          }
          &:last-child {
            flex: 1;
            color: rgb(156, 152, 152);
          }
        }
      }
    }
  }
}
:deep(.el-form-item__label) {
  font-size: 16px;
  color: #000;
}
</style>
