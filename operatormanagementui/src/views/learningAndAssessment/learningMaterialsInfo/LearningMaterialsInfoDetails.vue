<template>
  <div>
    <div>
      <div class="infos-body">
        <div class="infos-body-title">
          <h4>
            学习详情
          </h4>
          <el-button size="large" @click="backRefresh">返回</el-button>
        </div>
        <div class="head">
          <div class="head-boxTop">
            <div>
              <span>中心：</span>
              <span>{{ DetailsList?.siteName }}</span>
            </div>
            <div>
              <span>端：</span>
              <span>{{ DetailsList?.appType }}</span>
            </div>
            <div>
              <span>标题：</span>
              <span>{{ DetailsList?.trianingTitle }}</span>
            </div>
            <div>
              <span>下发规则：</span>
              <span>{{ DetailsList?.distributionRuleString }}</span>
            </div>
          </div>
        </div>
        <div class="subjectsLearningInfoDetails-table">
          <trial-table
            ref="learningMaterialsInfoDetailsRef"
            title=""
            :request="getResearchCenterList"
            :columns="columns"
            :pagination="false"
            :search="searchConfig"
            :hideCenter="true"
            :rightBtnhide="false"
            :pickerChangeUse="true"
            :defaultSort="defaultSort"
          >
            <template #hideCenter>
              <div class="learningMaterialsInfo-dct-number">
                <div class="sundry-number-people">
                  <div>
                    已下发人数：{{ DetailsList?.numberOfIssued }}
                  </div>
                  <div>
                    学习人数：{{ DetailsList?.numberOfUser }}
                  </div>
                  <div>
                    学习次数：{{ DetailsList?.numberOfTraining }}
                  </div>
                  <div>
                    平均学习时长：{{ DetailsList?.averageTimeOfTrainingText }}
                  </div>
                </div>
              </div>
            </template>
            <template #averageTimeOfTraining="scope">
              <span>
                {{ scope.row.averageTimeOfTrainingText }}
              </span>
            </template>
          </trial-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, reactive, toRefs, inject, nextTick, onBeforeUpdate } from 'vue'
import { getDPTrainingDocumentStatistics } from '@/api/learningAndAssessment'
import { parseTime } from '@/utils'
export default defineComponent({
  name: 'LearningMaterialsInfoDetails', // 学习资料统计详情页
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
    // 中心id
    siteId: {
      type: String,
      default: ''
    },
    dateTime: {
      type: Object,
      default: null
    }
  },
  setup({ request, siteId, dateTime }) {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      learningMaterialsInfoDetailsRef: null,
      dateValue: [],
      DetailsList: [],
      firstEnterNumber: 0,
      // studyName: store.state.studyItem.studyName,
      loading: false,
      launchDCTNumber: 0, // dct启动次数
      columns: [
        // { type: 'selection' }, // table勾选框
        { label: '', prop: 'parameterOne', width: 200 },
        { label: '', prop: 'parameterTwo', width: 260 },
        { label: '首次学习', prop: 'firstTrainingTime', width: 260 },
        { label: '学习次数', prop: 'numberOfTraining', width: 260, sortable: true },
        {
          label: '平均学习时长',
          prop: 'averageTimeOfTraining',
          sortable: true,
          numberToStingSort: 'averageTimeOfTraining', // 自定义单元格内容的插槽名称
        },
        // { label: '平均学习时长', prop: 'averageTimeOfTrainingText', sortable: true },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'daterange',
            label: '日期：',
            name: 'dataTime',
            defaultValue: [
              dateTime.startTime,
              dateTime.endTime,
            ],
            filterable: true,
            clearable: false,
          },
        ],
      },
      defaultSort: { prop: 'averageTimeOfTraining', order: 'descending' },
      async getResearchCenterList(params) {
        const { studyId } = store.state.studyItem
        const myParams = {
          siteId: RESEARCHCENTER_INFOS.researchContent.siteId,
          startTime: '',
          endTime: ''
        }
        if (params.dataTime) {
          myParams.startTime = params.dataTime[0]
          myParams.endTime = params.dataTime[1]
        }
        try {
          if (!params.dataTime && state.firstEnterNumber === 0) {
            myParams.startTime = dateTime.startTime
            myParams.endTime = dateTime.endTime
            nextTick(() => {
              if (state.learningMaterialsInfoDetailsRef) {
                state.learningMaterialsInfoDetailsRef.oldSearchModel.startTime = parseTime(new Date(new Date(new Date().getTime() - 3600 * 1000 * 24 * 30)), '{y}-{m}-{d}')
                state.learningMaterialsInfoDetailsRef.oldSearchModel.endTime = parseTime(new Date(), '{y}-{m}-{d}')
              }
            })
            state.firstEnterNumber = 1
          }
          const rest = await getDPTrainingDocumentStatistics(studyId, RESEARCHCENTER_INFOS.researchContent.trainingId, myParams)
          state.DetailsList = rest
          if (state.DetailsList?.appType === '研究者端') {
            state.columns[0].label = '研究人员'
            state.columns[1].label = '角色'
          } else if (state.DetailsList?.appType === '受试者端') {
            state.columns[0].label = '受试者编号'
            state.columns[1].label = '入组日期'
          }
          return {
            data: rest.users,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
    })
    onBeforeUpdate(() => {
      state.firstEnterNumber = 0
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="scss" scoped>
.infos-body {
  width: 100%;
  margin: 20px 0;
  padding: 0 20px 10px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 5px;
  .infos-body-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .head {
    width: 100%;
    background: #fff;
    padding: 5px 20px 0;
    box-sizing: border-box;
    .head-boxTop {
      width: 100%;
      div {
        margin-bottom: 20px;
      }
    }
    .head-boxCenter {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      div {
        width: 33%;
        display: flex;
        span {
          font-weight: normal;
          &:first-child {
            width: 130px;
            text-align: right;
          }
          &:last-child {
            flex: 1;
            color: rgb(156, 152, 152);
          }
        }
      }
    }
  }
  .subjectsLearningInfoDetails-picker {
    padding: 0 0 0 10px;
  }
  .learningMaterialsInfo-dct-number {
    padding: 0 20px 0;
    .sundry-number-people {
      display: flex;
      // justify-content: space-around;
      div {
        flex: 1;
      }
    }
  }
}
:deep(.el-form-item__label) {
  font-size: 16px;
  color: #000;
}
</style>
