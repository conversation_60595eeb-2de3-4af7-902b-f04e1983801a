<template>
  <div>
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="financeApproveTableRef"
        title=""
        :request="getfinanceApproveList"
        :columns="financeApproveColumns"
        :search="financeApproveSearchConfig"
        :pagination="financeApprovePaginationConfig"
        hide-center
        :showbtnfalg-excel="showbtnfalgExcel"
        :row-style="rowStyle"
        :request-export-excel="requestExportExcel"
      >
        <template #hideCenter>
          <div class="finance-hide-center flex">
            <div class="mr-12">
              <span>项目余额：</span>
              <span>￥{{ moneyForm?.studyFinanceAmount }}</span>
            </div>
            <div class="mr-12">
              <span>未提现金额：</span>
              <span>￥{{ moneyForm?.patientTotalAmount }}</span>
            </div>
            <div class="mr-12">
              <span>申请中金额：</span>
              <span>￥{{ moneyForm?.freezeTotalAmount }}</span>
            </div>
            <div class="mr-12">
              <span>已提现金额：</span>
              <span>￥{{ moneyForm?.withdrawalTotalAmount }}</span>
            </div>
          </div>
        </template>
        <template #operate="scope">
          <el-button
            v-permission="['manage.financeApprove.details']"
            size="small"
            text
            type="primary"
            @click="financeApproveEditItemForm(scope.row)"
          >
            详情
          </el-button>
        </template>
      </trial-table>
    </div>
    <FinanceApproveDetails v-if="RESEARCHCENTER_INFOS?.contentVisible" :request="onload" />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, provide, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import FinanceApproveDetails from '@/views/financeManagement/FinanceApproveDetails.vue'
import { getPatientFinanceSummary, getWithdrawalOrder } from '@/api/financeManagement'

export default defineComponent({
  name: 'FinanceApprove', // 提现申请
  components: {
    FinanceApproveDetails
  },
  setup() {
    const store = useStore()
    const { studyId, studyName } = store.state.studyItem
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
    })
    const state = reactive({
      // manage.financeApprove.exportExcel
      showbtnfalgExcel: store.state.studyItem.permissions.includes('manage.financeApprove.exportExcel'),
      studyName: studyName,
      financeApproveTableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      financeApproveColumns: [
        // { type: 'selection' },
        { label: '申请时间', prop: 'applyTime', minWidth: 180 },
        { label: '单号', prop: 'orderNo', minWidth: 230 },
        { label: '中心', prop: 'siteName', minWidth: 180 },
        { label: '申请人', prop: 'applyMan', minWidth: 150 },
        { label: '提现方式', prop: 'withdrawalTypeName', minWidth: 130 },
        { label: '提现金额(元)', prop: 'amount', minWidth: 150 },
        { label: '单据状态', prop: 'statusName', minWidth: 130 },
        { label: '付款日期', prop: 'payDate', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 金额
      moneyForm: {},
      // 搜索配置
      financeApproveSearchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'daterange',
            label: '申请日期',
            name: 'dataTime',
            defaultValue: [
              '',
              ''
            ],
            filterable: true,
            clearable: true,
          },
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            // defaultValue: store.state.studyItem.sites[0].siteId,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '状态',
            name: 'status',
            options: [
              {
                value: 1,
                name: '待处理'
              },
              {
                value: 2,
                name: '已打款'
              },
              {
                value: -1,
                name: '已拒绝'
              }
            ],
            filterable: true,
          },
          {
            type: 'text',
            label: '申请人',
            name: 'applyMan',
            defaultValue: '',
          },
          {
            type: 'text',
            label: '单号',
            name: 'orderNo',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      financeApprovePaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      rowStyle: ({ row }) => {
        if (row.status === 1) {
          return { background: '#f4ebdf' }
        }
      },
      // 请求函数
      async getfinanceApproveList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          if (params?.dataTime) {
            params.applyDateStart = params.dataTime[0]
            params.applyDateEnd = params.dataTime[1]
          }
          delete params?.dataTime
          const data = await getWithdrawalOrder(studyId, params)
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data.items,
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      financeApproveRefresh: () => {
        state.financeApproveTableRef.refresh()
      },
      // 导出
      requestExportExcel: (params) => {
        if (params?.dataTime) {
          params.applyDateStart = params.dataTime[0]
          params.applyDateEnd = params.dataTime[1]
        }
        window.open(
          `${
            window.location.origin
          }/api/Operator/Compensation/${studyId}/PatientFinance/WithdrawalOrder/Export?applyDateStart=${params?.applyDateStart}&orderNo=${params?.orderNo}&applyDateEnd=${params?.applyDateEnd}&applyMan=${params?.applyMan || ''}&siteId=${params?.siteId || ''}&status=${params?.status || ''}`
        )
      },
      // 新增-编辑
      financeApproveEditItemForm: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.financeApproveTableRef
      },
      onload: () => {
        getPatientFinanceSummary(studyId).then((res) => {
          state.moneyForm = res
        })
      }
    })
    onMounted(() => {
      state.onload()
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  }
})
</script>

<style scoped lang="less">
.head {
  width: 100%;
  background: #fff;
  padding: 20px 20px;
  box-sizing: border-box;
  h3 {
    margin: 0;
  }
}
.finance-hide-center {
  padding: 20px 0;
  color: #d9001b;
  font-size: 17px;
}
</style>
