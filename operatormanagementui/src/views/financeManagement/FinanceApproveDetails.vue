<template>
  <div class="receipts-details">
    <div class="receipts-title flex justify-between items-center flex-wrap">
      <div class="finance-hide-center flex">
        <div class="mr-12">
          <span>项目余额：</span>
          <span>￥{{ moneyForm?.studyFinanceAmount }}</span>
        </div>
        <div class="mr-12">
          <span>未提现金额：</span>
          <span>￥{{ moneyForm?.patientTotalAmount }}</span>
        </div>
        <div class="mr-12">
          <span>申请中金额：</span>
          <span>￥{{ moneyForm?.freezeTotalAmount }}</span>
        </div>
        <div class="mr-12">
          <span>已提现金额：</span>
          <span>￥{{ moneyForm?.withdrawalTotalAmount }}</span>
        </div>
      </div>
      <div class="ml-auto">
        <el-button v-if="detailsData?.status === 1 && roleBtnShow" :loading="loading" type="danger" @click="approverClick(1)">拒绝</el-button>
        <el-button v-if="detailsData?.status === 1 && roleBtnShow" :loading="loading" type="primary" @click="approverClick(2)">打款</el-button>
        <el-button :loading="loading" @click="backRefresh">返回</el-button>
      </div>
    </div>
    <div class="receipts-mesg mb-5">
      <div class="receipts-go flex justify-between items-center mb-3">
        <h3>申请人信息</h3>
      </div>
      <div class="receipts-msg-centent flex">
        <div class="mr-28">
          <span class="mr-3 compensate-color">中心</span>
          <span>{{ detailsData?.siteName }}</span>
        </div>
        <div class="mr-28">
          <span class="mr-3 compensate-color">受试者编号</span>
          <span>{{ detailsData?.applyMan }}</span>
        </div>
        <div class="mr-28">
          <span class="mr-3 compensate-color">姓名</span>
          <span>{{ detailsData?.applyManName }}</span>
        </div>
      </div>
    </div>
    <div class="receipts-mesg mb-5">
      <div class="flex justify-between items-center mb-10">
        <div class="flex">
          <h4>单号{{ detailsData?.orderNo }}</h4>
          <div class="ml-5">
            <span v-if="detailsData?.status === 1" style="color: #fac563;">处理中</span>
            <span v-else-if="detailsData?.status === 2" style="color: #4b7902;">已打款</span>
            <span v-else-if="detailsData?.status === -1" style="color: #d9001b;">已拒绝</span>
          </div>
        </div>
      </div>
      <div class="receipts-box">
        <div class="mb-5">
          <span class="compensate-color mr-3">提现方式</span>
          <span>{{ detailsData?.withdrawalTypeName }}</span>
        </div>
        <div class="mb-5">
          <span class="compensate-color mr-3">银行</span>
          <span>{{ detailsData?.bank }}</span>
        </div>
        <div class="mb-5">
          <span class="compensate-color mr-3">银行卡号</span>
          <span>{{ detailsData?.cardNumber }}</span>
        </div>
        <div class="mb-5">
          <span class="compensate-color mr-3">银行卡开户支行</span>
          <span>{{ detailsData?.openAccountBank }}</span>
        </div>
        <div class="mb-5">
          <span class="compensate-color mr-3">户名</span>
          <span>{{ detailsData?.accountName }}</span>
        </div>
        <div class="mb-5">
          <span class="compensate-color mr-3">身份证号</span>
          <span>{{ detailsData?.idCard }}</span>
        </div>
        <div class="mb-5">
          <span class="compensate-color mr-3">提现金额</span>
          <span v-if="detailsData?.amount">￥{{ detailsData.amount }}</span>
        </div>
      </div>
    </div>
    <div v-if="detailsData?.status === 2" class="receipts-mesg mb-5">
      <div class="mb-5">
        <h3>付款信息</h3>
      </div>
      <div class="receipts-payment">
        <div class="flex mb-5 flex-wrap">
          <div class="mr-12 flex">
            <span class="mr-3 compensate-color">付款日期</span>
            <span>{{ detailsData?.payDate }}</span>
          </div>
          <div class="mr-12 flex">
            <span class="mr-3 compensate-color">付款账户</span>
            <span>{{ detailsData?.payAccount }}</span>
          </div>
          <div class="flex">
            <span class="mr-3 compensate-color">交易凭证</span>
            <span>{{ detailsData?.transactionReceipt }}</span>
          </div>
        </div>
        <div class="flex">
          <div class="compensate-color whitespace-nowrap mr-3">
            附件
          </div>
          <div class="flex flex-wrap flex-1">
            <div v-for="(ite, idx) in detailsData.attachmentEditViewModels" :key="idx" class="img-size mr-3 overflow-hidden flex items-center">
              <el-image
                class="w-full"
                :src="ite.fileUrl"
                hide-on-click-modal
                lazy
                :preview-src-list="srcList"
                fit="cover"
                :initial-index="initialIndex"
                @click="imageInitialClick(idx)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 流程 -->
    <div class="receipts-mesg">
      <div class="font-semibold mb-3">流程</div>
      <!-- :active="2" -->
      <el-steps direction="vertical" space="120px">
        <el-step v-for="item in detailsData?.aprRuntimeLogViewModels" :key="item.detailCode">
          <template #title>
            <div class="flex justify-between font-normal compensate-color mb-3">
              <div class="flex-1">
                <div class="font-color-000">{{ item?.detailName }}</div>
                <div>{{ item?.remark }}</div>
                <div>{{ item?.comment }}</div>
              </div>
              <div class="auditor-all text-right">
                {{ item?.requestDate }}
              </div>
            </div>
          </template>
          <template #icon>
            <div class="icon-sty">
              <div class="icon-sty-in">
                <img v-if="item?.aprStatus === 'Processing'" src="@/assets/svg/pendingApprover.svg" alt="">
                <img v-else-if="item?.aprStatus === 'Approved'" src="@/assets/svg/passApprover.svg" alt="">
                <img v-else-if="item?.aprStatus === 'Rejected'" src="@/assets/svg/rejectApprover.svg" alt="">
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>

    <!-- 弹窗 -->
    <trial-dialog v-model="approverMyDialog" title="审批" class="!z-[2000]">
      <template #DialogBody>
        <el-form ref="approverFormRef" :model="approverForm" :rules="approverRules" label-position="top">
          <el-form-item label="审批意见" prop="comment">
            <el-input v-model="approverForm.comment" rows="8" maxlength="500" show-word-limit type="textarea" placeholder="请输入审批意见" />
          </el-form-item>
          <div v-if="btnIndex === 2">
            <div class="flex">
              <el-form-item label="付款日期" prop="payDate" class="margin-1" style="width: 30%">
                <el-date-picker
                  v-model="approverForm.payDate"
                  type="date"
                  style="width: 100%"
                  placeholder="年/月/日"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>
              <el-form-item label="付款账户" prop="payAccount" class="margin-1" style="width: 30%">
                <el-input
                  v-model="approverForm.payAccount"
                  class="w-full"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <el-form-item label="交易凭证" prop="transactionReceipt" class="margin-1" style="width: 30%">
                <el-input
                  v-model="approverForm.transactionReceipt"
                  class="w-full"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
            </div>
            <el-form-item label="附件（如转账记录截图等）">
              <div>
                <UploadImg ref="approverImgRef" :file-size="5" :http-request="httpRequest" :limit="99" />
              </div>
            </el-form-item>
          </div>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-center">
          <el-button :loading="loading" plain @click="approverMyDialog = false">取消</el-button>
          <el-button v-if="btnIndex === 1" :loading="Loading" type="danger" @click="saveData">确认拒绝</el-button>
          <el-button v-if="btnIndex === 2" :loading="Loading" type="primary" @click="saveData">确定打款</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { getPatientFinanceSummary, getWithdrawalOrderOrder, postWithdrawalOrder } from '@/api/financeManagement'
import { defineComponent, inject, nextTick, onMounted, reactive, toRefs } from 'vue'
import UploadImg from '@/components/UploadImg/index.vue'
import { useStore } from 'vuex'
import { postPublicDocumentFile } from '@/api/home'

export default defineComponent({
  name: 'FinanceApproveDetails', // 提现申请
  components: {
    UploadImg
  },
  props: {
    request: {
      type: Function,
      default: () => {}
    }
  },
  setup(props) {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      Loading: false,
      initialIndex: 0,
      moneyForm: {},
      detailsData: {},
      srcList: [],
      approverMyDialog: false,
      approverFormRef: null,
      approverForm: {
        comment: '',
        payDate: '',
        transactionReceipt: '',
        payAccount: '',
        status: 0,
      },
      btnIndex: 0,
      approverRules: {},
      approverImgRef: null,
      roleBtnShow: false,
      imageInitialClick: (index) => {
        state.initialIndex = index
      },
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.refresh()
        RESEARCHCENTER_INFOS.contentVisible = false
        props.request()
      },
      // 拒绝/打款
      approverClick: (index) => {
        state.approverForm = {
          comment: '',
          payDate: '',
          transactionReceipt: '',
          payAccount: '',
          status: 0,
        }
        state.btnIndex = index
        if (index === 1) {
          state.approverForm.status = -1
          state.approverMyDialog = true
          state.approverRules = {
            comment: [
              { required: true, message: '请输入', trigger: 'blur' },
            ]
          }
        } else if (index === 2) {
          if (state.detailsData?.amount > state.moneyForm?.studyFinanceAmount) {
            ElMessageBox.confirm(
              '本次提现金额已超出项目余额，请确认是否继续？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              }
            )
              .then(() => {
                state.approverMyDialog = true
                state.approverForm.status = 2
                state.disposeData()
              })
              .catch(() => {})
          } else {
            state.approverMyDialog = true
            state.approverForm.status = 2
            state.disposeData()
          }
        }
      },
      disposeData: () => {
        state.approverRules = {
          payDate: [
            { required: true, message: '请选择', trigger: 'blur' },
          ],
          transactionReceipt: [
            { required: true, message: '请选择', trigger: 'blur' },
          ],
          payAccount: [
            { required: true, message: '请选择', trigger: 'blur' },
          ],
        }
        if (state.detailsData?.attachmentEditViewModels) {
          nextTick(() => {
            if (state.approverImgRef) {
              state.approverImgRef.fileList = state.detailsData?.attachmentEditViewModels.map((item) => {
                item.url = item.fileUrl
                return item
              })
            }
          })
        }
      },
      // 弹窗保存
      saveData: () => {
        state.approverFormRef.validate((valid) => {
          if (valid) {
            state.approverForm.currentRuntimeId = state.detailsData?.currentRuntimeId || ''
            state.Loading = true
            const withdrawalOrderLoading = ElLoading.service({
              lock: true,
              text: 'Loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            if (state.btnIndex === 2) {
              state.approverForm.attachmentEditViewModels = state.approverImgRef.fileList.map((item) => {
                const dataImg = {
                  fileUrl: item.url,
                  id: item.id
                }
                return dataImg
              })
            }
            postWithdrawalOrder(studyId, state.detailsData?.patientId || '', RESEARCHCENTER_INFOS.researchContent?.id || '', state.approverForm).then(() => {
              ElMessage.success('保存成功')
              state.Loading = false
              state.approverMyDialog = false
              withdrawalOrderLoading.close()
              state.onLoad()
            }).catch(() => {
              state.Loading = false
              withdrawalOrderLoading.close()
            })
          }
        })
      },
      // 上传图片
      httpRequest: (fileObj) => {
        const myFormDataObj = new FormData()
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        const imgLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        postPublicDocumentFile(studyId, 3, 1, myFormDataObj).then((res) => {
          state.approverImgRef.fileList[state.approverImgRef.fileList.length - 1].url = res.fileUrl
          state.approverImgRef.fileList[state.approverImgRef.fileList.length - 1].id = res.id
          imgLoading.close()
        }).catch(() => {
          state.approverImgRef.fileList.splice(state.approverImgRef.fileList.length - 1, 1)
          imgLoading.close()
        })
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        getPatientFinanceSummary(studyId).then((res) => {
          state.moneyForm = res
        })
        const orderLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        getWithdrawalOrderOrder(studyId, RESEARCHCENTER_INFOS.researchContent?.id || '').then((res) => {
          if (res?.attachmentEditViewModels && Array.isArray(res.attachmentEditViewModels)) {
            state.srcList = res.attachmentEditViewModels.map((item) => item.fileUrl)
          }
          if (res?.canOperateUserIds && res?.canOperateUserIds.indexOf(store.state.studyItem.dctUserId) !== -1) {
            state.roleBtnShow = true
          }
          state.detailsData = res
          orderLoading.close()
        }).catch(() => {
          orderLoading.close()
        })
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.compensate-color {
  color: #9c9898;
}
.receipts-mesg {
  padding: 20px;
  background: #fff;
  h3 {
    margin: 0;
  }
  h4 {
    margin: 0;
  }
  .receipts-msg-centent {
    padding: 0 20px;
  }
  .receipts-box {
    padding: 0 20px;
  }
  .receipts-payment {
    padding: 0 20px;
  }
}
.receipts-title {
  padding: 20px 0;
  .finance-hide-center {
    color: #d9001b;
    font-size: 17px;
  }
}
.compensate-color {
  color: #9c9898;
}
.auditor-all {
  width: 30%;
  margin-left: 20px;
 }
.icon-sty {
  width: 24px;
  height: 24px;
  background: #5995FF;
  border-radius: 50%;
  position: relative;
  .icon-sty-in {
    width: 16px;
    height: 16px;
    position: absolute;
    bottom: -2px;
    right: -2px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.margin-1 {
  margin-right: 1%;
}
.img-size {
  width: 150px;
  height: 150px;
  border-radius: 5px;
}
</style>
