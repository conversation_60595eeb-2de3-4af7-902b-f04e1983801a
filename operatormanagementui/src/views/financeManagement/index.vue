<template>
  <div>
    <div class="head">
      <h3 v-html="studyName" />
    </div>
    <div class="flex finance-box mb-5">
      <div class="mr-28">
        <div class="mb-2">收入</div>
        <div class="font-semibold">￥{{ financeData?.inAmount }}</div>
      </div>
      <div class="mr-28">
        <div class="mb-2">冲减</div>
        <div class="font-semibold">￥{{ financeData?.offsettingAmount }}</div>
      </div>
      <div class="mr-28">
        <div class="mb-2">支出</div>
        <div class="font-semibold">￥{{ financeData?.outAmount }}</div>
      </div>
      <div class="mr-28">
        <div class="mb-2">余额</div>
        <div class="font-semibold">￥{{ financeData?.amount }}</div>
      </div>
    </div>
    <trial-table
      ref="financeManagementTableRef"
      title=""
      :request="getfinanceManagementList"
      :columns="financeManagementColumns"
      :pagination="false"
    >
      <template #toolbar>
        <el-button
          v-permission="['manage.financeManagement.createOrEdit']"
          type="primary" 
          @click="financeManagementEditItemForm(null, 0)"
        >
          新建
        </el-button>
        <el-button
          v-permission="['manage.financeManagement.exportExcel']"
          type="primary"
          @click="exportFinance"
        >
          导出
        </el-button>
      </template>
      <template #operate="scope">
        <el-button
          v-show="scope.row?.creator !== '系统'"
          v-permission="['manage.financeManagement.createOrEdit']"
          size="small"
          text
          type="primary"
          @click="financeManagementEditItemForm(scope.row, 2)"
        >
          编辑
        </el-button>
        <el-button 
          v-show="scope.row?.creator !== '系统'"
          v-permission="['manage.financeManagement.details']"
          size="small"
          text
          type="primary"
          @click="financeManagementEditItemForm(scope.row, 1)"
        >
          详情
        </el-button>
      </template>
      <template #remarkSlot="scope">
        <div class="whitespace-pre-wrap content-height" v-html="scope.row?.remark" />
      </template>
    </trial-table>
    <!-- 弹窗 -->
    <trial-dialog v-model="financeMyDialog" class="!z-[2000]" :title="myTitle">
      <template #DialogBody>
        <el-form ref="financeFormRef" :model="financeForm" :rules="financeRules" label-position="top">
          <div class="flex">
            <el-form-item label="发生日期" prop="changeDate" class="margin-1" style="width: 24%">
              <el-date-picker
                v-model="financeForm.changeDate"
                type="date"
                style="width: 100%"
                placeholder="年/月/日"
                value-format="YYYY-MM-DD"
                clearable
                :disabled="myTitle === '详情'"
              />
            </el-form-item>
            <el-form-item label="类型" prop="changeType" class="margin-1" style="width: 24%">
              <el-select
                v-model="financeForm.changeType"
                class="w-full"
                placeholder="请选择"
                clearable
                :disabled="myTitle === '详情'"
              >
                <el-option
                  v-for="(item, index) in opitons"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="金额(元)" prop="amount" class="margin-1" style="width: 24%">
              <el-input
                v-model.trim="financeForm.amount"
                class="w-full"
                placeholder="请输入"
                :disabled="myTitle === '详情'"
                maxlength="12"
                oninput="value=value.replace(/[^\d.]/g, '')
                  .replace(/^\./, '')
                  .replace(/(\.[^\.]*)\./g, '$1')
                  .replace(/^0+(\d)/, '$1')
                  .replace(/^(\d+\.\d\d).*$/, '$1')"
                @change="changeInput"
              />
            </el-form-item>
            <el-form-item label="交易凭证" prop="transactionReceipt" class="margin-1" style="width: 24%">
              <el-input
                v-model="financeForm.transactionReceipt"
                class="w-full"
                :placeholder="myTitle === '详情' ? '' : '请输入'"
                :disabled="myTitle === '详情'"
              />
            </el-form-item>
          </div>
          <el-form-item label="说明" prop="remark">
            <el-input v-model="financeForm.remark" rows="7" maxlength="9999" type="textarea" :placeholder="myTitle === '详情' ? '' : '请输入'" :disabled="myTitle === '详情'" />
          </el-form-item>
          <el-form-item label="附件（支持上传jpg,jpeg,png格式图片）">
            <div>
              <UploadImg ref="mateImgRef" :file-size="5" :before-file-upload-type="['.jpg', '.jpeg', '.png']" :disabled-img="disabledImg" :http-request="httpRequest" :limit="99" />
            </div>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-center">
          <el-button :loading="loading" plain @click="financeMyDialog = false">取消</el-button>
          <el-button
            v-if="myTitle !== '详情'"
            :loading="loading"
            type="primary"
            @click="saveData"
          >确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import UploadImg from '@/components/UploadImg/index.vue'
import { postPublicDocumentFile } from '@/api/home'
import { getStudyFinance, getStudyFinanceSummary, postStudyFinance } from '@/api/financeManagement'

export default defineComponent({
  name: 'FinanceManagement', // 项目款
  components: {
    UploadImg
  },
  setup() {
    const store = useStore()
    const { studyName, studyId } = store.state.studyItem
    const state = reactive({
      rolesBtnShow: false,
      loading: false,
      studyName: studyName,
      financeData: {},
      myTitle: '新建',
      financeMyDialog: false,
      financeManagementTableRef: null,
      mateImgRef: null,
      fileList: [],
      disabledImg: false,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      financeManagementColumns: [
        // { type: 'selection' },
        { label: '发生日期', prop: 'changeDate', minWidth: 130 },
        { label: '类型', prop: 'changeTypeName', minWidth: 120 },
        { label: '金额(元)', prop: 'amount', minWidth: 150 },
        { label: '交易凭证', prop: 'transactionReceipt', minWidth: 230 },
        { label: '说明', prop: 'remark', minWidth: 260, tdSlot: 'remarkSlot', },
        { label: '创建时间', prop: 'createTime', minWidth: 200 },
        { label: '创建人', prop: 'creator', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      financeFormRef: null,
      financeForm: {
        changeDate: '',
        changeType: '',
        amount: '',
        transactionReceipt: '',
        remark: ''
      },
      financeRules: {
        changeDate: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
        changeType: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        amount: [
          { required: true, message: '请输入', trigger: 'blur' },

        ],
      },
      opitons: [
        {
          name: '支出',
          value: -1
        },
        {
          name: '冲减',
          value: -2
        },
        {
          name: '收入',
          value: 1
        },
      ],
      httpRequest: (fileObj) => {
        const myFormDataObj = new FormData()
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        const imgLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        postPublicDocumentFile(studyId, 4, 1, myFormDataObj).then((res) => {
          state.mateImgRef.fileList[state.mateImgRef.fileList.length - 1].url = res.fileUrl
          state.mateImgRef.fileList[state.mateImgRef.fileList.length - 1].id = res.id
          imgLoading.close()
        }).catch(() => {
          state.mateImgRef.fileList.splice(state.mateImgRef.fileList.length - 1, 1)
          imgLoading.close()
        })
      },
      exportFinance: () => {
        window.open(
          `${
            window.location.origin
          }/api/Operator/Compensation/${studyId}/StudyFinance/Summary/Export`
        )
      },
      // 请求函数
      async getfinanceManagementList() {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const data = await getStudyFinanceSummary(studyId)
          state.financeData = data
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data?.studyFinanceChangeRecordEditViewModels || [],
          }
        } catch (e) {
          console.log(e)
        }
      },
      changeInput: (value) => {
        if (value.indexOf('.') !== -1 && !value.split('.')[1]?.length) {
          state.financeForm.amount = value.split('.')[0]
        } else {
          state.financeForm.amount = value
        }
      },
      // 刷新
      financeManagementRefresh: () => {
        //  RESEARCHCENTER_INFOS.resetMethod.handleReset()
        state.financeManagementTableRef.refresh()
      },
      saveData: () => {
        state.financeFormRef.validate((valid) => {
          if (valid) {
            state.financeForm.attachmentEditViewModels = state.mateImgRef.fileList.map((item) => {
              const dataImg = {
                fileUrl: item.url,
                id: item.id
              }
              return dataImg
            })
            state.opitons.forEach((item) => {
              if (item?.value === state.financeForm?.changeType) {
                state.financeForm.changeTypeName = item.name
              }
            })
            state.loading = true
            postStudyFinance(studyId, state.financeForm?.id || '', state.financeForm).then(() => {
              state.loading = false
              state.financeMyDialog = false
              state.financeManagementRefresh()
            }).catch(() => {
              state.loading = false
            })
          }
        })
      },
      // 新增-编辑
      financeManagementEditItemForm: (row, index) => {
        state.financeForm = {
          changeDate: '',
          changeType: '',
          amount: '',
          transactionReceipt: '',
          remark: ''
        }
        state.financeMyDialog = true
        if (index === 1) {
          state.myTitle = '详情'
          state.disabledImg = true
          state.gainDetail(row)
        } else if (index === 2) {
          state.disabledImg = false
          state.myTitle = '编辑'
          state.gainDetail(row)
        } else {
          state.disabledImg = false
          state.myTitle = '新建'
        }
      },
      gainDetail: (row) => {
        const financeLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        getStudyFinance(studyId, row?.id).then((res) => {
          if (res?.attachmentEditViewModels) {
            state.mateImgRef.fileList = res.attachmentEditViewModels.map((item) => {
              item.url = item.fileUrl
              return item
            })
          }
          state.financeForm = res
          financeLoading.close()
        }).catch(() => {
          financeLoading.close()
        })
      }
    })
    onMounted(() => {
      // 处理按钮权限
      const roles = ['admin', '财务']
      const role = store.state.account.userinfo?.roles || []
      state.rolesBtnShow = roles.some((item) => role.includes(item))
    })
    return {
      ...toRefs(state),
    }
  }
})
</script>

<style scoped lang="less">
.head {
  width: 100%;
  background: #fff;
  padding: 20px 20px;
  box-sizing: border-box;
  h3 {
    margin: 0;
  }
}
.finance-box {
  padding: 20px;
  background: #fff;
}
.margin-1 {
  margin-right: 1%;
}
.content-height {
  max-height: 100px;
  overflow-y: auto;
}
.content-height::-webkit-scrollbar {
  display: none;
}
</style>
