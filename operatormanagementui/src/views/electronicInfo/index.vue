<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :showbtnfalg-excel="rolesExportBtnShow"
        :request-export-excel="getResearchCenterExport"
        :pagination="paginationConfig"
      >
        <template #icfVersionNumber="scope">
          <div class="tdTooltip" v-for="item in scope.row.icfVersions">
            <div v-if="!showToolTip(item.icfVersionNumber)">
              {{ item.icfVersionNumber }}
            </div>
            <el-tooltip
              v-else
              class="box-item"
              effect="dark"
              :content="item.icfVersionNumber"
              placement="top"
            >
              {{ item.icfVersionNumber }}
            </el-tooltip>
          </div>
        </template>
        <template #icfVersionDate="scope">
          <div class="tdTooltip" v-for="item in scope.row.icfVersions">
            {{ item.icfVersionDate }}
          </div>
        </template>
        <template #operate="scope">
          <span
            v-permission="['manage.electronicInfo.details']"
            class="editBtnBlue"
            @click="editResearchCenterInfoItem(scope.row)"
          >
            详情
          </span>
        </template>
      </trial-table>
    </div>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <Details :request="onLoad" />
    </div>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import Details from '@/views/electronicInfo/ElectronicInfosDetails.vue'
import {
  getStudyICFVersionNumber, // 获取某课题下知情同意版本信息
  getPatientICFStatement, // 获取电子知情列表信息
  exportPatientICFStatement // 导出电子知情列表信息
} from '@/api/electronicInfo'

export default defineComponent({
  name: 'ElectronicInfo', // 电子知情
  components: {
    Details,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      rolesExportBtnShow: false,
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { label: '中心编号', prop: 'edcSiteCode', minWidth: 100 },
        { label: '中心名称', prop: 'siteName', minWidth: 250 },
        // { label: '昵称', prop: 'nickName', minWidth: 100 },
        { label: '姓名', prop: 'realName', minWidth: 100 },
        { label: '受试者编号', prop: 'patNumber', minWidth: 100 },
        { label: '知情版本', prop: 'icfVersionNumber', width: 300, tdSlot: 'icfVersionNumber', showOverflowTooltip: false },
        { label: '版本日期', prop: 'icfVersionDate', minWidth: 130, tdSlot: 'icfVersionDate', showOverflowTooltip: false },
        { label: '流程类型', prop: 'flowType', minWidth: 110 },
        { label: '流程状态', prop: 'icfStatus', minWidth: 80 },
        { label: '流程创建时间', prop: 'icfCreateDate', minWidth: 200 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        // labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '知情版本',
            name: 'studyICFVerId',
            defaultValue: null,
          },
          {
            type: 'select',
            label: '流程状态',
            name: 'patICFStatus',
            defaultValue: null,
            options: [
              {
                value: 1,
                name: '进行中',
              },
              {
                value: 2,
                name: '已结束',
              },
            ],
          },
          {
            label: '受试者信息',
            name: 'patNumber',
            type: 'input',
            defaultValue: null
          }
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      getResearchCenterExport: async (params) => {
        exportPatientICFStatement(studyId, params).then(res => {
          var blob = new Blob([Uint8Array.from(atob(res.item2), c => c.charCodeAt(0))], { type: 'application/octet-stream' });
          const url = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', res.item1)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) // 下载完成移除元素
          window.URL.revokeObjectURL(url) // 释放掉blob对象
        });
      },
      // 获取列表数据方法
      getResearchCenterList: async (params) => {
        try {
          const rest = await getPatientICFStatement(studyId, params)
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          //
        }
      },
      // 编辑某项中心
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      onLoad: async () => {
        getStudyICFVersionNumber(studyId).then((res) => {
          // 导出excel按钮权限配置
          // const roles = ['admin', 'PM', 'PI', 'SUBI', 'CRA', 'Audit', 'CRC']
          // const role = store.state.account.userinfo?.roles || []
          // manage.electronicInfo.exportExcel
          state.rolesExportBtnShow = store.state.studyItem.permissions.includes('manage.electronicInfo.exportExcel')
          // roles.some((item) => role.includes(item))
          const mergedIcfVerisons = [];
          const icfVersionOptions = []
          res.forEach(item => {
            mergedIcfVerisons.push(...item.icfVersions);
          });
          mergedIcfVerisons.forEach((item) =>
            icfVersionOptions.push({
              name: item.icfVersionNumber,
              value: item.id,
              label: item.icfVersionNumber
            })
          )
          state.searchConfig.fields[1].options = icfVersionOptions
        })
      },
      showToolTip: (txt) => {
        let cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0;
        let otCount = txt.length - cnCount
        return (cnCount * 2 + otCount) > 38
      }
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    h3 {
      margin: 0;
    }
  }
}
.tdTooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

