<template>
  <div class="informedVersionUpdate">
    <div>
      <div class="head">
        <h3 v-html="studyName" />
      </div>
      <trial-table
        ref="informedVersionUpdateRef"
        title=""
        :request="getInformedVersionUpdateList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
        hide-center
        max-height="500px"
        :showbtnfalg-excel="rolesExportBtnShow"
        :request-export-excel="getResearchCenterExport"
        @selectionChange="informedVersionUpdateSelectionChange"
      >
        <!-- 导出插槽 有权限-->
        <!-- <template #exportTableBtn="scope">
          <el-button
            v-if="exBtnShow"
            type="primary"
            size=""
            class="editBtnBlue"
            @click="handleExport"
          >
            导出
          </el-button>
        </template> -->
        <template #hideCenter>
          <div class="my-3 flex justify-end">
            <el-button
              v-permission="['manage.informedVersionUpdate.manualPush']"
              type="primary"
              @click="openManualPushDialog"
              >人工推送</el-button
            >
            <el-button
              v-permission="['manage.informedVersionUpdate.confirmPush']"
              type="primary"
              @click="editInformedVersionUpdateInfoItem(null, 2, '批量设置')"
              >批量设置</el-button
            >
          </div>
        </template>
        <template #patSlot="scope">
          <span>{{ scope.row?.patNumber }}</span>
        </template>
        <template #operate="scope">
          <div
            v-if="
              scope.row.pushStatus === 1 &&
              scope.row.icfCreateType != 2
            "
          >
            <el-button
              v-permission="['manage.informedVersionUpdate.confirmPush']"
              text
              type="primary"
              @click="
                editInformedVersionUpdateInfoItem(scope.row, 0, '确认推送')
              "
              >确认推送</el-button
            >
            <el-button
              v-permission="['manage.informedVersionUpdate.confirmPush']"
              text
              type="danger"
              @click="editInformedVersionUpdateInfoItem(scope.row, 1, '不适用')"
              >不适用</el-button
            >
          </div>
        </template>
        <template #icfVersionNumber="scope">
          <div class="tdTooltip" v-for="item in scope.row.icfVersions">
            <div v-if="!showToolTip(item.icfVersionNumber)">
              {{ item.icfVersionNumber }}
            </div>
            <el-tooltip
              v-else
              class="box-item"
              effect="dark"
              :content="item.icfVersionNumber"
              placement="top"
            >
              {{ item.icfVersionNumber }}
            </el-tooltip>
          </div>
        </template>
        <template #icfVersionDate="scope">
          <div class="tdTooltip" v-for="item in scope.row.icfVersions">
            {{ item.icfVersionDate }}
          </div>
        </template>
      </trial-table>
    </div>
    <trial-dialog v-model="dialogVisible" :title="myTitle">
      <template #DialogBody>
        <el-form
          ref="informedVersionUpdateFormRef"
          :model="myFormData"
          :rules="rules"
          label-position="top"
        >
          <el-form-item v-if="myTitle === '批量设置'" prop="nextBtn">
            <el-radio-group v-model="myFormData.nextBtn" class="flex">
              <el-radio :label="1">确认推送</el-radio>
              <el-radio :label="2">不适用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-else label="备注" prop="pushRemark">
            <el-input
              v-model="myFormData.pushRemark"
              type="textarea"
              maxlength="200"
              rows="8"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="centerflex" :class="{ 'mt-32': myTitle === '批量设置' }">
          <el-button :loading="loading" @click="dialogVisible = false"
            >取 消</el-button
          >
          <el-button
            v-if="myTitle !== '批量设置'"
            type="primary"
            :loading="loading"
            @click="submit(0)"
            >确 定</el-button
          >
          <el-button v-else type="primary" :loading="loading" @click="submit(1)"
            >下一步</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <trial-dialog
      v-model="manualPushVisible"
      title="创建推送"
      :my-dialog-body-style="{ top: '0', marginTop: '50px', marginBottom: '0' }"
    >
      <template #DialogBody>
        <el-form
          ref="manualPushRef"
          :model="formManualPush"
          :rules="checkRules"
          label-position="top"
        >
          <el-form-item
            required
            prop="siteId"
            label="中心"
            class="itmFirstLevel"
          >
            <el-select
              class="munualPushSite"
              v-model="formManualPush.siteId"
              placeholder="请选择中心"
              @change="siteChangeHandle"
            >
              <el-option
                v-for="site in sites"
                :key="site.siteId"
                :label="site.siteName"
                :value="site.siteId"
              >
                {{ site.siteName }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="patients" class="itmPatient">
            <el-button
              type="primary"
              @click="openPatientDialog"
              class="choosePatients"
              >选择受试者</el-button
            >
          </el-form-item>
          <trial-table
            :request="getPatientListData"
            ref="chosenPatRef"
            :columns="patColumns"
            max-height="500px"
            class="patList"
          ></trial-table>
          <el-form-item prop="consents" class="itmPatient">
            <el-button
              type="primary"
              @click="openConsentDialog"
              class="choosePatients"
              >选择知情版本</el-button
            >
          </el-form-item>
          <trial-table
            :request="getConsentListData"
            ref="chosenConsentRef"
            :columns="consentColumns"
            max-height="500px"
            class="patList"
          >
            <template #icfVersionNumber="scope">
              <div class="tdTooltip" v-for="item in scope.row.icfVersions">
                <div v-if="!showToolTip(item.icfVersionNumber)">
                  {{ item.icfVersionNumber }}
                </div>
                <el-tooltip
                  v-else
                  class="box-item"
                  effect="dark"
                  :content="item.icfVersionNumber"
                  placement="top"
                >
                  {{ item.icfVersionNumber }}
                </el-tooltip>
              </div>
            </template>
            <template #icfVersionDate="scope">
              <div class="tdTooltip" v-for="item in scope.row.icfVersions">
                {{ item.icfVersionDate }}
              </div>
            </template>
          </trial-table>
          <el-form-item required prop="remark" label="备注" class="itmRemark">
            <el-input
              v-model="formManualPush.remark"
              type="textarea"
              maxlength="200"
              rows="8"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="centerflex">
          <el-button @click="manualPushVisible = false">取 消</el-button>
          <el-button
            type="primary"
            :loading="submitManualPushLoading"
            @click="submitManualPush()"
            >确 定</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="patientDialogVisible" title="选择受试者">
      <template #DialogBody>
        <el-transfer
          v-model="transferValue"
          filterable
          :titles="['未选择的受试者', '已选择的受试者']"
          class="flex justify-center"
          :props="transferProps"
          :data="transferData"
          filter-placeholder="请输入受试者编号或姓名"
        />
      </template>
      <template #footer>
        <div class="centerflex">
          <el-button @click="patientDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="savePatients()">确 定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog
      v-model="consentDialogVisible"
      title="选择知情版本"
      :my-dialog-body-style="{ width: '70%' }"
    >
      <template #DialogBody>
        <trial-table
          ref="informedVersionDetailsRef"
          title=""
          border
          :request="getResearchCenterList"
          :columns="verColumns"
        >
          <template #icfVersionNumberStr="scope">
            <div class="tdTooltip" v-for="item in scope.row.icfVersions">
              <div v-if="!showToolTipDialog(item.icfVersionNumber)">
                {{ item.icfVersionNumber }}
              </div>
              <el-tooltip
                v-else
                class="box-item"
                effect="dark"
                :content="item.icfVersionNumber"
                placement="top"
              >
                {{ item.icfVersionNumber }}
              </el-tooltip>
            </div>
          </template>
          <template #icfVersionDateStr="scope">
            <div v-for="(e, idx) in scope.row.icfVersions" :key="idx">
              {{ e.icfVersionDate }}
            </div>
          </template>
          <template #operate="scope">
            <span
              v-show="scope.row.isEnable === true"
              style="color: #1e87f0; cursor: pointer"
              @click="saveConsent(scope.row)"
              >选 择</span
            >
          </template>
        </trial-table>
      </template>
      <template #footer>
        <div class="centerflex">
          <el-button @click="consentDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, nextTick, onBeforeMount, reactive, toRefs } from 'vue'
import {
  getICFStatementPush,
  getStudyICFVersionNumber,
  postICFStatementPushSetStatus,
  exportICFStatementPush,
  getPatientList,
  getTemplateVersion,
  validManualPush,
  saveManualPush,
} from '@/api/electronicInfo'

export default defineComponent({
  name: 'InformedVersionUpdate', // 版本更新推送
  setup() {
    const store = useStore()
    const { studyId } = store.state.account.userinfo
    // const router = useRouter()
    const selectable = (row) => {
      return row.pushStatus === 1
    }
    const state = reactive({
      // exBtnShow: false,
      submitManualPushLoading: false,
      chosenPatRef: null,
      chosenConsentRef: null,
      transferValue: [],
      transferData: [],
      transferPatientList: [],
      transferProps: {
        key: 'id',
        label: 'displayValueStr',
      },
      patientDialogVisible: false,
      consentDialogVisible: false,
      manualPushVisible: false,
      formManualPush: {
        siteId: '',
        patients: [],
        consents: [],
        remark: '',
      },
      manualPushRef: null,
      checkRules: reactive<FormRules<typeof formManualPush>>({
        siteId: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (state.formManualPush.siteId === '') {
                callback(new Error('请选择'))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        patients: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (state.formManualPush.patients.length === 0) {
                callback(new Error('请选择'))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        consents: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (state.formManualPush.consents.length === 0) {
                callback(new Error('请选择'))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
        remark: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (state.formManualPush.remark === '') {
                callback(new Error('请输入'))
              }
              callback()
            },
            trigger: 'blur',
          },
        ],
      }),
      rolesExportBtnShow: false,
      informedVersionUpdateRef: null,
      sites: store.state.studyItem.sites,
      studyName: store.state.studyItem.studyName,
      rolesBtnShow: false,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { type: 'selection', selectable }, // table勾选框
        { label: '创建时间', prop: 'icfCreateDate', minWidth: 180 },
        { label: '创建类型', prop: 'icfCreateTypeDesc', minWidth: 100 },
        { label: '中心', prop: 'siteName', minWidth: 230 },
        { label: '姓名', prop: 'realName', minWidth: 120 },
        {
          label: '受试者编号',
          prop: 'patNumber',
          minWidth: 120,
          tdSlot: 'patSlot',
        },
        { label: '受试者状态', prop: 'groupStatusDesc', minWidth: 100 },
        {
          label: '版本名称',
          prop: 'icfVersionNumber',
          minWidth: 300,
          tdSlot: 'icfVersionNumber',
          showOverflowTooltip: false,
        },
        {
          label: '版本日期',
          prop: 'icfVersionDate',
          minWidth: 130,
          tdSlot: 'icfVersionDate',
          showOverflowTooltip: false,
        },
        { label: '流程类型', prop: 'flowType', minWidth: 130 },
        { label: '状态', prop: 'pushStatusDesc', minWidth: 110 },
        { label: '操作人', prop: 'pushDoctor', minWidth: 150 },
        { label: '备注', prop: 'pushRemark', minWidth: 200 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
          minWidth: 200,
        },
      ],
      patColumns: [
        { label: '受试者编号', prop: 'patientNo', minWidth: 100 },
        { label: '受试者姓名', prop: 'realName', minWidth: 100 },
        { label: '状态', prop: 'groupStatus', minWidth: 100 },
      ],
      consentColumns: [
        {
          label: '版本名称',
          prop: 'icfVersionNumber',
          minWidth: 200,
          tdSlot: 'icfVersionNumber',
          showOverflowTooltip: false,
        },
        {
          label: '版本日期',
          prop: 'icfVersionDate',
          width: 250,
          tdSlot: 'icfVersionDate',
          showOverflowTooltip: false,
        },
      ],
      verColumns: [
        {
          label: '版本名称',
          tdSlot: 'icfVersionNumberStr',
          width: 250,
          showOverflowTooltip: false,
        },
        { label: '版本日期', tdSlot: 'icfVersionDateStr', minWidth: 150 },
        { label: '流程类型', prop: 'flowType', minWidth: 150 },
        { label: '流程元素', prop: 'icfFeature', minWidth: 500 },
        { label: '状态', prop: 'enableStatus', minWidth: 100 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        // labelWidth: '90px', // 必须带上单位
        inputWidth: '175px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心',
            name: 'siteId',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            type: 'select',
            label: '创建类型',
            name: 'icfCreateType',
            defaultValue: null,
            options: [
              {
                value: 1,
                name: '系统创建',
              },
              {
                value: 2,
                name: '人工创建',
              },
            ],
            filterable: true,
          },
          {
            type: 'select',
            label: '版本名称',
            name: 'studyICFVerId',
            defaultValue: null,
            options: [],
          },
          {
            label: '受试者信息',
            name: 'patNumber',
            type: 'input',
            defaultValue: null,
          },
          {
            type: 'select',
            label: '状态',
            name: 'pushStatus',
            defaultValue: null,
            options: [
              {
                value: 1,
                name: '待确认',
              },
              {
                value: 2,
                name: '已推送',
              },
              {
                value: 3,
                name: '不适用',
              },
            ],
          },
        ],
      },
      // 人工推送
      saveConsent: (row) => {
        state.formManualPush.consents = [row]
        state.consentDialogVisible = false
        state.chosenConsentRef.tableData = state.formManualPush.consents
        state.manualPushRef.validateField('consents', (valid) => {})
      },
      showToolTipDialog: (txt) => {
        let cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0
        let otCount = txt.length - cnCount
        return cnCount * 2 + otCount > 28
      },
      getResearchCenterList: async () => {
        const res = await getTemplateVersion(state.formManualPush.siteId)
        return {
          data: res || [],
        }
      },
      openConsentDialog: () => {
        if (state.formManualPush.siteId == '') {
          ElMessage.warning('请选择中心')
          state.manualPushRef.validateField('siteId', (valid) => {})
          return
        }
        state.consentDialogVisible = true
      },
      openManualPushDialog: () => {
        state.manualPushVisible = true
        nextTick(() => {
          state.chosenPatRef.tableData = []
        })
      },
      siteChangeHandle: () => {
        //更改中心，重置受试者和知情版本
        state.chosenPatRef.tableData = []
        state.formManualPush.consents = []
        state.chosenConsentRef.tableData = []
        state.formManualPush.patients = []
      },
      submitManualPush: () => {
        state.submitManualPushLoading = true
        state.manualPushRef.validate((valid) => {
          if (valid) {
            let param = {
              siteId: state.formManualPush.siteId,
              patientIds: state.formManualPush.patients.map((a) => a.patientId),
              siteAndICFId: state.formManualPush.consents[0].id,
              remark: state.formManualPush.remark,
            }

            validManualPush(studyId, param).then((res) => {
              if (res && res?.code == '0') {
                saveManualPush(studyId, param).then((resSave) => {
                  if (resSave) {
                    state.resetForm()
                    state.manualPushVisible = false
                    ElMessage.success('推送成功')
                  }
                })
              } else if (res && res?.code == '2') {
                ElMessageBox.confirm(
                  res.msg + '<br/>' + res.patientInfo,
                  '提示',
                  {
                    confirmButtonText: '确认推送',
                    cancelButtonText: '返回',
                    dangerouslyUseHTMLString: true,
                    cancelButtonClass: 'noDiaBtn',
                    confirmButtonClass: 'yesDiaBtn',
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                  }
                )
                  .then(() => {
                    saveManualPush(studyId, param).then((resSave) => {
                      if (resSave) {
                        state.resetForm()
                        state.manualPushVisible = false
                        ElMessage.success('推送成功')
                      }
                    })
                  })
                  .catch(() => {
                    state.resetForm()
                    state.manualPushVisible = false
                  })
              } else if (res && res?.code == '1') {
                ElMessageBox.alert(
                  res.msg + '<br/>' + res.patientInfo,
                  '提示',
                  {
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: '返回',
                    dangerouslyUseHTMLString: true,
                    cancelButtonClass: 'cancelDiaBtn',
                  }
                )
                  .then(() => {
                    state.resetForm()
                    state.manualPushVisible = false
                  })
                  .catch(() => {
                    state.submitManualPushLoading = false
                  })
              }
            })
          } else {
            state.submitManualPushLoading = false
          }
        })
      },
      resetForm: () => {
        state.formManualPush.siteId = ''
        state.formManualPush.remark = ''
        state.informedVersionUpdateRef.refresh()
        state.transferValue = []
        state.submitManualPushLoading = false
      },
      openPatientDialog: () => {
        state.getPatientList()
        state.patientDialogVisible = true
      },
      savePatients: () => {
        state.formManualPush.patients = state.transferPatientList.filter(
          (t) => state.transferValue.indexOf(t.patientId) > -1
        )
        state.patientDialogVisible = false
        state.chosenPatRef.tableData = state.formManualPush.patients
        state.manualPushRef.validateField('patients', (valid) => {})
      },
      getPatientListData: () => {
        return state.formManualPush.patients
      },
      getConsentListData: () => {
        return state.formManualPush.consents
      },
      // 导出版本更新列表
      getResearchCenterExport: async (params) => {
        exportICFStatementPush(studyId, params).then((res) => {
          var blob = new Blob(
            [Uint8Array.from(atob(res.item2), (c) => c.charCodeAt(0))],
            { type: 'application/octet-stream' }
          )
          const url = window.URL.createObjectURL(new Blob([blob]))
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.setAttribute('download', res.item1)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link) // 下载完成移除元素
          window.URL.revokeObjectURL(url) // 释放掉blob对象
        })
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      dialogVisible: false,
      informedVersionUpdateFormRef: null,
      myFormData: {
        pushRemark: '',
        nextBtn: null,
        icfStatementIds: [],
        pushStatus: null,
        patientIds: [],
      },
      rules: {},
      myTitle: '',
      loading: false,
      informedVersionUpdateSelectionArr: [],
      informedRow: null,
      // 导出
      // handleExport: () => {
      //   ElMessage('暂未完成导出功能接口对接')
      // },
      // 获取列表数据方法
      getInformedVersionUpdateList: async (params) => {
        try {
          const rest = await getICFStatementPush(
            store.state.studyItem.studyId,
            params
          )
          // 必须要返回一个对象，包含data数组和total总数
          // if (rest?.items) {
          //   rest.items.forEach(el => {
          //     if (el.groupStatus === 1) {
          //       el.groupStatusText = '待入组'
          //     } else if (el.groupStatus === 2) {
          //       el.groupStatusText = '已入组'
          //     } else if (el.groupStatus === 3) {
          //       el.groupStatusText = '完成实验'
          //     } else if (el.groupStatus === 4) {
          //       el.groupStatusText = '已脱落'
          //     } else if (el.groupStatus === 5) {
          //       el.groupStatusText = '筛选失败'
          //     } else if (el.groupStatus === 7) {
          //       el.groupStatusText = '知情中'
          //     } else if (el.groupStatus === 8) {
          //       el.groupStatusText = '筛选中'
          //     } else {
          //       el.groupStatusText = ''
          //     }
          //   })
          // }
          return {
            data: rest?.items || [],
            total: +rest.totalItemCount,
          }
        } catch (e) {
          //
        }
      },
      submit: (index) => {
        state.informedVersionUpdateFormRef.validate((valid) => {
          if (valid) {
            if (index === 0) {
              state.myFormData.icfStatementIds = []
              if (state.informedRow && state.informedRow?.icfStatementId) {
                state.myFormData.icfStatementIds.push(
                  state.informedRow.icfStatementId
                )
                state.myFormData.patientIds.push(state.informedRow.dctPatientId)
              } else {
                const icfStatementIds =
                  state.informedVersionUpdateSelectionArr.map(
                    (item) => item?.icfStatementId
                  )
                const patientIds = state.informedVersionUpdateSelectionArr.map(
                  (item) => item?.dctPatientId
                )
                state.myFormData.icfStatementIds = icfStatementIds
                state.myFormData.patientIds = patientIds
              }
              if (
                state.myTitle === '批量设置-不适用' ||
                state.myTitle === '不适用'
              ) {
                state.myFormData.pushStatus = 3
              } else if (
                state.myTitle === '批量设置-确认推送' ||
                state.myTitle === '确认推送'
              ) {
                state.myFormData.pushStatus = 2
              }
              state.loading = true
              postICFStatementPushSetStatus(studyId, state.myFormData)
                .then(() => {
                  state.dialogVisible = false
                  state.loading = false
                  ElMessage.success('保存成功')
                  state.informedVersionUpdateRef.refresh()
                })
                .catch(() => {
                  state.loading = false
                })
            } else if (index === 1) {
              if (state.myFormData?.nextBtn === 1) {
                state.editInformedVersionUpdateInfoItem(
                  null,
                  0,
                  '批量设置-确认推送'
                )
              } else if (state.myFormData?.nextBtn === 2) {
                state.editInformedVersionUpdateInfoItem(
                  null,
                  1,
                  '批量设置-不适用'
                )
              }
            }
          }
        })
      },
      informedVersionUpdateSelectionChange: (arr) => {
        state.informedVersionUpdateSelectionArr = arr
      },
      // 编辑某项中心
      editInformedVersionUpdateInfoItem: (row, index, name) => {
        state.myFormData = {
          pushRemark: '',
          nextBtn: null,
          icfStatementIds: [],
          pushStatus: null,
          patientIds: [],
        }
        state.rules = {}
        state.informedRow = row
        if (index === 1) {
          state.rules = {
            pushRemark: [
              { required: true, message: '请输入', trigger: 'blur' },
            ],
          }
        } else if (index === 2) {
          if (state.informedVersionUpdateSelectionArr.length < 2) {
            ElMessage.warning('请选择两条或以上的数据')
            return
          }
          state.rules = {
            nextBtn: [{ required: true, message: '请选择', trigger: 'change' }],
          }
        }
        state.myTitle = name
        state.dialogVisible = true
      },
      onLoad: async () => {
        getStudyICFVersionNumber(studyId).then((res) => {
          // 导出excel按钮权限配置
          // manage.informedVersionUpdate.exportExcel
          state.rolesExportBtnShow = store.state.studyItem.permissions.includes('manage.informedVersionUpdate.exportExcel') // roles.some((item) => role.includes(item))
          const mergedIcfVerisons = []
          const icfVersionOptions = []
          res.forEach((item) => {
            mergedIcfVerisons.push(...item.icfVersions)
          })
          mergedIcfVerisons.forEach((item) =>
            icfVersionOptions.push({
              name: item.icfVersionNumber,
              value: item.id,
              label: item.icfVersionNumber,
            })
          )
          state.searchConfig.fields[2].options = icfVersionOptions
        })
      },
      getPatientList: () => {
        state.transferData = []
        getPatientList(studyId, state.formManualPush.siteId).then((res) => {
          if (res) {
            state.transferPatientList = res.items
            res.items.forEach((itm) => {
              state.transferData.push({
                id: itm.patientId,
                displayValueStr:
                  itm.patientNo + '  ' + itm.realName + '  ' + itm.groupStatus,
              })
            })
          }
        })
      },
      showToolTip: (txt) => {
        let cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0
        let otCount = txt.length - cnCount
        return cnCount * 2 + otCount > 38
      },
    })
    onBeforeMount(() => {
      // const roles = ['admin', 'PI', 'SUBI', 'CRC']
      // const role = store.state.account?.userinfo?.roles || []
      // state.rolesBtnShow = roles.some((item) => role.includes(item))
      // if (!state.rolesBtnShow) {
      //   state.columns.splice(0, 1)
      // }
      // 导出按钮权限
      // const exBtnShow = ['admin', 'PM','PI', 'SUBI', 'CRA','Audit', 'CRC']
      // state.exBtnShow = exBtnShow.some((item) => role.includes(item))
      state.onLoad()
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    h3 {
      margin: 0;
    }
  }
}
.tdTooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.my-dialog-body) {
  & > h4 {
    margin-top: 0;
  }
}
.my-dialog {
  z-index: 1000 !important;
}
.munualPushSite {
  width: 320px;
}
.itmPatient {
  margin-top: 20px;
  margin-bottom: 20px;
  display: inline-flex;
  & > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: static;
      margin-left: 10px;
    }
  }
}
.centerflex {
  margin-top: 20px;
}
:deep(.el-transfer-panel__item) {
  & > .el-checkbox__label {
    white-space: pre;
  }
}
.patList {
  & > :deep(.my-el-table),
  :deep(.head) {
    padding-left: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
}
.itmFirstLevel {
  margin-bottom: 0;
}
.itmRemark {
  padding-right: 20px;
  margin-top: 20px;
}
</style>
<style>
.cancelDiaBtn {
  position: relative;
  right: calc(50% - 30px);
  margin-top: 10px;
}
.noDiaBtn {
  position: relative;
  right: calc(50% - 80px);
  margin-top: 10px;
}
.yesDiaBtn {
  position: relative;
  right: calc(50% - 80px);
  margin-top: 10px;
}
</style>
