<template>
  <div class="details">
    <div class="top">
      <h3 v-html="studyName"></h3>
      <div>
        <el-button @click="backRefresh">返回</el-button>
        <el-button
          v-show="
            DetailsList?.patientICFStatement?.icfStatus !== '已结束'
          "
          v-permission="['manage.electronicInfo.switchToOfflineConsent']"
          type="primary"
          @click="transferLine"
          >转线下知情</el-button
        >
      </div>
    </div>
    <div v-show="!showImg" class="content-top">
      <div class="bottom">
        <div>
          <span>中心名称:</span>
          <span>{{ DetailsList?.patientICFStatement?.siteName }}</span>
        </div>
        <div>
          <span>姓名:</span>
          <span>{{ DetailsList?.patientICFStatement?.realName }}</span>
        </div>
        <div>
          <span>受试者编号:</span>
          <span>{{ DetailsList?.patientICFStatement?.patNumber }}</span>
        </div>
      </div>
      <div class="bottom">
        <div>
          <span>知情版本:</span>
          <div class="divWrap">
            <span
              v-for="(item, index) in DetailsList?.patientICFStatement
                ?.icfVersions"
              :key="index"
              :class="index == 0 ? 'firstLevelSpan' : 'nextLevelSpan'"
            >
              {{ item.icfVersionNumber }}
            </span>
          </div>
        </div>
        <div>
          <span>版本日期:</span>
          <!-- <span>{{ DetailsList?.patientICFStatement?.icfVersionDate }}</span> -->
          <div class="divWrap">
            <span
              v-for="(item, index) in DetailsList?.patientICFStatement
                ?.icfVersions"
              :key="index"
              :class="index == 0 ? 'firstLevelSpan' : 'nextLevelSpan'"
            >
              {{ item.icfVersionDate }}
            </span>
          </div>
        </div>
        <div />
      </div>
      <div class="bottom">
        <div>
          <span>流程类型:</span>
          <span>{{ DetailsList?.patientICFStatement?.flowType }}</span>
        </div>
        <div>
          <span class="title">流程状态:</span>
          <span
            :class="
              DetailsList?.patientICFStatement?.icfStatus == '进行中'
                ? 'conduct'
                : 'end'
            "
          >
            {{ DetailsList?.patientICFStatement?.icfStatus }}
          </span>
        </div>
        <div />
      </div>
      <h3 class="content-title">知情轨迹</h3>
      <div
        v-for="(item, index) in DetailsList.icfStatusHistories"
        :key="index"
        class="contentBox"
      >
        <div class="childrenList">
          <div
            v-html="
              showIcfStatusStr(
                index,
                item?.icfStatusStr,
                DetailsList?.icfStatusHistories
              )
            "
          />
          <div>
            <span
              v-if="item?.doctorName && item?.icfStatus == 902"
              v-html="item?.innerLastUpdate"
            />
            <span
              style="margin-left: 20px; display: block"
              v-if="item?.doctorName && item?.icfStatus == 902"
              :style="{
                display: item?.icfStatus == 902 ? 'unset' : '',
              }"
              v-html="item?.doctorName"
            />
            <!-- 发起签署模块 -->
            <span
              v-if="item?.icfStatus == 902 && item?.initSignRemark"
              v-for="rmkItem in item?.initSignRemark"
              style="display: block; margin-left: 0; margin-top: 20px"
              v-html="rmkItem"
            ></span>
            <span
              v-if="
                item?.lastUpdate &&
                item?.icfStatus != 902 &&
                item?.conferenceId == null
              "
              v-html="item?.lastUpdate"
            />
            <span
              v-if="
                item?.lastUpdate &&
                item?.icfStatus != 902 &&
                item?.conferenceId != null
              "
              >{{ item?.lastUpdate }}</span
            >
            <span
              class="meetingDetail"
              v-if="
                item?.lastUpdate &&
                item?.icfStatus != 902 &&
                item?.conferenceId != null
              "
              ><el-button
                type="primary"
                link
                @click="showMeetingDetail(item?.conferenceId)"
                style="font-size: 16px"
                >详 情</el-button
              ></span
            >
            <span
              v-if="item?.audio"
              class="underline whitespace-nowrap ml-[20px]"
              @click="audioClick(item)"
              >{{ item?.audio }}</span
            >
            <span
              v-if="item?.doctorName && item?.icfStatus != 902"
              v-html="item?.doctorName"
            />
            <span
              v-if="
                [1002, 1003, 1004, 4004, 4005, 4006].includes(item?.icfStatus)
              "
            >
              {{ item?.title }}
            </span>
            <span
              v-if="
                item?.icfStatus != 902 &&
                item?.icfRemark &&
                item?.icfStatus != 400
              "
              class="icfRemark"
              v-html="item?.icfRemark"
            />
          </div>
        </div>
      </div>
      <div
        v-for="(item, index) in DetailsList?.patientICFStatement?.icfVersions"
        class="singleButton"
      >
        <div>{{ index == 0 ? '知情同意书' : '' }}</div>
        <div>
          <span>{{ item.icfVersionNumber }}</span>
          <div>
            <el-button
              v-permission="['manage.electronicInfo.previewConsentForm']"
              type="primary"
              @click="goPdfBook(item.studyICFUrl)"
              link
              style="font-size: 16px"
              >预 览</el-button
            >
          </div>
          <div>
            <el-button
              v-permission="['manage.electronicInfo.downloadConsentForm']"
              link
              type="primary"
              style="font-size: 16px"
              @click="
                downloadPdf(
                  item.studyICFUrl,
                  item.icfVersionNumber,
                  item.icfVersionDate
                )
              "
            >
              下 载
            </el-button>
          </div>
        </div>
      </div>
      <div
        v-if="DetailsList?.patientICFStatement?.showVerifySignResult"
        v-for="(item, index) in DetailsList?.patientICFStatement?.icfVersions"
        class="singleButton"
      >
        <div>{{ index == 0 ? '验签结果' : '' }}</div>
        <div>
          <span>{{ item.icfVersionNumber }}</span>
          <el-button
            v-permission="['manage.electronicInfo.previewSignatureResult']"
            link
            type="primary"
            style="font-size: 16px; margin-left: 20px"
            @click="previewResult(item.id)"
            >预 览</el-button
          >
        </div>
      </div>
      <div
        class="singleButton"
        v-if="DetailsList?.patientICFStatement?.patientICFStatus === 404"
      >
        <div>线下签署文件</div>
        <div>
          <span>
            <el-upload
              v-permission="['manage.electronicInfo.uploadOfflineSignedFile']"
              v-model:file-list="fileList"
              class="upload-file"
              accept=".pdf"
              multiple
              action="#"
              :before-upload="beforeUpload"
              :http-request="uploadSignatureFile"
              :show-file-list="false"
            >
              <el-button type="primary">上 传</el-button>
              <template #tip>
                <div class="el-upload__tip">支持pdf格式，大小不超过10M</div>
              </template>
            </el-upload>
          </span>
        </div>
      </div>
      <div v-if="DetailsList?.patientICFStatement?.patientICFStatus === 404">
        <div
          v-for="(file, fileIndex) in offlineFiles"
          :key="fileIndex"
          class="singleButton"
        >
          <div></div>
          <div>
            <span>{{ file.signedFileName }}</span>
            <span style="margin: 0 10px; color: #9c9898">{{
              ' 操作人：' +
              (file.fileOperatorName == null ? '' : file.fileOperatorName) +
              ' 操作时间：' +
              (file.fileUploadTime == null ? '' : file.fileUploadTime) +
              ' '
            }}</span>
            <div v-if="!file.isDeleted" class="btnPreview">
              <el-button
                v-permission="['manage.electronicInfo.previewOfflineSignedFile']"
                type="primary"
                @click="goPdfBook(file.signedFileUrl)"
                link
                style="font-size: 16px"
                >预 览</el-button
              >
            </div>
            <div v-if="!file.isDeleted" class="btnDownload">
              <el-button
                v-permission="['manage.electronicInfo.downloadOfflineSignedFile']"
                link
                type="primary"
                style="font-size: 16px"
                @click="
                  downloadOfflineSignatureFile(
                    file.signedFileUrl,
                    file.signedFileName
                  )
                "
              >
                下 载
              </el-button>
            </div>
            <div v-if="!file.isDeleted" class="btnDelete">
              <el-button
                v-permission="['manage.electronicInfo.deleteOfflineSignedFile']"
                link
                type="danger"
                class="deleteFile"
                @click="
                  deleteOfflineSignatureFile(file.patientICFStatementFileId)
                "
              >
                删 除
              </el-button>
            </div>
            <div v-if="file.isDeleted" class="deleted">
              已删除{{
                file.fileDeleteOperatorName == null
                  ? ''
                  : file.fileDeleteTime == ''
                  ? '（' + file.fileDeleteOperatorName + '）'
                  : '（' +
                    file.fileDeleteOperatorName +
                    '，' +
                    file.fileDeleteTime +
                    '）'
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <trial-dialog
      v-model="dialogVisible"
      :my-dialog-body-style="{ width: '40%' }"
      class="electronicInfosDetails"
    >
      <template #DialogBody>
        <div class="mb-5">
          <span class="mr-5 ft-18-px font-semibold">转线下知情</span>
          <span class="electronic-tips"
            >将跳过当前线上知情流程，请及时进行线下知情，线下签署后可按需上传签署文件</span
          >
        </div>
        <el-form
          ref="electronicInfosDetailsRef"
          :model="myFormData"
          :rules="rules"
          label-position="top"
        >
          <el-form-item label="说明" prop="remark">
            <el-input
              v-model="myFormData.remark"
              type="textarea"
              maxlength="200"
              rows="8"
              show-word-limit
              placeholder="请补充转线下知情的原因及相关信息"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="centerflex">
          <el-button :loading="loading" @click="dialogVisible = false"
            >取 消</el-button
          >
          <el-button type="primary" :loading="loading" @click="submit"
            >确 定</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <!-- 弹窗 -->
    <trial-dialog
      v-model="poseDialog"
      :my-dialog-body-style="{ width: '40%' }"
      class="electronicInfosDetails"
    >
      <template #DialogBody>
        <div>
          <div class="text-[18px] font-semibold">{{ audiosObj?.title }}</div>
          <div class="my-[30px]">
            首次{{ poseDialogTypeFlag === 1 ? '观看' : '阅读' }}：{{ audiosObj?.innerLastUpdate }}
            <span class="ml-[20px]"
              >共计{{ poseDialogTypeFlag === 1 ? '观看' : '阅读' }}：{{ audiosObj?.viewingDuration }}</span
            >
          </div>
          <div class="mb-[20px]">{{ audiosObj?.audio }}</div>
          <audio
            v-if="audioSrc"
            style="display: none"
            ref="audioPlayer"
            controls
            @ended="handleEnded"
          >
            <source :src="audioSrc" type="audio/mpeg" />
          </audio>
          <div v-if="audiosObj?.audios">
            <div
              v-for="(ite, index) in audiosObj.audios"
              :key="index"
              class="mb-[20px] flex"
            >
              <span>{{ ite?.createDateStr }}</span>
              <span class="mx-[10px]">{{ ite?.totalSeconds || 0 }}秒</span>
              <span
                v-if="ite?.stopPlayShow"
                class="editBtnBlue"
                @click="playAudio(ite)"
                >播放</span
              >
              <span v-else class="editBtnBlue" @click="stopAudio(ite)"
                >暂停</span
              >
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="centerflex">
          <el-button @click="poseDialog = false">关 闭</el-button>
        </div>
      </template>
    </trial-dialog>
    <el-dialog v-model="showPdfDialog" top="5vh" width="60%" title="预 览">
      <div class="pdf-preview-box">
        <canvas
          class="pdf-viewer"
          v-for="i in pdfParams.pdfPageTotal"
          :key="i"
          :id="'pdf-render' + i"
        ></canvas>
      </div>
    </el-dialog>
    <!-- 验签结果 -->
    <el-dialog v-model="showImg" top="5vh" width="70%" title="文件验签结果">
      <div v-if="showImg" class="content-img-box">
        <!-- <div class="all-box flex justify-end">
          <el-button :loading="loading" @click="showImg = false"
            >返 回</el-button
          >
          <el-button :loading="loading" type="primary" @click="downloadImg"
            >下 载</el-button
          >
        </div> -->
        <div ref="boxRef" class="all-box" style="width: 1200px">
          <div class="page-body" style="background-color: #fff">
            <div class="verify-result-content" style="background-color: #fff">
              <!-- <div
                class="text-center verify-title"
                style="background-color: #fff"
              >
                文件验签结果
              </div> -->
              <div v-if="visaVerification" class="result-content mt-[35px]">
                <p class="second-title">
                  文件名称：{{ visaVerification?.FileName }}
                </p>
                <p class="second-title !mt-[10px]">
                  文件有效性：{{ visaVerification?.Msg }}
                </p>
                <p class="assential-information">
                  <span class="second-title">签名信息</span>
                  <span class="autograph-num">
                    文件中共有
                    <span class="red-text">{{
                      visaVerification?.SignatureInfos?.length || 0
                    }}</span>
                    个签名
                  </span>
                </p>
                <div
                  v-for="(item, index) in visaVerification?.SignatureInfos"
                  :key="index"
                >
                  <div class="show-message">
                    <div class="horizen-layer flex justify-between">
                      <div class="left-content">
                        <div class="contruct-body flex">
                          <p class="contruct-label">签章结果：</p>
                          <div class="contruct-text">
                            <span class="green-text">{{
                              item?.VerifyDesc
                            }}</span>
                          </div>
                        </div>
                        <div class="contruct-body flex">
                          <p class="contruct-label">签章人：</p>
                          <div class="contruct-text">
                            {{ item.Signatory }}
                          </div>
                        </div>
                        <div class="contruct-body flex">
                          <p class="contruct-label">颁发机构：</p>
                          <div class="contruct-text">
                            {{ item.Organization }}
                          </div>
                        </div>
                        <div class="contruct-body flex">
                          <p class="contruct-label">签名时间：</p>
                          <div class="contruct-text">
                            {{ item.TimeStamp }}
                            <span class="green-text">{{
                              item?.TimeStampDesc
                            }}</span>
                          </div>
                        </div>
                        <div class="contruct-body flex">
                          <p class="contruct-label">是否使用时间戳：</p>
                          <div class="contruct-text">
                            {{ item?.HasTimeStamp }}
                          </div>
                        </div>
                        <div class="contruct-body flex">
                          <p class="contruct-label">签名算法：</p>
                          <div class="contruct-text">
                            {{ item.StrAlgName }}
                          </div>
                        </div>
                        <div class="contruct-body flex">
                          <p class="contruct-label">签名状态：</p>
                          <div class="contruct-text">
                            {{ item?.Status }}
                          </div>
                        </div>
                      </div>
                      <div class="right-content">
                        <h4 class="text-center m-0">电子印章</h4>
                      </div>
                    </div>
                  </div>
                  <div style="height: 30px"></div>
                </div>
              </div>
              <div v-else class="mt-[35px]">
                <p
                  style="color: #999; line-height: 20px"
                  class="p-[50px] text-center text-[14px] m-0"
                >
                  抱歉，未查询到签名信息
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  inject,
  onMounted,
  nextTick,
  // ref,
} from 'vue'
import {
  getCASignatureResult,
  getICFStatementStatusHistories,
  postOfflineTransfer,
  uploadPatientICFOfflineSignedFile,
  deletePatientICFOfflineSignedFile,
} from '@/api/electronicInfo'
import { useStore } from 'vuex'
import { delay } from '@/utils'
import html2canvas from 'html2canvas'
// import type { ElFile } from 'element-plus/es/components/upload/src/upload.type'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'ElectronicInfosDetails', // 电子知情详情
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup() {
    const router = useRouter()
    let pdfDoc = null
    const loadFile = async () => {
      let pdfjs = await import('pdfjs-dist/build/pdf')
      let pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.min.mjs')
      pdfjs.GlobalWorkerOptions.workerSrc =
        '../../node_modules/pdfjs-dist/build/pdf.worker.min.mjs'
      pdfjs.getDocument(state.pdfUrl).promise.then(async (doc) => {
        pdfDoc = doc
        state.pdfParams.pdfPageTotal = doc.numPages
        for (let pageNum = 1; pageNum <= doc.numPages; pageNum++) {
          await getPdfPage(pageNum)
        }
      })
    }
    // 加载pdf的某一页
    const getPdfPage = (number) => {
      return new Promise((resolve, reject) => {
        pdfDoc
          .getPage(number)
          .then((page) => {
            const canvas = document.getElementById(`pdf-render${number}`)
            const context = canvas.getContext('2d')
            const scale = 1
            const dpr = window.devicePixelRatio || 1
            const bsr =
              context.webkitBackingStorePixelRatio ||
              context.mozBackingStorePixelRatio ||
              context.msBackingStorePixelRatio ||
              context.oBackingStorePixelRatio ||
              context.backingStorePixelRatio ||
              1
            const ratio = dpr / bsr
            const viewport = page.getViewport({
              scale: state.pdfParams.pdfScale,
            })
            const canvasWidth = Math.floor(viewport.width * ratio)
            const canvasHeight = Math.floor(viewport.height * ratio)
            canvas.width = canvasWidth
            canvas.height = canvasHeight
            canvas.style.width = Math.floor(1000) + 'px'
            canvas.style.height =
              Math.floor((viewport.height * 1000) / viewport.width) + 'px'

            let renderContext = {
              canvasContext: context,
              viewport: viewport,
              transform: [ratio, 0, 0, ratio, 0, 0],
            }

            page
              .render(renderContext)
              .promise.then(() => {
                resolve()
              })
              .catch((error) => {
                reject(error)
              })
          })
          .catch((error) => {
            reject(error)
          })
      })
    }

    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const { studyId } = store.state.account.userinfo
    const state = reactive({
      fileList: [],
      btnResultVisible: false,
      btnConsentVisible: false,
      btnOfflineUploadVisible: false,
      btnConsentDownloadVisible: false,
      offlineFiles: [],
      studyName: store.state.studyItem.studyName,
      DetailsList: {
        icfStatusHistories: [],
        patientICFStatement: {
          patientICFStatus: null,
          icfVersions: [],
          patientICFStatementFiles: [],
          // showVerifySignResult: false,
        },
      },
      electronicInfosDetailsRef: null,
      dialogVisible: false,
      rolesBtnShow: false,
      loading: false,
      boxRef: null,
      imageSrc: '',
      myFormData: {
        remark: '',
      },
      rules: {
        remark: [{ required: true, message: '请输入', trigger: 'blur' }],
      },
      visaVerification: null,
      poseDialog: false,
      audiosObj: {},
      audioSrc: '',
      audioPlayer: null,
      pdfParams: {
        currentPageNumber: 1,
        pdfScale: 2,
        pdfPageTotal: 0, // 总页数
      },
      showPdfDialog: false,
      pdfUrl: '',
      poseDialogTypeFlag: 1, // 1视频  2知情同意书
      audioClick: (item) => {
        // console.log(item)
        if (item?.audios && item.audios?.length > 0) {
          item.audios.forEach((ite) => {
            ite.stopPlayShow = true
          })
          state.audiosObj = item
          // 1视频  2知情同意书
          state.poseDialogTypeFlag = item?.icfStatus === 102 ? 1 : 2
          state.poseDialog = true
        }
      },
      // 开始播放
      playAudio: (item) => {
        state.audioSrc = ''
        state.audioSrc = item.fileUrl
        item.stopPlayShow = false
        state.interviewAudioList(item)
        nextTick(() => {
          state.audioPlayer.load()
          state.audioPlayer.play()
        })
      },
      // 停止播放
      stopAudio: (item) => {
        state.audioPlayer.pause()
        item.stopPlayShow = false
        state.interviewAudioList(item, 1)
      },
      interviewAudioList: (data, num = 0) => {
        state.audiosObj.audios.forEach((item) => {
          if (num === 1) {
            item.stopPlayShow = true
          } else if (item.id !== data.id) {
            item.stopPlayShow = true
          }
        })
      },
      // 播放完成调用
      handleEnded: () => {
        state.interviewAudioList({}, 1)
      },
      transferLine: () => {
        state.myFormData.remark = ''
        state.dialogVisible = true
      },
      goPdfBook: async (url) => {
        if (url != null) {
          state.pdfUrl = url
          await loadFile()
          state.showPdfDialog = true
        }
      },
      downloadPdf: (pdfUrl, number, date) => {
        delay(() => {
          fetch(pdfUrl)
            .then((response) => response.blob())
            .then((blob) => {
              const url = window.URL.createObjectURL(new Blob([blob]))
              const link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              const fileName = '知情版本' + number + '-版本日期' + date + '.pdf'
              link.setAttribute('download', fileName)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) // 下载完成移除元素
              window.URL.revokeObjectURL(url) // 释放掉blob对象
            })
            .catch((error) => {
              console.log(error)
            })
        }, 600)
      },
      downloadOfflineSignatureFile: (pdfUrl, fileName) => {
        delay(() => {
          fetch(pdfUrl)
            .then((response) => response.blob())
            .then((blob) => {
              const url = window.URL.createObjectURL(new Blob([blob]))
              const link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', fileName)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) // 下载完成移除元素
              window.URL.revokeObjectURL(url) // 释放掉blob对象
            })
            .catch((error) => {
              console.log(error)
            })
        }, 600)
      },
      deleteOfflineSignatureFile: (patICFStatementFileId) => {
        ElMessageBox.confirm('是否确认删除？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deletePatientICFOfflineSignedFile(patICFStatementFileId).then(
              (res) => {
                if (res) {
                  state.offlineFiles = res
                  ElMessage.success('删除成功')
                  // 刷新文件列表
                }
              }
            )
          })
          .catch(() => {})
      },
      submit: () => {
        state.electronicInfosDetailsRef.validate((valid) => {
          if (valid) {
            state.loading = true
            const data = {
              ...state.myFormData,
              icfStatementID:
                RESEARCHCENTER_INFOS?.researchContent?.icfStatementId || '',
            }
            postOfflineTransfer(
              studyId,
              RESEARCHCENTER_INFOS?.researchContent?.dctSiteId,
              RESEARCHCENTER_INFOS?.researchContent?.dctPatientId,
              data
            )
              .then(() => {
                ElMessage.success('转线下知情成功')
                state.loading = false
                state.dialogVisible = false
                state.onLoad()
              })
              .catch(() => {
                state.loading = false
              })
          }
        })
      },
      showImg: false,
      previewResult: (id) => {
        state.showImg = true
        // 调用接口
        state.loading = true
        const ICFStatementID =
          RESEARCHCENTER_INFOS.researchContent?.icfStatementId
        getCASignatureResult(ICFStatementID, { studyIcfVersionId: id })
          .then((res) => {
            if (res) {
              state.visaVerification = JSON.parse(res)
            }
            state.loading = false
          })
          .catch(() => {
            state.loading = false
          })
      },
      downloadImg: () => {
        delay(async () => {
          if (state.boxRef) {
            state.loading = true
            html2canvas(state.boxRef)
              .then((canvas) => {
                const imageURL = canvas.toDataURL()
                const fileName = `验签-${state.DetailsList?.patientICFStatement?.realName}-知情版本${state.DetailsList?.patientICFStatement?.icfVersionNumber}-版本日期${state.DetailsList?.patientICFStatement?.icfVersionDate}.png`
                const link = document.createElement('a')
                link.style.display = 'none'
                link.href = imageURL
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) // 下载完成移除元素
                state.loading = false
              })
              .catch(() => {
                state.loading = false
              })
          }
        }, 600)
      },
      showMeetingDetail: (conferenceId) => {
        router.push({ path: '/meetingManagement', query: { id: conferenceId } })
      },
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.refresh()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      onLoad: () => {
        getICFStatementStatusHistories(
          RESEARCHCENTER_INFOS.researchContent.icfStatementId
        ).then((res) => {
          state.DetailsList = res
          state.offlineFiles = res?.patientICFStatement?.offlineSignedFiles || []
          // const roles = ['admin', 'PI', 'SUBI', 'CRC']
          // const role = store.state.account.userinfo?.roles || []
          // state.rolesBtnShow = roles.some((item) => role.includes(item))
          // 知情同意预览权限
          // const consentPreviewRoles = [
          //   'admin',
          //   'PI',
          //   'SUBI',
          //   'CRA',
          //   'Audit',
          //   'CRC',
          // ]
          // 知情同意下载权限
          // const consentDownloadRoles = ['admin', 'PI', 'SUBI', 'CRC']
          // // 知情同意验签权限
          // const resultPreviewRoles = [
          //   'admin',
          //   'PI',
          //   'SUBI',
          //   'CRA',
          //   'Audit',
          //   'CRC',
          // ]
          // 线下签署文件权限
          // const offlineUploadRoles = ['admin', 'PI', 'SUBI', 'CRC']
          // state.btnConsentVisible = consentPreviewRoles.some((item) =>
          //   role.includes(item)
          // )
          // state.btnConsentDownloadVisible = consentDownloadRoles.some((item) =>
          //   role.includes(item)
          // )
          // state.btnResultVisible = resultPreviewRoles.some((item) =>
          //   role.includes(item)
          // )
          // state.btnOfflineUploadVisible = offlineUploadRoles.some((item) =>
          //   role.includes(item)
          // )
        })
      },
      showIcfStatusStr: (index, txt, arr) => {
        let showTxt = txt
        if (index > 0) {
          let preTxt = arr[index - 1]?.icfStatusStr
          if (preTxt == showTxt) {
            showTxt = ''
          }
        }
        return showTxt
      },
      uploadSignatureFile: (file) => {
        const formData = new FormData()
        formData.append('SignedFiles', file.file)
        uploadPatientICFOfflineSignedFile(
          RESEARCHCENTER_INFOS?.researchContent?.icfStatementId,
          formData
        ).then((res) => {
          let notDelFiles = state.offlineFiles.filter((t) => !t.isDeleted)
          state.offlineFiles.splice(notDelFiles.length, 0, res)
        })
      },
      beforeUpload: (file) => {
        if (file.size > 10 * 1024 * 1024) {
          ElMessage.warning('文件超过10M，请重新上传')
          return false
        }
        return true
      },
    })

    onMounted(async () => {
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  h3 {
    line-height: 40px;
    width: 80%;
    margin-bottom: 0;
    margin-top: 0;
  }
}
.details {
  width: 100%;
  .content-img-box {
    background: #fff;
    padding: 0px 20px 20px 20px;
    box-sizing: border-box;
    min-width: 1250px;
    border-radius: 10px;
    height: 740px;

    overflow: auto;
    border-top: 1px solid #eff1f4;
    border-bottom: 1px solid #eff1f4;
    padding: 0px 27px 11px 27px;
    text-align: center;

    &::-webkit-scrollbar {
      width: 17px;
    }

    &::-webkit-scrollbar-thumb {
      background: #8798af;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
  .content-top {
    width: 100%;
    background: #fff;
    // margin-top: 20px;
    padding: 20px 20px;
    box-sizing: border-box;
    // margin-bottom: 20px;
    .bottomTop {
      display: flex;
      justify-content: space-between;
      div {
        display: flex;
        &:last-child {
          width: 33%;
        }
        span {
          &:first-child {
            width: 120px;
            text-align: right;
          }
          &:last-child {
            flex: 1;
            margin-left: 10px;
          }
        }
      }
    }
    .bottom {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      div {
        width: calc(100% - 120px);
        display: flex;
        span {
          &:first-child {
            width: 120px;
            text-align: right;
          }
          &:last-child {
            flex: 1;
            margin-left: 10px;
            word-break: break-all;
            word-wrap: break-word;
          }
        }
      }
      .conduct {
        color: rgb(13, 134, 199);
      }
      .end {
        color: green;
      }
    }
    .content-title {
      width: 100%;
      margin-top: 30px;
      padding-bottom: 8px;
      border-bottom: 2px solid rgb(224, 221, 221);
    }
    .contentBox {
      width: 100%;
      .childrenList {
        width: 100%;
        display: flex;
        margin-top: 20px;
        div {
          &:nth-child(1) {
            color: rgb(156, 152, 152);
            width: 200px;
            text-align: right;
          }
          &:nth-child(2) {
            flex: 1;
            margin-left: 20px;
            span {
              &:nth-child(2),
              &:nth-child(3) {
                margin-left: 20px;
              }
              // &:nth-child(3) {
              //   margin-left: 5px;
              //   color: rgb(156, 152, 152);
              // }
            }
            .icfRemark {
              display: block;
              width: 100%;
              margin-left: 0 !important;
              margin-top: 20px;
              word-break: break-all;
              word-wrap: break-word;
            }
          }
        }
      }
    }
    .boxed {
      width: 100%;
      display: flex;
      margin-top: 20px;
      div {
        &:nth-child(1) {
          color: rgb(156, 152, 152);
          width: 200px;
          text-align: right;
        }
        &:nth-child(2) {
          display: flex;
          margin-left: 20px;
        }
      }
    }
    .boxedContetn {
      width: 100%;
      padding-left: 220px;
      box-sizing: border-box;
      div {
        display: flex;
        margin-top: 20px;
        span {
          &:nth-child(2) {
            margin-left: 20px;
          }
          &:nth-child(3) {
            margin-left: 20px;
            color: rgb(156, 152, 152);
          }
        }
      }
    }
    .contentBox {
      width: 100%;
      .childrenList {
        width: 100%;
        display: flex;
        div {
          &:nth-child(1) {
            width: 200px;
          }
          &:nth-child(2) {
            margin-left: 20px;
          }
        }
      }
    }
    .single {
      width: 100%;
      display: flex;
      margin-top: 20px;
      div {
        width: 200px;
        text-align: right;
        color: rgb(156, 152, 152);
      }
      span {
        margin-left: 20px;
      }
    }
    .singleBox {
      padding-left: 220px;
      box-sizing: border-box;
      margin-top: 20px;
    }
    .singleButton {
      width: 100%;
      display: flex;
      margin-top: 20px;
      div {
        &:nth-child(1) {
          width: 200px;
          text-align: right;
          color: rgb(156, 152, 152);
        }
        &:nth-child(2) {
          margin-left: 20px;
          display: flex;
          margin-right: 10px;
          a {
            display: block;
            text-decoration: none;
            width: 50px;
            height: 24px;
            line-height: 24px;
            background: #409eff;
            text-align: center;
            color: #fff;
            font-size: 12px;
            border-radius: 5px;
          }
        }
        &:nth-child(3) {
          position: relative;
          bottom: 1px;
        }
      }
    }
  }
}
.electronicInfosDetails {
  :deep(.my-dialog-body) {
    min-height: auto;
    h4 {
      margin: 0;
    }
  }
  .electronic-tips {
    color: #f59a23;
  }
}
.all-box {
  margin: 0 auto;
  padding: 20px 2px;
  .page-body {
    background: #fff;
    box-shadow: 1px 1px 5px 2px rgba(0, 0, 0, 0.1);
    overflow: auto;

    .verify-result-content {
      padding: 0 50px;
      .verify-title {
        border-bottom: 1px solid #e5e5e5;
        height: 80px;
        line-height: 80px;
        position: relative;
        margin: 0;
        font-size: 21px;
        font-weight: 700;
      }
      .second-title {
        font-weight: 700;
        margin: 0 0 20px 0;
        font-size: 16px;
        color: #25303a;
      }
      .assential-information {
        margin: 30px 0 20px 0;
        .autograph-num {
          font-size: 14px;
          color: #999;
          display: inline-block;
          margin-left: 12px;
          .red-text {
            color: #cbb486;
          }
        }
      }
      .green-text {
        color: #67c23a;
      }

      .show-message {
        font-size: 14px;
        background-color: #fef7f6;
        border: 1px solid #e5e5e5;
        border-radius: 5px;
        // margin-bottom: 30px;
        padding: 30px;
        .left-content {
          width: calc(100% - 260px);
          .contruct-body {
            margin: 10px 0 20px 0;
            .contruct-label {
              width: 140px;
              text-align: left;
              color: #999;
              margin: 0;
            }
            .contruct-text {
              flex: 1;
              margin: 0;
            }
          }
        }
        .right-content {
          position: relative;
          width: 260px;
          padding: 10px;
          height: 100%;
        }
      }
    }
  }
}
.divWrap {
  display: block !important;
  .firstLevelSpan {
    display: block;
    word-break: break-all;
    word-wrap: break-word;
    width: unset !important;
    text-align: left !important;
    margin-left: 10px;
  }
  .nextLevelSpan {
    display: block;
    margin-top: 10px;
    margin-left: 10px;
  }
}

.pdf-preview-box {
  overflow: auto;
  height: 80vh;
  border-top: 1px solid #eff1f4;
  border-bottom: 1px solid #eff1f4;
  padding: 0px 27px 11px 27px;
  text-align: center;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #8798af;
    border-radius: 5px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}
.upload-file {
  display: flex;
  width: 100% !important;
}
.el-upload__tip {
  font-size: 16px;
  color: #9c9898;
}
.deleteFile {
  font-size: 16px;
  bottom: 1px;
  position: relative;
}
.btnPreview {
  position: relative;
  bottom: 3px;
  margin-right: 10px;
  cursor: pointer;
}
.btnDownload {
  position: relative;
  bottom: 2px;
  margin-right: 10px;
  cursor: pointer;
}
.btnDelete {
  position: relative;
  bottom: 1px;
  margin-left: 3px;
  cursor: pointer;
}
.meetingDetail {
  display: inline-flex;
  height: inherit;
}
.deleted {
  margin-left: 10px;
  color: #9c9898;
}
</style>
