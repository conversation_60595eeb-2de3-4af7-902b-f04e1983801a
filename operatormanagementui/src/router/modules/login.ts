// import { getItem } from '@/utils/storage'
// import store from '@/store'
const Login = () => import('@/views/login/index.vue')
const ForgetPassword = () => import('@/views/login/ForgetPassword.vue')
const Study = () => import('@/views/login/study.vue')
export default [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    beforeEnter: () => { // 无token时才可以进入登录页!getItem('VEA-TOKEN')
      // var url = window.location.href// 获取url地址
      // const newsot = url.split('?newStore=')[1]// 获取newStore后面的参数
      // const de = decodeURI(newsot)
      window.location.href = `${
        window.location.origin
      }/operatorui/#/login?flag=2`
      // console.log('json', JSON.parse(de))
      // console.log(store)
      // var str = url.substr(1);
      // var strs = str.split("&");
      // strs[0].split("=")[1];
      // var urlParmStr = strs[0].split("=")[1];
      // var objbillEntry = JSON.parse(urlParmStr.replace(/\n/g, "\\n").replace(/\r/g, "\\r"));
      // console.log(urlParmStr)
      // console.log(JSON.parse(urlParmStr))
      // console.log(JSON.parse(newObj), 'login中的路由守卫')
      // if (!store?.getters?.account?.userinfo?.token) {
      //   return true
      // } else {
      //   return false
      // }
    }
  },
  {
    path: '/forgetPassword',
    name: 'ForgetPassword',
    component: ForgetPassword,
  },
  {
    path: '/study',
    name: 'Study',
    component: Study,
    beforeEnter: (to, from) => {
      // 如果是从 /home 页面跳转过来的，则阻止访问
      if (from && from.path === '/home') {
        // console.log('阻止从 /home 页面访问 /study 页面，重定向到 /home')
        return { path: '/home', replace: true }
      }
      // 如果是从其他页面跳转过来的，允许访问
      return true
    }
  },
]
