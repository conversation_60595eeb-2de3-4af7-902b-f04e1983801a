const Layout = () => import('@/layout/index.vue')
const interactiveCommunication = () => import('@/views/interactiveCommunication/index.vue')
const researchersCommunicationInfo = () => import('@/views/interactiveCommunication/ResearchersCommunicationInfo.vue')

export default [
  {
    path: '/interactiveCommunication',
    component: Layout,
    name: '',
    meta: {
      title: '互动沟通',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/interactiveCommunication',
        name: 'InteractiveCommunication',
        component: interactiveCommunication,
        hidden: true,
        meta: {
          title: '受试者沟通统计',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/researchersCommunicationInfo',
        name: 'ResearchersCommunicationInfo',
        component: researchersCommunicationInfo,
        hidden: true,
        meta: {
          title: '研究人员沟通统计',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
