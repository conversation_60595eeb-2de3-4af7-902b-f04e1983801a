const Layout = () => import('@/layout/index.vue')
const visitInside = () => import('@/views/visitInside/index.vue')

export default [
  {
    path: '/visitInside',
    component: Layout,
    name: '',
    meta: {
      title: '访视窗口期内',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/visitInside',
        name: 'VisitInside',
        component: visitInside,
        hidden: true,
        meta: {
          // title: '访视窗口期内',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
