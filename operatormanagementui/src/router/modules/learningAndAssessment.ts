const Layout = () => import('@/layout/index.vue')
const subjectsLearningInfo = () => import('@/views/learningAndAssessment/subjectsLearningInfo/index.vue')
const researchersLearningInfo = () => import('@/views/learningAndAssessment/researchersLearningInfo/index.vue')
const learningMaterialsInfo = () => import('@/views/learningAndAssessment/learningMaterialsInfo/index.vue')

export default [
  {
    path: '/subjectsLearningInfo',
    component: Layout,
    name: '',
    meta: {
      title: '学习&测评',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/subjectsLearningInfo',
        name: 'SubjectsLearningInfo',
        component: subjectsLearningInfo,
        hidden: true,
        meta: {
          title: '受试者学习统计',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/researchersLearningInfo',
        name: 'ResearchersLearningInfo',
        component: researchersLearningInfo,
        hidden: true,
        meta: {
          title: '研究人员学习统计',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/learningMaterialsInfo',
        name: 'LearningMaterialsInfo',
        component: learningMaterialsInfo,
        hidden: true,
        meta: {
          title: '学习资料统计',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
