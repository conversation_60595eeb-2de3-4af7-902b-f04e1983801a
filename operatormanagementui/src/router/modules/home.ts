const Layout = () => import('@/layout/index.vue')
const Home = () => import('@/views/home/<USER>')

export default [
  {
    path: '/home',
    component: Layout,
    name: 'Dashboard',
    meta: {
      title: '',
    },
    icon: 'home',
    // beforeEnter: () => { // 路由守卫,通过审核后可以进入
    //   var url = window.location.href// 获取url地址
    //   const newsot = url.split('?newStore=')[1]// 获取newStore后面的参数
    //   if (newsot !== undefined && store.state.studyItem !== '') {
    //     const de = decodeURI(newsot)
    //     const newUserinfo = JSON.parse(de)
    //     if (newUserinfo?.myLogingOutTime) {
    //       localStorage.setItem('logingOutTime', newUserinfo.myLogingOutTime)
    //     }
    //     store.commit('account/setUserinfo', newUserinfo)
    //     store.commit('app/setToken', newUserinfo.token)
    //     store.commit('setStudyItem', newUserinfo)
    //   } else if (store.state.studyItem === '' && newsot === undefined) {
    //     window.location.href = `${window.location.origin
    //     }operatorui/#/login`
    //   }
    // },
    children: [
      {
        path: '',
        name: 'home',
        component: Home,
        meta: {
          title: 'Dashboard', //
          affix: true,
        },
      },
    ],
  },
]
