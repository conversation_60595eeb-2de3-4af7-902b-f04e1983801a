const Layout = () => import('@/layout/index.vue')
const orderManagement = () => import('@/views/orderManagement/index.vue')
const MaterialOrder = () => import('@/views/orderManagement/MaterialOrder.vue')
const goodsRepertory = () => import('@/views/orderManagement/goodsRepertory/index.vue')
export default [
  {
    path: '/orderManagement',
    component: Layout,
    name: '',
    meta: {
      title: '订单管理',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/orderManagement',
        name: 'OrderManagement',
        component: orderManagement,
        hidden: true,
        meta: {
          title: '物流订单',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/goodsRepertory',
        name: 'GoodsRepertory',
        component: goodsRepertory,
        meta: {
          title: '物资库存记录',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/materialOrder',
        name: 'MaterialOrder',
        component: MaterialOrder,
        hidden: true,
        meta: {
          title: '物资订单',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
