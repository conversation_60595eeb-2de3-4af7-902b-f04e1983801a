const Layout = () => import('@/layout/index.vue')
const electronicInfo = () => import('@/views/electronicInfo/index.vue')
const informedVersionUpdate = () => import('@/views/electronicInfo/InformedVersionUpdate.vue')

export default [
  {
    path: '/electronicInfo',
    component: Layout,
    name: '',
    meta: {
      title: '电子知情',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/electronicInfo',
        name: 'ElectronicInfo',
        component: electronicInfo,
        hidden: true,
        meta: {
          title: '知情流程',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/informedVersionUpdate',
        name: 'InformedVersionUpdate',
        component: informedVersionUpdate,
        hidden: true,
        meta: {
          title: '版本更新推送',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
