const Layout = () => import('@/layout/index.vue')
const DataMigration = () => import('@/views/dataMigration/index.vue')
const MigrationLog = () => import('@/views/dataMigration/MigrationLog.vue')
export default [
  {
    path: '/dataMigration',
    component: Layout,
    name: '',
    meta: {
      title: '数据迁移',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/dataMigration',
        name: 'DataMigration',
        component: DataMigration,
        hidden: true,
        meta: {
          title: '数据迁移',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/migrationLog',
        name: 'MigrationLog',
        component: MigrationLog,
        hidden: true,
        meta: {
          title: '迁移记录',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
