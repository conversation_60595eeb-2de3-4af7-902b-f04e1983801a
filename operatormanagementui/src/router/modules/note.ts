// const Layout = () => import('@/layout/index.vue')
// const NoteList = () => import('@/views/note/index.vue')
// const SendingSettings = () => import('@/views/note/SendingSettings.vue')


// export default [
//   {
//     path: '/note',
//     component: Layout,
//     name: 'Note',
//     meta: {
//       title: '短信',
//     },
//     // icon: 'el-icon-message',
//     roles: ['admin', 'visitor'],
//     children: [
//       {
//         path: '/note',
//         name: 'NoteList',
//         component: NoteList,
//         meta: {
//           title: '短信列表',
//           roles: ['admin', 'visitor'],
//         },
//       },
//       {
//         path: 'sendingsettings',
//         name: 'SendingSettings',
//         component: SendingSettings,
//         meta: {
//           title: '短信发送设置',
//           roles: ['admin', 'visitor'],
//         },
//       },
//     ],
//   },
// ]
