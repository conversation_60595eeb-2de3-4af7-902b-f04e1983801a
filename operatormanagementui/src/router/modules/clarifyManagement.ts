const Layout = () => import('@/layout/index.vue')
const clarifyManagement = () => import('@/views/clarifyManagement/index.vue')
const planChange = () => import('@/views/planChange/index.vue')

export default [
  {
    path: '/clarifyManagement',
    component: Layout,
    name: '',
    meta: {
      title: '数据管理',
    },
    // roles: ['admin', 'PM', 'PI', 'SUBI', 'CRA', 'Audit', 'CRC', 'DM', 'MA'],
    children: [
      {
        path: '/clarifyManagement',
        name: 'ClarifyManagement',
        component: clarifyManagement,
        hidden: true,
        meta: {
          title: '数据澄清',
          // roles: [
          //   'admin',
          //   'PM',
          //   'PI',
          //   'SUBI',
          //   'CRA',
          //   'Audit',
          //   'CRC',
          //   'DM',
          //   'MA',
          // ],
        },
      },
      {
        path: '/planChange',
        name: 'PlanChange',
        component: planChange,
        meta: {
          title: '计划外修改',
          // roles: [
          //   'admin',
          //   'PM',
          //   'PI',
          //   'SUBI',
          //   'CRA',
          //   'Audit',
          //   'CRC',
          //   'DM',
          //   'MA',
          // ],
        },
      },
    ],
    // 把计划外修改的路由写在这
  },
]
