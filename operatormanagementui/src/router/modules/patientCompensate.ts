const Layout = () => import('@/layout/index.vue')
const patientCompensate = () => import('@/views/patientCompensate/index.vue')
const compensateReceipts = () => import('@/views/patientCompensate/CompensateReceipts.vue')

export default [
  {
    path: '/patientCompensate',
    component: Layout,
    name: '',
    meta: {
      title: '受试者补偿/报销',
    },
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/patientCompensate',
        name: 'PatientCompensate',
        component: patientCompensate,
        hidden: true,
        meta: {
          title: '补偿/报销统计',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/compensateReceipts',
        name: 'CompensateReceipts',
        component: compensateReceipts,
        hidden: true,
        meta: {
          title: '补偿/报销单据',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
