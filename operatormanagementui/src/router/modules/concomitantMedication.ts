const Layout = () => import('@/layout/index.vue')
const concomitantMedication = () => import('@/views/concomitantMedication/index.vue')

export default [
  {
    path: '/concomitantMedication',
    component: Layout,
    name: '',
    meta: {
      title: '合并用药',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/concomitantMedication',
        name: 'ConcomitantMedication',
        component: concomitantMedication,
        hidden: true,
        meta: {
          // title: '合并用药',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
