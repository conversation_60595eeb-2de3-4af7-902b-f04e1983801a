const Layout = () => import('@/layout/index.vue')
const financeManagement = () => import('@/views/financeManagement/index.vue')
const financeApprove = () => import('@/views/financeManagement/FinanceApprove.vue')
export default [
  {
    path: '/financeManagement',
    component: Layout,
    name: '',
    meta: {
      title: '财务管理',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/financeManagement',
        name: 'FinanceManagement',
        component: financeManagement,
        hidden: true,
        meta: {
          title: '项目款',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/financeApprove',
        name: 'FinanceApprove',
        component: financeApprove,
        hidden: true,
        meta: {
          title: '提现申请',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
