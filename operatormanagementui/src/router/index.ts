import { createRouter, createWebHashHistory } from 'vue-router'

import redirect from '@/router/modules/redirect'
import error from '@/router/modules/error'
import login from '@/router/modules/login'
import lock from '@/router/modules/lock'
import home from '@/router/modules/home'
import test from '@/router/modules/test'
import subjectsList from '@/router/modules/subjectsList'
import visitInside from '@/router/modules/visitInside'
import visitOutside from '@/router/modules/visitOutside'
import concomitantMedication from '@/router/modules/concomitantMedication'
import discomfortRecord from '@/router/modules/discomfortRecord'
import electronicInfo from '@/router/modules/electronicInfo'
import meetingManagement from '@/router/modules/meetingManagement'
import clarifyManagement from '@/router/modules/clarifyManagement'
import interactiveCommunication from '@/router/modules/interactiveCommunication'
import learningAndAssessment from '@/router/modules/learningAndAssessment'
import doctorPatientBinding from '@/router/modules/doctorPatientBinding'
import orderManagement from '@/router/modules/orderManagement'
import dataMigration from '@/router/modules/dataMigration'
import materialsRecycle from '@/router/modules/materialsRecycle'
import patientCompensate from '@/router/modules/patientCompensate'
import financeManagement from '@/router/modules/financeManagement'
// import note from '@/router/modules/note'

// 左侧菜单
export const allMenus = [...home, ...test]

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    ...redirect, // 统一的重定向配置
    ...login,
    // ...note,
    ...subjectsList,
    ...visitInside,
    ...visitOutside,
    ...concomitantMedication,
    ...discomfortRecord,
    ...electronicInfo,
    ...meetingManagement,
    ...clarifyManagement,
    ...allMenus,
    ...error,
    ...lock,
    ...interactiveCommunication,
    ...learningAndAssessment,
    ...doctorPatientBinding,
    ...orderManagement,
    ...dataMigration,
    ...materialsRecycle,
    ...patientCompensate,
    ...financeManagement,
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { left: 0, top: 0 }
    }
  },
})

export default router
