import { createStore } from 'vuex'

const modulesFiles = import.meta.globEager('./modules/*.ts')
const modules = Object.entries(modulesFiles).reduce((modules, [path, mod]) => {
  const moduleName = path.replace(/^\.\/modules\/(.*)\.\w+$/, '$1')
  modules[moduleName] = mod.default
  return modules
}, {})

const state = {
  refreshalg: true,
  studyItem: null,
  refreshFlag: false,
}
const mutations = {
  setRefreshalg(state, data) {
    state.refreshalg = data
  },
  setStudyItem(state, data) {
    state.studyItem = data
  },
}
const getters = {
  app: state => state.app,
  account: state => state.account,
  errorLog: state => state.errorLog,
  menu: state => state.menu,
  tags: state => state.tags,
}

export default createStore({
  modules,
  state,
  mutations,
  getters,
})
