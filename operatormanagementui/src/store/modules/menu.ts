import { getMenusByProject, transferMenuData } from '@/api/menu'
import { useStore } from 'vuex'
// import { GetMenus } from '@/api/menu';
// import store from '@/store';
// const hasPermission = (role, route) => {
//   if (!!route.meta && !!route.meta.roles && !route.meta.roles.includes(role)) {
//     return false
//   }
//   return true
// }

// const generateUrl = (path, parentPath) => {
//   return path.startsWith('/')
//     ? path
//     : path
//     ? `${parentPath}/${path}`
//     : parentPath
// }
// 方式一：根据角色生成菜单
// const getFilterMenus = (arr, role, parentPath = '') => {
//   const menus = []
//   arr.forEach(item => {
//     if (hasPermission(role, item) && !item.hidden) {
//       const menu = {
//         url: generateUrl(item.path, parentPath),
//         title: item.meta.title,
//         icon: item.icon,
//       }
//       if (item.children) {
//         if (item.children.filter(child => !child.hidden).length <= 1) {
//           menu.url = generateUrl(item.children[0].path, menu.url)
//         } else {
//           menu.children = getFilterMenus(item.children, role, menu.url)
//         }
//       }
//       menus.push(menu)
//     }
//   })

//   return menus
// }

export default {
  namespaced: true,
  state: {
    menus: [],
  },
  mutations: {
    SET_MENUS(state, data) {
      state.menus = data
    },
  },
  actions: {
    // async generateMenus({ commit }, roles) {
    //   /* 所有角色
    //   ["Unknow","OnSite","SUBI","admin",
    //   "PM","CRA","DM","CRC","PI",'物资回收专员','财务']
    //   首页-互动沟通
    //   roles: ["SUBI","admin","PM","CRA","CRC","PI"],
    //   */
    //   // // 方式一：根据角色生成菜单
    //   // const menus = getFilterMenus(allMenus, roles)
    //   // commit('SET_MENUS', menus)

    //   // // 方式二：从后台获取菜单
    //   let adminArr = [
    //     {
    //       url: '/home',
    //       title: 'Dashboard',
    //       roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', 'DM', 'Audit'],
    //       // icon: 'home',
    //       // children: [
    //       //   {
    //       //     url: '/home', // '/home/<USER>'
    //       //     title: '详情',
    //       //   },
    //       // ],
    //     },
    //     // {
    //     //   url: '/test',
    //     //   title: '权限管理',
    //     //   icon: 'el-icon-lock',
    //     //   children: [
    //     //     {
    //     //       url: '/test',
    //     //       title: '用户管理',
    //     //     },
    //     //     {
    //     //       url: '/test/auth',
    //     //       title: '角色管理',
    //     //     }
    //     //   ],
    //     // },
    //     {
    //       url: '/subjectsList',
    //       title: '受试者列表',
    //       roles: [
    //         'SUBI',
    //         'admin',
    //         'PM',
    //         'CRA',
    //         'CRC',
    //         'PI',
    //         'DM',
    //         'Audit',
    //         'MA',
    //         'DC'
    //       ],
    //       // children: [
    //       //   {
    //       //     url: '/subjectsList',
    //       //     title: '详情',
    //       //   },
    //       // ],
    //     },
    //     {
    //       url: '/visitInside',
    //       title: '访视窗口期内',
    //       roles: [
    //         'SUBI',
    //         'admin',
    //         'PM',
    //         'CRA',
    //         'CRC',
    //         'PI',
    //         'DM',
    //         'Audit',
    //         'MA',
    //       ],
    //       // children: [
    //       //   {
    //       //     url: '/visitInside',
    //       //     title: '详情',
    //       //   },
    //       // ],
    //     },
    //     {
    //       url: '/visitOutside',
    //       title: '访视任务缺失',
    //       roles: [
    //         'SUBI',
    //         'admin',
    //         'PM',
    //         'CRA',
    //         'CRC',
    //         'PI',
    //         'DM',
    //         'Audit',
    //         'MA',
    //       ],
    //       // children: [
    //       //   {
    //       //     url: '/visitOutside',
    //       //     title: '详情',
    //       //   },
    //       // ],
    //     },
    //     {
    //       url: '/discomfortRecord',
    //       title: '不适记录',
    //       roles: [
    //         'SUBI',
    //         'admin',
    //         'PM',
    //         'CRA',
    //         'CRC',
    //         'PI',
    //         'DM',
    //         'Audit',
    //         'MA',
    //       ],
    //       // children: [
    //       //   {
    //       //     url: '/discomfortRecord',
    //       //     title: '详情',
    //       //   },
    //       // ],
    //     },
    //     {
    //       url: '/concomitantMedication',
    //       title: '合并用药',
    //       roles: [
    //         'SUBI',
    //         'admin',
    //         'PM',
    //         'CRA',
    //         'CRC',
    //         'PI',
    //         'DM',
    //         'Audit',
    //         'MA',
    //       ],
    //       // children: [
    //       //   {
    //       //     url: '/concomitantMedication',
    //       //     title: '详情',
    //       //   },
    //       // ],
    //     },
    //     {
    //       url: '/electronicInfo',
    //       title: '电子知情',
    //       roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', 'Audit'],
    //       children: [
    //         {
    //           url: '/electronicInfo',
    //           title: '知情流程',
    //         },
    //         {
    //           url: '/informedVersionUpdate',
    //           title: '版本更新推送',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/dataClarify',
    //       title: '数据澄清',
    //       roles: [
    //         'admin',
    //         'PM',
    //         'PI',
    //         'SUBI',
    //         'CRA',
    //         'Audit',
    //         'CRC',
    //         'DM',
    //         'MA',
    //       ],
    //       children: [
    //         {
    //           url: '/clarifyManagement',
    //           title: '澄清列表',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/meetingManagement',
    //       title: '会议管理',
    //       roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', 'Audit'],
    //       children: [
    //         {
    //           url: '/meetingManagement',
    //           title: '会议列表',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/learningAndAssessment',
    //       title: '学习&测评',
    //       roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', 'Audit'],
    //       children: [
    //         {
    //           url: '/subjectsLearningInfo',
    //           title: '受试者学习统计',
    //         },
    //         {
    //           url: '/researchersLearningInfo',
    //           title: '研究人员学习统计',
    //         },
    //         {
    //           url: '/learningMaterialsInfo',
    //           title: '学习资料统计',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/interactiveCommunication',
    //       title: '互动沟通',
    //       roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', 'Audit'],
    //       children: [
    //         {
    //           url: '/interactiveCommunication',
    //           title: '受试者沟通统计',
    //         },
    //         {
    //           url: '/researchersCommunicationInfo',
    //           title: '研究人员沟通统计',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/doctorPatientBinding',
    //       title: '医患绑定',
    //       roles: ['SUBI', 'admin', 'CRC', 'PI'],
    //       children: [
    //         {
    //           url: '/doctorPatientBinding',
    //           title: '医患绑定',
    //         },
    //         {
    //           url: '/doctorList',
    //           title: '医生列表',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/materialsRecycle',
    //       title: '物资发放回收',
    //       roles: [
    //         'SUBI',
    //         'admin',
    //         'PM',
    //         'CRA',
    //         'CRC',
    //         'PI',
    //         '物资回收专员',
    //         'Audit',
    //       ],
    //     },
    //     {
    //       url: '/orderManagement',
    //       title: '订单管理',
    //       roles: [
    //         'SUBI',
    //         'admin',
    //         'PM',
    //         'CRA',
    //         'CRC',
    //         'PI',
    //         '物资回收专员',
    //         'Audit',
    //       ],
    //       children: [
    //         {
    //           url: '/orderManagement',
    //           title: '物流订单',
    //         },
    //         {
    //           url: '/goodsRepertory',
    //           title: '物资库存记录',
    //         },
    //         {
    //           url: '/materialOrder',
    //           title: '物资订单',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/dataMigration',
    //       title: '数据迁移',
    //       roles: ['admin', 'DM'],
    //       children: [
    //         {
    //           url: '/dataMigration',
    //           title: '数据迁移',
    //         },
    //         {
    //           url: '/migrationLog',
    //           title: '迁移记录',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/patientCompensate',
    //       title: '受试者补偿/报销',
    //       roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', 'Audit'],
    //       children: [
    //         // {
    //         // url: '/patientCompensate',
    //         // title: '补偿/报销统计',
    //         // },
    //         {
    //           url: '/compensateReceipts',
    //           title: '补偿/报销单据',
    //         },
    //       ],
    //     },
    //     {
    //       url: '/financeManagement',
    //       title: '财务管理',
    //       roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', '财务', 'Audit'],
    //       children: [
    //         {
    //           url: '/financeManagement',
    //           title: '项目款',
    //           roles: ['admin', 'PM', '财务'],
    //         },
    //         {
    //           url: '/financeApprove',
    //           title: '提现申请',
    //         },
    //       ],
    //     },
    //   ]
    //   // roles= ['PM']
    //   let dataArr: any = []
    //   if (roles && roles?.includes('admin')) {
    //     // 如果admin 先不做操作
    //     dataArr = dataArr.concat(adminArr)
    //   } else {
    //     // 匹配菜单
    //     adminArr.forEach((el) => {
    //       if (el.roles.some((item) => roles.includes(item))) {
    //         if (el?.children) {
    //           const arr = []
    //           el.children?.forEach((ite) => {
    //             if (ite?.roles) {
    //               if (ite.roles.some((it) => roles.includes(it))) {
    //                 arr.push(ite)
    //               }
    //             } else {
    //               arr.push(ite)
    //             }
    //           })
    //           el.children = arr
    //         }
    //         dataArr.push(el)
    //       }
    //     })
    //     adminArr = [...dataArr]
    //   }
    //   // await GetMenus();
    //   commit('SET_MENUS', adminArr)
    // },
    // 
    async generateMenus({ commit }, role) {
      const store = useStore()
      const { studyId } = store.state.account.userinfo
      if (studyId && store.getters.app.authorization) {
        getMenusByProject(studyId).then((res: any) => {
          // 删除res.childSysMenus第一个元素
          // res.childSysMenus.shift()
          const transferData = transferMenuData(res?.childSysMenus || [])
          commit('SET_MENUS', transferData)
        })
      }
    },
  },
}
