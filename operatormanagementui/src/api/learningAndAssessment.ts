import request from '@/utils/request'

// 查询受试者学习情况
export function getPatientTrainingList(studyId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Training/Patient`,
    method: 'get',
    params: { ...data }
  })
}

// 查询某个受试者学习情况
export function getCertainPatientTraining(studyId, patientId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Training/Patient/${patientId}`,
    method: 'get',
    params: { ...data }
  })
}

// 导出受试者
export function getPatientTrainingExport(id, data) {
  return request({
    url: `api/Operator/DoctorPatient/${id}/Training/Patient/Export`,
    method: 'get',
    data: { ...data },
  })
}

// 查询研究者学习情况
export function getDoctorTrainingList(studyId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Training/Doctor`,
    method: 'get',
    params: { ...data }
  })
}

// 查询某个研究者学习情况
export function getCertainDoctorTraining(studyId, doctorId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Training/Doctor/${doctorId}`,
    method: 'get',
    params: { ...data }
  })
}

// 导出研究者者
export function getDoctorTrainingExport(id, data) {
  return request({
    url: `api/Operator/DoctorPatient/${id}/Training/Doctor/Export`,
    method: 'get',
    data: { ...data }
  })
}

// 查看学习统计资料
export function getDPTrainingDocument(studyId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Training/Document`,
    method: 'get',
    params: { ...data }
  })
}

// 查询某个学习资料统计
export function getDPTrainingDocumentStatistics(studyId, documentId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Training/Document/${documentId}`,
    method: 'get',
    params: { ...data }
  })
}

// 导出学习资料统计
export function getPatientTrainingDocumentExport(id, data) {
  return request({
    url: `api/Operator/DoctorPatient/${id}/Training/Document/Export`,
    method: 'get',
    data: { ...data }
  })
}
