import request from '@/utils/request'

/* 获取受试者数据迁移列表 */
export const getPatientListInStudy = (studyId, params) => {
    return request({
        url: `api/Operator/Study/${studyId}/PatientListInStudy`,
        method: 'get',
        params: params
    })
}

/* 访视迁移记录 */
export const getVisitRecord = (params) => {
    return request({
        url: `api/Operator/Study/VisitRecord`,
        method: 'get',
        params: params
    })
}
/* 访视迁移 */
export const getSaveMigrateData = (studyId, data) => {
    return request({
        url: `api/Operator/Study/${studyId}/SaveMigrateData`,
        method: 'put',
        data
    })
}
/*课题版本信息 */
export const getDropStudyVersions = (studyId) => {
    return request({
        url: `api/Operator/Study/DropStudyVersions/${studyId}`,
        method: 'get',
    })
}
/*迁移记录list */
export const getMigrateRecord = (studyId, params) => {
    return request({
        url: `api/Operator/Study/${studyId}/MigrateRecord`,
        method: 'get',
        params: params
    })
}

/*回滚 */
export const getMigrateRollBack = (studyId, recordId) => {
    return request({
        url: `api/Operator/Study/${studyId}/${recordId}/MigrateRollBack`,
        method: 'get',
    })
}

/*预生成迁移报告 */
export const postExportMigrateData = (studyId, data) => {
    return request({
        url: `api/Operator/Study/${studyId}/ExportMigrateData`,
        method: 'post',
        data
    })
}

/*导出文件 */
export const getExportMigrateDataById = (id, params) => {
    return request({
        url: `api/Operator/Study/${id}/ExportMigrateDataById`,
        method: 'get',
        params: params
    })
}