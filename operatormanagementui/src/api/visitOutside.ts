import request from '@/utils/request'

/* 获取某课题下的获取某课题下的访视任务超窗列表 */
export const getStudyPatinetOverVisit = (id, data) => {
    return request({
        url: `api/Operator/DoctorPatient/${id}/PatientOverVisit`,
        method: "get",
        params: { ...data }
    });
};

/* 导出某课题下的访视任务超窗列表 */
export const getStudyPatinetOverVisitExport = (id, data) => {
    return request({
        url: `api/Operator/DoctorPatient/${id}/PatientOverVisit/Export`,
        method: "get",
        params: { ...data }
    });
};

/* 获取全部问卷列表数据 */
export const getStudyPatinetOverQuests = (id) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/Quests`,
    method: 'get'
  })
}
