import request from '@/utils/request'

// 获取患者聊天
export function getChatPatient(studyId, data) {
    return request({
        url: `api/Operator/DoctorPatient/${studyId}/Chat/Patient`,
        method: 'get',
        params: { ...data }
    })
}

// 导出某课题下受试者沟通统计
export function getChatPatientExport(studyId, data) {
    return request({
        url: `api/Operator/DoctorPatient/${studyId}/Chat/Patient/Export`,
        method: 'get',
        params: { ...data }
    })
}

// 获取研究者聊天
export function getChatDoctor(studyId, data) {
    return request({
        url: `api/Operator/DoctorPatient/${studyId}/Chat/Doctor`,
        method: 'get',
        params: { ...data }
    })
}

// 导出某课题下研究者沟通统计
export function getChatDoctorExport(studyId, data) {
    return request({
        url: `api/Operator/DoctorPatient/${studyId}/Chat/Doctor/Export`,
        method: 'get',
        params: { ...data }
    })
}
