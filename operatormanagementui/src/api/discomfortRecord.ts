import request from '@/utils/request'

/* 获取某课题下不良事件问卷 */
export const getStudyAEQuest = (id, params) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/AEQuest`,
    method: 'get',
    params: params
  })
}

/* 导出某课题下不良事件问卷 */
export const getStudyAEQuestExport = (id, params) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/AEQuest/Export`,
    method: 'get',
    params: params
  })
}

/* 获取某个不良事件问卷详情 */
export const getStudyAEQuestDetails = (id, rowId) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/AEQuest/${rowId}`,
    method: 'get'
  })
}
