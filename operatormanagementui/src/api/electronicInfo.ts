import request from '@/utils/request'

// 根据课题Id，返回课题下知情同意版本信息
export const getStudyICFVersionNumber = (id) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Template/Version`,
    method: 'get',
    params: { isAuthFilter: true }
  })
}

// 根据课题Id获取电子知情列表信息
export const getPatientICFStatement = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Statements`,
    method: 'get',
    params: { ...data },
  })
}

// 下载电子知情列表信息
export const exportPatientICFStatement = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Statements/Export`,
    method: 'get',
    params: { ...data },
  })
}

// 根据电子知情Id，返回患者的电子知情详情及知情轨迹集合
export const getICFStatementStatusHistories = (id) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Histories`,
    method: 'get',
  })
}

// 获取版本更新推送列表
export const getICFStatementPush = (studyId, params) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${studyId}/ICFStatementPush`,
    method: 'get',
    params: params,
  })
}

// 下载版本更新推送列表
export const exportICFStatementPush = (studyId, params) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${studyId}/ICFStatementPush/Export`,
    method: 'get',
    params: params,
  })
}

// 转线下知情
export const postOfflineTransfer = (studyId, siteId, patientId, data) => {
  return request({
    url: `api/Doctor/Patient/${studyId}/${siteId}/${patientId}/OfflineTransfer`,
    method: 'post',
    data,
  })
}
// 知情版本更新推送
export const postICFStatementPushSetStatus = (studyId, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${studyId}/ICFStatementPush/SetStatus`,
    method: 'post',
    data,
  })
}

// 获取验签结果
export const getCASignatureResult = (icfStatementID, data) => {
  return request({
    url: `api/Signature/${icfStatementID}/GetCASignatureResult`,
    method: 'get',
    params: data,
  })
}

// 上传线下签署文件
export const uploadPatientICFOfflineSignedFile = (icfStatementID, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${icfStatementID}/OfflineSignedFile`,
    method: 'post',
    data,
  })
}

// 删除线下签署文件
export const deletePatientICFOfflineSignedFile = (patICFStatementFileId) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${patICFStatementFileId}/OfflineSignedFile`,
    method: 'delete',
  })
}

// 获取患者列表
export const getPatientList = (studyId, siteId) => {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Patients/BaseInfo?pageIndex=1&pageSize=100000000`,
    method: 'get',
    params: { siteId: siteId }
  })
}

// 获取知情版本列表
export const getTemplateVersion = (siteId) => {
  return request({
    url: `api/Operator/DoctorPatientICF/Template/${siteId}/SiteInfos`,
    method: 'get'
  })
}

// 校验受试者
export const validManualPush = (studyId, data) => {
  return request({
    url: `/api/Operator/DoctorPatientICF/${studyId}/ICFStatementPush/Manual/Valid`,
    method: 'post',
    data,
  })
}

// 提交人工推送
export const saveManualPush = (studyId, data) => {
  return request({
    url: `/api/Operator/DoctorPatientICF/${studyId}/ICFStatementPush/Manual`,
    method: 'post',
    data,
  })
}