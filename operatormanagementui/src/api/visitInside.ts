import request from '@/utils/request'

// 获取窗口期人员名单
export function getWindowPhasePeople(id, data) {
  return request({
    url: `api/Operator/DoctorPatient/${id}/PatientInVisit`,
    method: 'get',
    params: { ...data }
  })
}

// 导出窗口期人员名单
export function getWindowPhasePeopleExport(id, data) {
  return request({
    url: `api/Operator/DoctorPatient/${id}/PatientInVisit/Export`,
    method: 'get',
    params: { ...data }
  })
}
