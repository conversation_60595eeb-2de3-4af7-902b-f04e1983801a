import request from '@/utils/request'

// 获取回收列表信息
export function getPatientMaterialDistributionRecovery(studyId, params) {
  return request({
    url: `api/Operator/Study/${studyId}/PatientMaterialDistributionRecovery`,
    method: 'get',
    params
  })
}

// 获取发放回收详情
export function getPatientMaterialDistributionRecoveryDetail(studyId, siteId, patientId) {
  return request({
    url: `api/Operator/Study/${studyId}/PatientMaterialDistributionRecovery/${siteId}/${patientId}`,
    method: 'get',
  })
}

// 获取受试者物资回收详情
export function getMaterialRecovery(studyId, siteId, patientId, recoverId) {
  return request({
    url: `api/Operator/Study/${studyId}/${siteId}/${patientId}/MaterialRecovery/${recoverId}`,
    method: 'get',
  })
}

// 删除受试者物资回收详情
export function deleteMaterialRecovery(studyId, siteId, patientId, recoverId) {
  return request({
    url: `api/Operator/Study/${studyId}/${siteId}/${patientId}/MaterialRecovery/${recoverId}`,
    method: 'delete',
  })
}

// 获取课题下的物资列表
export const getMaterials = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/Materials`,
    method: 'get'
  })
}

// 保存回收详情
export const postMaterialRecovery = (studyId, siteId, patientId, recoverId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/${siteId}/${patientId}/MaterialRecovery/${recoverId}`,
    method: 'post',
    data
  })
}

// 物流订单状态
export const getLogisticsOrderState = (studyId, params) => {
  return request({
    url: `api/Operator/Study/${studyId}/LogisticsOrderState`,
    method: 'get',
    params
  })
}
