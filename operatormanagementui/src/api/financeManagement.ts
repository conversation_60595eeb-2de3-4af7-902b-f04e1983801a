import request from '@/utils/request'

// 获取项目款列表
export const getStudyFinanceSummary = (studyId) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/StudyFinance/Summary`,
    method: 'get'
  })
}

// 获取提现申请列表
export const getWithdrawalOrder = (studyId, params) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/PatientFinance/WithdrawalOrder`,
    method: 'get',
    params
  })
}

// 获取体现统计信息
export const getPatientFinanceSummary = (studyId) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/PatientFinance/Summary`,
    method: 'get',
  })
}

// 获取受试者余额体现订单详情
export const getWithdrawalOrderOrder = (studyId, orderId) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/PatientFinance/WithdrawalOrder/${orderId}`,
    method: 'get',
  })
}

// 保存或流转受试者余额提现订单
export const postWithdrawalOrder = (studyId, patientId, orderId, data) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/${patientId}/PatientFinance/WithdrawalOrder?orderId=${orderId}`,
    method: 'post',
    data
  })
}

// 保存项目款记录
export const postStudyFinance = (studyId, recordId, data) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/StudyFinance?recordId=${recordId}`,
    method: 'post',
    data
  })
}

// 获取项目款详情
export const getStudyFinance = (studyId, recordId) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/StudyFinance/${recordId}`,
    method: 'get',
  })
}
