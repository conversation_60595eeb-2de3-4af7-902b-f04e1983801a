import request from '@/utils/request'

// 获取患者列表信息
export function getPatientList(studyId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/Patients`,
    method: 'get',
    params: { ...data },
  })
}
// 获取标签下拉
export function getPatientTagList(studyId) {
  return request({
    url: `api/Operator/Study/${studyId}/PatientTag`,
    method: 'get',
  })
}
// 获取问卷详细信息
export function getQuestDetailsMessage(patientId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/BGQuest`,
    method: 'get',
    params: { ...data },
  })
}

export function getQuestDetailsMessagePdf(patientId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/BGQuest/Export`,
    method: 'get',
    params: { ...data },
  })
}

// 获取原图
export function getQuestFileImage(fileId) {
  return request({
    url: `api/Operator/DoctorPatient/Quest/File/${fileId}`,
    method: 'get',
  })
}

// 获取病例的访视列表信息
export function getCrfVisitInfo(patientId) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/Visits`,
    method: 'get',
  })
}

// 获取病历的crf信息
export function getCrfQuestInfo(patientId, questId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/Quest/${questId}`,
    method: 'get',
    params: {
      ...data,
    },
  })
}

// 根据患者问卷Id导出问卷详细信息PDF
export function postCrfQuestInfoExportPDF(patientId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/Quest/Export`,
    method: 'post',
    responseType: 'arraybuffer',
    data: { ...data },
  })
}

// 重新筛选
export function patientRescreening(patientId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/Rescreening`,
    method: 'post',
    data: { ...data },
  })
}

// 重新筛选导出
export function exportPatRescreeningInfo(patientId) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/Rescreening`,
    method: 'get',
  })
}

// 获取更新后的问卷修改内容
export function putPatientQuestChangeData(questId, data) {
  return request({
    url: `api/Operator/DoctorPatient/${questId}/PatientQuestChangeData`,
    method: 'put',
    data,
  })
}
// 更新问卷修改内容
export function putUpdatePatientQuestChangeData(data) {
  return request({
    url: `api/Operator/DoctorPatient/UpdatePatientQuestChangeData`,
    method: 'put',
    data,
  })
}

// 导出事件信息PDF
export function getExportEventInformationPDF(patientId) {
  return request({
    url: `api/Operator/DoctorPatient/${patientId}/ExportEventInformationPDF`,
    method: 'get',
  })
}

/*上传pdf文件*/
// export const postQuestFile = (questId, dctCode, data) => {
//   return request({
//       url: `api/Patient/Quest/File/${questId}/${dctCode}`,
//       method: 'post',
//       data,
//   })
// };