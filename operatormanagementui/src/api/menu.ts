import request from '@/utils/request'

// 获取菜单
// export const GetMenus = () => {
//   return request({
//     url: '/api/menus',
//     method: 'get',
//   })
// }
// 根据项目获取菜单
export const getMenusByProject = (studyId) => {
  return request({
    url: `/api/Operator/Study/${studyId}/CurrentUserPermissions`,
    method: 'get',
    params: { appType: 5 }
  })
}
// 转换菜单结构
export const transferMenuData = (menus) => {
  // 过滤isNavMenu为0的菜单
  return menus
    .map(menu => {
      if (menu.isNavMenu !== 1) return null
      const {
        menuName: title,
        menuUrl: url,
        childSysMenus,
        ...rest
      } = menu
    // 递归处理子节点
    const children = transferMenuData(childSysMenus)
    // 构建基础对象
    const transformed = {
      title,
      url,
      ...rest
    };
    // 仅在存在子节点时添加children属性
    if (children?.length > 0) {
      transformed.children = children;
    }
    return transformed
    })
    .filter(Boolean)
}