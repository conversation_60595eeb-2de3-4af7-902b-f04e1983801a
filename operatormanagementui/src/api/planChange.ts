import request from '@/utils/request'

// 
// export function getLogisticsOrder(studyId, params) {
//   return request({
//     url: `api/Operator/Study/${studyId}/LogisticsOrder`,
//     method: 'get',
//     params: params
//   })
// }

// 获取问卷窗口列表
export function postQuestUnPlannedWindowList(data?: any) {
  return request({
    url: `/api/Operator/DoctorPatient/QuestUnPlannedWindow`,
    method: 'post',
    data
  })
}

// 获取问卷窗口详情（通过windowId）
export function getQuestUnPlannedWindowDetail(windowId: string, params?: any) {
  return request({
    url: `/api/Operator/DoctorPatient/${windowId}/QuestUnPlannedWindowDetail`,
    method: 'get',
    params: params
  })
}

// 获取问卷窗口详情（通过questId）
export function getQuestUnPlannedWindowDetail2(questId: string, params?: any) {
  return request({
    url: `/api/Operator/DoctorPatient/${questId}/QuestUnPlannedWindowDetail2`,
    method: 'get',
    params: params
  })
}

// 保存/更新问卷计划外窗口
export function saveOrUpdateQuestUnplannedWindow(data: any) {
  return request({
    url: '/api/Operator/DoctorPatient/SaveOrUpdateQuestUnplannedWindow',
    method: 'post',
    data
  })
}

// 获取研究者问卷修改改人列表
export function getUnplannedSelectUsers(questId: string) {
  return request({
    url: `/api/Operator/DoctorPatient/${questId}/UnplannedSelectUsers`,
    method: 'get',
  })
}

// 导出计划外报告
export function exportQuestUnPlannedWindow(studyId: string, data) {
  return request({
    url: `/api/Operator/DoctorPatient/${studyId}/ExportQuestUnPlannedWindow`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    // responseType: 'blob', // 导出一般为文件流
  })
}
