import request from '@/utils/request'

// 后台管理澄清列表
export const getClarifyList = (studyId, data) => {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/QuestClarify`,
    method: 'get',
    params: { ...data },
  })
}

// 澄清详情
export const getClarifyDetail = (clarifyId) => {
  return request({
    url: `api/Operator/DoctorPatient/${clarifyId}/QuestClarifyDetail`,
    method: 'get',
  })
}

// 关闭澄清
export const closeClarify = (data) => {
  return request({
    url: `api/Patient/Quest/CloseQuestClarify`,
    method: 'put',
    data,
  })
}

// 特定题目澄清列表
export const getQuestClarifyListInfo = (questDataId, questId) => {
  return request({
    url: `api/Patient/Quest/${questDataId}/GetQuestClarifyListInfo/${questId}`,
    method: 'get',
    params: { IsManagement: true },
  })
}
