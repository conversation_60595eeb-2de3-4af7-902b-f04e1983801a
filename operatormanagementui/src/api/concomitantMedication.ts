import request from '@/utils/request'

/* 获取某课题下合并用药问卷 */
export const getStudyCMQuest = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/CMQuest`,
    method: 'get',
    params: { ...data }
  })
}

/* 导出某课题下合并用药问卷 */
export const getStudyCMQuestExport = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/CMQuest/Export`,
    method: 'get',
    params: { ...data }
  })
}

/* 获取某个合并用药问卷详情 */
export const getStudyCMQuestDetails = (id, rowId, data) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/CMQuest/${rowId}`,
    method: 'get',
    data
  })
}
