import request from '@/utils/request'

// 查询物流订单信息
export function getLogisticsOrder(studyId, data) {
  return request({
    url: `api/Operator/Study/${studyId}/LogisticsOrder`,
    method: 'get',
    params: { ...data }
  })
}

// 获取物流订单详情信息
export function getLogisticsOrderOrderId(studyId, orderId, params) {
  return request({
    url: `api/Operator/Study/${studyId}/LogisticsOrder/${orderId}`,
    method: 'get',
    params: params
  })
}

// 取消订单
export function putCancelLogisticsOrderOrderId(studyId, orderId, data) {
  return request({
    url: `api/Operator/Study/${studyId}/CancelLogisticsOrder/${orderId}`,
    method: 'put',
    data
  })
}

// 获取课题下寄送物品类型
export function getGoodsType(studyId) {
  return request({
    url: `api/Operator/Study/${studyId}/GoodsType`,
    method: 'get',
    // params: params
  })
}
/**物资订单查询
* @param studyId
* @param {
    orderType: 'number',
    siteId: 'string',
    materialName: 'string',
    createTimeBegin: 'string',
    createTimeEnd: 'string',
    orderState: 'string',
    orderNo: 'string',
    waybillNo: 'string',
  }
* @return {
  "items": [
    {
      "id": "string",
      "orderType": 0,
      "dctSiteId": "string",
      "dctSiteName": "string",
      "orderNo": "string",
      "waybillNo": "string",
      "logisticsProvider": "string",
      "orderState": 0,
      "orderStateDisplay": "string",
      "materials": [
        {
          "materialId": "string",
          "materialName": "string",
          "materialSpecs": "string",
          "materialPic": "string",
          "number": 0
        }
      ],
      "rcptName": "string",
      "rcptPhoneNo": "string",
      "rcptProvinceCode": "string",
      "rcptProvinceName": "string",
      "rcptCityCode": "string",
      "rcptCityName": "string",
      "rcptAreaCode": "string",
      "rcptAreaName": "string",
      "rcptAddressDetail": "string"
    }
  ],
  "totalItemCount": 0
}
*/
export function getMaterialOrder(studyId, params) {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialOrder`,
    method: 'get',
    params: params
  })
}
/**物资订单详情
* @param studyId
* @param orderId
* @return {
  "creator": "string",
  "createTime": "string",
  "lastUpdator": "string",
  "lastUpdateTime": "string",
  "remark": "string",
  "signTime": "string",
  "signUsername": "string",
  "waybillRecords": [
    {
      "id": "string",
      "waybillTime": "string",
      "logisticsInfo": "string"
    }
  ],
  "id": "string",
  "orderType": 0,
  "dctSiteId": "string",
  "dctSiteName": "string",
  "orderNo": "string",
  "waybillNo": "string",
  "logisticsProvider": "string",
  "orderState": 0,
  "orderStateDisplay": "string",
  "materials": [
    {
      "materialId": "string",
      "materialName": "string",
      "materialSpecs": "string",
      "materialPic": "string",
      "number": 0
    }
  ],
  "rcptName": "string",
  "rcptPhoneNo": "string",
  "rcptProvinceCode": "string",
  "rcptProvinceName": "string",
  "rcptCityCode": "string",
  "rcptCityName": "string",
  "rcptAreaCode": "string",
  "rcptAreaName": "string",
  "rcptAddressDetail": "string"
}
*/
export function getMaterialOrderDetails(studyId, orderId) {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialOrder/${orderId}`,
    method: 'get',
  })
}
/**取消物资订单
* @param studyId
* @param orderId
*/
export function putCancelMaterialOrder(studyId, orderId) {
  return request({
    url: `api/Operator/Study/${studyId}/CancelMaterialOrder/${orderId}`,
    method: 'put',
  })
}
/**保存修改物资订单的订单记录
* @param {orderRecord: string}
*/
export function postMaterialOrderRecord(data) {
  return request({
    url: `api/Operator/Study/MaterialOrderRecord`,
    method: 'post',
    data
  })
}

// 获取课题下的物资列表
export const getMaterials = (studyId, params) => {
  return request({
    url: `api/Operator/Study/${studyId}/Materials`,
    method: 'get',
    params: params
  })
}

// 获取人工补单的信息
export const getManualReplenishment = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/ManualReplenishment`,
    method: 'get',
    // params: {
    //   materialId: materialId
    // }
  })
}
// 获取物资发放计划详情
export const getMaterialDistributionPlan = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialDistributionPlan`,
    method: 'get',
    params: { ...data }
  })
}
// 保存人工补单
export const postManualReplenishment = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/ManualReplenishment`,
    method: 'put',
    data
  })
}
// 获取选择中心的渠道
export const getChannels = (siteId) => {
  return request({
    url: `api/Operator/Study/${siteId}/OMSChannels`,
    method: 'get',
  })
}
