
# # 这个就是从dockerhub拿下来的, 主要是dockerhub经常被墙, 缓存下提高效率
# # 如果要在本地编译, 记得pull 下package.trialdata.cn/trialdatadocker/library/nginxalpine:*******
# # 然后tag成ipackage的地址
FROM ipackage.trialdata.cn/trialdatadocker/library/nginxalpine:******* as base


# # 桌面端我们主要用的是 element, 所以换一个编译包
# FROM ipackage.trialdata.cn/trialdatadocker/library/h5-builder:*******  AS build
FROM ipackage.trialdata.cn/trialdatadocker/library/element-builder:*******  AS build
WORKDIR  /usr/app
COPY . .


# RUN npm install -g cnpm --registry=https://registry.npm.taobao.org/
#RUN cnpm i
#RUN npm config set registry https://registry.npm.taobao.org/
#RUN npm config set cdregistry.registry https://package.trialdata.cn/npm/trialdataNpm/
#RUN cnpm config set registry https://package.trialdata.cn/npm/trialdataNpm/
# RUN cnpm config set @taobao-public-scope:registry https://registry.npm.taobao.org/

# RUN cnpm login --registry=https://package.trialdata.cn/npm/trialdataNpm/ --scope=your-private-scope --always-auth
# RUN cnpm login --registry=https://package.trialdata.cn/npm/trialdataNpm/ --username=poweruser --password=PowerUser@0202 --email=<EMAIL>
#RUN docker login https://package.trialdata.cn/npm/trialdataNpm/ -u poweruser -p PowerUser@0202


#ENV NPM_CONFIG_REGISTRY=https://package.trialdata.cn/npm/trialdataNpm/
#ENV NPM_CONFIG_USERNAME=poweruser
#ENV NPM_CONFIG_PASSWORD=PowerUser@0202
#ENV NPM_CONFIG_EMAIL=<EMAIL>

#RUN cnpm login --registry=$NPM_CONFIG_REGISTRY --scope=@your-private-scope --always-auth
#RUN cnpm login --registry=$NPM_CONFIG_REGISTRY --always-auth --username=$NPM_CONFIG_USERNAME --password=$NPM_CONFIG_PASSWORD --email=$NPM_CONFIG_EMAIL
#RUN echo -e "$NPM_CONFIG_USERNAME\n$NPM_CONFIG_PASSWORD\n$NPM_CONFIG_EMAIL" | cnpm login --registry=$NPM_CONFIG_REGISTRY --always-auth --stdin

# RUN cnpm config list
RUN npm i     --registry=https://registry.npmmirror.com
RUN npm run k8s

FROM base AS final
WORKDIR /app
# 这次把文件放到patientui下面, 是因为目前编译出来默认在patientui目录下, 这个需要调整.
COPY --from=build /usr/app/dist/prod /usr/share/nginx/html
COPY --from=build /usr/app/verinfo.txt /usr/share/nginx/html/verinfo.txt
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 启动应用跑的代码
CMD ["nginx", "-g", "daemon off;"]
# 使用envsubt 用环境变量替换后台api的参数
# CMD ["/bin/sh","-c",""""""]


