import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import viteSvgIcons from 'vite-plugin-svg-icons'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

// https://vitejs.dev/config/
// @ts-ignore
export default ({ mode }) => defineConfig({
  publicDir: 'public',
  base:'/OperatorManagementUI/', //默认顶级目录
  resolve: {
    alias: [
      {find: '@', replacement: path.resolve(__dirname, 'src')}
    ]
  },
  server: {
    // base: '/operatorui/', // 生产中服务时的基本公共路径
    // 是否自动在浏览器打开
    open: true,
    host: true,
    hmr: true,
    proxy: {
      '/api': {
        target: process.env.VITE_APP_BASE_URL, // 后端接口的域名
        // rewrite: (path) => path.replace(/^\/api/, '')
        changeOrigin: true,
      },
    },
    port: 8087 //本地启动端口号
  },
  build: {
    sourcemap: true,
    manifest: true,
    terserOptions: {
      compress: {
        keep_infinity: true,
        // 删除console
        drop_console: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          // 'element-plus': ['element-plus'],
          echarts: ['echarts']
        }
      }
    },
    chunkSizeWarningLimit: 600
  },
  optimizeDeps: {
    //include: ['axios', 'nprogress', 'mockjs',] //依赖预编译
  },
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    viteSvgIcons({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(__dirname, 'src/assets/svg')],
      // 指定symbolId格式
      symbolId: 'icon-[dir]-[name]',
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        // 全局变量
        additionalData: '@import "./src/assets/style/global-variables.scss";',
      },
    },
    postcss: {
      plugins: [
        require('autoprefixer'),
        require('tailwindcss'),
        require('postcss-nested'),
        require('postcss-simple-vars'),
        require('postcss-import')
      ]
    }
  }
  
})
