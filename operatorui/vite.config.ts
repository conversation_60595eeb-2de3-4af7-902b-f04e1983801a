import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import viteSvgIcons from 'vite-plugin-svg-icons'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import vueSetupExtend from 'vite-plugin-vue-setup-extend'

// https://vitejs.dev/config/
export default ({ mode }) => defineConfig({
  define: {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false', // 禁用详细的错误信息
  },
  publicDir: 'public',
  base: '/operatorui/', // 默认顶级目录
  resolve: {
    alias: [
      { find: '@', replacement: path.resolve(__dirname, 'src') }
    ]
  },
  server: {
    base: '/operatorui/', // 生产中服务时的基本公共路径
    // 是否自动在浏览器打开
    open: true,
    host: true,
    hmr: true,
    proxy: {
      '/api': {
        target: process.env.VITE_APP_BASE_URL, // 后端接口的域名
        // rewrite: (path) => path.replace(/^\/api/, '')
        changeOrigin: true,
      },
    },
    port: 8086 // 本地启动端口号
  },
  build: {
    sourcemap: true,
    manifest: true,
    terserOptions: {
      compress: {
        keep_infinity: true,
        // 删除console
        drop_console: true,
      },
    },
    rollupOptions: {
      output: {
        // manualChunks: {
        //   // 'element-plus': ['element-plus'],
        //   // echarts: ['echarts']
        // }
      }
    },
    chunkSizeWarningLimit: 600
  },
  optimizeDeps: {
    // include: ['axios', 'nprogress', 'mockjs',] // 依赖预编译
  },
  plugins: [
    vue(),
    vueSetupExtend(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    viteSvgIcons({
      // 指定需要缓存的图标文件夹
      iconDirs: [path.resolve(__dirname, 'src/assets/svg')],
      // 指定symbolId格式
      symbolId: 'icon-[dir]-[name]',
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        // 全局变量
        additionalData: '@import "./src/assets/style/global-variables.scss";',
      },
    },
    postcss: {
      plugins: [
        require('autoprefixer'),
        require('tailwindcss'),
        require('postcss-nested'),
        require('postcss-simple-vars'),
        require('postcss-import')
      ]
    }
  }
})
