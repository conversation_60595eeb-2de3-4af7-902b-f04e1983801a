<!-- table表格 -->
<template>
<div class="page-box">
  <el-config-provider :locale="locale">
  <!-- 搜索选项 -->
  <el-form
    v-if="!!search"
    ref="searchForm"
    class="search"
    :model="searchModel"
    :inline="true"
    :label-position="searchLabelPosition"
    :label-width="search.labelWidth"
  >
    <slot name="head-nav-slot" />
    <el-form-item
      v-for="item in search.fields"
      :key="item.name"
      :label="item.label"
      :prop="item.name"
    >
      <el-select
        v-if="item.type === 'select'"
        v-model="searchModel[item.name]"
        :filterable="!!item.filterable"
        :multiple="!!item.multiple"
        :clearable="!item?.clearable"
        allow-create
        :placeholder="`请选择${item.label}`"
        :style="{ width: search.inputWidth, ...item.style }"
      >
        <el-option
          v-for="option of item.options"
          :key="option.value"
          :label="option.name"
          :value="option.value"
        />
      </el-select>
      <el-select
        v-else-if="item.type === 'selectChangeSearch'"
        v-model="searchModel[item.name]"
        :filterable="!!item.filterable"
        :multiple="!!item.multiple"
        :clearable="!item?.clearable"
        allow-create
        :placeholder="`请选择${item.label}`"
        :style="{ width: search.inputWidth, ...item.style }"
        @change="handleSearch"
      >
        <el-option
          v-for="option of item.options"
          :key="option.value"
          :label="option.name"
          :value="option.value"
        />
      </el-select>
      <el-radio-group
        v-else-if="item.type === 'radio'"
        v-model="searchModel[item.name]"
        :style="{ width: search.inputWidth, ...item.style }"
      >
        <el-radio
          v-for="option of item.options"
          :key="option.value"
          :label="option.value"
          >{{ option.name }}
        </el-radio>
      </el-radio-group>
      <!-- <el-radio-group
        v-model="searchModel[item.name]"
        v-else-if="item.type === 'radio-button'"
        :style="{ width: search.inputWidth, ...item.style }"
      >
        <el-radio-button
          v-for="option of item.options"
          :key="option.value"
          :label="option.value"
          >{{ option.name }}</el-radio-button
        >
      </el-radio-group> -->

      <el-checkbox-group
        v-else-if="item.type === 'checkbox'"
        v-model="searchModel[item.name]"
        :style="{ width: search.inputWidth, ...item.style }"
      >
        <el-checkbox
          v-for="option of item.options"
          :key="option.value"
          :label="option.value"
          >{{ option.name }}</el-checkbox
        >
      </el-checkbox-group>

      <el-checkbox-group
        v-else-if="item.type === 'checkbox-button'"
        v-model="searchModel[item.name]"
        :style="{ width: search.inputWidth, ...item.style }"
      >
        <el-checkbox-button
          v-for="option of item.options"
          :key="option.value"
          :label="option.value"
          >{{ option.name }}</el-checkbox-button
        >
      </el-checkbox-group>

      <el-date-picker
        v-else-if="item.type === 'date'"
        v-model="searchModel[item.name]"
        type="date"
        format="YYYY-MM-DD"
        clearable
        :placeholder="`请选择${item.label}`"
        :style="{ width: search.inputWidth, ...item.style }"
        @change="
          handleDateChange($event, item, 'YYYY-MM-DD', pickerChangeUse)
        "
      />

      <el-date-picker
        v-else-if="item.type === 'datetime'"
        v-model="searchModel[item.name]"
        type="datetime"
        clearable
        format="YYYY-MM-DD HH:mm:ss"
        :style="{ width: search.inputWidth, ...item.style }"
        :placeholder="`请选择${item.label}`"
        @change="
          handleDateChange(
            $event,
            item,
            'YYYY-MM-DD HH:mm:ss',
            pickerChangeUse
          )
        "
      />

      <el-date-picker
        v-else-if="item.type === 'daterange'"
        v-model="searchModel[item.name]"
        type="daterange"
        format="YYYY-MM-DD"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :clearable="item.clearable"
        :style="{ width: search.inputWidth, ...item.style }"
        @change="
          handleRangeChange($event, item, 'YYYY-MM-DD', pickerChangeUse)
        "
      />

      <el-date-picker
        v-else-if="item.type === 'datetimerange'"
        v-model="searchModel[item.name]"
        type="datetimerange"
        format="YYYY-MM-DD HH:mm:ss"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :style="{ width: search.inputWidth, ...item.style }"
        @change="
          handleRangeChange(
            $event,
            item,
            'YYYY-MM-DD HH:mm:ss',
            pickerChangeUse
          )
        "
      />

      <el-input
        v-else
        v-model.trim="searchModel[item.name]"
        maxlength="1000"
        clearable
        :placeholder="`请输入${item.label}`"
        :style="{ width: search.inputWidth, ...item.style }"
      />
    </el-form-item>

    <el-form-item class="search-btn">
      <el-button v-if="rightBtnhide" type="primary" @click="handleSearch">
        <el-icon><Search /></el-icon>
        查询
      </el-button>
      <el-button v-if="rightBtnhide" @click="handleReset">
        <el-icon><RefreshRight /></el-icon>重置
      </el-button>
      <el-button
        v-if="showbtnfalgExcel"
        type="primary"
        @click="exportBtnExcel"
      >
        导出Excel
      </el-button>
    </el-form-item>
  </el-form>
  <!-- 加上了中间颜色显示 -->
  <div v-if="hideCenter">
    <slot name="hideCenter" />
  </div>
  <!-- title 和 工具栏 -->
  <div v-if="!hideTitleBar" class="head">
    <slot name="title">
      <span class="title">{{ title }}</span>
    </slot>
    <div class="toolbar">
      <slot name="toolbar" />
    </div>
  </div>
  <!-- table表格栏
  element-loading-text="拼命加载中"
  element-loading-spinner="el-icon-loading"
  :element-loading-svg="`
  <path class='path' d='
    M 30 15
    L 28 17
    M 25.61 25.61
    A 15 15, 0, 0, 1, 15 30
    A 15 15, 0, 1, 1, 27.99 7.5
    L 15 15
  ' style='stroke-width: 4px; fill: rgba(0, 0, 0, 0)'/>
  `"
  class="custom-loading-svg"
  element-loading-svg-view-box="-10, -10, 50, 50"
  element-loading-background="rgba(0, 0, 0, 0.8)"
  -->
  <div class="my-el-table">
    <el-table
      v-loading="loading"
      element-loading-text="拼命加载中"
      :data="tableData"
      :row-key="rowKey"
      tooltip-effect="dark"
      :stripe="!tableRowClassName"
      :default-sort="defaultSort"
      :border="border"
      :row-class-name="tableRowClassName"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-for="item in columns"
        :key="item.label"
        :filter-method="item.filters && filterHandler"
        show-overflow-tooltip
        :sortable="item.sortable"
        v-bind="item"
      >
        <template v-if="!!item.labelSlot" #header="scope">
          <slot :name="item.labelSlot" v-bind="scope" />
        </template>
        <!-- numberToStingSort 排序可以排数字不能排字符串  yong数字进行排序，渲染用字符串 -->
        <template
          v-if="!!item.tdSlot || !!item.numberToStingSort"
          #default="scope"
        >
          <slot v-if="!!item.tdSlot" :name="item.tdSlot" v-bind="scope" />
          <slot
            v-if="!!item.numberToStingSort"
            :name="item.numberToStingSort"
            v-bind="scope"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 分页 -->
  <el-pagination
    v-if="paginationConfig.show && total > 0"
    v-model:currentPage="pageIndex"
    v-model:pageSize="pageSize"
    class="pagination"
    :style="paginationConfig.style"
    :layout="paginationConfig.layout"
    :total="total"
    :page-sizes="paginationConfig.pageSizes"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
  </el-config-provider>
</div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onBeforeMount } from "vue";
import { Search, RefreshRight } from "@element-plus/icons-vue";
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'dayjs/locale/zh-cn'

const delay = (function () {
  let timer: any = 0;
  return function (callback, ms) {
    clearTimeout(timer);
    timer = setTimeout(callback, ms);
  };
})();
const formatDate = (date, format) => {
  const obj = {
    "M+": date.getMonth() + 1,
    "D+": date.getDate(),
    "H+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
    "q+": Math.floor((date.getMonth() + 3) / 3),
    "S+": date.getMilliseconds(),
  };
  if (/(y+)/i.test(format)) {
    format = format.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  for (const k in obj) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? obj[k]
          : ("00" + obj[k]).substr(("" + obj[k]).length)
      );
    }
  }
  return format;
};
const getSearchModel = (search) => {
  const searchModel = {};
  if (search && search.fields) {
    search.fields.forEach((item) => {
      switch (item.type) {
        case "checkbox":
        case "checkbox-button":
          searchModel[item.name] = [];
          break;
        default:
          break;
      }
      if (item.defaultValue !== undefined) {
        searchModel[item.name] = item.defaultValue;
        // 日期范围和时间范围真实变量默认值
        if (
          (item.type === "daterange" || item.type === "datetimerange") &&
          !!item.trueNames &&
          Array.isArray(item.defaultValue)
        ) {
          item.defaultValue.forEach((val, index) => {
            searchModel[item.trueNames[index]] = val;
          });
        }
      }
    });
  }
  return searchModel;
};
export default defineComponent({
  components: {
    Search,
    RefreshRight,
  },
  props: {
    // 搜索的Label居位 > left top
    searchLabelPosition: {
      type: String,
      default: 'left',
    },
    // 导出Excel方法
    showbtnfalgExcel: {
      type: Boolean,
      default: false,
    },
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
    // 请求数据的方法Excel
    requestExportExcel: {
      type: Function,
      default: () => {},
    },
    // 请求数据的方法Pdf
    requestExportPdf: {
      type: Function,
      default: () => {},
    },
    // 表格标题
    title: {
      type: String,
      default: "",
    },
    // 是否隐藏标题栏
    hideTitleBar: {
      type: Boolean,
      default: false,
    },
    // 搜索表单配置，false表示不显示搜索表单
    search: {
      type: [Boolean, Object],
      default: false,
    },
    border: {
      type: Boolean,
      default: false,
    },
    // 表头配置
    columns: {
      type: Array,
      default: () => [],
    },
    // 行数据的Key，同elementUI的table组件的row-key
    rowKey: {
      type: String,
      default: "id",
    },
    // 分页配置，false表示不显示分页
    pagination: {
      type: [Boolean, Object],
      default: () => ({}),
    },
    // 表格上放插槽插入的内容的显示和隐藏
    hideCenter: {
      type: Boolean,
      default: false,
    },
    // 自定义表格的颜色
    tableRowClassName: {
      type: Function,
    },
    // 排序高亮
    defaultSort: {
      type: [Boolean, Object],
      default: () => ({}),
    },
    // 右侧的按钮(查询和重置)的显示和隐藏
    rightBtnhide: {
      type: Boolean,
      default: true,
    },
    // 日期选择完默认所有值调用查询接口
    pickerChangeUse: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    // 优化搜索字段，
    // 1、如果搜索配置有transform处理函数，执行transform
    // 2、删除日期范围默认的name字段
    const optimizeFields = (search) => {
      const searchModel = JSON.parse(JSON.stringify(state.searchModel));
      if (search && search.fields) {
        search.fields.forEach((item) => {
          if (!searchModel.hasOwnProperty(item.name)) {
            return;
          }
          if (item.transform) {
            searchModel[item.name] = item.transform(searchModel[item.name]);
          }
          if (
            (item.type === "daterange" || item.type === "datetimerange") &&
            !!item.trueNames
          ) {
            delete searchModel[item.name];
          }
        });
      }
      return searchModel;
    };

    // 请求列表数据
    const getTableData = async () => {
      state.loading = true;
      const searchModel = state.oldSearchModel;
      const { data, total } = await props.request({
        pageIndex: state.pageIndex,
        pageSize: state.pageSize,
        ...searchModel,
      });
      state.loading = false;
      state.tableData = data;
      state.total = total;
    };

    onBeforeMount(() => {
      getTableData();
    });

    const state = reactive({
      searchFalg: false, // 是否搜索
      searchModel: getSearchModel(props.search),
      oldSearchModel: {},
      loading: false,
      tableData: [],
      total: 0,
      pageIndex: 1,
      locale: zhCn,
      pageSize: (!!props.pagination && props.pagination.pageSize) || 10,
      paginationConfig: {
        show: false,
      },
      // 导出Excel
      exportBtnExcel(e) {
        delay(() => {
          state.oldSearchModel = { ...optimizeFields(props.search) };
          const searchModel = state.oldSearchModel;
          props.requestExportExcel(searchModel);
        }, 600);
      },
      // state.searchModel所有的搜索条件
      handleSearch() {
        delay(() => {
          state.searchFalg = true;
          state.oldSearchModel = { ...optimizeFields(props.search) };
          state.pageIndex = 1;
          getTableData();
        }, 600);
      },
      // 重置函数
      handleReset() {
        if (JSON.stringify(state.searchModel) === "{}") {
          return;
        }
        delay(() => {
          state.oldSearchModel = {};
          state.pageIndex = 1;
          state.searchModel = getSearchModel(props.search);
          getTableData();
        }, 600);
      },
      // 刷新
      refresh() {
        getTableData();
      },

      // 当前页变化
      handleCurrentChange() {
        getTableData();
      },
      // 改变每页size数量
      handleSizeChange() {
        state.pageIndex = 1;
        getTableData();
      },
      // 全选
      handleSelectionChange(arr) {
        emit("selectionChange", arr);
      },
      // 过滤方法
      filterHandler(value, row, column) {
        const property = column["property"];
        return row[property] === value;
      },
      // 日期范围
      handleDateChange(date, item, format, pickerChangeUse) {
        state.searchModel[item.name] = date ? formatDate(date, format) : "";
        if (pickerChangeUse) {
          state.handleSearch();
        }
      },
      handleRangeChange(date, item, format, pickerChangeUse) {
        const arr = !!date && date.map((d) => formatDate(d, format));
        state.searchModel[item.name] = arr || [];
        if (!item.trueNames) {
          // 选择完日期直接查询
          if (pickerChangeUse) {
            state.handleSearch();
          }
          return;
        }
        if (arr) {
          arr.forEach((val, index) => {
            state.searchModel[item.trueNames[index]] = val;
          });
        } else {
          item.trueNames.forEach((key) => {
            delete state.searchModel[key];
          });
        }
        // 选择完日期直接查询
        if (pickerChangeUse) {
          state.handleSearch();
        }
      },
    });

    if (typeof props.pagination === "object") {
      const { layout, pageSizes, style } = props.pagination;
      state.paginationConfig = {
        show: true,
        layout: layout || "total, sizes, prev, pager, next, jumper",
        pageSizes: pageSizes || [10, 20, 30, 40, 50, 100],
        style: style || {},
      };
    }

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style scoped>
.page-box {
  width: 100%;
  box-sizing: border-box;
}
.page-box .search {
  padding: 20px 20px 0;
  background: #fff;
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
}
.page-box .el-form-item {
  margin-bottom: 20px;
}
.page-box .search-btn {
  margin-left: auto;
}
.page-box .search /deep/ .el-input-number,
.el-input__inner {
  text-align: left;
}
.page-box .head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0;
  background: #fff;
}
.page-box .head .title {
  font-size: 18px;
  font-weight: 700;
}
.page-box .my-el-table {
  padding: 20px;
  background: #fff;
}
.my-el-table /deep/ th {
  background: #f6f6f6 !important;
  color: rgba(0, 0, 0, 0.85);
}
.page-box .pagination {
  padding: 0 20px 20px;
  background: #fff;
  text-align: right;
  display: flex;
  justify-content: center;
}
.page-box .pagination:last-child {
  margin-right: 0;
}
</style>
