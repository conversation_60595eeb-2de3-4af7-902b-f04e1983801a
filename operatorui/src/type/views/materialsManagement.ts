
// export interface MaterialDistributionRule {
// 	id: string;
// 	materialDistributionPlanId: string;
// 	ruleName: string;
// 	ruleBaseOn: number;
// 	theDaysAfterBaseOn: string;
// 	materialDistributionRuleState?: number | null | undefined;
// 	isEnable: boolean;
// }

export interface MaterialDistributionPlanAndMaterialConfig {
	planAndMaterialConfigId: string;
	materialDistributionPlanId: string;
	number: number;
	id: string;
	materialType?: any;
	omsMaterialId?: any;
	materialName?: any;
	materialSpecs?: any;
	lastUpdator?: any;
	lastUpdatetime?: any;
	isRandom: boolean;
	materialId: string;
	materialSepcId?: any;
	specCode?: any;
	omsMaterialSpecId?: any;
}

export interface MaterialDistributionPlanAndSiteConfig {
	id: string;
	materialDistributionPlanId: string;
	siteId: string;
	siteName: string;
}

export interface MaterialDistributionArm {
	id: string;
	armCode: string;
	armName: string;
}

export interface RuleForm {
	id: string;
	planName: string;
	distributionTarget: number;
	tag: string;
	studyId: string;
	materialDistributionRules: any[];
	materialDistributionPlanAndMaterialConfigs: MaterialDistributionPlanAndMaterialConfig[];
	materialDistributionPlanAndSiteConfigs: MaterialDistributionPlanAndSiteConfig[];
	materialDistributionArms: MaterialDistributionArm[];
	tagArr: string[];
}

export interface PlanName {
	required: boolean;
	message: string;
	trigger: string;
}

export interface DistributionTarget {
	required: boolean;
	message: string;
	trigger: string;
}

export interface Rule {
	planName: PlanName[];
	distributionTarget: DistributionTarget[];
}

export interface TagOption {
	id: string;
	armCode: string;
	armName: string;
}

export interface MyDialogBodyStyle {
	width: string;
}

export interface ApplyDate {
	siteId: string;
	siteName: string;
	edcSiteCode: string;
	edcSiteStatus: number;
	siteStatusStr: string;
	statusStrAndsiteName: string;
}

export interface ApplyColumn {
	label: string;
	prop: string;
}

export interface TableData {
	siteId: string;
	siteName: string;
	edcSiteCode: string;
	edcSiteStatus: number;
	siteStatusStr: string;
	statusStrAndsiteName: string;
}

export interface Colorpicker {
	confirm: string;
	clear: string;
}

export interface Week {
	sun: string;
	mon: string;
	tue: string;
	wed: string;
	thu: string;
	fri: string;
	sat: string;
}

export interface Month {
	jan: string;
	feb: string;
	mar: string;
	apr: string;
	may: string;
	jun: string;
	jul: string;
	aug: string;
	sep: string;
	oct: string;
	nov: string;
	dec: string;
}

export interface Datepicker {
	now: string;
	today: string;
	cancel: string;
	clear: string;
	confirm: string;
	selectDate: string;
	selectTime: string;
	startDate: string;
	startTime: string;
	endDate: string;
	endTime: string;
	prevYear: string;
	nextYear: string;
	prevMonth: string;
	nextMonth: string;
	year: string;
	month1: string;
	month2: string;
	month3: string;
	month4: string;
	month5: string;
	month6: string;
	month7: string;
	month8: string;
	month9: string;
	month10: string;
	month11: string;
	month12: string;
	weeks: Week;
	months: Month;
}

export interface Select {
	loading: string;
	noMatch: string;
	noData: string;
	placeholder: string;
}

export interface Cascader {
	noMatch: string;
	loading: string;
	placeholder: string;
	noData: string;
}

export interface Pagination {
	goto: string;
	pagesize: string;
	total: string;
	pageClassifier: string;
	page: string;
	prev: string;
	next: string;
	currentPage: string;
	prevPages: string;
	nextPages: string;
	deprecationWarning: string;
}

export interface Messagebox {
	title: string;
	confirm: string;
	cancel: string;
	error: string;
}

export interface Upload {
	deleteTip: string;
	delete: string;
	preview: string;
	continue: string;
}

export interface Table {
	emptyText: string;
	confirmFilter: string;
	resetFilter: string;
	clearFilter: string;
	sumText: string;
}

export interface Tree {
	emptyText: string;
}

export interface Transfer {
	noMatch: string;
	noData: string;
	titles: string[];
	filterPlaceholder: string;
	noCheckedFormat: string;
	hasCheckedFormat: string;
}

export interface Image {
	error: string;
}

export interface PageHeader {
	title: string;
}

export interface Popconfirm {
	confirmButtonText: string;
	cancelButtonText: string;
}

export interface El {
	colorpicker: Colorpicker;
	datepicker: Datepicker;
	select: Select;
	cascader: Cascader;
	pagination: Pagination;
	messagebox: Messagebox;
	upload: Upload;
	table: Table;
	tree: Tree;
	transfer: Transfer;
	image: Image;
	pageHeader: PageHeader;
	popconfirm: Popconfirm;
}

export interface Locale {
	name: string;
	el: El;
}

export interface PaginationConfig {
	show: boolean;
	layout: string;
	pageSizes: number[];
	style: any;
}

export interface ReleaseplanApplyRef {
	searchLabelPosition: string;
	showbtnfalgExcel: boolean;
	title: string;
	hideTitleBar: boolean;
	search: boolean;
	border: boolean;
	columns: {
        label: string;
        prop: string;
    }[];
	rowKey: string;
	pagination: Pagination;
	hideCenter: boolean;
	defaultSort: any;
	rightBtnhide: boolean;
	pickerChangeUse: boolean;
	searchFalg: boolean;
	searchModel: any;
	oldSearchModel: any;
	loading: boolean;
	tableData: TableData[];
	pageIndex: number;
	locale: Locale;
	pageSize: number;
	paginationConfig: PaginationConfig;
}

export interface SubstanceDate {
	id: string;
	materialType: string;
	omsMaterialId: string;
	materialName: string;
	materialSpecs: string;
	lastUpdator: string;
	lastUpdatetime: string;
	isRandom: boolean;
	materialId?: any;
	materialSepcId?: any;
	specCode?: any;
	omsMaterialSpecId?: any;
	materialTypeText: string;
	number: string;
}

export interface SubstanceColumn {
	label?: string;
	prop?: string;
	width?: string;
	tdSlot?: string | undefined;
	fixed?: string;
	align?: string;
}

export interface Column {
	label: string;
	prop: string;
	width: string;
}

export interface ReleaseplanSubstanceRef {
	searchLabelPosition: string;
	showbtnfalgExcel: boolean;
	title: string;
	hideTitleBar: boolean;
	search: boolean;
	border: boolean;
	columns: Column[];
	rowKey: string;
	pagination: Pagination;
	hideCenter: boolean;
	defaultSort: any;
	rightBtnhide: boolean;
	pickerChangeUse: boolean;
	searchFalg: boolean;
	searchModel: any;
	oldSearchModel: any;
	loading: boolean;
	tableData: any[];
	pageIndex: number;
	locale: Locale;
	pageSize: number;
	paginationConfig: PaginationConfig;
}

export interface SubstanceMyDialogBodyStyle {
	width: string;
}

export interface MaterialSpec {
	required: boolean;
	message: string;
	trigger: string;
}

export interface Number {
	required: boolean;
	message: string;
	trigger: string;
}

export interface MaterialDistributionRule {
	id: string;
	materialDistributionPlanId: string;
	ruleName: string;
	ruleBaseOn: number;
	theDaysAfterBaseOn: number;
	materialDistributionRuleState: number;
}

export interface RuleFormObj {
	id: string;
	planName: string;
	distributionTarget: number;
	tag: string;
	studyId: string;
	materialDistributionRules: MaterialDistributionRule[];
	materialDistributionPlanAndMaterialConfigs: MaterialDistributionPlanAndMaterialConfig[];
	materialDistributionPlanAndSiteConfigs: MaterialDistributionPlanAndSiteConfig[];
	materialDistributionArms: MaterialDistributionArm[];
}
/* ReleasePlanDetail.vue */
export interface ReleasePlanDetailRootObject {
	saveLoading: boolean;
	releasePlanDetailRef: any;
	ruleForm: any;
	rules: Rule;
	tagOptions: TagOption[];
	myDialogBodyStyle: MyDialogBodyStyle;
	filterPlaceholder: string;
	applyFlagShow: boolean;
	applyValue: string[];
	applyDate: ApplyDate[];
	applyColumns: ApplyColumn[];
	releaseplanApplyRef: any;
	substanceFlagShow: boolean;
	substanceVlaue: any[];
	substanceDate: SubstanceDate[];
	substanceColumns: SubstanceColumn[];
	releaseplanSubstanceRef: any;
	substanceObj: any;
	substanceSetMes: boolean;
	substanceMyDialogBodyStyle: SubstanceMyDialogBodyStyle;
	substanceFormRef?: any;
	substanceForm: any;
	substanceRules: any;
	shuttleValue: any[];
	shuttleData: any[];
	leftDefaultChecked: any[];
	rightDefaultChecked: any[];
	shuttleProps: any;
	ruleFormObj: any;
    routerGo: Function;
    addruleClick: Function;
    deleteRule: Function;
    saveData: Function;
    shuttleCancel: Function;
    shuttleConfirm: Function;
    applyClick: Function;
    getApplyList: Function;
    applyTab: Function;
    substanceTab: Function;
    substanceClick: Function;
    substanceConfirm: Function;
    getSubstanceList: Function;
    onLoad: Function;
}
