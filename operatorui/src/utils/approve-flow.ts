// 初始化，即transferXML的item
// const processConfig = {
//   nodeName: '流程开始',
//   type: 0,
//   priorityLevel: '',
//   settype: '',
//   conditionList: [],
//   nodeUserList: [],
//   conditionNodes: [],
// }

export function generateXML(processConfig, lastSeq, endPointParentId) {
  var xmltext = transferNode(processConfig, '', '', 1, null)
  xmltext += `<Node id="${guid()}" type="End" APR="" seq="${
    lastSeq + 1
  }" ParentID="${endPointParentId.join(',')}"></Node>`
}

// 生成节点
export function transferNode(item, result, parentid, seq, priorityLevel) {
  const id = guid()
  if (item.type === 0) {
    result += `<Node id="${id}" type="Start" seq="${seq}" apr="${
      item.nodeUserList.key ? item.nodeUserList.key : ''
    }" aprtype="${item.settype}" parentid=""></Node>`
  } else if (item.type === 1) {
    result += `<Node id="${id}" type="Master" priority="${
      priorityLevel ? priorityLevel : 1
    }" seq="${seq}" rule="" rulesname="" rulescontent="" description="" apr="${
      item.nodeUserList.key ? item.nodeUserList.key : ''
    }" aprtype="${item.settype}" parentid="${parentid}"></Node>`
  } else if (item.type == 4) {
    result += `<Node id="${id}" type="Branches" seq="${seq}">${transferBranch(
      item.conditionNodes,
      parentid
    )}</Node>`
  }

  if (item.childNode) {
    return transferNode(item.childNode, result, guid, seq + 1, priorityLevel)
  } else {
    return result
  }
}

// 生成Branch
export function transferBranch(conditions, parentid) {
  var result = ''
  conditions.forEach((element, index) => {
    const id = guid()
    result += `<Node id="${id}" type="Branch" rule="${transferCondition(
      element.conditionList
    )}" rulesname="${
      element.nodeName
    }" rulescontent="" description="" priority="${
      element.priorityLevel
    }" seq="1" APR="" APRType="" parentid="${parentid}"></Node>`
    if (element.childNode) {
      result += transferNode(
        element.childNode,
        '',
        id,
        2,
        element.priorityLevel
      )
    }
  })
  return result
}

// 生成条件分支
export function transferCondition(conditionList) {
  var item = ''
  if (conditionList.length > 0) {
    conditionList.forEach((element, index) => {
      if (Array.isArray(element) && element.length > 0) {
        if (index != 0) {
          item += ' or '
        }
        element.forEach((el, i) => {
          if (i != 0) {
            item += ' and '
          }
          if (i == 0) {
            item + '('
            item += convertToString(el)
          } else if (i == element.length - 1) {
            item += convertToString(el)
            item + ')'
          } else {
            item += convertToString(el)
          }
        })
      }
    })
  }
  return item
}

// 条件分支处理 转为字符串 变量用[]包裹
export function convertToString(element) {
  const dic = {
    lt: '&lt;',
    gt: '&gt;',
    contains: 'like',
    equal: '=',
    exception: 'not in',
    range: 'in',
    lm: '&lt;=',
    gm: '&gt;=',
    eq: '=',
  }
  var fieldCode = element.fieldCode
  if (element.fieldCode === 'ZCustome') {
    fieldCode = element.Customefield
  }
  if (element.type === 'enum') {
    return '(' + '[' + fieldCode + ']' + ' in ' + element.value1 + ')'
  } else if (element.way === 'between') {
    return (
      '(' +
      '[' +
      fieldCode +
      ']' +
      ' &gt; ' +
      element.value1 +
      '&amp;&amp;' +
      '[' +
      fieldCode +
      ']' +
      ' &lt; ' +
      element.value2 +
      ')'
    )
  } else {
    return (
      '(' +
      '[' +
      fieldCode +
      '] ' +
      dic[element.way] +
      ' ' +
      element.value1 +
      ')'
    )
  }
}

// 生成36位guid
export function guid() {
  return (
    S4() +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    '-' +
    S4() +
    S4() +
    S4()
  )
}

export function S4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
}
