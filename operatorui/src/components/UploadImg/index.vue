<template>
  <div class="UploadImg">
    <el-upload
      v-model:file-list="fileList"
      :class="{'upload-img-box': fileList?.length === limit || disabledImg}"
      list-type="picture-card"
      accept="image/*"
      action="#"
      :http-request="httpRequest"
      :on-exceed="handleExceed"
      :limit="limit"
      :before-upload="beforeUpload"
      :disabled="disabledImg"
      :multiple="multiple"
    >
      <el-icon><Plus /></el-icon>
      <template #file="{ file }">
        <div v-if="customImgSty">
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
          <!-- 可以自定义样式 -->
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              @click="imagePreviewClick(file)"
            >
              <el-icon><zoom-in /></el-icon>
            </span>
            <span
              v-if="downloadShow"
              class="el-upload-list__item-delete"
              @click="handleDownload(file)"
            >
              <el-icon><Download /></el-icon>
            </span>
            <span
              v-if="!disabledImg"
              class="el-upload-list__item-delete"
              @click="handleRemove(file)"
            >
              <el-icon><Delete /></el-icon>
            </span>
          </span>
        </div>
        <div v-else>
          <!-- 自定义 -->
          <slot name="customImg" :file="file" />
        </div>
      </template>
    </el-upload>
    <el-image-viewer
      v-if="imgShow"
      :url-list="imageViewer"
      :initial-index="imgIndex"
      :hide-on-click-modal="hideOnClickModal"
      @close="imagePreviewClose"
    />
    <el-dialog v-model="dialogVisible">
      <img class="w-full" :src="dialogImageUrl">
    </el-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, reactive, toRefs } from 'vue'
import { Delete, Download, Plus, ZoomIn } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'UploadImg', // 上传图片
  components: {
    Plus,
    Download,
    Delete,
    ZoomIn
  },
  props: {
    // 图片类型
    beforeFileUploadType: {
      type: Array,
      default: () => []
    },
    // 图片大小默认为2mb
    fileSize: {
      type: Number,
      default: 2
    },
    // 提示文案
    titleTips: {
      type: String,
      default: '图片'
    },
    // 自定请求
    httpRequest: {
      type: Function,
      default: () => {}
    },
    // 个数
    limit: {
      type: Number,
      default: 1
    },
    // 点击遮罩也可关闭
    hideOnClickModal: {
      type: Boolean,
      default: true
    },
    // 使用预览还是弹窗
    previewUse: {
      type: Boolean,
      default: false
    },
    // 禁用
    disabledImg: {
      type: Boolean,
      default: false
    },
    // 多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 下载按钮的显示
    downloadShow: {
      type: Boolean,
      default: false
    },
    // 下载图片的
    downloadUrl: {
      type: Function,
      default: () => {}
    },
    // 自定义样式
    customImgSty: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const state = reactive({
      dialogVisible: false,
      dialogImageUrl: null,
      fileList: [
        // {
        //   url: '',
        //   id: ''
        // }
      ], // 页面预览显示的
      imageViewer: [],
      imgShow: false,
      imgIndex: -1,
      beforeFileUploadTypeFlag: false,
      imagePreviewClick: (file) => {
        // 调用请求原图的接口
        // 这里放预览图片
        if (!props.previewUse) {
          // 如果要请求选图在说
          state.fileList.forEach((item, index) => {
            if ((item?.id === file?.id || item?.uid === file?.uid) && item?.url === file?.url) {
              state.imgIndex = index
            }
            // 调用请求原图的接口
            if (item?.url) {
              state.imageViewer.push(item.url)
            }
            state.imgShow = true
          })
        } else {
          // 调用请求原图的接口
          // 自带的那种 可能请求原图
          state.dialogVisible = true
          state.dialogImageUrl = file?.url
        }
      },
      imagePreviewClose: () => {
        state.imageViewer = []
        state.imgShow = false
      },
      beforeUpload: (file) => {
        const fileName = file.name
        const pos = fileName.lastIndexOf('.')
        const lastName = fileName.substring(pos, fileName.length)
        const limitFileType = lastName.toLowerCase()
        state.beforeFileUploadTypeFlag = false
        let pictureSize = true // 图片大小
        if (props?.beforeFileUploadType?.length) {
          props.beforeFileUploadType.forEach((item) => {
            if (item === limitFileType) {
              state.beforeFileUploadTypeFlag = true
              return
            }
          })
        } else {
          state.beforeFileUploadTypeFlag = true
        }
        if (!state.beforeFileUploadTypeFlag) {
          pictureSize = false
          ElMessage.error(
            `文件仅支持${props.beforeFileUploadType + ''}格式，请重新上传!`
          )
        }
        if (pictureSize) {
          const isLt2M = file.size / 1024 / 1024 < props.fileSize
          if (!isLt2M) {
            state.beforeFileUploadTypeFlag = false
            ElMessage.error(
              `上传${props.titleTips}大小不能超过 ${props.fileSize * 1024}KB!`
            )
          }
        }
        return state.beforeFileUploadTypeFlag
      },
      handleExceed: () => { // 按钮是隐藏掉了，防止从控制台扒出来加个这个事件
        ElMessage.warning(`一次最多只能上传${props.limit}个图片`)
      },
      // 删除图
      handleRemove: (file) => {
        state.fileList.forEach((item, index) => {
          if (item?.url === file.url && (item?.id === file?.id || item?.uid === file?.uid)) {
            state.fileList.splice(index, 1)
          }
        })
      },
      // 下载图片
      handleDownload: (file) => {
        props.downloadUrl(file)
      },
      // 先放着-
      // httpRequest: (fileObj) => {
      //   const myFormDataObj = new FormData()
      //   myFormDataObj.append('CheckImageFiles', fileObj.file)
      //   postStudyFile(studyId, myFormDataObj).then((res) => {
      //     state.fileList.push({
      //       url: res.thumbnailUrl
      //     })
      //     state.mateImgRef.fileList = state.fileList
      //   })
      // },

      // switchImg: (i) => {
      //   getArtworkImage('0006015b-13b8-0f94-0000-000000000001').then((res) =>{
      //     state.imageViewer[i] = res?.url
      //   })
      //   console.log(i)
      // },
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.UploadImg {
  min-height: 156px;
}
.upload-img-box {
  :deep(.el-upload--picture-card) {
    display: none;
  }
}
</style>
