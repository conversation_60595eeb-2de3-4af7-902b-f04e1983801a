<template>
  <trial-dialog
    v-model="VerificationRuleDialogVisible"
    :title="VerificationRuleTitle"
    my-title-class="flex my-1 mb-[20px]"
  >
    <template #DialogBody>
      <el-form
        ref="VerificationRuleFormRef"
        :model="VerificationRuleFormData"
        :rules="VerificationRuleRules"
        class="verificationRuleForm"
      >
        <div class="flex">
          <el-form-item
            label="规则名称"
            :label-width="VerificationRuleFormLabelWidth"
            prop="ruleName"
            maxlength="1000"
          >
            <el-input
              v-model.trim="VerificationRuleFormData.ruleName"
              autocomplete="off"
            />
          </el-form-item>
          <!-- 核查效力-->
          <el-form-item
            label="核查效力"
            :label-width="VerificationRuleFormLabelWidth"
            prop="verificationRuleLevel"
          >
            <el-select
              v-model="VerificationRuleFormData.verificationRuleLevel"
              placeholder="请选择"
              style="width: 160px"
            >
              <el-option
                v-for="item in verificationEffectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="状态"
            :label-width="VerificationRuleFormLabelWidth"
            prop="isEnable"
          >
            <el-switch v-model="VerificationRuleFormData.isEnable" />
          </el-form-item>
        </div>
        <el-form-item
          label="规则代码"
          :label-width="VerificationRuleFormLabelWidth"
          prop="verificationRuleCode"
        >
          <!-- show-word-limit -->
          <el-input
            v-model.trim="VerificationRuleFormData.verificationRuleCode"
            type="textarea"
            maxlength="9999"
            autocomplete="off"
          />
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="centerflex">
        <el-button plain @click="closeDialogVisible">取消</el-button>
        <el-button
          :loading="VerificationRuleLoading"
          type="primary"
          @click="VerificationRuleSubmit"
        >保存</el-button>
      </div>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { postQuestTemplateVerificationRule } from '@/api/customTask'
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'VerificationRuleForm',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const state = reactive({
      verificationEffectOptions: [
        {
          label: '强制修改',
          value: 1,
        },
        {
          label: '非强制修改',
          value: 2,
        },
      ],
      VerificationRuleFormData: {
        id: '',
        dctQuestTemplateId: '',
        ruleName: '',
        verificationRuleCode: '',
        isEnable: false,
      },
      VerificationRuleTitle: '核查规则',
      VerificationRuleFormRef: null,
      VerificationRuleDialogVisible: false,
      VerificationRuleFormLabelWidth: '86px',
      VerificationRuleRules: {
        ruleName: [{ required: true, message: '请输入', trigger: 'blur' }],
        verificationRuleLevel: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
        verificationRuleCode: [{ required: true, message: '请输入', trigger: 'blur' }],
      },
      VerificationRuleLoading: false,
      closeDialogVisible: () => {
        state.VerificationRuleDialogVisible = false
      },
      VerificationRuleSubmit: () => {
        state.VerificationRuleFormRef.validate((valid) => {
          if (valid) {
            // console.log(state.VerificationRuleFormData?.verificationRuleCode);
            // .includes('return')
            if (state.VerificationRuleFormData?.verificationRuleCode.length < 7) {
              ElMessage({
                message: '核查规则代码不规范',
                type: 'warning',
              })
              return
            }
            state.VerificationRuleLoading = true
            postQuestTemplateVerificationRule(state.VerificationRuleFormData).then(() => {
              props.request()
              ElMessage.success('保存成功')
              state.VerificationRuleLoading = false
              state.closeDialogVisible()
            }).catch(() => {
              state.VerificationRuleLoading = false
            })
          }
        })
      },
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
:deep(.verificationRuleForm) {
  .el-textarea__inner {
    min-height: 260px !important;
  }
  // :deep(.el-textarea__inner) {
  //     min-height: 300px;
  // }
}
</style>
