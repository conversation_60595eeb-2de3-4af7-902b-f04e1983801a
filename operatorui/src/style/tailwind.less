// @tailwind base;
// @layer base {
//     img {
//         border: none;
//     }
// }
@tailwind components;
@tailwind utilities;
@layer utilities {
    .mg-b-22-px{
        margin-bottom: 22px;
    }
    .ft-14 {
        font-size: 14px;
    }
    .ft-15 {
        font-size: 15px;
    }
    .ft-16 {
        font-size: 16px;
    }
    .editBtnBlue {
        color: #409eff;
        cursor: pointer; /*小手*/
    }
    .mt-30-px {
        margin-top: 30px;
    }
    .mb-18-px {
        margin-bottom: 18px;
    }
    /*通用 编辑表单 模块*/
    .common-form-module {
        background: #fff;
        padding: 20px;
        box-sizing: border-box;
        border-radius: 10px;
    }
    /*角色权限设置-姓名*/
    .real-name-br{
        padding: 0 0 6px 0;
        border-bottom: 2px solid #E2E2E2;
    }
    .w-120-px {
        width: 120px;
    }
    .w-150-px {
        width: 150px;
    }
}