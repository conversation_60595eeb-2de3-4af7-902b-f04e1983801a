.centerflex {
  display: flex;
  justify-content: center;
  align-items: center;
}

/*水平居中 - 换行*/
.centerflex-w {
  display: flex;
  justify-content: center;
}

.centerflex-w-wrap {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

/*垂直居中 - 换行*/
.centerflex-h {
  display: flex;
  align-items: center;
}

.centerflex-h-wrap {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
/* 强制不换行 */
.none-warp-text {
  white-space: nowrap;
}

.wrap2 {
  display: -webkit-box !important;
  word-break: break-all;
  word-wrap: break-word;
  overflow: hidden;
  /*…省略形式*/
  text-overflow: ellipsis;
  /*从上向下垂直排列子元素*/
  -webkit-box-orient: vertical;
  /*文本可分两行*/
  -webkit-line-clamp: 2;
 }
 /* 单行文本溢出省略 */
.none-warp-text-auto {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.footer-flex-end {
  display: flex;
  justify-content: flex-end
}

.margin-t-10 {
  margin-top: 10px;
}

.margin-b-10 {
  margin-bottom: 10px;
}
/* 穿梭框样式调整 */
.el-transfer-panel {
  width: calc((100% - 170px) / 2) !important;
  height: 400px;
  // .el-checkbox__label{
  //   width: 70%;
  //   word-break: break-all !important;
  //   word-wrap: break-word !important;
  //   white-space: pre-wrap !important;
  // }
}
.el-transfer-panel__body {
  height: calc(100% - 42px) !important;
}

.el-transfer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center
}

.el-transfer-panel__filter {
  width: calc(100% - 30px) !important;
}

.el-transfer-panel__item.el-checkbox .el-checkbox__label {
  overflow: visible !important;
}