/* 改变主题色变量 */
$--color-primary: $mainColor;

/* 改变 icon 字体路径变量，必需 */
$--font-path: "element-plus/lib/theme-chalk/fonts";

// @import "element-plus/packages/theme-chalk/src/index";

@font-face {
  font-family: 'iconfont';  /* Project id 3133671 */
  src: url('//at.alicdn.com/t/font_3133671_5yvp327mc8a.woff2?t=1641966258489') format('woff2'),
  url('//at.alicdn.com/t/font_3133671_5yvp327mc8a.woff?t=1641966258489') format('woff'),
  url('//at.alicdn.com/t/font_3133671_5yvp327mc8a.ttf?t=1641966258489') format('truetype');
}
.iconfont{
    font-family:"iconfont" !important;
    font-size:12px;font-style:normal;
    -webkit-font-smoothing: antialiased;
    -webkit-text-stroke-width: 0.2px;
    -moz-osx-font-smoothing: grayscale;
}