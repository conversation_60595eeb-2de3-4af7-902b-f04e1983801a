<template>
  <div class="details">
    <div class="head">
      <div class="headLeft">
        <span v-if="!stateChange.id">新建方案</span>
        <span v-else>编辑方案</span>
      </div>
      <div class="headRight">
        <el-row>
          <el-button size="small" @click="routerGo('back')">返回</el-button>
          <!-- <el-button
            v-if="stateChange.id !== ''"
            size="small"
            type="danger"
            @click="confirmBtm('danger')"
          >删除</el-button> -->
          <el-button size="small" type="primary" @click="confirmBtm('primary')">保存</el-button>
        </el-row>
      </div>
    </div>
    <!-- 新建药品 -->
    <div class="main">
      <div class="mainLeft">
        <el-form
          ref="DosageNewForm"
          :model="DosageDetailsForm"
          :rules="rules"
          :label-position="labelPosition"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="方案名称" prop="schemeName">
                <el-input
                  v-model="DosageDetailsForm.schemeName"
                  placeholder="请填写方案名称"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="11">
              <el-form-item label="CRF版本" prop="dctStudyVersionId">
                <el-select
                  v-model="DosageDetailsForm.dctStudyVersionId"
                  placeholder="请选择CRF版本"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in crfList"
                    :key="index"
                    :label="item.versionNumber"
                    :value="item.studyVersionId"
                    @click="crfBtn(item.studyVersionId)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="2" />
            <el-col :span="11">
              <el-form-item v-show="groupingDesignSzie == '分组实验'" label="适配分组" prop="armName">
                <el-select
                  v-model="DosageDetailsForm.armName"
                  placeholder="请选择适配分组"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in groupingList"
                    :key="index"
                    :label="item.armName"
                    :value="item.armName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="cycle"><span>*</span>给药周期</div>
        </el-form>
      </div>

      <!-- 添加周期 -->
      <!--  -->
      <div
        v-for="(item, index) in DosageDetailsForm.drugPeriods"
        :key="index"
        class="cycleShow"
      >
        <el-form
          ref="DosageForm"
          :model="DosageDetailsForm"
          :rules="rules"
          :label-position="labelPosition"
        >
          <!-- 内容 v-if="item.prePopulatedId === '2'"-->
          <el-row>
            <div v-if="item?.visitChangedAlerts" class="visitChangedAlerts" v-html="item.visitChangedAlerts" />
            <div class="cycle">自</div>
            <el-col :span="3" class="myleft">
              <el-form-item>
                <el-select
                  v-model="item.startVisitTemplateId"
                  placeholder="请选择访视"
                >
                  <el-option
                    v-for="(cycleTrueItem, cycleTrueIndex) in cycleTrueList"
                    :key="cycleTrueIndex"
                    :label="cycleTrueItem.visitName"
                    :value="cycleTrueItem.dctVisitTemplateId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="cycle myleft">第</div>
            <el-col :span="2" class="myleft">
              <el-form-item>
                <el-input
                  v-model.trim="item.startDay"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <div class="cycle myleft">起，至</div>
            <el-col :span="3" class="myleft">
              <el-form-item>
                <el-select
                  v-model="item.endVisitTemplateId"
                  placeholder="请选择访视"
                >
                  <el-option
                    v-for="(cycleFalseItem, cycleFalseIndex) in cycleFalseList"
                    :key="cycleFalseIndex"
                    :label="cycleFalseItem.visitName"
                    :value="cycleFalseItem.dctVisitTemplateId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="cycle myleft">第</div>
            <el-col :span="2" class="myleft">
              <el-form-item>
                <el-input
                  v-model.trim="item.endDay"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <div class="cycle myleft">天，每间隔</div>
            <el-col :span="2" class="myleft">
              <el-form-item>
                <el-select v-model="item.intervalDay" placeholder="请选择">
                  <el-option :value="1" />
                  <el-option :value="2" />
                  <el-option :value="3" />
                  <el-option :value="4" />
                  <el-option :value="5" />
                  <el-option :value="6" />
                  <el-option :value="7" />
                  <el-option :value="8" />
                  <el-option :value="9" />
                  <el-option :value="10" />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="cycle myleft">天</div>
          </el-row>
          <el-row>
            <div class="cycle">服药用品</div>
            <el-col :span="5">
              <el-form-item class="myleft">
                <el-select
                  v-model="item.dctDrugId"
                  placeholder="请选择服药用品"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(medicineItem, indexa) in medicineList"
                    :key="indexa"
                    :label="medicineItem.drugName"
                    :value="medicineItem.id"
                    @click="drugDoseUnitBtn(medicineItem.drugDoseUnit, index)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="cycle myleft">当天需给药</div>
            <el-col :span="3" class="myleft">
              <el-form-item>
                <el-select
                  v-model="item.numberOfTimesPerDay"
                  placeholder="请选择"
                >
                  <el-option :value="1" />
                  <el-option :value="2" />
                  <el-option :value="3" />
                  <el-option :value="4" />
                  <el-option :value="5" />
                </el-select>
              </el-form-item>
            </el-col>
            <div class="cycle myleft"><span>次，</span> 默认给药计量</div>
            <el-col :span="3" class="myleft">
              <el-form-item>
                <el-input
                  v-model.trim="item.defaultDose"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="请输入"
                />
              </el-form-item>
            </el-col>
            <div class="cycle myleft">{{ item.drugDoseUnit }}</div>
          </el-row>
          <el-row>
            <div class="cycle">给药提醒</div>
            <el-col :span="15" class="myleft">
              <el-form-item>
                <el-input
                  v-model="item.drugRemind"
                  placeholder="请填写给药提醒"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="addCycleDelete" @click="cycleBtn('delete', index)">
          删除
        </div>
      </div>
      <div class="addCycleAdd" @click="cycleBtn('add', '')">添加周期</div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import {
  studyDrugDoseUnitDictionariesInfoTrue,
  studyDrugDoseUnitDictionariesInfoFalse,
  studyDropDrugInfos,
  studyDropScheme,
  studyDeleteDrug,
  studyDropStudyVersions,
  studyStudyArmsInfo,
} from '@/api/dosageRegimenManagement'
export default defineComponent({
  name: 'DosageDetails', // 给药方案管理
  props: {
    stateChange: {
      type: Object,
    },
    requestLoad: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const store = useStore()
    const state = reactive({
      labelPosition: 'top',
      cycleShow: false,
      groupingDesignSzie: '',
      DosageDetailsForm: {
        id: null,
        schemeName: '', // 方案名称
        armName: '', // 分组名称
        versionNumber: '', // CRF版本名称
        dctStudyVersionId: '', // CRF版本ID
        drugPeriods: [
          {
            id: '',
            dctDrugSchemeId: '', // 给药方案ID
            dctDrugId: '', // 药品编号
            drugName: '', // 服用药品名称
            drugDisplayName: '', // 药品显示名称（用户端）
            drugRemind: '', // 给药提醒
            drugDoseUnit: '', // 给药单位
            startVisitTemplateId: '', // 给药周期（开始节点）
            startDay: null, // 开始节点天数
            endVisitTemplateId: '', // 给药周期（结束节点）
            endDay: null, // 结束节点天数
            intervalDay: null, // 间隔天数
            numberOfTimesPerDay: null, // 当天给药次数
            defaultDose: null, // 默认给药剂量
            // prePopulatedId: '1', // 默认服药方案（非预生成服药）
          },
        ],
      },
      description: {},
      cycleTrueList: [],
      cycleFalseList: [],
      medicineList: {},
      crfList: {}, // CRF版本信息
      groupingList: {}, // 下拉分组信息
      DosageForm: null,
      DosageNewForm: null,
      rules: {
        // 方案名称
        schemeName: [
          { required: true, message: '请填写方案名称', trigger: 'blur' },
        ],
        // CRF版本
        dctStudyVersionId: [
          { required: true, message: '请选择CRF版本', trigger: 'blur' },
        ],
        // 开始访视
        startVisitTemplateId: [
          { required: true, message: '请选择开始节点', trigger: 'blur' },
        ],
        // 开始访视天数
        startDay: [{ required: true, message: '请输入', trigger: 'blur' }],
        // 结束访视
        endVisitTemplateId: [
          { required: true, message: '请选择开始节点', trigger: 'blur' },
        ],
        // 结束访视天数
        endDay: [{ required: true, message: '请输入', trigger: 'blur' }],
        // 间隔天数
        intervalDay: [{ required: true, message: '请选择', trigger: 'blur' }],
        // 服药用品
        dctDrugId: [
          { required: true, message: '请选择服用药品', trigger: 'blur' },
        ],
        // 当天需给药
        numberOfTimesPerDay: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
        // 默认给药剂量
        defaultDose: [
          { required: true, message: '请填写数值', trigger: 'blur' },
        ],
      },
      routerGo: (type) => {
        if (type === 'back') {
          props.stateChange.stateChangeFalgOld = false
          props.requestLoad()
        }
      },
      // 选择CRF版本
      crfBtn: (versionid) => {
        // 开始
        studyDrugDoseUnitDictionariesInfoTrue(versionid).then((res: any) => {
          state.cycleTrueList = res
          state.DosageDetailsForm.drugPeriods.forEach((item) => {
            item.startVisitTemplateId = ''
          })
        })
        // 结束
        studyDrugDoseUnitDictionariesInfoFalse(versionid).then((res: any) => {
          state.cycleFalseList = res
          state.DosageDetailsForm.drugPeriods.forEach((item) => {
            item.endVisitTemplateId = ''
          })
        })
      },
      // 药品单位
      drugDoseUnitBtn: (itemVlaue, index) => {
        state.DosageDetailsForm.drugPeriods[index].drugDoseUnit = itemVlaue
      },
      // 添加周期
      cycleBtn: (type, index) => {
        if (type === 'add') {
          state.DosageDetailsForm.drugPeriods.push({
            id: '',
            dctDrugSchemeId: '', // 给药方案ID
            dctDrugId: '', // 药品编号
            drugName: '', // 服用药品名称
            drugDoseUnit: '', // 给药单位
            drugDisplayName: '', // 药品显示名称（用户端）
            drugRemind: '', // 给药提醒
            startVisitTemplateId: '', // 给药周期（开始节点）
            startDay: null, // 开始节点天数
            endVisitTemplateId: '', // 给药周期（结束节点）
            endDay: null, // 结束节点天数
            intervalDay: null, // 间隔天数
            numberOfTimesPerDay: null, // 当天给药次数
            defaultDose: null, // 默认给药剂量
          })
        } else if (type === 'delete') {
          for (let i = 0; i < state.DosageDetailsForm.drugPeriods.length; i++) {
            state.DosageDetailsForm.drugPeriods.splice(index, i)
          }
        }
      },
      // 删除确认
      confirmBtm: (type) => {
        state.DosageDetailsForm.id = props.stateChange.id
        let flag = true
        const item = state.DosageDetailsForm.drugPeriods
        if (type === 'primary') {
          state.DosageNewForm.validate((valid) => {
            if (valid) {
              for (let i = 0; i < item.length; i++) {
                if (
                  !item[i].dctDrugId ||
                  !item[i].startVisitTemplateId ||
                  !item[i].startDay ||
                  !item[i].endVisitTemplateId ||
                  !item[i].endDay ||
                  !item[i].intervalDay ||
                  !item[i].numberOfTimesPerDay ||
                  !item[i].defaultDose
                ) {
                  ElMessage.warning({
                    showClose: true,
                    message: '给药周期内容不可为空',
                  })
                  flag = false
                  return
                }
              }
              if (flag) {
                for (let i = 0; i < item.length; i++) {
                  item[i].startDay = Number(item[i].startDay)
                  item[i].endDay = Number(item[i].endDay)
                  item[i].intervalDay = Number(item[i].intervalDay)
                  item[i].defaultDose = Number(item[i].defaultDose)
                }
                studyDropScheme(state.DosageDetailsForm).then((res: any) => {
                  ElMessage.success({
                    message: '保存成功',
                    duration: 1000,
                  })
                  props.stateChange.stateChangeFalgOld = false
                  props.requestLoad()
                })
              }
            }
          })
        } else if (type === 'danger') {
          ElMessageBox.confirm('是否确认删除？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          }).then(() => {
            studyDeleteDrug(props.stateChange.id).then((res: any) => {
              ElMessage.success({
                type: 'success',
                message: '删除成功!',
              })
              props.stateChange.stateChangeFalgOld = false
              props.requestLoad()
            })
          })
        }
      },
    })

    onMounted(() => {
      state.groupingDesignSzie = props.stateChange.groupingDesignString
      if (props.stateChange.id) {
        state.DosageDetailsForm = props.stateChange.ruleFormList
        // 开始
        studyDrugDoseUnitDictionariesInfoTrue(
          props.stateChange.ruleFormList.dctStudyVersionId
        ).then((res: any) => {
          state.cycleTrueList = res
        })
        // 结束
        studyDrugDoseUnitDictionariesInfoFalse(
          props.stateChange.ruleFormList.dctStudyVersionId
        ).then((res: any) => {
          state.cycleFalseList = res
        })
      }
      // 服药用品
      studyDropDrugInfos(store.state.studyItem.studyId).then((res: any) => {
        state.medicineList = res
      })

      // CRF版本信息
      studyDropStudyVersions(store.state.studyItem.studyId).then((res: any) => {
        state.crfList = res
      })

      // 下拉分组信息
      studyStudyArmsInfo(store.state.studyItem.studyId).then((res: any) => {
        state.groupingList = res
      })
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.el-select {
  min-width: 150px;
}
.details {
  width: 100%;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  .head {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #ccc;
    .headLeft {
      width: 50%;
      display: flex;
      justify-content: space-between;
      span {
        height: 40px;
        line-height: 40px;
        color: black;
        text-align: left;
      }
    }
  }
  //新建药品
  .main {
    width: 94%;
    margin: 0 auto;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .mainLeft {
      width: 70%;
      .slice {
        overflow: hidden;
        box-sizing: border-box;
        span {
          display: block;
          height: 100%;
          line-height: 70px;
          margin: 10px;
        }
      }
    }
    .cycleShow {
      width: 100%;
      border-top: 1px solid #ccc;
      padding: 20px 0;
      position: relative;
      &:nth-child(1) {
        border: none !important;
        padding: 100px !important;
        border-top: 10px solid #ccc !important;
        .addCycleDelete {
          display: none !important;
        }
      }
      .addCycleDelete {
        position: absolute;
        top: 20px;
        right: 0;
        cursor: pointer;
        color: red;
      }
    }
  }
  //   其他设置
  .container {
    width: 94%;
    margin: 0 auto;
    margin-top: 20px;
    span {
      margin-left: 10px;
    }
  }
}
.cycle {
  font-size: 14px;
  span {
    color: red !important;
  }
}
.cycle {
  line-height: 30px;
}
.myleft {
  margin-left: 10px;
}
.addCycleAdd {
  display: inline-block;
  color: rgb(1, 81, 255);
  cursor: pointer;
}
.visitChangedAlerts{
  width: 100%;
  padding: 0 10% 0 0;
  box-sizing: border-box;
  text-align: center;
  color: red;
  margin: 0 0 10px 0;
}
</style>
