<template>
  <div class="details">
    <div class="flex justify-between items-center">
      <div class="text-[20px] font-semibold">
        {{ stateChange?.id ? '编辑' : '新建' }}方案
      </div>
      <div>
        <el-button @click="routerGo('back')">返回</el-button>
        <el-button type="primary" @click="confirmBtm"> 保存 </el-button>
      </div>
    </div>
    <!-- 新建药品 -->
    <div class="main">
      <div class="mb-[15px]">
        <el-form
          ref="DosageNewForm"
          :model="DosageDetailsForm"
          label-position="top"
          :rules="DosageNewFormRules"
        >
          <!-- 给药方案、CRF版本、适配分组 -->
          <el-row class="flex mb-[30px]">
            <el-col :span="10">
              <el-form-item label="方案名称" prop="drugRegimenName">
                <el-input
                  v-model="DosageDetailsForm.drugRegimenName"
                  placeholder="请填写方案名称"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="1" />
            <el-col :span="6">
              <el-form-item label="CRF版本" prop="dctStudyVersionId">
                <el-select
                  v-model="DosageDetailsForm.dctStudyVersionId"
                  placeholder="请选择CRF版本"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in crfList"
                    :key="index"
                    :label="
                      item.versionStatus === 1
                        ? 'V' + item.versionNumber + '测试版'
                        : 'V' + item.versionNumber + '正式版'
                    "
                    :value="item.studyVersionId"
                    @click="editionBtn(item)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="1" />
            <el-col v-if="groupingDesignSzie === '分组实验'" :span="6">
              <el-form-item label="适配分组" prop="armCodes">
                <el-select
                  v-model="DosageDetailsForm.armCodes"
                  multiple
                  default-first-option
                  placeholder="请选择适配分组"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, index) in groupingList"
                    :key="index"
                    :label="item.armName"
                    :value="item.armCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 添加周期 -->
          <div
            v-for="(item, index) in DosageDetailsForm.dctDrugPlans"
            :key="index"
            class="mainContent"
          >
            <div class="cycleLine">
              <div><span>*</span>给药周期</div>
              <!-- 数据量大于0时显示删除按钮 -->
              <div
                v-if="index > 0"
                class="deleteBtn"
                @click="cycleBtn('delete', index)"
              >
                删除
              </div>
            </div>
            <div class="contentBox">
              <el-row>
                <el-col :span="6">
                  <el-form-item
                    :prop="`dctDrugPlans[${index}].dctDrugId`"
                    :rules="{
                      required: true,
                      message: '请选择',
                      trigger: 'change',
                    }"
                  >
                    <el-select
                      v-model="item.dctDrugId"
                      placeholder="请选择药物"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(
                          medicineListItem, medicineListIndex
                        ) in medicineList"
                        :key="medicineListIndex"
                        :label="medicineListItem.drugName"
                        :value="medicineListItem.id"
                        @click="drugDoseUnitBtn(medicineListItem, index)"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="1" />
                <el-col :span="6">
                  <el-form-item>
                    <el-select
                      v-model="item.generationType"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option label="预生成用药计划" :value="1" />
                      <!-- 按需记录以后做 -->
                      <!-- <el-option label="按需记录" value="2" /> -->
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <div class="text">用药频率</div>
                <el-radio-group
                  v-model="item.drugFrequency"
                  style="margin-bottom: 20px"
                  @change="drugFrequencyChange(index)"
                >
                  <el-radio :label="1" class="myleft">每天</el-radio>
                  <el-radio :label="2" class="myleft radioBtn">
                    每
                    <div>
                      <el-form-item
                        :prop="`dctDrugPlans[${index}].frequencyDay`"
                        :rules="
                          item.drugFrequency == 2
                            ? [
                                {
                                  required: true,
                                  message: '请输入',
                                  trigger: 'blur',
                                },
                              ]
                            : [
                                {
                                  required: false,
                                  message: '',
                                  trigger: 'blur',
                                },
                              ]
                        "
                      >
                        <el-input
                          v-model.trim="item.frequencyDay"
                          oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                          @blur="item.frequencyDay = $event.target.value"
                          placeholder="请输入"
                          style="width: 100%"
                          :disabled="item.drugFrequency !== 2"
                        />
                      </el-form-item>
                    </div>
                    天
                  </el-radio>
                  <el-radio :label="3" class="radioleft radioBtn">
                    每
                    <div>
                      <el-form-item
                        :prop="`dctDrugPlans[${index}].frequencyHours`"
                        :rules="
                          item.drugFrequency == 3
                            ? {
                                required: true,
                                message: '请输入',
                                trigger: 'blur',
                              }
                            : { required: false }
                        "
                      >
                        <el-input
                          v-model.trim="item.frequencyHours"
                          oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                          @blur="item.frequencyHours = $event.target.value"
                          placeholder="请输入"
                          style="width: 100%"
                          :disabled="item.drugFrequency !== 3"
                        />
                      </el-form-item>
                    </div>
                    小时
                  </el-radio>
                </el-radio-group>
              </el-row>
              <el-row>
                <div class="text">计划开始用药</div>
                <el-form-item>
                  <el-select
                    v-model="item.drugStartPlanType"
                    placeholder="请选择"
                    style="width: 100%"
                    class="myleft"
                  >
                    <el-option label="由医生指定" :value="1" />
                  </el-select>
                </el-form-item>
                <div class="text myleft">计划结束用药</div>
                <el-select
                  v-model="item.drugEndPlanType"
                  placeholder="请选择"
                  class="myleft !w-auto"
                >
                  <el-option label="按疗程" :value="1" />
                  <el-option
                    label="患者结束研究"
                    :value="2"
                    :disabled="item.drugFrequency === 3"
                  />
                </el-select>
                <!-- 计划结束用药，选择‘按疗程’时展示该内容 -->
                <div v-if="item.drugEndPlanType === 1" style="display: flex">
                  <div class="text myleft">共</div>
                  <div>
                    <el-form-item
                      :prop="`dctDrugPlans[${index}].drugPeriod`"
                      :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                      }"
                    >
                      <el-input
                        v-model.trim="item.drugPeriod"
                        oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                        @blur="item.drugPeriod = $event.target.value"
                        placeholder="请输入"
                        class="myleft"
                      />
                    </el-form-item>
                  </div>
                  <div
                    class="text myleft"
                    style="margin-left: 20px"
                    v-html="item.drugFrequency !== 3 ? '天' : '次'"
                  />
                </div>
              </el-row>
              <el-row>
                <div class="text">默认用药剂量</div>
                <!-- <el-input
                  v-model.trim="item.drugDosage"
                  oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                  @blur="item.drugDosage = $event.target.value"
                  placeholder="请输入"
                  style="width: 15%"
                  class="myleft"
                /> -->
                <el-input-number
                  :min="0"
                  v-model="item.drugDosage"
                  style="width: 15%"
                  class="inputNumber"
                  :precision="2"
                  :controls="false"
                />
                <div class="text myleft" v-html="item.drugDoseUnit" />
              </el-row>
              <!-- 每天/每隔N天显示 -->
              <el-row v-if="item.drugFrequency !== 3">
                <div
                  v-for="(itemEd, indexEd) in item.dctDrugRecords"
                  :key="indexEd"
                  class="medicationBox"
                >
                  <div class="medicationBoxLeft">
                    <div class="left">
                      <span>当天<br />第{{ indexEd + 1 }}次</span>
                    </div>
                    <div class="right">
                      <div style="width: 150px">
                        <el-select
                          v-model="itemEd.specifiedDrugTime"
                          placeholder="请选择"
                          @change="specifiedDrugTimeChange(itemEd)"
                        >
                          <el-option label="指定用药时间" :value="2" />
                          <el-option label="不指定用药时间" :value="1" />
                        </el-select>
                      </div>
                      <div class="myleft" style="width: 150px">
                        <el-time-picker
                          v-model="itemEd.drugDisplayTime"
                          placeholder="请选择时间"
                          value-format="HH:mm"
                          format="HH:mm"
                          :clearable="false"
                          :disabled="itemEd.specifiedDrugTime === 1"
                          style="width: 100%; margin-right: 20px"
                        />
                        <span>需展示具体用药时间时设置</span>
                      </div>
                      <div class="myleft">
                        <el-input
                          v-model.trim="itemEd.drugDisplayText"
                          placeholder="用药时间相关提示文案"
                        />
                        <span>例如：早晨、中午、晚上、睡前等</span>
                      </div>
                      <div class="myleft" style="width: 120px">
                        <el-select
                          v-model="itemEd.drugRecordType"
                          placeholder="请选择"
                          style="min-width: 40px"
                          @change="
                            (e) => {
                              drugRecordTypeChange(e, index, indexEd)
                            }
                          "
                        >
                          <el-option label="提醒+记录" :value="3" />
                          <el-option label="仅提醒" :value="1" />
                          <el-option label="仅记录" :value="2" />
                        </el-select>
                      </div>
                      <div class="text myleft">关联服药问卷</div>
                      <div class="myleft" style="width: 170px">
                        <el-form-item
                          :prop="`dctDrugPlans[${index}].dctDrugRecords[${indexEd}].drugQuestTemplateID`"
                          :rules="
                            itemEd.drugRecordType !== 1
                              ? {
                                  required: true,
                                  message: '请选择',
                                  trigger: 'change',
                                }
                              : { required: false }
                          "
                        >
                          <el-select
                            v-model="itemEd.drugQuestTemplateID"
                            placeholder="请选择"
                            :disabled="itemEd.drugRecordType === 1"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="(editionItem, editionIndex) in editionList"
                              :key="editionIndex"
                              :label="editionItem?.questTemplateName"
                              :value="editionItem?.questTemplateId"
                            />
                          </el-select>
                        </el-form-item>
                      </div>
                      <div class="w-full flex flex-wrap">
                        <div class="text">填写窗口：下发第</div>
                        <div class="flex">
                          <el-form-item
                            :prop="`dctDrugPlans[${index}].dctDrugRecords[${indexEd}].fillInStartDay`"
                            :rules="
                              itemEd.drugRecordType !== 1
                                ? {
                                    required: true,
                                    message: '请输入',
                                    trigger: 'blur',
                                  }
                                : { required: false }
                            "
                          >
                            <el-input
                              v-model.trim="itemEd.fillInStartDay"
                              oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                              placeholder="请输入"
                              disabled
                              class="myleft"
                              style="width: 120px"
                            />
                          </el-form-item>
                          <div class="myleft text">天</div>
                          <div class="myleft" style="width: 150px">
                            <el-form-item
                              :prop="`dctDrugPlans[${index}].dctDrugRecords[${indexEd}].fillInStartTime`"
                              :rules="
                                itemEd.drugRecordType !== 1
                                  ? {
                                      required: true,
                                      message: '请选择',
                                      trigger: 'blur',
                                    }
                                  : { required: false }
                              "
                            >
                              <el-time-picker
                                v-model="itemEd.fillInStartTime"
                                placeholder="请选择时间"
                                value-format="HH:mm"
                                format="HH:mm"
                                :clearable="false"
                                :disabled="itemEd.drugRecordType === 1"
                                style="width: 100%"
                                @change="
                                  triggerTimeValidator(1, index, indexEd)
                                "
                              />
                            </el-form-item>
                          </div>
                          <div class="myleft text">至</div>
                          <div class="myleft text">第</div>
                          <el-form-item
                            :prop="`dctDrugPlans[${index}].dctDrugRecords[${indexEd}].fillInEndDay`"
                            :rules="
                              itemEd.drugRecordType !== 1
                                ? {
                                    required: true,
                                    message: '请输入',
                                    trigger: 'blur',
                                  }
                                : { required: false }
                            "
                          >
                            <el-input
                              v-model.trim="itemEd.fillInEndDay"
                              oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                              @blur="itemEd.fillInEndDay = $event.target.value"
                              placeholder="请输入"
                              :disabled="itemEd.drugRecordType === 1"
                              class="myleft"
                              style="width: 120px"
                              @change="triggerTimeValidator(1, index, indexEd)"
                            />
                          </el-form-item>
                          <div class="myleft text">天</div>
                          <div class="myleft" style="width: 150px">
                            <el-form-item
                              :prop="`dctDrugPlans[${index}].dctDrugRecords[${indexEd}].fillInEndTime`"
                              :rules="[
                                itemEd.drugRecordType !== 1
                                  ? {
                                      required: true,
                                      message: '请选择',
                                      trigger: 'blur',
                                    }
                                  : { required: false },
                                {
                                  validator: drugPlanEditTimeValidator,
                                  trigger: 'blur',
                                },
                              ]"
                            >
                              <el-time-picker
                                v-model="itemEd.fillInEndTime"
                                placeholder="请选择时间"
                                value-format="HH:mm"
                                format="HH:mm"
                                :clearable="false"
                                :disabled="itemEd.drugRecordType === 1"
                                style="width: 100%"
                              />
                            </el-form-item>
                          </div>
                        </div>
                        <div class="text myleft">首次填写后</div>
                        <div class="flex" style="min-width: 170px">
                          <el-input
                            v-model="itemEd.changeDeadline"
                            oninput="value=value.replace(/[^\d]+/g,'')"
                            @blur="itemEd.changeDeadline = $event.target.value"
                            placeholder="请输入"
                            :disabled="itemEd.drugRecordType === 1"
                            class="myleft"
                          />
                          <el-select
                            v-model="itemEd.timeUnit"
                            placeholder="请选择"
                            :disabled="itemEd.drugRecordType === 1"
                            style="min-width: 40px"
                            class="myleft"
                          >
                            <el-option label="天" :value="1" />
                            <el-option label="小时" :value="2" />
                            <el-option label="分钟" :value="3" />
                          </el-select>
                        </div>
                        <div class="text myleft">内可修改</div>
                      </div>

                      <div style="width: 100%; display: flex; margin-top: 20px">
                        <div class="text">推送当天提醒时间</div>
                        <div class="myleft">
                          <el-form-item
                            :prop="`dctDrugPlans[${index}].dctDrugRecords[${indexEd}].drugReminderTime`"
                            :rules="
                              itemEd.drugRecordType !== 2
                                ? {
                                    required: true,
                                    message: '请选择',
                                    trigger: 'blur',
                                  }
                                : { required: false }
                            "
                          >
                            <el-time-picker
                              v-model="itemEd.drugReminderTime"
                              placeholder="请选择时间"
                              value-format="HH:mm"
                              format="HH:mm"
                              :disabled="itemEd.drugRecordType === 2"
                            />
                          </el-form-item>
                        </div>
                        <div class="text myleft">提醒内容</div>
                        <div class="myleft" style="flex: 1">
                          <el-form-item
                            :prop="`dctDrugPlans[${index}].dctDrugRecords[${indexEd}].drugReminderText`"
                            :rules="
                              itemEd.drugRecordType !== 2
                                ? {
                                    required: true,
                                    message: '请输入',
                                    trigger: 'blur',
                                  }
                                : { required: false }
                            "
                          >
                            <el-input
                              v-model.trim="itemEd.drugReminderText"
                              :disabled="itemEd.drugRecordType === 2"
                              style="width: 100%"
                            />
                          </el-form-item>
                        </div>
                      </div>
                      <img
                        src="@/assets/closeIcon.svg"
                        v-if="indexEd > 0"
                        @click="recordBtn('delete', index, indexEd)"
                        class="closeImg"
                      />
                    </div>
                  </div>
                  <img
                    src="@/assets/addIcon.svg"
                    @click="recordBtn('add', index, '')"
                    class="medicationBoxRight"
                    v-if="indexEd === 0"
                  />
                </div>
              </el-row>
              <!-- 当天未完成对应用药记录时，在N进行提醒 -->
              <el-row v-if="item.drugFrequency !== 3">
                <el-checkbox
                  v-model="item.unfinishReminder"
                  class="radioBtn"
                  @change="unfinishReminderChange(index)"
                />
                <div class="text myleft">当天未完成对应用药记录时，在</div>
                <div class="checkDiv">
                  <el-form-item
                    :prop="`dctDrugPlans[${index}].unfinishReminderTime`"
                    :rules="
                      item.unfinishReminder
                        ? { required: true, message: '请选择', trigger: 'blur' }
                        : { required: false }
                    "
                  >
                    <el-time-picker
                      v-model="item.unfinishReminderTime"
                      placeholder="请选择时间"
                      value-format="HH:mm"
                      clearable
                      format="HH:mm"
                      style="width: 100%"
                      :disabled="!item.unfinishReminder"
                    />
                  </el-form-item>
                </div>
                <div class="text myleft">进行提醒</div>
              </el-row>
              <!-- 每隔N小时显示 -->
              <el-row v-if="item.drugFrequency === 3">
                <div class="drugHoursRecord">
                  <div>
                    <el-select
                      v-model="item.dctDrugHoursRecords.drugRecordType"
                      placeholder="请选择"
                      style="min-width: 150px"
                      @change="
                        (e) => {
                          drugRecordTypeTwoChange(e, index)
                        }
                      "
                    >
                      <el-option label="提醒+记录" :value="3" />
                      <el-option label="仅提醒" :value="1" />
                      <el-option label="仅记录" :value="2" />
                    </el-select>
                  </div>
                  <div class="text myleft">关联服药问卷</div>
                  <div class="myleft">
                    <el-form-item
                      :prop="`dctDrugPlans[${index}].dctDrugHoursRecords.drugQuestTemplateID`"
                      :rules="
                        item.dctDrugHoursRecords.drugRecordType !== 1
                          ? {
                              required: true,
                              message: '请选择',
                              trigger: 'change',
                            }
                          : { required: false }
                      "
                    >
                      <el-select
                        v-model="item.dctDrugHoursRecords.drugQuestTemplateID"
                        placeholder="请选择"
                        :disabled="
                          item.dctDrugHoursRecords.drugRecordType === 1
                        "
                      >
                        <el-option
                          v-for="(editionItem, editionIndex) in editionList"
                          :key="editionIndex"
                          :label="editionItem?.questTemplateName"
                          :value="editionItem?.questTemplateId"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="w-full flex flex-wrap" style="margin-top: 20px">
                    <div class="text">填写窗口：下发第</div>
                    <div class="flex">
                      <el-form-item
                        :prop="`dctDrugPlans[${index}].dctDrugHoursRecords.fillInStartDay`"
                        :rules="
                          item.dctDrugHoursRecords.drugRecordType !== 1
                            ? {
                                required: true,
                                message: '请输入',
                                trigger: 'blur',
                              }
                            : { required: false }
                        "
                      >
                        <el-input
                          v-model.trim="item.dctDrugHoursRecords.fillInStartDay"
                          oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                          placeholder="请输入"
                          disabled
                          class="myleft"
                          style="width: 120px"
                        />
                      </el-form-item>
                      <div class="myleft text">天</div>
                      <div class="myleft" style="width: 150px">
                        <el-form-item
                          :prop="`dctDrugPlans[${index}].dctDrugHoursRecords.fillInStartTime`"
                        >
                          <el-time-picker
                            v-model="item.dctDrugHoursRecords.fillInStartTime"
                            placeholder="请选择时间"
                            value-format="HH:mm"
                            format="HH:mm"
                            :clearable="false"
                            disabled
                            style="width: 100%"
                            @change="triggerTimeValidator(2, index)"
                          />
                        </el-form-item>
                      </div>
                      <div class="myleft text">至</div>
                      <div class="myleft text">第</div>
                      <el-form-item
                        :prop="`dctDrugPlans[${index}].dctDrugHoursRecords.fillInEndDay`"
                        :rules="
                          item.dctDrugHoursRecords.drugRecordType !== 1
                            ? {
                                required: true,
                                message: '请输入',
                                trigger: 'blur',
                              }
                            : { required: false }
                        "
                      >
                        <el-input
                          v-model.trim="item.dctDrugHoursRecords.fillInEndDay"
                          oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                          @blur="
                            item.dctDrugHoursRecords.fillInEndDay =
                              $event.target.value
                          "
                          placeholder="请输入"
                          :disabled="
                            item.dctDrugHoursRecords.drugRecordType === 1
                          "
                          class="myleft"
                          style="width: 120px"
                          @change="triggerTimeValidator(2, index)"
                        />
                      </el-form-item>
                      <div class="myleft text">天</div>
                      <div class="myleft" style="width: 150px">
                        <el-form-item
                          :prop="`dctDrugPlans[${index}].dctDrugHoursRecords.fillInEndTime`"
                        >
                          <el-time-picker
                            v-model="item.dctDrugHoursRecords.fillInEndTime"
                            placeholder="请选择时间"
                            value-format="HH:mm"
                            format="HH:mm"
                            :clearable="false"
                            disabled
                            style="width: 100%"
                          />
                        </el-form-item>
                      </div>
                    </div>
                    <div class="text myleft">首次填写后</div>
                    <div class="flex" style="min-width: 170px">
                      <el-input
                        v-model="item.dctDrugHoursRecords.changeDeadline"
                        oninput="value=value.replace(/[^\d]+/g,'')"
                        @blur="
                          item.dctDrugHoursRecords.changeDeadline =
                            $event.target.value
                        "
                        placeholder="请输入"
                        :disabled="
                          item.dctDrugHoursRecords.drugRecordType === 1
                        "
                        class="myleft"
                      />
                      <el-select
                        v-model="item.dctDrugHoursRecords.timeUnit"
                        placeholder="请选择"
                        :disabled="
                          item.dctDrugHoursRecords.drugRecordType === 1
                        "
                        style="min-width: 40px"
                        class="myleft"
                      >
                        <el-option label="天" :value="1" />
                        <el-option label="小时" :value="2" />
                        <el-option label="分钟" :value="3" />
                      </el-select>
                    </div>
                    <div class="text myleft">内可修改</div>
                  </div>

                  <div style="width: 100%; display: flex; margin-top: 20px">
                    <div class="text">提醒内容</div>
                    <div class="myleft" style="width: 60%">
                      <el-form-item
                        :prop="`dctDrugPlans[${index}].dctDrugHoursRecords.drugReminderText`"
                        :rules="
                          item.dctDrugHoursRecords.drugRecordType !== 2
                            ? {
                                required: true,
                                message: '请输入',
                                trigger: 'blur',
                              }
                            : { required: false }
                        "
                      >
                        <el-input
                          v-model.trim="
                            item.dctDrugHoursRecords.drugReminderText
                          "
                          :disabled="
                            item.dctDrugHoursRecords.drugRecordType === 2
                          "
                          style="width: 100%"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
      <div class="addCycleAdd" @click="cycleBtn('add', '')">添加周期</div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { deepClone } from '@/utils'
import {
  studyDropDrugInfos, // 获取服用药品信息
  studyStudyArmsInfo, // 获取分组信息
  getQuestTemplateDropSource, // 获取获取问卷信息
  postQuestTemplateDrugRegimen, // 保存数据
  getQuestTemplateDrugRegimen, // 获取详情数据
} from '@/api/dosageRegimenManagement'
import { getDropStudyVersionsCRF } from '@/api/home' // 获取CRF版本信息
export default defineComponent({
  name: 'DosageDetails', // 给药方案管理
  props: {
    stateChange: {
      type: Object,
    },
    requestLoad: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const store = useStore()
    const state = reactive({
      cycleShow: false,
      groupingDesignSzie: '', // 分组实验/单臂实验
      DosageDetailsForm: {
        id: null,
        drugRegimenName: '', // 方案名称
        dctStudyVersionId: '', // CRF版本ID
        armCodes: [], // 适配分组的数组
        dctDrugPlans: [
          {
            id: '',
            dctDrugRegimenId: '', // 给药方案ID
            dctDrugId: '', // 药品编号ID
            generationType: 1, // 用药计划, 0 = 未知, 1 = 预生成用药计划, 2 = 按需记录
            drugFrequency: 1, // 用药频率，1-每天，2-每隔N天，3-每隔N小时
            frequencyDay: '', // 用药频率每隔N天
            frequencyHours: '', // 用药频率每隔N小时
            frequencyInterval: 0, // 给后端传的字段，用药频率间隔 （用药频率为天是整数，为小时是小数）
            drugStartPlanType: 1, // 计划开始用药, 0 = 未知, 1 = 由医生指定
            drugEndPlanType: 1, // 计划结束用药, 0 = 未知, 1 = 按疗程, 2 = 患者结束研究
            drugPeriod: 1, // 计划结束用药-用药周期（共几天）
            drugDosage: 0, // 用药剂量Number类型
            unfinishReminder: false, // 当天未完成对应用药记录提醒状态
            unfinishReminderTime: '', // 传给后端的数据，当天未完成对应用药记录提醒时间
            drugDoseUnit: '', // 药品剂量单位(如：片)
            dctDrugRecords: [
              // 服药记录
              {
                id: '', // 主键Id
                dctDrugPlanId: '', // 服药计划Id
                specifiedDrugTime: 2, // 用药时间，2-指定用药时间，1-不指定用药时间
                drugDisplayTime: '00:00', // 给后端传的时间，具体用药时间，用药展示时间
                drugDisplayText: '', // 记录-用药相关提示文案
                drugRecordType: 3, // 3-提醒+记录，1-仅提醒，2-仅记录
                drugQuestTemplateID: '', // 关联服药问卷
                editWindowDay: null,
                changeDeadline: 7,
                timeUnit: 1,
                drugReminderTime: '', // 给后端传的时间，提醒时间
                drugReminderText:
                  '请您遵从医嘱用药，并及时在服药日志中进行记录。', // 提醒内容
                fillInStartDay: 1, //填写开始天数
                fillInStartTime: '00:00', //填写开始时间
                fillInEndDay: 1, //填写结束天数
                fillInEndTime: '23:59', //填写结束时间
              },
            ],
            dctDrugHoursRecords: {
              // 每隔小时用药记录
              id: '', // 主键Id
              dctDrugPlanId: '', // 服药计划Id
              drugRecordType: 3, // 3-提醒+记录，1-仅提醒，2-仅记录
              drugQuestTemplateID: '', // 关联服药问卷
              editWindowDay: null,
              changeDeadline: 7,
              timeUnit: 1,
              drugReminderText:
                '请您遵从医嘱用药，并及时在服药日志中进行记录。', // 提醒内容
              fillInStartDay: 1, //填写开始天数
              fillInStartTime: '00:00', //填写开始时间
              fillInEndDay: 1, //填写结束天数
              fillInEndTime: '23:59', //填写结束时间
            },
          },
        ],
      },
      medicineList: [], // 服药用品
      crfList: {}, // CRF版本信息
      groupingList: {}, // 下拉分组信息
      editionList: [], // 当前CRF版本下的所有问卷
      DosageNewForm: null,
      DosageNewFormRules: {
        drugRegimenName: [
          { required: true, message: '请输入方案名称', trigger: 'blur' },
        ],
        // CRF版本
        dctStudyVersionId: [
          { required: true, message: '请选择CRF版本', trigger: 'change' },
        ],
        // 分组
        armCodes: [
          { required: true, message: '请选择适配分组', trigger: 'change' },
        ],
      },
      // 用药频率change
      drugFrequencyChange: (index) => {
        state.DosageNewForm.clearValidate(`dctDrugPlans[${index}].frequencyDay`)
        state.DosageNewForm.clearValidate(
          `dctDrugPlans[${index}].frequencyHours`
        )
      },
      drugRecordTypeChange: (e, index, indexEd) => {
        if (e == 1) {
          state.DosageNewForm.clearValidate(
            `dctDrugPlans[${index}].dctDrugRecords[${indexEd}].drugQuestTemplateID`
          )
          state.DosageNewForm.clearValidate(
            `dctDrugPlans[${index}].dctDrugRecords[${indexEd}].editWindowDay`
          )
        } else if (e == 2) {
          state.DosageNewForm.clearValidate(
            `dctDrugPlans[${index}].dctDrugRecords[${indexEd}].drugReminderTime`
          )
          state.DosageNewForm.clearValidate(
            `dctDrugPlans[${index}].dctDrugRecords[${indexEd}].drugReminderText`
          )
        }
      },
      drugRecordTypeTwoChange: (e, index) => {
        if (e == 1) {
          state.DosageNewForm.clearValidate(
            `dctDrugPlans[${index}].dctDrugHoursRecords.drugQuestTemplateID`
          )
          state.DosageNewForm.clearValidate(
            `dctDrugPlans[${index}].dctDrugHoursRecords.editWindowDay`
          )
        } else if (e == 2) {
          state.DosageNewForm.clearValidate(
            `dctDrugPlans[${index}].dctDrugHoursRecords.drugReminderText`
          )
        }
      },
      unfinishReminderChange: (index) => {
        state.DosageNewForm.clearValidate(
          `dctDrugPlans[${index}].unfinishReminderTime`
        )
      },
      routerGo: (type) => {
        if (type === 'back') {
          props.stateChange.stateChangeFalgNew = false
          props.requestLoad()
        }
      },
      specifiedDrugTimeChange: (itemEd) => {
        if (itemEd.specifiedDrugTime === 1) {
          itemEd.drugDisplayTime = null
        } else {
          itemEd.drugDisplayTime = '00:00'
        }
      },
      // CRF版本，获取问卷
      editionBtn: (val) => {
        state.DosageDetailsForm.dctDrugPlans.forEach((val) => {
          val.dctDrugHoursRecords.drugQuestTemplateID = ''
          val.dctDrugRecords.forEach((item) => {
            item.drugQuestTemplateID = ''
          })
        })
        getQuestTemplateDropSource(val.studyVersionId).then((res) => {
          state.editionList = res
        })
      },
      // 药品单位
      drugDoseUnitBtn: (itemVlaue, index) => {
        state.DosageDetailsForm.dctDrugPlans[index].drugDoseUnit =
          itemVlaue.drugDoseUnit
      },
      // 添加or删除周期
      cycleBtn: (type, index) => {
        if (type === 'add') {
          state.DosageDetailsForm.dctDrugPlans.push({
            id: '',
            dctDrugRegimenId: '', // 给药方案ID
            dctDrugId: '', // 药品编号ID
            generationType: 1, // 用药计划, 0 = 未知, 1 = 预生成用药计划, 2 = 按需记录
            drugFrequency: 1, // 用药频率，1-每天，2-每隔N天，3-每隔N小时
            frequencyDay: '', // 用药频率每隔N天
            frequencyHours: '', // 用药频率每隔N小时
            frequencyInterval: 0, // 给后端传的字段，用药频率间隔 （用药频率为天是整数，为小时是小数）
            drugStartPlanType: 1, // 计划开始用药, 0 = 未知, 1 = 由医生指定
            drugEndPlanType: 1, // 计划结束用药, 0 = 未知, 1 = 按疗程, 2 = 患者结束研究
            drugPeriod: 1, // 计划结束用药-用药周期（共几天）
            drugDosage: 0, // 用药剂量Number类型
            unfinishReminder: false, // 当天未完成对应用药记录提醒状态
            unfinishReminderTime: '', // 传给后端的数据，当天未完成对应用药记录提醒时间
            drugDoseUnit: '', // 药品剂量单位(如：片)
            dctDrugRecords: [
              // 服药记录
              {
                id: '', // 主键Id
                dctDrugPlanId: '', // 服药计划Id
                specifiedDrugTime: 2, // 用药时间，2-指定用药时间，1-不指定用药时间
                drugDisplayTime: '00:00', // 给后端传的时间，具体用药时间，用药展示时间
                drugDisplayText: '', // 记录-用药相关提示文案
                drugRecordType: 3, // 3-提醒+记录，1-仅提醒，2-仅记录
                drugQuestTemplateID: '', // 关联服药问卷
                editWindowDay: null,
                changeDeadline: 7,
                timeUnit: 1,
                drugReminderTime: '', // 给后端传的时间，提醒时间
                drugReminderText:
                  '请您遵从医嘱用药，并及时在服药日志中进行记录。', // 提醒内容
                fillInStartDay: 1, //填写开始天数
                fillInStartTime: '00:00', //填写开始时间
                fillInEndDay: 1, //填写结束天数
                fillInEndTime: '23:59', //填写结束时间
              },
            ],
            dctDrugHoursRecords: {
              // 每隔小时用药记录
              id: '', // 主键Id
              dctDrugPlanId: '', // 服药计划Id
              drugRecordType: 3, // 3-提醒+记录，1-仅提醒，2-仅记录
              drugQuestTemplateID: '', // 关联服药问卷
              editWindowDay: null,
              changeDeadline: 7,
              timeUnit: 1,
              drugReminderText:
                '请您遵从医嘱用药，并及时在服药日志中进行记录。', // 提醒内容
              fillInStartDay: 1, //填写开始天数
              fillInStartTime: '00:00', //填写开始时间
              fillInEndDay: 1, //填写结束天数
              fillInEndTime: '23:59', //填写结束时间
            },
          })
        } else if (type === 'delete') {
          state.DosageDetailsForm.dctDrugPlans.splice(index, 1)
        }
      },
      // （添加or删除）给药周期中的记录
      recordBtn: (type, index, indexEd) => {
        if (type === 'add') {
          state.DosageDetailsForm.dctDrugPlans[index].dctDrugRecords.push({
            id: '', // 主键Id
            dctDrugPlanId: '', // 服药计划Id
            specifiedDrugTime: 2, // 用药时间，2-指定用药时间，1-不指定用药时间
            drugDisplayTime: '00:00', // 给后端传的时间，具体用药时间，用药展示时间
            drugDisplayText: '', // 记录-用药相关提示文案
            drugRecordType: 3, // 3-提醒+记录，1-仅提醒，2-仅记录
            drugQuestTemplateID: '', // 关联服药问卷
            editWindowDay: null,
            changeDeadline: 7,
            timeUnit: 1,
            drugReminderTime: '', // 给后端传的时间，提醒时间
            drugReminderText: '请您遵从医嘱用药，并及时在服药日志中进行记录。', // 提醒内容
            fillInStartDay: 1, //填写开始天数
            fillInStartTime: '00:00', //填写开始时间
            fillInEndDay: 1, //填写结束天数
            fillInEndTime: '23:59', //填写结束时间
          })
        } else if (type === 'delete') {
          state.DosageDetailsForm.dctDrugPlans[index].dctDrugRecords.splice(
            indexEd,
            1
          )
        }
      },
      // 保存
      confirmBtm: () => {
        state.DosageNewForm.validate((valid) => {
          if (valid) {
            const objFrom = deepClone(state.DosageDetailsForm)
            const loading = ElLoading.service({
              lock: true,
              text: 'Loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            objFrom.dctDrugPlans.forEach((item) => {
              // 每隔N天
              if (item.drugFrequency === 2) {
                item.frequencyInterval = Number(item.frequencyDay)
              }
              // 每隔N小时
              if (item.drugFrequency === 3) {
                item.frequencyInterval = Number(item.frequencyHours)
              }
              // 如果用药剂量=空则赋值成0
              if (!item.drugDosage) {
                item.drugDosage = 0
              }
            })
            postQuestTemplateDrugRegimen(store.state.studyItem.studyId, objFrom)
              .then(() => {
                loading.close()
                ElMessage.success('保存成功')
                props.requestLoad()
                props.stateChange.stateChangeFalgNew = false
              })
              .catch(() => {
                loading.close()
              })
          }
        })
      },
      onLoad: () => {
        // 获取详情的数据
        if (props.stateChange?.id) {
          const loading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          getQuestTemplateDrugRegimen(props.stateChange.id)
            .then((res: any) => {
              loading.close()
              state.DosageDetailsForm = res
              getQuestTemplateDropSource(res?.dctStudyVersionId).then((res) => {
                state.editionList = res
              })
              state.DosageDetailsForm.dctDrugPlans.forEach((val) => {
                if (val.dctDrugHoursRecords.drugRecordType === 0) {
                  val.dctDrugHoursRecords.drugRecordType = 3
                  val.dctDrugHoursRecords.changeDeadline = 7
                  val.dctDrugHoursRecords.timeUnit = 1
                  val.dctDrugHoursRecords.fillInStartDay = 1
                  val.dctDrugHoursRecords.fillInStartTime = '00:00'
                  val.dctDrugHoursRecords.fillInEndDay = 1
                  val.dctDrugHoursRecords.fillInEndTime = '23:59'
                }
                if (val.dctDrugRecords.length === 0) {
                  val.dctDrugRecords = [
                    // 服药记录
                    {
                      id: '', // 主键Id
                      dctDrugPlanId: '', // 服药计划Id
                      specifiedDrugTime: 2, // 用药时间，2-指定用药时间，1-不指定用药时间
                      drugDisplayTime: '00:00', // 给后端传的时间，具体用药时间，用药展示时间
                      drugDisplayText: '', // 记录-用药相关提示文案
                      drugRecordType: 3, // 3-提醒+记录，1-仅提醒，2-仅记录
                      drugQuestTemplateID: '', // 关联服药问卷
                      editWindowDay: null,
                      changeDeadline: 7,
                      timeUnit: 1,
                      drugReminderTime: '', // 给后端传的时间，提醒时间
                      drugReminderText:
                        '请您遵从医嘱用药，并及时在服药日志中进行记录。', // 提醒内容
                      fillInStartDay: 1, //填写开始天数
                      fillInStartTime: '00:00', //填写开始时间
                      fillInEndDay: 1, //填写结束天数
                      fillInEndTime: '23:59', //填写结束时间
                    },
                  ]
                }
                if (!val.dctDrugHoursRecords?.drugReminderText) {
                  val.dctDrugHoursRecords.drugReminderText =
                    '请您遵从医嘱用药，并及时在服药日志中进行记录。'
                }
                if (val.drugFrequency === 2) {
                  val.frequencyDay = val.frequencyInterval
                }
                if (val.drugFrequency === 3) {
                  val.frequencyHours = val.frequencyInterval
                }
              })
            })
            .catch(() => {
              loading.close()
            })
        }
      },
      //填写窗口时间校验，结束时间>开始时间
      drugPlanEditTimeValidator: (rule, value, callback) => {
        let prop = rule.field.split('.')
        const row =
          state.DosageDetailsForm.dctDrugPlans[prop[0].match(/\[(\d+)\]/)[1]]
            .dctDrugRecords[prop[1].match(/\[(\d+)\]/)[1]]
        const startTime = row.fillInStartTime
        const startDay = row.fillInStartDay
        const endTime = row.fillInEndTime
        const endDay = row.fillInEndDay
        if (
          +startDay >= 0 &&
          +endDay >= 0 &&
          startDay == endDay &&
          startTime &&
          value &&
          value <= startTime
        ) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      drugHourPlanEditTimeValidator: (rule, value, callback) => {
        let prop = rule.field.split('.')
        const row =
          state.DosageDetailsForm.dctDrugPlans[prop[0].match(/\[(\d+)\]/)[1]]
            .dctDrugHoursRecords
        const startTime = row.fillInStartTime
        const startDay = row.fillInStartDay
        const endTime = row.fillInEndTime
        const endDay = row.fillInEndDay
        if (
          +startDay >= 0 &&
          +endDay >= 0 &&
          startDay === endDay &&
          startTime &&
          value &&
          value <= startTime
        ) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      triggerTimeValidator(type, index, indexEd) {
        if (type === 1) {
          state.DosageNewForm.validateField(
            `dctDrugPlans[${index}].dctDrugRecords[${indexEd}].fillInEndTime`
          )
        } else if (type === 2) {
          state.DosageNewForm.validateField(
            `dctDrugPlans[${index}].dctDrugHoursRecords.fillInEndTime`
          )
        }
      },
    })

    onMounted(() => {
      state.onLoad()
      state.groupingDesignSzie = props.stateChange.groupingDesignString
      // 服药用品
      studyDropDrugInfos(store.state.studyItem.studyId).then((res: any) => {
        state.medicineList = res
      })

      // CRF版本信息
      getDropStudyVersionsCRF(store.state.studyItem.studyId).then(
        (res: any) => {
          state.crfList = res
        }
      )

      // 下拉分组信息
      studyStudyArmsInfo(store.state.studyItem.studyId).then((res: any) => {
        state.groupingList = res
      })
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.el-select {
  min-width: 150px;
}
.details {
  width: 100%;
  min-width: 1300px;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  //新建药品
  .main {
    width: 94%;
    margin: 0 auto;
    margin-top: 20px;
    // 给药周期
    .mainContent {
      width: 100%;
      margin-bottom: 40px;
      // 线段
      .cycleLine {
        width: 100%;
        display: flex;
        padding-bottom: 5px;
        justify-content: space-between;
        box-sizing: border-box;
        border-bottom: 2px solid #ccc;
        div {
          height: 30px;
          line-height: 30px;
          font-size: 14px;
          span {
            color: red;
            margin-right: 5px;
          }
        }
        .deleteBtn {
          padding: 0 20px;
          height: 30px;
          box-sizing: border-box;
          background: rgb(224, 26, 26);
          color: #fff;
          border-radius: 5px;
          cursor: pointer;
        }
      }
      // 给药周期内容
      .contentBox {
        width: 100%;
        margin-top: 20px;
        // 每天/每隔N天显示
        .medicationBox {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          .medicationBoxLeft {
            width: 90%;
            display: flex;
            margin-bottom: 30px;
            .left {
              width: 120px;
              background: #d7d7d7;
              text-align: center;
              padding: 20px 0;
              box-sizing: border-box;
              span {
                display: block;
                margin-top: 35px;
              }
            }
            .right {
              flex: 1;
              display: flex;
              flex-wrap: wrap;
              background: #f3f3f3;
              padding: 20px;
              box-sizing: border-box;
              position: relative;
              div {
                span {
                  display: block;
                  margin: 5px 0;
                  font-size: 12px;
                  color: #696666;
                }
              }
              .closeImg {
                position: absolute;
                right: 0;
                top: 0;
                width: 30px;
                cursor: pointer;
              }
            }
          }
          .medicationBoxRight {
            margin-left: 10px;
            width: 30px;
            cursor: pointer;
          }
        }
        // 每隔N小时显示
        .drugHoursRecord {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          background: #f3f3f3;
          padding: 20px;
          box-sizing: border-box;
        }
      }
    }
  }
}

.checkDiv {
  margin: 0 10px 10px 10px;
  display: flex;
  flex-wrap: wrap;
}

.el-row {
  margin-top: 20px;
}
.el-form-item {
  margin-bottom: 0 !important;
}

.text {
  line-height: 30px;
  font-size: 14px;
}

.myleft {
  margin-left: 10px;
}
.inputNumber {
  margin-left: 10px;
  & :deep(.el-input__inner) {
    text-align: left !important;
  }
}
.radioBtn {
  display: flex;
  justify-content: space-between;
  div {
    margin: 0 5px;
    display: flex;
    position: relative;
    span {
      position: absolute;
      left: 5px;
      bottom: -30px;
      color: red;
      font-size: 12px;
      font-weight: normal;
    }
  }
}
::v-deep .el-radio__label {
  display: flex;
  line-height: 33px;
}
::v-deep .el-checkbox__label {
  display: flex;
  line-height: 33px;
}
.radioleft {
  margin-left: 60px;
}
// 添加周期按钮
.addCycleAdd {
  display: inline-block;
  padding: 5px 20px;
  box-sizing: border-box;
  color: #fff;
  background: #409eff;
  border-radius: 5px;
  cursor: pointer;
}
</style>
