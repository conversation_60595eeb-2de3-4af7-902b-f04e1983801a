<template>
  <div class="dosageRegimenManagement">
    <!-- 甲状腺项目给药方案 -->
    <div v-if="stateChange.stateChangeFalg === 1">
      <div class="head">
        <span>研究性质</span>
        <div class="head-box">
          <div v-if="dosageRegimenManagementList" class="head-boxBottom">
            <div>
              <span>分组设计</span>
              <span
                v-if="dosageRegimenManagementList?.groupingDesign"
                v-html="dosageRegimenManagementList.groupingDesign"
              />
            </div>
            <div>
              <span>盲法设置</span>
              <span
                v-if="dosageRegimenManagementList?.blindedSet"
                v-html="dosageRegimenManagementList.blindedSet"
              />
            </div>
            <div>
              <span>随机方式</span>
              <span
                v-if="dosageRegimenManagementList?.randomFashion"
                v-html="dosageRegimenManagementList.randomFashion"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="dosageRegimenManagement-main">
        <div class="mainTop">
          <span>给药方案</span>
          <el-button type="primary" @click="stateTabChange($event)"
            >新建方案</el-button
          >
        </div>
        <div
          v-for="(item, index) in dosageRegimenManagementList.drugSchemes"
          :key="index"
          class="containerContent"
        >
          <div class="edit">
            <span :data-msg="item.id" @click="stateTabChange($event)"
              >编辑</span
            >
          </div>
          <div class="containerContentBox">
            <div class="containerContentBoxRight">
              <div class="single">
                <div>
                  <span class="lable">方案名称</span>
                  <span class="texts">{{ item.schemeName }}</span>
                </div>
              </div>
              <div class="content">
                <div>
                  <span>CRF版本</span>
                  <span>{{ item.versionNumber }}</span>
                </div>
                <div
                  v-if="
                    dosageRegimenManagementList.groupingDesign !== '单臂实验'
                  "
                >
                  <span>适配分组</span>
                  <span>{{ item.armName }}</span>
                </div>
              </div>
              <div
                v-for="(clItem, clIndex) in item.drugPeriods"
                :key="clIndex"
                class="single"
              >
                <div
                  v-if="clItem?.visitChangedAlerts"
                  class="visitChangedAlerts"
                  v-html="clItem.visitChangedAlerts"
                />
                <div>
                  <span v-if="clIndex === 0" class="lable">给药周期</span>
                  <span class="texts" :class="{ 'mg-l-140-px': clIndex > 0 }"
                    >自【{{ clItem.startVisitName }}】第【{{
                      clItem.startDay
                    }}】天，至【{{ clItem.endVisitName }}】第【{{
                      clItem.endDay
                    }}】天，每间隔【{{ clItem.intervalDay }}】天，服用【{{
                      clItem.drugName
                    }}】，当天需给药【{{
                      clItem.numberOfTimesPerDay
                    }}】次，默认给药剂量【{{ clItem.defaultDose }}】
                    {{ clItem.drugDoseUnit }} <br />
                    {{ clItem.drugRemind }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <DosageDetailsOld
      v-if="stateChange.stateChangeFalgOld"
      :state-change="stateChange"
      :request-load="onLoad"
    />
    <!-- 新的给药方案 -->
    <div v-if="stateChange.stateChangeFalg === 2">
      <div class="head">
        <span>研究性质</span>
        <div class="head-box">
          <div v-if="dosageRegimenManagementListNew" class="head-boxBottom">
            <div>
              <span>分组设计</span>
              <span
                v-if="dosageRegimenManagementListNew?.groupingDesign"
                v-html="dosageRegimenManagementListNew.groupingDesign"
              />
            </div>
            <div>
              <span>盲法设置</span>
              <span
                v-if="dosageRegimenManagementListNew?.blindedSet"
                v-html="dosageRegimenManagementListNew.blindedSet"
              />
            </div>
            <div>
              <span>随机方式</span>
              <span
                v-if="dosageRegimenManagementListNew?.randomFashion"
                v-html="dosageRegimenManagementListNew.randomFashion"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="dosageRegimenManagement-main">
        <div class="mainTop">
          <span>给药方案</span>
          <el-button type="primary" @click="administrationBtn(null)">新建方案</el-button>
        </div>
        <div
          v-for="(item, index) in dosageRegimenManagementListNew?.drugRegimens"
          :key="index"
          class="containerContent"
        >
          <div class="edit">
            <span @click="administrationBtn(item)">编辑</span>
          </div>
          <div class="containerContentBox">
            <div class="containerContentBoxRight">
              <div class="single">
                <div>
                  <span class="lable">方案名称</span>
                  <span class="texts">{{ item?.drugRegimenName }}</span>
                </div>
              </div>
              <div
                class="content"
              >
                <div>
                  <span>CRF版本</span>
                  <span
                    v-html="
                      item?.versionStatus === 1
                        ? 'V' + item?.versionNumber + '测试版'
                        : 'V' + item?.versionNumber + '正式版'
                    "
                  />
                </div>
                <div v-if="item?.armName">
                  <span>适配分组</span>
                  <span>{{ item.armName }}</span>
                </div>
              </div>
              <div
                v-for="(clItem, clIndex) in item?.drugPlanPeriods"
                :key="clIndex"
                class="single"
              >
                <!-- <div
                  v-if="clItem?.visitChangedAlerts"
                  class="visitChangedAlerts"
                  v-html="clItem.visitChangedAlerts"
                /> -->
                <div>
                  <span v-if="clIndex === 0" class="lable">给药周期</span>
                  <div class="texts" :class="{ 'mg-l-140-px': clIndex > 0 }">
                    <span>
                      {{ clItem?.drugPlanPeriod }}
                      <!-- 【药物名称】，用药频次【每隔8天】，当天用药【1】次，默认用药剂量【30ml】，计划开始用药【由医生指定】，计划结束用药【患者结束研究】 -->
                    </span>
                    <!-- <span>
                      【药物名称】，用药频次【每天】，当天用药【1】次，默认用药剂量【30ml】，计划开始用药【由医生指定】，计划结束用药【按疗程】共【10】天
                    </span>
                    <span>
                      【药物名称】，用药频次【每隔12小时】，默认用药剂量【30ml】，计划开始用药【由医生指定】，计划结束用药【按疗程】共【5】次
                    </span> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <DosageDetailsNew
      v-if="stateChange.stateChangeFalgNew"
      :state-change="stateChange"
      :request-load="onLoad"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import DosageDetailsOld from '@/views/dosageRegimenManagement/DosageDetailsOld.vue'
import DosageDetailsNew from '@/views/dosageRegimenManagement/DosageDetailsNew.vue'
import {
  studyDrugScheme, // 老的给药方案列表数据
  studyDrugInfo, // 老的给药方案详情数据
  getQuestTemplateDrugRegimenes, // 新的给药方案列表数据
} from '@/api/dosageRegimenManagement'
import { useRouter } from 'vue-router'
export default defineComponent({
  name: 'DosageRegimenManagement', // 给药方案管理
  components: {
    DosageDetailsOld,
    DosageDetailsNew,
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const state = reactive({
      studyName: '', // 项目名称
      dosageRegimenManagementList: {
        groupingDesign: '', // 分组设计
        blindedSet: '', // 盲法设置
        randomFashion: '', // 随即方式
        drugSchemes: [], // 研究对象药物列表信息
      },
      dosageRegimenManagementListNew: {
        groupingDesign: '', // 分组设计
        blindedSet: '', // 盲法设置
        randomFashion: '', // 随即方式
        drugRegimens: [], // 研究对象药物列表信息
      },
      stateChange: {
        stateChangeFalg: 1,
        stateChangeFalgOld: false,
        stateChangeFalgNew: false,
        id: '',
        ruleFormList: {},
        groupingDesignString: '',
      },
      // 新建or编辑 老的给药方案
      stateTabChange: (e) => {
        state.stateChange.id = e.target.dataset.msg
        if (
          !state.stateChange.id &&
          state.studyName ===
            '甲状腺片与左甲状腺素钠片对照治疗分化型甲状腺癌术后患者的有效性与安全性临床研究'
        ) {
          state.stateChange.stateChangeFalg = 0
          state.stateChange.stateChangeFalgOld = true
          state.stateChange.groupingDesignString =
            state.dosageRegimenManagementList.groupingDesign
        } else {
          studyDrugInfo(state.stateChange.id).then((res: any) => {
            state.stateChange.ruleFormList = res
            state.stateChange.stateChangeFalg = 0
            state.stateChange.stateChangeFalgOld = true
          })
        }
      },
      // 新建or编辑 新的给药方案
      administrationBtn: (e) => {
        if (e) {
          state.stateChange.id = e?.drugRegimenId || ''
        } else {
          state.stateChange.id = ''
        }
        if (
          !state.stateChange.id &&
          state.studyName !==
            '甲状腺片与左甲状腺素钠片对照治疗分化型甲状腺癌术后患者的有效性与安全性临床研究'
        ) {
          state.stateChange.stateChangeFalg = 0
          state.stateChange.stateChangeFalgNew = true
          state.stateChange.groupingDesignString =
            state.dosageRegimenManagementListNew.groupingDesign
        } else {
          state.stateChange.groupingDesignString =
            state.dosageRegimenManagementListNew.groupingDesign
          state.stateChange.stateChangeFalg = 0
          state.stateChange.stateChangeFalgNew = true
        }
      },
      onLoad: () => {
        state.studyName = store.state?.studyItem?.studyName
        if (
          state.studyName ===
          '甲状腺片与左甲状腺素钠片对照治疗分化型甲状腺癌术后患者的有效性与安全性临床研究'
        ) {
          state.stateChange.stateChangeFalg = 1
          if (store.state?.studyItem?.studyId) {
            studyDrugScheme(store.state.studyItem.studyId).then((res: any) => {
              state.dosageRegimenManagementList = res
            })
          } else {
            router.push('/study')
          }
        } else {
          state.stateChange.stateChangeFalg = 2
          if (store.state?.studyItem?.studyId) {
            getQuestTemplateDrugRegimenes(store.state.studyItem.studyId).then(
              (res: any) => {
                state.dosageRegimenManagementListNew = res
              }
            )
          } else {
            router.push('/study')
          }
        }
      },
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.el-select {
  min-width: 150px;
}
.dosageRegimenManagement {
  .head {
    width: 100%;
    span {
      font-weight: 700;
    }
    .head-box {
      width: 100%;
      background: #fff;
      margin-top: 10px;
      padding: 20px 0px;
      box-sizing: border-box;
      div {
        display: flex;
        span {
          font-weight: normal;
          &:first-child {
            width: 130px;
            text-align: right;
            color: rgb(156, 152, 152);
          }
          &:last-child {
            margin-left: 10px;
          }
        }
      }
      .head-boxBottom {
        width: 70%;
        margin-left: 20px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
  .dosageRegimenManagement-main {
    width: 100%;
    margin-top: 20px;
    .mainTop {
      display: flex;
      justify-content: space-between;
      height: 30px;
      line-height: 30px;
      span {
        font-weight: 700;
      }
      div {
        display: inline-block;
        color: #fff;
        padding: 0 10px;
        background: rgb(64, 158, 255);
        cursor: pointer;
      }
    }
    .containerContent {
      width: 100%;
      background: #fff;
      padding: 10px 20px 20px;
      margin-top: 20px;
      box-sizing: border-box;
      overflow: hidden;
      .edit {
        text-align: right;
        span {
          color: rgb(0, 127, 247);
          cursor: pointer;
        }
      }
      .containerContentBox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .containerContentBoxRight {
          flex: 1;
          .content {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            div {
              width: 50%;
              display: flex;
              span {
                &:first-child {
                  width: 130px;
                  text-align: right;
                  color: rgb(156, 152, 152);
                }
                &:last-child {
                  margin-left: 10px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
          .single {
            margin-top: 15px;
            .visitChangedAlerts {
              width: 100%;
              padding: 0 10%;
              box-sizing: border-box;
              text-align: center;
              color: red;
              margin: 0 0 10px 0;
            }
            div {
              display: flex;
              .texts {
                flex: 1;
                display: block;
                margin-left: 10px;
                span {
                  display: block;
                  margin-top: 10px;
                  &:first-child {
                    margin-top: 0;
                  }
                }
              }
              .mg-l-140-px {
                margin-left: 140px;
              }
              .lable {
                width: 130px;
                text-align: right;
                color: rgb(156, 152, 152);
              }
            }
          }
        }
      }
    }
  }
}
</style>
