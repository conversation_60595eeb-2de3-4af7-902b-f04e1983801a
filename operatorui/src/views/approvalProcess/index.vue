<template>
  <div>
    <div v-if="!RESEARCHCENTER_INFOS?.contentVisible">
      <trial-table
        ref="approvalProcessTableRef"
        title=""
        :request="getapprovalProcessList"
        :columns="approvalProcessColumns"
        :pagination="paginationConfig"
      >
        <!-- 工具栏 -->
        <template #toolbar>
          <el-button type="primary" @click="approvalProcessEditItemForm(null)">
            新建
          </el-button>
        </template>
        <template #operate="scope">
          <el-button
            size="small"
            text
            type="primary"
            @click="approvalProcessEditItemForm(scope.row)"
          >
            编辑
          </el-button>
        </template>
      </trial-table>
    </div>
    <ApprovalProcessDetail v-if="RESEARCHCENTER_INFOS?.contentVisible" />
  </div>
</template>

<script lang="ts">
import { defineComponent, provide, reactive, toRefs } from 'vue'
import ApprovalProcessDetail from '@/views/approvalProcess/approvalProcessDetail.vue'
import { useStore } from 'vuex'
import { getAPR } from '@/api/approvalProcess'

export default defineComponent({
  name: 'ApprovalProcess', // 审批流
  components: {
    ApprovalProcessDetail,
  },
  setup() {
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
      setTitle: '',
    })
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      approvalProcessTableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      approvalProcessColumns: [
        // { type: 'selection' },
        { label: '审批流名称', prop: 'name', minWidth: 260 },
        { label: '更新时间', prop: 'modifyTime', minWidth: 230 },
        { label: '更新人', prop: 'modifyUser', minWidth: 180 },
        { label: '创建时间', prop: 'createTime', minWidth: 230 },
        { label: '创建人', prop: 'createUser', minWidth: 180 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 请求函数
      async getapprovalProcessList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const data = await getAPR(studyId, params)
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data.items,
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      approvalProcessRefresh: () => {
        state.approvalProcessTableRef.refresh()
      },
      // 新增-编辑
      approvalProcessEditItemForm: (row) => {
        if (row) {
          RESEARCHCENTER_INFOS.setTitle = '编辑'
        } else {
          RESEARCHCENTER_INFOS.setTitle = '新建'
        }
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.approvalProcessTableRef
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
