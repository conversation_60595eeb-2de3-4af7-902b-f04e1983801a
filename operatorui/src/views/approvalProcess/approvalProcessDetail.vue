<template>
  <div class="approvalProcess">
    <div class="flex justify-between items-center">
      <h3>{{ RESEARCHCENTER_INFOS?.setTitle }}</h3>
      <div>
        <el-button :loading="loading" type="primary" @click="saveData">保存</el-button>
        <el-button :loading="loading" @click="backRefresh">返回</el-button>
      </div>
    </div>
    <el-form ref="approvalProcessFromRef" :model="ruleForm" :rules="rules" label-position="top">
      <el-form-item label="审批流名称" prop="planName" class="margin-1" style="width: 30%">
        <el-input
          v-model.trim="ruleForm.planName"
          class="w-full"
          placeholder="请输入"
          maxlength="999"
        />
      </el-form-item>
      <el-button :loading="loading" type="primary" @click="addruleClick">添加节点</el-button>
      <div v-for="(item, index) in ruleForm.ruleArr" :key="index" class="mb-10">
        <div class="flex node-bottom items-center justify-between mb-5">
          <div class="font-semibold mt-5 mb-3">
            节点{{ index + 1 }}
          </div>
          <el-icon v-if="ruleForm.ruleArr.length !== 1" class="node-icon" color="#d81e06" :size="25" @click="removeruleClick(index)"><CircleCloseFilled /></el-icon>
        </div>
        <div class="flex">
          <el-form-item label="节点名称" :prop="`ruleArr[${index}].name`" class="mr-3" style="width: 30%" :rules="{ required: true, message: '请输入', trigger: 'blur' }">
            <el-input
              v-model.trim="item.name"
              class="w-full"
              placeholder="请输入"
              maxlength="999"
            />
          </el-form-item>
          <el-form-item label="审批对象" :prop="`ruleArr[${index}].approvertype`" class="flex-1 mr-3" :rules="{ required: true, message: '请选择', trigger: 'blur' }">
            <el-select
              v-model="item.approvertype"
              class="w-full"
              placeholder="请选择"
            >
              <el-option label="按角色" value="Role" />
            </el-select>
          </el-form-item>
          <el-form-item label="角色" :prop="`ruleArr[${index}].approver`" class="flex-1 mr-3" :rules="{ required: true, message: '请选择', trigger: 'change' }">
            <el-select
              v-model="item.approver"
              class="w-full"
              placeholder="请选择"
            >
              <el-option
                v-for="ite in roleArr"
                :key="ite.id"
                :label="ite.roleName"
                :value="ite.roleId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="或签/会签" :prop="`ruleArr[${index}].iscountersign`" class="flex-1 mr-3" :rules="{ required: true, message: '请选择', trigger: 'change' }">
            <el-select
              v-model="item.iscountersign"
              class="w-full"
              placeholder="请选择"
            >
              <el-option label="或签" value="0" />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex mt-3">
          <span class="mr-5">其他节点设置</span>
          <el-form-item>
            <el-checkbox-group v-model="item.checkboxnode">
              <el-checkbox :label="1" disabled>审批人=申请人时自动通过</el-checkbox>
              <el-checkbox :label="2">找审批人时继承医患绑定</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script lang='ts'>
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { CircleCloseFilled } from '@element-plus/icons-vue'
import { guid } from '@/utils/approve-flow'
import { deepClone } from '@/utils'
import { getAPRChart, getRoles, postAPRChart } from '@/api/approvalProcess'

export default defineComponent({
  name: 'ApprovalProcessDetail', // 审批流详情页
  components: {
    CircleCloseFilled
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const { studyId } = store.state.studyItem
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      loading: false,
      approvalProcessFromRef: null,
      ruleCopyArr: [], // 进入页面转化得到的arr
      ruleForm: {
        planName: '',
        ruleArr: [
          {
            name: '',
            approvertype: 'Role',
            approver: '',
            iscountersign: '0',
            checkboxnode: [1, 2]
          },
        ]
      },
      rules: {
        planName: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      },
      roleArr: [
      ],
      backRefresh: () => {
        state.approvalProcessFromRef.resetFields()
        RESEARCHCENTER_INFOS.resetMethod.refresh()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      addruleClick: () => {
        state.ruleForm.ruleArr.push({
          name: '',
          approvertype: 'Role',
          approver: '',
          iscountersign: '0',
          checkboxnode: [1, 2]
        })
      },
      removeruleClick: (index) => {
        state.ruleForm.ruleArr.splice(index, 1)
      },
      // 保存接口
      saveData: () => {
        state.approvalProcessFromRef.validate((valid) => {
          if (valid) {
            const ruleForm = deepClone(state.ruleForm)
            // 转为字符串
            const rootXml = `<Root name="${ruleForm?.planName || ''}">`
            let arrXml = ''
            if (RESEARCHCENTER_INFOS.setTitle === '新建') {
              ruleForm.ruleArr.unshift({
                type: 'Start',
              })
              ruleForm.ruleArr.push({
                type: 'End',
              })
            } else {
              // 放另一个
              ruleForm.ruleArr.unshift(state.ruleCopyArr[0])
              ruleForm.ruleArr.push(state.ruleCopyArr[state.ruleCopyArr.length - 1])
            }
            ruleForm.ruleArr.forEach((item, index) => {
              if (!item?.id) { // 防止无
                item.id = guid()
              }
              if (index === 0 || (index + 1) === ruleForm.ruleArr?.length) {
                arrXml += `<Node type="${item.type}" name="${item.type === 'End' ? '结束' : '开始'}" id="${item.id}" parentid="${item.type === 'End' ? ruleForm.ruleArr[index - 1].id : ''}" seq="${index + 1}" priority="1" />`
              } else {
                if (item.checkboxnode.includes(1)) {
                  item.asvariable = '1'
                } else {
                  item.asvariable = '0'
                }
                if (item.checkboxnode.includes(2)) {
                  item.isextend = '1'
                } else {
                  item.isextend = '0'
                }
                arrXml += `<Node id="${item.id}" name="${item.name}" type="Master" approvertype="${item.approvertype}" approver="${item.approver}" iscountersign="${item.iscountersign}" asvariable="${item.asvariable}" isextend="${item.isextend}" parentid="${ruleForm.ruleArr[index - 1].id}" priority="1" seq="${index + 1}" />`
              }
            })
            const xmlText = `${rootXml}${arrXml}</Root>`
            const data = {
              xmlText,
              groupCode: ''
            }
            let headCode = ''
            if (RESEARCHCENTER_INFOS.setTitle === '新建') {
              headCode = guid()
            } else {
              headCode = ruleForm.headerCode
              data.groupCode = ruleForm?.groupCode
            }
            state.loading = true
            postAPRChart(studyId, headCode, data).then(() => {
              ElMessage.success('保存成功')
              RESEARCHCENTER_INFOS.contentVisible = false
              RESEARCHCENTER_INFOS.resetMethod.refresh()
              state.loading = false
            })
          }
        })
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        if (RESEARCHCENTER_INFOS.setTitle === '编辑') {
          getAPRChart(studyId, RESEARCHCENTER_INFOS.researchContent?.code).then((res) => {
            if (res?.xmlText) {
              let nodeStrings = res.xmlText.match(/<Node.*?\/>/g)
              // 过滤空字符串
              nodeStrings = nodeStrings.filter((str) => {
                return str.trim() !== ''
              })
              // 转换为节点对象数组
              res.ruleArr = nodeStrings.map((str) => {
                const node = {}
                const attrs = str.match(/\w+=".*?"/g)
                attrs.forEach((attr) => {
                  const parts = attr.split('=')
                  const key = parts[0]
                  const value = parts[1].replace(/"/g, '')
                  node[key] = value
                })
                return node
              })

              const nodeRoot = res.xmlText.match(/<Root.*?>/g)
              res.planName = nodeRoot[0].match(/name="(.*?)"/)[1]
            }
            res.ruleArr.forEach((item) => {
              item.checkboxnode = []
              if (item.asvariable === '1') {
                item.checkboxnode.push(1)
              }
              if (item.isextend === '1') {
                item.checkboxnode.push(2)
              }
            })
            state.ruleCopyArr = deepClone(res.ruleArr)
            res.ruleArr.shift() // 删除第一个
            res.ruleArr.pop() // 删除第一个
            state.ruleForm = res
            console.log('state.ruleForm');
            
          })
        }
      }
    })
    onMounted(() => {
      getRoles(studyId).then((res) => {
        state.roleArr = res
      })
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.approvalProcess {
  background: #fff;
  padding: 20px;
  border-radius: 5px;
  .node-bottom {
    border-bottom: 2px solid #d7d7d7;
    .node-icon {
      cursor: pointer;
    }
  }
}
</style>
