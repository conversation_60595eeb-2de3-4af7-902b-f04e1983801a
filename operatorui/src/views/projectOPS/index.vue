<template>
  <div>
    <div class="w-full p-5 tips text-center font-semibold">
      本功能模块主要应用于按项目&中心颗粒度，创建运维计划，在计划的开始时间至结束时间段内，受试者端、研究端将暂停服务，请慎重使用！！！
    </div>
    <trial-table
      ref="projectTableRef"
      :request="getprojectList"
      :columns="projectColumns"
      :pagination="projectPaginationConfig"
      hide-center
      :showbtnfalg="true"
      @selectionChange="projectHandleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #hideCenter>
        <div class="my-5 text-right">
          <el-button
            type="primary"
            @click="projectEditItemForm(null)"
          >新建</el-button>
        </div>
      </template>
      <template #logistics="scope">
        <div class="whitespace-pre-wrap content-height" v-html="scope.row?.content" />
      </template>
      <template #remark="scope">
        <div class="whitespace-pre-wrap content-height" v-html="scope.row?.remark" />
      </template>
      <template #operate="scope">
        <el-button size="small" text type="primary" @click="projectEditItemForm(scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          text
          type="danger"
          @click="projectDeleteItem(scope.row)"
        >删除</el-button>
      </template>
    </trial-table>

    <!-- 弹窗 -->
    <trial-dialog v-model="projectShow" :title="myTitle">
      <template #DialogBody>
        <el-form
          ref="projectShowRef"
          label-position="top"
          label-width="120px"
          :model="dataForm"
          :rules="rules"
        >
          <div class="flex justify-between">
            <el-form-item style="width: 21%" label="开始日期" prop="startTimeDate">
              <el-date-picker
                v-model="dataForm.startTimeDate"
                type="date"
                placeholder="年/月/日"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item style="width: 21%" label="开始时间" prop="startTimeTime">
              <el-time-picker
                v-model="dataForm.startTimeTime"
                type="datetime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="--"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item style="width: 21%" label="结束日期" prop="endTimeDate">
              <el-date-picker
                v-model="dataForm.endTimeDate"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="年/月/日"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item style="width: 21%" label="结束时间" prop="endTimeTime">
              <el-time-picker
                v-model="dataForm.endTimeTime"
                type="datetime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="--"
                style="width: 100%"
              />
            </el-form-item>
          </div>
          <div class="my-3">
            <el-button type="primary" class="mr-4" @click="applyCentre">适用中心</el-button>
            <span>已选择 {{ shuttleValue.length }}/{{ shuttleData.length }} 家中心</span>
          </div>
          <el-form-item label="公告内容（受试者端、研究端展示）" prop="content">
            <el-input v-model="dataForm.content" rows="8" maxlength="999" type="textarea" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" rows="8" maxlength="999" type="textarea" />
          </el-form-item>
          <div class="flex items-center">
            <div class="mr-3">状态</div>
            <el-switch v-model="dataForm.statusShow" />
          </div>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-center">
          <el-button :loading="loading" plain @click="projectShowCancel">取消</el-button>
          <el-button :loading="loading" type="primary" @click="projectShowConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <!-- 适用中心弹窗 -->
    <trial-dialog v-model="applyFlagShow" :my-dialog-body-style="myDialogBodyStyleDialog">
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="中心名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button :loading="loading" plain @click="shuttleCancel">取消</el-button>
          <el-button :loading="loading" type="primary" @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { getOutOfServiceConfig, getOutOfServiceConfigList, deleteDeleteOutOfServiceConfig, putOutOfServiceConfig } from '@/api/projectOPS'
import { getCustomTasksTransfer } from '@/api/customTask'
import { parseTime } from '@/utils'

export default defineComponent({
  name: 'ProjectOPS', // 运维计划
  setup() {
    const store = useStore()
    const state = reactive({
      projectTableRef: null,
      projectShowRef: null,
      shuttleValue: [],
      shuttleData: [],
      shuttleValueCopy: [],
      myTitle: '',
      myDialogBodyStyleDialog: {
        width: '60%'
      },
      loading: false,
      shuttleProps: {
        key: 'siteId',
        label: 'statusStrAndsiteName'
      },
      applyFlagShow: false,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      projectColumns: [
        // { type: 'selection' },
        { label: '开始时间', prop: 'startTime', minWidth: 180 },
        { label: '结束时间', prop: 'endTime', minWidth: 180 },
        { label: '适用中心', prop: 'siteNum', minWidth: 120 },
        { label: '公告内容', prop: 'content', minWidth: 300, tdSlot: 'logistics', showOverflowTooltip: false },
        { label: '备注', prop: 'remark', minWidth: 300, numberToStingSort: 'remark', showOverflowTooltip: false },
        { label: '更新时间', prop: 'lastUpdatetime', minWidth: 180 },
        { label: '更新人', prop: 'lastUpdator', minWidth: 130 },
        { label: '创建时间', prop: 'createTime', minWidth: 180 },
        { label: '创建人', prop: 'creator', minWidth: 130 },
        { label: '状态', prop: 'statusText', minWidth: 130 },
        {
          label: '操作',
          fixed: 'right',
          width: 150,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],

      // 分页配置
      projectPaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 5, // 每页条数
        pageSizes: [5, 10, 20, 50, 100],
        style: { textAlign: 'left' },
      },
      projectSelectedItems: [],
      projectShow: false,
      // 选择
      projectHandleSelectionChange(arr) {
        state.projectSelectedItems = arr
      },
      dataForm: {
        id: '',
        studyId: store.state.studyItem.studyId,
        startTimeDate: '',
        startTimeTime: '',
        endTimeDate: '',
        endTimeTime: '',
        content: '',
        remark: '',
        statusShow: true,
        startTime: '',
        endTime: '',
      },
      rules: {
        startTimeDate: [{ required: true, message: '请选择', trigger: 'blur' }],
        startTimeTime: [{ required: true, message: '请选择', trigger: 'blur' }],
        endTimeDate: [{ required: true, message: '请选择', trigger: 'blur' }],
        endTimeTime: [{ required: true, message: '请选择', trigger: 'blur' }],
        content: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      // 请求函数
      async getprojectList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const res = await getOutOfServiceConfigList(store.state.studyItem.studyId, params)
          state.shuttleValue = []
          state.shuttleValueCopy = state.shuttleValue
          if (res.items?.length) {
            res.items.forEach(item => {
              item.statusText = item.status === 1 ? '启用' : '禁用'
              item.siteNum = item.sites?.length || ''
              item.startTime = parseTime(new Date(item.startTime), '{y}-{m}-{d} {h}:{i}:{s}')
              item.endTime = parseTime(new Date(item.endTime), '{y}-{m}-{d} {h}:{i}:{s}')
              item.lastUpdatetime = parseTime(new Date(item.lastUpdatetime), '{y}-{m}-{d} {h}:{i}:{s}')
              item.createTime = parseTime(new Date(item.createTime), '{y}-{m}-{d} {h}:{i}:{s}')
            })
          }
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: res.items,
            total: +res.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 新增-编辑
      projectEditItemForm: (row) => {
        // 为了重值
        state.dataForm = {
          id: '',
          studyId: store.state.studyItem.studyId,
          startTimeDate: '',
          startTimeTime: '',
          endTimeDate: '',
          endTimeTime: '',
          content: '',
          remark: '',
          statusShow: true,
          startTime: '',
          endTime: '',
        }
        state.projectShow = true
        state.shuttleValue = []
        if (row) {
          state.myTitle = '编辑'
          getOutOfServiceConfig(row.id).then(res => {
            if (res?.sites?.length) {
              res.sites.forEach(item => {
                state.shuttleValue.push(item.sitetId)
              })
            }
            state.shuttleValueCopy = state.shuttleValue
            if (res.status === 0) {
              res.statusShow = false
            } else if (res.status === 1) {
              res.statusShow = true
            }
            if (res?.startTime) {
              res.startTimeDate = parseTime(new Date(res.startTime), '{y}-{m}-{d}')
              res.startTimeTime = parseTime(new Date(res.startTime), '{h}:{i}')
            }
            if (res?.endTime) {
              res.endTimeDate = parseTime(new Date(res.endTime), '{y}-{m}-{d}')
              res.endTimeTime = parseTime(new Date(res.endTime), '{h}:{i}')
            }
            state.dataForm = res
          })
        } else {
          state.myTitle = '新建'
        }
      },
      // 删除
      projectDeleteItem: (row) => {
        ElMessageBox.confirm(
          '是否确认删除？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            deleteDeleteOutOfServiceConfig(row.id).then(() => {
              state.projectTableRef.refresh()
              ElMessage.success('删除成功')
            })
          })
          .catch(() => {})
      },
      applyCentre: () => {
        state.applyFlagShow = true
        state.projectShow = false
      },
      shuttleCancel: () => {
        state.shuttleValue = state.shuttleValueCopy
        state.applyFlagShow = false
        state.projectShow = true
      },
      shuttleConfirm: () => {
        state.shuttleValueCopy = state.shuttleValue
        state.applyFlagShow = false
        state.projectShow = true
      },
      projectShowCancel: () => {
        state.resetFrom()
        state.shuttleValue = []
      },
      // 重置表单
      resetFrom: () => {
        state.projectShow = false
      },
      projectShowConfirm: () => {
        if (!state.shuttleValue?.length) {
          ElMessage.warning('请选择适用中心')
          return
        }
        state.projectShowRef.validate((valid) => {
          if (valid) {
            state.dataForm.startTime = `${state.dataForm.startTimeDate} ${state.dataForm.startTimeTime}`
            state.dataForm.endTime = `${state.dataForm.endTimeDate} ${state.dataForm.endTimeTime}`
            if (new Date(state.dataForm.startTime).getTime() > new Date(state.dataForm.endTime).getTime()) {
              ElMessage.error('开始日期不可以晚于结束日期')
              return
            }
            if (state.dataForm.statusShow) {
              state.dataForm.status = 1
            } else {
              state.dataForm.status = 0
            }
            state.dataForm.sites = []
            state.shuttleValue.forEach(item => {
              state.dataForm.sites.push({
                sitetId: item
              })
            })
            state.loading = true
            putOutOfServiceConfig(state.dataForm).then(() => {
              state.projectTableRef.refresh()
              state.shuttleValue = []
              state.loading = false
              state.resetFrom()
              ElMessage.success('保存成功')
            }).catch(() => {
              state.loading = false
            })
          }
        })
      },
    })
    onMounted(async() => {
      const rest: any = await getCustomTasksTransfer(store.state.studyItem.studyId, {})
      if (rest?.customTaskSiteRuleList) {
        rest.customTaskSiteRuleList.forEach((item) => {
          rest.studySiteList.push(item)
        })
      }
      state.shuttleData = rest.studySiteList
      state.shuttleData.forEach((ite: any) => {
        ite.statusStrAndsiteName = `${ite.edcSiteCode}-(${ite.siteStatusStr})  ${ite.siteName}`
      })
    })
    return {
      ...toRefs(state),
    }
  }
})
</script>

<style lang="less">
  .tips {
    background-color: #fff;
    box-sizing: border-box;
  }
  .content-height {
    max-height: 300px;
    overflow-y: auto;
  }
  .content-height::-webkit-scrollbar {
    display: none;
  }
</style>

