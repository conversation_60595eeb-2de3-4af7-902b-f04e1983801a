<template>
  <div class="riskControl">
    <div class="flex justify-between items-center">
      <h3>资金风控</h3>
      <div>
        <el-button type="primary" @click="saveData">保存</el-button>
      </div>
    </div>
    <div class="risk">
      <div class="mb-5">受试者补偿/报销</div>
      <el-form
        ref="riskControlFormRef"
        :model="ruleForm"
        :rules="rules"
      >
        <div class="flex">
          <el-form-item class="mr-5">
            <el-checkbox-group v-model="ruleForm.checkboxnode">
              <el-checkbox :label="1">累计申请金额上限</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="上限" prop="accumulatedApplyAmountUpperLimit">
            <el-input
              v-model.trim="ruleForm.accumulatedApplyAmountUpperLimit"
              class="w-full"
              placeholder="请输入"
              maxlength="12"
              :disabled="ruleForm.checkboxnode?.length === 0"
              oninput="value=value.replace(/[^\d.]/g, '')
                  .replace(/^\./, '')
                  .replace(/(\.[^\.]*)\./g, '$1')
                  .replace(/^0+(\d)/, '$1')
                  .replace(/^(\d+\.\d\d).*$/, '$1')"
              @change="changeInput"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script lang='ts'>
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { getFundControl, postFundControl } from '@/api/riskControlManagement'

export default defineComponent({
  name: 'RiskControlManagement', // 资金风控
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      riskControlFormRef: null,
      ruleForm: {
        checkboxnode: [],
        ruleForm: ''
      },
      rules: {
        accumulatedApplyAmountUpperLimit: [
          { required: true, message: '请输入', trigger: 'blur' },
        ]
      },
      saveData: () => {
        const data = {
          accumulatedApplyAmountUpperLimit: state.ruleForm?.accumulatedApplyAmountUpperLimit
        }
        if (state.ruleForm.checkboxnode?.length) {
          data.enableAccumulatedApplyAmountUpperLimit = true
          state.riskControlFormRef.validate((valid) => {
            if (valid) {
              postFundControl(studyId, data).then(() => {
                ElMessage.success('保存成功')
                state.onLoad()
              })
            }
          })
        } else {
          data.enableAccumulatedApplyAmountUpperLimit = false
          postFundControl(studyId, data).then(() => {
            ElMessage.success('保存成功')
            state.onLoad()
          })
        }
      },
      changeInput: (value) => {
        if (value.indexOf('.') !== -1 && !value.split('.')[1]?.length) {
          state.ruleForm.accumulatedApplyAmountUpperLimit = value.split('.')[0]
        } else {
          state.ruleForm.accumulatedApplyAmountUpperLimit = value
        }
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        getFundControl(studyId).then((res) => {
          if (res?.enableAccumulatedApplyAmountUpperLimit) {
            state.ruleForm.checkboxnode = [1]
          }
          state.ruleForm.accumulatedApplyAmountUpperLimit = res?.accumulatedApplyAmountUpperLimit
        })
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.riskControl {
  background: #fff;
  padding: 20px;
}
.risk {
  padding: 0 20px;
}
</style>
