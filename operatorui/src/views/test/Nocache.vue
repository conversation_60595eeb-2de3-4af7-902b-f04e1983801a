<template>
  <div class="nocache-container">
    <!-- 抽屉 -->
    <el-drawer v-model="drawer" title="抽屉">
      <!-- 折叠面板 -->
      <div class="drawer-modules">
        <el-collapse v-model="activeName" accordion>
          <el-collapse-item title="一致性 Consistency" name="1">
            <div>
              与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；
            </div>
            <div>
              在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。
            </div>
          </el-collapse-item>
          <el-collapse-item title="反馈 Feedback" name="2">
            <div>
              控制反馈：通过界面样式和交互动效让用户可以清晰的感知自己的操作；
            </div>
            <div>页面反馈：操作后，通过页面元素的变化清晰地展现当前状态。</div>
          </el-collapse-item>
          <el-collapse-item title="效率 Efficiency" name="3">
            <div>简化流程：设计简洁直观的操作流程；</div>
            <div>
              清晰明确：语言表达清晰且表意明确，让用户快速理解进而作出决策；
            </div>
            <div>
              帮助用户识别：界面简单直白，让用户快速识别而非回忆，减少用户记忆负担。
            </div>
          </el-collapse-item>
          <el-collapse-item title="可控 Controllability" name="4">
            <div>
              用户决策：根据场景可给予用户操作建议或安全提示，但不能代替用户进行决策；
            </div>
            <div>
              结果可控：用户可以自由的进行操作，包括撤销、回退和终止当前操作等。
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-drawer>

    <!-- 穿梭框 -->
    <div style="width: 100%; margin: 20px 0">
      <el-transfer
        v-model="transferValue"
        filterable
        :titles="['所有城市', '选择的城市']"
        :filter-method="filterMethod"
        filter-placeholder="请输入城市名称"
        :data="transferAllData"
        @change="handleChange"
      />
    </div>

    <el-button style="margin-left: 16px" type="primary" @click="drawer = true">
      打开右侧抽屉
    </el-button>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  /* 有以下两种方式设置页面不缓存:
当前页面不设置name属性,该name必须跟路由配置的name一致，不一致或者不设置name则不缓存
或者路由配置的meta增加noCache: true
*/
  name: 'TestNoCache',
  setup() {
    // 调用得到完整数据的数组
    const generateData = () => {
      const data = []
      const cities = ['上海', '北京', '广州', '深圳', '南京', '西安', '成都']
      const spell = [
        'shanghai',
        'beijing',
        'guangzhou',
        'shenzhen',
        'nanjing',
        'xian',
        'chengdu',
      ]
      cities.forEach((city, index) => {
        data.push({
          label: city,
          key: index,
          spell: spell[index], // spell[index]
        })
      })
      return data
    }

    const state = reactive({
      drawer: false,
      activeName: '1',
      transferAllData: generateData(), // 左侧全部数据
      transferValue: [], // 穿梭后的数据

      filterMethod: (query, item) => {
        // 搜索方式
        return item.label.indexOf(query) > -1
      },

      handleChange: (value, direction, movedKeys) => {
        console.log(value, direction, movedKeys)
        console.log(state.transferValue) // 右穿梭框的值 (数组:索引)
      },
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.nocache-container {
  width: 100%;
  overflow: hidden;
  // 折叠面板
  .drawer-modules {
    padding: 0 20px;
    box-sizing: border-box;
  }
}
</style>
