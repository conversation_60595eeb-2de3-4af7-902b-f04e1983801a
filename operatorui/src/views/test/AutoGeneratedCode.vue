<template>
  <div>
    <div v-if="!showAutoGeneratedCodeModule" class="">
      <div>输入密码开启</div>
      <el-input v-model="password" type="password" placeholder="请输入密码" class="w-2/5 mr-3 mt-3" />
      <el-button type="primary" @click="savePasswordShowAutoGeneratedCodeModuleFun">
        开启
      </el-button>
      <el-button type="primary" @click="savePasswordShowAutoGeneratedCodeModuleFun('perpetual')">
        记住密码开启
      </el-button>
    </div>
    <div v-else-if="showAutoGeneratedCodeModule">
      <!-- 按钮 -->
      <div>
        <el-button type="" @click="setInitHtmlFun">
          一键生成初始化代码
        </el-button>
        <el-button type="" @click="setElFormHtmlFun(0)">
          一键生成EL-Form弹窗
        </el-button>
        <el-button type="" @click="setCustomDialogHtmlFun(0)">
          一键生成自定义-Form弹窗
        </el-button>
        <el-button
          type=""
          @click="setTableInitHtml(0)"
        >
          一键生成初始化Table
        </el-button>
        <div class="my-[20px] flex items-center">
          <el-input v-model="variableElForm" placeholder="请输入" class="!w-1/4 mr-3" />
          <el-button type="primary" @click="setElFormHtmlFun(1)">
            一键换名生成EL-Form弹窗
          </el-button>
          <el-button
            type="success"
            @click="setElFormHtmlFun(2)"
          >
            一键换名下载EL-Form弹窗的.vue文件
          </el-button>
        </div>

        <div class="flex items-center">
          <el-input v-model.trim="variableCustomDialog" placeholder="请输入" class="!w-1/4 mr-3" />
          <el-button type="primary" @click="setCustomDialogHtmlFun(1)">
            一键换名生成自定义-Form弹窗
          </el-button>
          <el-button
            type="success"
            @click="setCustomDialogHtmlFun(2)"
          >
            一键换名下载自定义-Form弹窗的.vue文件
          </el-button>
        </div>
        <div class="my-[20px] flex items-center">
          <el-input v-model.trim="variableTableInit" placeholder="请输入" class="!w-1/4 mr-3" />
          <el-button
            type="primary"
            @click="setTableInitHtml(1)"
          >
            一键换名生成初始化Table
          </el-button>
          <el-button
            type="success"
            @click="setTableInitHtml(2)"
          >
            一键换名下载初始化Table的.vue文件
          </el-button>
        </div>
      </div>
      <!-- 内容区 -->
      <div class="mt-3">
        <h3 class="mb-[10px]">生成的内容区：</h3>
        <el-input v-model="commonHtml" type="textarea" autosize />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, } from 'vue'
// import { useStore } from 'vuex'
// import Form from '@/views/test/ceshi/init.vue'

export default defineComponent({
  name: 'AutoGeneratedCode', //
  // components: {
  //   Form
  // },
  setup() {
    // const store = useStore()
    const passwordArr = ['yj', 'tao', 'yinjun', 'wanghongtao', 'wht', 'jun']
    const state = reactive({
      initHtml: `<template>
  <div class="">
    初始模板
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
} from 'vue'
// import { useStore } from 'vuex'

export default defineComponent({
  name: '', //
  setup() {
    // const store = useStore()
    const state = reactive({
      onLoad: () => {
        //
      },
    })
    onMounted(() => {
    })
    return {
      ...toRefs(state)
    }
  }
})
<\/script>
<style lang="less" scoped></style>`,
      elFormHtml: `<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
  >
    <el-form ref="formRef" :model="myFormData" :rules="rules">
      <el-form-item
        label="名称"
        :label-width="formLabelWidth"
        prop="name"
      >
        <el-input
          v-model.trim="myFormData.name"
          autocomplete="off"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          @click="closeDialogVisible"
        >取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
} from 'vue'
// import { useStore } from 'vuex'
import { deepClone } from '@/utils'

export default defineComponent({
  name: 'Form',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    },
    formData: {
      type: Object,
      // default: () => {}
    },
  },
  setup(props) {
    // const store = useStore()
    /* 父组件使用：
      <Form
        ref="myFormRef"
        :form-data="formData"
      />
      import Form from '@/views/test/ceshi/init.vue'
          components: {
        Form
      },
      myFormRef: null,
      formData: { name: '1' },
      // 打开form弹窗
      openFormFun: () => {
        state.myFormRef.dialogVisible = true
      },
    */

    const state = reactive({
      myFormData: {
        name: '',
      },
      title: '编辑',
      formRef: null,
      dialogVisible: false,
      formLabelWidth: '86px',
      rules: {
        name: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      },
      loading: false,
      closeDialogVisible: () => {
        state.dialogVisible = false
      },

      submit: () => {
        state.formRef.validate((valid) => {
          if (valid) {
            state.loading = true
            props.request()
            state.loading = false
            state.closeDialogVisible()
          }
        })
      },

    })

    onMounted(() => {
      if (props?.formData) {
        state.myFormData = deepClone(props.formData)
      }
    })

    return {
      ...toRefs(state),
    }
  }
})
<\/script>
`,
      customDialogHtml: `<template>
        <trial-dialog v-model="dialogVisible" :title="title" :title-class="flex justify-center my-1">
            <template #DialogBody>
            <el-form ref="formRef" :model="myFormData" :rules="rules">
                <el-form-item
                label="名称"
                :label-width="formLabelWidth"
                prop="name"
                >
                <el-input
                  v-model.trim="myFormData.name"
                  autocomplete="off"
                />
                </el-form-item>
            </el-form>
            </template>
            <template #footer>
            <div class="flex justify-end">
                <el-button plain @click="closeDialogVisible">取消</el-button>
                <el-button
                :loading="loading"
                type="primary"
                @click="submit"
                >保存</el-button>
            </div>
            </template>
        </trial-dialog>
        </template>

        <script lang="ts">
        import { defineComponent, reactive, toRefs, onMounted } from 'vue'
        // import { useStore } from 'vuex'
        import { deepClone } from '@/utils'

        export default defineComponent({
        name: 'Form',
        props: {
            // 请求数据的方法
            request: {
            type: Function,
            default: () => {},
            },
            formData: {
            type: Object,
            // default: () => {}
            },
        },
        setup(props) {
            // const store = useStore()
            /* 父组件使用：
            <Form
                ref="myFormRef"
                :form-data="formData"
            />
            import Form from '@/views/test/ceshi/init.vue'
                components: {
                Form
            },
            myFormRef: null,
            formData: { name: '1' },
            // 打开form弹窗
            openFormFun: () => {
                state.myFormRef.dialogVisible = true
            },
            */

            const state = reactive({
            myFormData: {
              name: '',
            },
            title: '编辑',
            formRef: null,
            dialogVisible: false,
            formLabelWidth: '86px',
            rules: {
              name: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            loading: false,
            closeDialogVisible: () => {
              state.dialogVisible = false
            },

            submit: () => {
              state.formRef.validate((valid) => {
                if (valid) {
                  state.loading = true
                  props.request()
                  state.loading = false
                  state.closeDialogVisible()
                }
              })
            },
            })
            onMounted(() => {
              if (props?.formData) {
                state.myFormData = deepClone(props.formData)
              }
            })
            return {
            ...toRefs(state),
            }
        },
        })
        <\/script>
        `,
      tableInitHtml: `<template>
  <div>
    <trial-table
      ref="TableRef"
      title="列表"
      :request="getList"
      :columns="columns"
      :search="searchConfig"
      :pagination="paginationConfig"
      @selectionChange="handleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <el-button type="primary" @click="editItemForm(null)">
          添加
        </el-button>
      </template>
      <template #operate="scope">
        <el-button size="small" type="primary" @click="editItemForm(scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="deleteItem(scope.row)"
        >删除</el-button>
      </template>
    </trial-table>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'Table',
  setup() {
    const state = reactive({
      TableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        { type: 'selection' },
        { label: '名称', prop: 'name', minWidth: 180 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            label: '名称',
            name: 'name',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 10, // 每页条数
        pageSizes: [10, 20, 30, 40, 50, 100],
        style: { textAlign: 'left' },
      },
      selectedItems: [],
      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      // 请求函数
      async getList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const list = [
            {
              id: 1,
              name: '超级管理员',
            },
            {
              id: 2,
              name: '测试人员',
            },
            {
              id: 3,
              name: '数据管理员',
            }
          ]
          const { data } = await new Promise((rs) => {
            setTimeout(() => {
              rs({
                code: 200,
                data: {
                  list,
                  total: list.length,
                },
              })
            }, 1000)
          })
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data.list,
            total: +data.total,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      refresh: () => {
        state.TableRef.refresh()
      },
      // 新增-编辑
      editItemForm: (row) => {
        //
      },
      // 删除
      deleteItem: (row) => {
        ElMessageBox.confirm(
          '您将要删除'+row.name+', 是否继续?',
          '删除提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            ElMessage.success('删除成功')
          })
          .catch(() => {})
      }
    })
    return {
      ...toRefs(state),
    }
  }
})
<\/script>
`,
      variableElForm: '', // 替换EL-Form弹窗变量命
      variableCustomDialog: '', // 替换CustomDialog弹窗变量命
      variableTableInit: '', // 替换Table变量命
      // 其它字段
      showAutoGeneratedCodeModule: false,
      // 通用赋值
      commonHtml: ``,
      password: '',
      // 永久-开启生成模块
      savePasswordShowAutoGeneratedCodeModuleFun: (flag) => {
        if (state.password && passwordArr.indexOf(state.password) !== -1) {
          state.showAutoGeneratedCodeModule = true
          if (flag === 'perpetual') {
            localStorage.setItem('yjAutoPassword', state.password)
          }
        } else {
          ElMessage.error('密码错误')
        }
      },
      // 启动初始化代码
      setInitHtmlFun: () => {
        state.commonHtml = state.initHtml
      },
      // el的dialog+form弹窗
      setElFormHtmlFun: (flag) => {
        if (flag && state.variableElForm) {
          state.commonHtml = `<template>
    <el-dialog
      v-model="${state.variableElForm}DialogVisible"
      :title="${state.variableElForm}Title"
    >
      <el-form ref="${state.variableElForm}FormRef" :model="${state.variableElForm}FormData" :rules="${state.variableElForm}Rules">
        <el-form-item
          label="名称"
          :label-width="${state.variableElForm}FormLabelWidth"
          prop="name"
        >
          <el-input
            v-model.trim="${state.variableElForm}FormData.name"
            autocomplete="off"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="closeDialogVisible"
          >取 消</el-button>
          <el-button type="primary" :loading="${state.variableElForm}Loading" @click="${state.variableElForm}Submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </template>

  <script lang="ts">
  import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
  } from 'vue'
  // import { useStore } from 'vuex'

  export default defineComponent({
    name: '${state.variableElForm}Form',
    props: {
      // 请求数据的方法
      request: {
        type: Function,
        default: () => {}
      },
    },
    setup(props) {
      // const store = useStore()
      /* 父组件使用：
        <Form
          ref="myFormRef"
          :form-data="formData"
        />
        import Form from '@/views/test/ceshi/init.vue'
            components: {
          Form
        },
        myFormRef: null,
        // 打开form弹窗
        openFormFun: (row) => {
          state.myFormRef.${state.variableElForm}DialogVisible = true
          state.myFormRef.${state.variableElForm}FormData = row
        },
      */

      const state = reactive({
        ${state.variableElForm}FormData: {
          name: '',
        },
        ${state.variableElForm}Title: '编辑',
        ${state.variableElForm}FormRef: null,
        ${state.variableElForm}DialogVisible: false,
        ${state.variableElForm}FormLabelWidth: '86px',
        ${state.variableElForm}Rules: {
          name: [
            { required: true, message: '请输入', trigger: 'blur' },
          ],
        },
        ${state.variableElForm}Loading: false,
        closeDialogVisible: () => {
          state.${state.variableElForm}DialogVisible = false
        },
        ${state.variableElForm}Submit: () => {
          state.${state.variableElForm}FormRef.validate((valid) => {
            if (valid) {
              state.${state.variableElForm}Loading = true
              props.request()
              state.${state.variableElForm}Loading = false
              state.closeDialogVisible()
            }
          })
        },

      })

      onMounted(() => {
        //
      })

      return {
        ...toRefs(state),
      }
    }
  })
  <\/script>
  `
          // 下载
          if (flag === 2) {
            state.downloadFun(state.variableElForm, state.commonHtml)
          }
          return
        }
        state.commonHtml = state.elFormHtml
      },
      // 通用下载函数
      downloadFun: (name, commonHtml) => {
        // 转换为Blob对象
        const blob1 = new Blob([commonHtml], { type: 'text/plain;charset=utf-8' })
        // 创建下载链接
        const downloadUrl1 = URL.createObjectURL(blob1)
        // 触发下载
        const link1 = document.createElement('a')
        link1.href = downloadUrl1
        // 设置下载文件的名称
        link1.download = `${name}.vue`
        document.body.appendChild(link1)
        link1.click()
        document.body.removeChild(link1)
      },
      // 自定义的dialog+form弹窗
      setCustomDialogHtmlFun: (flag) => {
        if (flag && state.variableCustomDialog) {
          state.commonHtml = `<template>
        <trial-dialog v-model="${state.variableCustomDialog}DialogVisible" :title="${state.variableCustomDialog}Title" :title-class="flex justify-center my-1">
            <template #DialogBody>
            <el-form ref="${state.variableCustomDialog}FormRef" :model="${state.variableCustomDialog}FormData" :rules="${state.variableCustomDialog}Rules">
                <el-form-item
                label="名称"
                :label-width="${state.variableCustomDialog}FormLabelWidth"
                prop="name"
                >
                <el-input
                  v-model.trim="${state.variableCustomDialog}FormData.name"
                  autocomplete="off"
                />
                </el-form-item>
            </el-form>
            </template>
            <template #footer>
            <div class="flex justify-end">
                <el-button plain @click="closeDialogVisible">取消</el-button>
                <el-button
                :loading="${state.variableCustomDialog}Loading"
                type="primary"
                @click="${state.variableCustomDialog}Submit"
                >保存</el-button>
            </div>
            </template>
        </trial-dialog>
        </template>

        <script lang="ts">
        import { defineComponent, reactive, toRefs, onMounted } from 'vue'
        // import { useStore } from 'vuex'

        export default defineComponent({
        name: '${state.variableCustomDialog}Form',
        props: {
            // 请求数据的方法
            request: {
            type: Function,
            default: () => {},
            },
        },
        setup(props) {
            // const store = useStore()
            /* 父组件使用：
            <Form
                ref="myFormRef"
            />
            import Form from '@/views/test/ceshi/init.vue'
                components: {
                Form
            },
            myFormRef: null,
            // 打开form弹窗
            openFormFun: (row) => {
              state.${state.variableCustomDialog}FormRef.${state.variableCustomDialog}DialogVisible = true
              state.myFormRef.${state.variableCustomDialog}FormData = row
            },
            */

            const state = reactive({
            ${state.variableCustomDialog}FormData: {
              name: '',
            },
            ${state.variableCustomDialog}Title: '编辑',
            ${state.variableCustomDialog}FormRef: null,
            ${state.variableCustomDialog}DialogVisible: false,
            ${state.variableCustomDialog}FormLabelWidth: '86px',
            ${state.variableCustomDialog}Rules: {
              name: [{ required: true, message: '请输入', trigger: 'blur' }],
            },
            ${state.variableCustomDialog}Loading: false,
            closeDialogVisible: () => {
              state.${state.variableCustomDialog}DialogVisible = false
            },
            ${state.variableCustomDialog}Submit: () => {
              state.${state.variableCustomDialog}FormRef.validate((valid) => {
                if (valid) {
                  state.${state.variableCustomDialog}Loading = true
                  props.request()
                  state.${state.variableCustomDialog}Loading = false
                  state.closeDialogVisible()
                }
              })
            },
            })
            onMounted(() => {
              //
            })
            return {
            ...toRefs(state),
            }
        },
        })
        <\/script>
        `
          // 下载
          if (flag === 2) {
            state.downloadFun(state.variableCustomDialog, state.commonHtml)
          }
          return
        }
        state.commonHtml = state.customDialogHtml
      },
      // table的代码
      setTableInitHtml: (flag) => {
        if (flag && state.variableTableInit) {
          state.commonHtml = `<template>
  <div>
    <trial-table
      ref="${state.variableTableInit}TableRef"
      title="列表"
      :request="get${state.variableTableInit}List"
      :columns="${state.variableTableInit}Columns"
      :search="${state.variableTableInit}SearchConfig"
      :pagination="${state.variableTableInit}PaginationConfig"
      @selectionChange="${state.variableTableInit}HandleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <el-button type="primary" @click="${state.variableTableInit}EditItemForm(null)">
          添加
        </el-button>
      </template>
      <template #operate="scope">
        <el-button size="small" type="primary" @click="${state.variableTableInit}EditItemForm(scope.row)">
          编辑
        </el-button>
        <el-button
          size="small"
          type="danger"
          @click="${state.variableTableInit}DeleteItem(scope.row)"
        >删除</el-button>
      </template>
    </trial-table>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
  name: '${state.variableTableInit}Table',
  setup() {
    const state = reactive({
      ${state.variableTableInit}TableRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      ${state.variableTableInit}Columns: [
        { type: 'selection' },
        { label: '名称', prop: 'name', minWidth: 180 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      ${state.variableTableInit}SearchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            label: '名称',
            name: 'name',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      ${state.variableTableInit}PaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 10, // 每页条数
        pageSizes: [10, 20, 30, 40, 50, 100],
        style: { textAlign: 'left' },
      },
      ${state.variableTableInit}SelectedItems: [],
      // 选择
      ${state.variableTableInit}HandleSelectionChange(arr) {
        state.${state.variableTableInit}SelectedItems = arr
      },
      // 请求函数
      async get${state.variableTableInit}List(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const list = [
            {
              id: 1,
              name: '超级管理员',
            },
            {
              id: 2,
              name: '测试人员',
            },
            {
              id: 3,
              name: '数据管理员',
            }
          ]
          const { data } = await new Promise((rs) => {
            setTimeout(() => {
              rs({
                code: 200,
                data: {
                  list,
                  total: list.length,
                },
              })
            }, 1000)
          })
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data.list,
            total: +data.total,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      ${state.variableTableInit}Refresh: () => {
        state.${state.variableTableInit}TableRef.refresh()
      },
      // 新增-编辑
      ${state.variableTableInit}EditItemForm: (row) => {
        //
      },
      // 删除
      ${state.variableTableInit}DeleteItem: (row) => {
        ElMessageBox.confirm(
          '您将要删除'+row.name+', 是否继续?',
          '删除提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            ElMessage.success('删除成功')
          })
          .catch(() => {})
      }
    })
    return {
      ...toRefs(state),
    }
  }
})
<\/script>
`
          // 下载
          if (flag === 2) {
            state.downloadFun(state.variableTableInit, state.commonHtml)
          }
          return
        }
        state.commonHtml = state.tableInitHtml
      },
      // 初次加载时
      onLoad: () => {
        const yjAutoPassword = localStorage.getItem('yjAutoPassword')
        if (passwordArr.indexOf(yjAutoPassword) !== -1) {
          state.showAutoGeneratedCodeModule = true
        }
      },
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
</style>
