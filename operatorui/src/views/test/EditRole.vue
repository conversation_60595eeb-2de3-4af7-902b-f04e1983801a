<template>
  <trial-dialog
    v-model="ROLE_FORM_DATAS.dialogVisible"
    :title="ROLE_FORM_DATAS.title"
    :my-dialog-body-style="{
      maxHeight: 'calc(100vh - 320px)',
      overflow: 'auto'
    }"
    close-on-click-modal
    show-close
  >
    <template #DialogBody>
      <el-form ref="roleFormRef" :model="roleForm" :rules="rules">
        <!-- 角色分类 -->
        <el-form-item
          label="角色名称"
          :label-width="formLabelWidth"
          prop="roleName"
        >
          <el-input
            v-model.trim="roleForm.roleName"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          label="角色说明"
          :label-width="formLabelWidth"
          prop="description"
        >
          <el-input
            v-model.trim="roleForm.description"
            type="textarea"
            autocomplete="off"
          />
        </el-form-item>
        <!-- check-strictly 断开和父级关联-->
        <el-form-item
          label="权限选择"
          :label-width="formLabelWidth"
          prop="permissionIds"
        >
          <!-- :default-expand-all="true" -->
          <el-tree
            ref="roleTree"
            v-loading="roleOptionsLoadingFlag"
            :props="propsDatas"
            :data="roleOptions"
            :default-expanded-keys="roleForm.permissionIds"
            show-checkbox
            node-key="permissionId"
            @check-change="handleCheckChange"
          />
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <span class="flex justify-center">
        <el-button
          @click="ROLE_FORM_DATAS.dialogVisible = false"
        >取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit">确 定</el-button>
      </span>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  inject,
  onMounted,
  ref,
  nextTick,
} from 'vue'
// import { useStore } from 'vuex'
import {
  getRoleDetail,
  getRolePermissionTreeList,
  saveRoleAndRelationPermission,
  getPermissionTreeList
} from '@/api/test'

export default defineComponent({
  name: 'EditRole',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    }
  },
  setup(props) {
    // const store = useStore()
    const ROLE_FORM_DATAS: any = inject('ROLE_FORM_DATAS')
    const roleTree = ref<HTMLElement | null>(null)
    // 得到已经勾选的数组
    // const CheckedKeyArr = []
    // function checkes(oldArr) {
    //   if (Array.isArray(oldArr)) {
    //     oldArr.forEach((item) => {
    //       if (item.children) {
    //         checkes(item.children)
    //       }
    //       if (item.title) {
    //         CheckedKeyArr.push(item.title)
    //       }
    //     })
    //   }
    // }

    const state = reactive({
      roleOptionsLoadingFlag: false,
      // el-tree 默认展示框
      roleOptions: [
        // {
        //   id: 1,
        //   title: '权限管理',
        //   children: [
        //     {
        //       id: 10,
        //       title: '功能菜单',
        //       children: [
        //         {
        //           id: 19,
        //           title: '产品菜单',
        //         },
        //       ],
        //     },
        //   ],
        // },
      ],
      propsDatas: {
        // 勾选展示
        label: 'permissionName',
        children: 'children',
      },
      roleForm: {
        roleName: '',
      },
      roleFormRef: null,
      formLabelWidth: '86px',
      rules: {
        roleName: [
          { required: true, message: '请输入角色名', trigger: 'blur' },
        ],
      },
      loading: false,
      // 保存
      submit: () => {
        state.roleFormRef.validate((valid) => {
          if (valid) {
            // console.log(state.roleForm, roleTree.value.getCheckedKeys())
            // return
            state.loading = true
            saveRoleAndRelationPermission(
              {
                ...state.roleForm,
                permissionIds: roleTree.value.getCheckedKeys()
              }
            ).then(() => {
              ElMessage.success('保存成功')
              props.request()
              state.loading = false
              ROLE_FORM_DATAS.dialogVisible = false
            }).catch(() => {
              state.loading = false
            })
          }
        })
      },
      // 选中时的事件
      handleCheckChange: (data, checked, indeterminate) => {
        // console.log(roleTree.value.getCheckedKeys()) // 获取到选中的所有key(不带父级的)
        // console.log(data) // console.log(checked)console.log(indeterminate)
      },
      // 树形结构数据处理
      treeListSet: (arr) => {
        return arr.map((e) => {
          const item = {
            id: e.id || '',
            permissionName: e.permissionName || '',
            permissionId: e.permissionId || '',
            pPermissionId: e.pPermissionId || '',
            children: []
          }
          if (e?.childPermissions?.length) {
            item.children = state.treeListSet(e.childPermissions)
          }
          return item
        })
      },
    })

    onMounted(() => {
      state.roleOptionsLoadingFlag = true
      if (ROLE_FORM_DATAS.userFormData.id) {
        getRoleDetail(ROLE_FORM_DATAS.userFormData.id).then((res) => {
          state.roleForm = res
          if (ROLE_FORM_DATAS.userFormData.roleId) {
            getRolePermissionTreeList(
              ROLE_FORM_DATAS.userFormData.roleId
            ).then((rest: any) => {
              state.roleOptions = state.treeListSet(rest)
              // 设置已经勾选的->已有权限
              nextTick(() => {
                state.roleOptionsLoadingFlag = false
                // console.log(roleTree.value, 'roleTree.value', state.roleForm.permissionIds)
                roleTree.value.setCheckedKeys(state.roleForm.permissionIds)
              })
            })
          }
        }).catch(() => {
          state.roleOptionsLoadingFlag = false
        })
      } else {
        getPermissionTreeList().then((rest: any) => {
          state.roleOptions = state.treeListSet(rest)
          state.roleOptionsLoadingFlag = false
        }).catch(() => {
          state.roleOptionsLoadingFlag = false
        })
      }
      // if (store.state.menu.menus && ROLE_FORM_DATAS.userFormData.id) {
      // 设置已经勾选的->已有权限
      // nextTick(() => {
      //   checkes(
      //     store.state.menu.menus.slice(1, store.state.menu.menus.length)
      //   )
      //   roleTree.value.setCheckedKeys(CheckedKeyArr)
      // })
      // }
    })

    return {
      ...toRefs(state),
      ROLE_FORM_DATAS,
      roleTree
    }
  }
})
</script>

<style lang="less" scoped>
:deep(.el-tree__empty-text) {
  width: 300px;
  transform: translate(-20%, -50%);
}
</style>
