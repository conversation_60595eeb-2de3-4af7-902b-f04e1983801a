<template>
  <div>
    <trial-table
      ref="authTable"
      title="菜单列表"
      :request="getList"
      :columns="columns"
      :pagination="false"
      row-key="id"
      :tree-props="{ children: 'childPermissions', hasChildren: 'hasChildren' }"
      @selectionChange="handleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <!-- icon="el-icon-plus" -->
        <el-button type="primary" @click="editItem(null)">
          添加菜单
        </el-button>
        <!-- icon="el-icon-refresh"
          <el-button type="primary" @click="refresh">
            刷新
          </el-button> -->
      </template>
      <template #statusStr="scope">
        {{ scope.row.status === 1 ? '激活' : '失活' }}
      </template>
      <template #operate="scope">
        <el-button size="small" type="primary" @click="editItem(scope.row)">
          编辑
        </el-button>
        <!-- :disabled="scope.row.roleName === '超级管理员'" -->
        <el-button
          v-if="scope.row.status === 1"
          size="small"
          type="danger"
          @click="deleteItem(scope.row)"
        >失活</el-button>
        <el-button
          v-else
          size="small"
          type="primary"
          @click="deleteItem(scope.row)"
        >激活</el-button>
      </template>
    </trial-table>
    <!-- 编辑表单 -->
    <EditMenuForm
      v-if="permissionFormDatas.dialogVisible"
      :request="refresh"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, provide } from 'vue'
import { getPermissionTreeList, setPermissionStatus } from '@/api/test'
// import { useStore } from 'vuex'
import EditMenuForm from '@/views/test/EditMenu.vue'

export default defineComponent({
  name: 'MenuMnagement', // 菜单管理页面
  components: {
    EditMenuForm,
  },
  setup() {
    // const store = useStore()
    // const { studyId } = store.state.studyItem
    const permissionFormDatas: any = reactive({
      userFormData: {},
      dialogVisible: false,
      permissionOptions: [],
    })
    const state = reactive({
      authTable: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' },
        { label: '菜单名称', prop: 'permissionName', minWidth: 180 },
        { label: '菜单路由地址', prop: 'menuUrl', minWidth: 180 },
        { label: '状态', tdSlot: 'statusStr', minWidth: 120 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            label: '角色名称',
            name: 'roleName',
            defaultValue: '',
          },
          // {
          //   type: 'text',
          //   label: '角色类型',
          //   name: 'roleName',
          //   defaultValue: '',
          // },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 10, // 每页条数
        pageSizes: [10, 20, 50],
        style: { textAlign: 'left' },
      },
      selectedItems: [],
      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      // 请求函数
      async getList() {
        try {
          const data: any = await getPermissionTreeList()
          permissionFormDatas.permissionOptions = data
          return {
            data: data,
            total: data.length,
          }
        } catch (e) {
          console.log(e)
          // 当捕获到异常时，返回默认值，确保所有代码路径都有返回值
          return {
            data: [],
            total: 0,
          }
        }
      },
      // 刷新
      refresh: () => {
        state.authTable.refresh()
      },
      // 新增-编辑
      editItem: (row) => {
        permissionFormDatas.userFormData = { ...row }
        permissionFormDatas.dialogVisible = true
        permissionFormDatas.title = row ? '编辑' : '新增'
      },
      // 激活 失活
      deleteItem: (row) => {
        ElMessageBox.confirm(
          `您将${row.status === 1 ? '失' : '激'}活${
            row.permissionName
          }, 是否继续?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: row.status === 1 ? 'warning' : '',
          }
        )
          .then(() => {
            setPermissionStatus(row.id, {
              status: row.status === 1 ? 0 : 1,
            }).then(() => {
              state.refresh()
              ElMessage.success(
                `${row.permissionName}${row.status === 1 ? '失' : '激'}活设置成功`
              )
            })
          })
          .catch(() => {})
      },
    })

    // 传值
    provide('permissionFormDatas', permissionFormDatas)

    return {
      ...toRefs(state),
      permissionFormDatas,
    }
  },
})
</script>
