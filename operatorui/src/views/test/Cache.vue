<template>
  <div class="cache-container">
    <div>
      <div class="home">
        <h3 class="title">
          创达医药科技(上海)有限公司现有项目<span>（专注于临床研究领域给您提供真实世界研究的创新解决方案,药品器械的安全性检测研究。）</span>
        </h3>
        <div class="Content">
          <a
            class="ContentBox"
            href="https://danonetest.trialdata.cn/Account/Login"
            target="_blank"
          ><div>麒麟的数据查看和维护系统（test）</div> </a>
          <a
            class="ContentBox"
            href="https://ncctest.trialdata.cn/Home/Login"
            target="_blank"
          ><div>CTMS北京测试环境</div> </a>
          <a
            class="ContentBox"
            href="https://ncchtest.trialdata.cn/Home/Login"
            target="_blank"
          ><div>CTMS河北测试环境</div> </a>
          <a
            class="ContentBox"
            href="https://mdtest.trialdata.cn/login"
            target="_blank"
          ><div>科研病历平台测试环境</div> </a>
          <a
            class="ContentBox"
            href="https://oldstory.edc.trialdata.cn/"
            target="_blank"
          ><div>EDC项目(old)</div> </a>
          <a
            class="ContentBox"
            href="https://stageserver.edc.trialdata.cn/Member/Login?backurl=%2fStudy%2fPatient%2fIndex"
            target="_blank"
          ><div>EDC项目(stage)</div> </a>
          <a
            class="ContentBox"
            href="https://dctest.trialdata.cn/operatorui#/login"
            target="_blank"
          ><div>EDCT后台管理（test）</div>
            <div>
              DCT模式具备改善受试者可及性和便利性、提高临床研究质量与效率、优化医疗资源配置、降低研究成本等特点。
            </div></a>
          <a
            class="ContentBox"
            href="https://dctest.trialdata.cn/BackDoor/Account/Login"
            target="_blank"
          ><div>EDCT老后台（test）</div> </a>
          <a
            class="ContentBox"
            href="https://dctest.trialdata.cn/doctorui/?code=0817fJkl2k2jO94jyFol22sLQv07fJkh&amp;state=123#/login"
            target="_blank"
          ><div>EDCT研究者端（test）</div> </a>
          <a
            class="ContentBox"
            href="https://dctest.trialdata.cn/patientui/?code=051JuA0w3KIx9Z2K5c3w3UVPfE1JuA0Y&amp;state=123#/home"
            target="_blank"
          ><div>EDCT受试者端</div> </a>
          <a class="ContentBox" href="http://www.trialdata.cn/" target="_blank"><div>创达官网</div> </a>
          <a
            class="ContentBox"
            href="http://mgrwebtest.trialdata.cn/#/patient/patientQuestion"
            target="_blank"
          ><div>患者招募项目后台管理系统（test）</div> </a>
          <a
            class="ContentBox"
            href="https://microporttest.trialdata.cn/#/login"
            target="_blank"
          ><div>微创神通-中国急性缺血性脑卒中机械取栓数据库（test）</div> </a>
          <a
            class="ContentBox"
            href="https://mtuitest.trialdata.cn/#/login"
            target="_blank"
          ><div>分子分型小工具后台（test）</div>
          </a>
        </div>
      </div>
    </div>
    <!-- 取色器 -->
    <div class="color-picker-modules">
      更换背景色 :&nbsp;&nbsp;&nbsp;&nbsp;
      <el-color-picker
        v-model="colorPickerModel"
        show-alpha
        :predefine="predefineColors"
        @change="colorPickerChange"
      />
    </div>

    <!-- 透明度 -->
    <div class="opcity-bg">
      <span>调节背景透明度 :</span>
      <el-slider v-model="opcityValue" :format-tooltip="formatTooltip" />
    </div>

    <!-- 评分 -->
    <div class="score-modules">
      <div class="score-title">评分 :</div>
      <el-rate v-model="score" :colors="rateColors" allow-half />
      <p>{{ score }}分</p>
    </div>
    <!-- 暂无数据 -->
    <!-- <el-empty :image-size="200"></el-empty> -->
    <!-- 上传多文件 -->
    <div class="yy-upload">
      <YyUpload :upload-file-falg="true" />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onActivated,
  reactive,
  toRefs,
  nextTick,
  onDeactivated,
} from 'vue'
import YyUpload from '@/components/Upload/index.vue'
import { throttle } from '@/utils/index'
// import { useStore } from 'vuex'
export default defineComponent({
  name: 'TestCache', // 可缓存页面
  components: {
    YyUpload,
  },
  setup() {
    // const store = useStore()
    const state = reactive({
      colorPickerModel: 'rgba(158, 234, 235, 1)',
      score: null,
      opcityValue: 100,
      rateColors: ['#99A9BF', '#F7BA2A', '#FF9900'],
      predefineColors: [
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgba(255, 69, 0, 0.68)',
        'rgb(255, 120, 0)',
        'hsv(51, 100, 98)',
        'hsva(120, 40, 94, 0.5)',
        'hsl(181, 100%, 37%)',
        'hsla(209, 100%, 56%, 0.73)',
        '#c7158577',
      ],

      colorPickerChange: (color) => {
        const main = document.getElementsByClassName('main')[0]
        // let top = document.getElementsByClassName('top')[0]
        // let tagsContainer = document.getElementsByClassName('tags-container')[0] //tags-item
        main.style.background = color
      },

      formatTooltip: (val) => {
        throttle(() => {
          nextTick(() => {
            const main = document.getElementsByClassName('main')[0]
            main.style.opacity = val / 100
          })
          return val // -->   / 100;
        }, 300)()
      },
    })

    onActivated(() => {
      console.log('onActivated,keep-alive 缓存的组件激活时调用')
    })

    onDeactivated(() => {
      // console.log(state.opcityValue);
      console.log('onDeactivated,keep-alive 缓存的组件停用时调用')
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.cache-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  /*取色器*/
  .color-picker-modules {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  /*透明度 条*/
  .opcity-bg {
    width: 50%;
    margin: 20px;
  }
  /*评分*/
  .score-modules {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ff9900;
    margin: 50px 0 0 0;
    .score-title {
      color: #999;
      font-weight: 700;
      margin-right: 20px;
    }
    p {
      min-width: 40px;
      margin-left: 10px;
    }
  }

  .yy-upload {
    margin: 50px 0 0 0;
  }
}
</style>
