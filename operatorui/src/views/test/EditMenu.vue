<template>
  <trial-dialog
    v-model="permissionFormDatas.dialogVisible"
    :title="permissionFormDatas.title"
    show-close
  >
    <template #DialogBody>
      <el-form ref="permissionFormRef" :model="permissionForm" :rules="rules">
        <el-form-item
          label="类型"
          :label-width="formLabelWidth"
          prop="isSysPermission"
          class="!mb-0"
        >
          <el-radio-group v-model="permissionForm.isSysPermission">
            <el-radio :value="1" size="large">系统</el-radio>
            <el-radio :value="0" size="large">研究</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="菜单名称"
          :label-width="formLabelWidth"
          prop="permissionName"
        >
          <el-input
            v-model.trim="permissionForm.permissionName"
            autocomplete="off"
            max-length="999"
          />
        </el-form-item>
        <el-form-item
          label="Code"
          :label-width="formLabelWidth"
          prop="permissionId"
        >
          <el-input
            v-model.trim="permissionForm.permissionId"
            :disabled="permissionFormDatas.title === '编辑'"
            autocomplete="off"
            max-length="999"
          />
        </el-form-item>
        <el-form-item label="URL" :label-width="formLabelWidth" prop="menuUrl">
          <el-input v-model.trim="permissionForm.menuUrl" max-length="999" />
        </el-form-item>
        <!-- 父菜单 check-strictly 断开和父级关联-->
        <el-form-item
          label="父菜单"
          :label-width="formLabelWidth"
          prop="pPermissionId"
        >
          <el-tree-select
            v-if="permissionOptions?.length"
            v-model="permissionForm.pPermissionId"
            :data="permissionOptions"
            check-strictly
            :render-after-expand="false"
            placeholder=""
            clearable
            style="width: 240px"
            :props="{
              disabled: (data) => {
                if (data.id === permissionForm.id) {
                  setDisabledRecursive(data)
                }
                // data.id === currentId || data.pPermissionId === permissionForm.permissionId
                return data.disabled
              }
            }"
          />
        </el-form-item>
        <el-form-item
          label="导航菜单"
          :label-width="formLabelWidth"
          prop="isNavMenu"
          class="!mb-0"
        >
          <el-radio-group v-model="permissionForm.isNavMenu">
            <el-radio :value="1" size="large">是</el-radio>
            <el-radio :value="0" size="large">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="排序"
          :label-width="formLabelWidth"
          prop="orderSeq"
        >
          <el-input
            v-model.trim="permissionForm.orderSeq"
            autocomplete="off"
            max-length="999"
          />
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="remark">
          <el-input
            v-model.trim="permissionForm.remark"
            type="textarea"
            max-length="3999"
            autocomplete="off"
          />
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <span class="flex justify-center">
        <el-button
          @click="permissionFormDatas.dialogVisible = false"
        >取 消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="submit"
        >确 定</el-button>
      </span>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted, } from 'vue'
// import { useStore } from 'vuex'
import { getPermissionDetail } from '@/api/test'
import { savePermission } from '@/api/test'

export default defineComponent({
  name: 'EditMenu',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    // const store = useStore()
    const permissionFormDatas: any = inject('permissionFormDatas')

    const state = reactive({
      // el-tree 默认展示框
      permissionOptions: [],
      permissionForm: {
        permissionName: '',
        permissionId: '',
        id: '',
        isNavMenu: 1,
        isSysPermission: 1,
        menuUrl: '',
        orderSeq: '',
        pPermissionId: '',
        remark: '',
        status: 1,
      },
      permissionFormRef: null,
      formLabelWidth: '86px',
      rules: {
        roleName: [{ required: true, message: '请输入', trigger: 'blur' }],
      },
      loading: false,
      // 递归函数，用于设置节点及其子节点的disabled为true
      setDisabledRecursive: (node) => {
        if (node.id === state.permissionForm.id) {
          node.disabled = true
        }
        if (node.children?.length) {
          node.children.map((item) => {
            item.disabled = true
            if (item.children?.length) {
              state.setDisabledRecursive(item)
            }
          }
          )
        }
      },
      // 保存
      submit: () => {
        state.permissionFormRef.validate((valid) => {
          if (valid) {
            // console.log(state.permissionForm)
            // return
            state.loading = true
            savePermission(state.permissionForm)
              .then(() => {
                ElMessage.success('保存成功')
                props.request()
                state.loading = false
                permissionFormDatas.dialogVisible = false
              })
              .catch(() => {
                state.loading = false
              })
          }
        })
      },
      // 树形结构数据处理
      treeListSet: (arr) => {
        return arr.map((e) => {
          const item = {
            id: e.id || '',
            label: e.permissionName || '',
            value: e.permissionId || '',
            pPermissionId: e.pPermissionId || '',
            children: []
          }
          if (e?.childPermissions?.length) {
            item.children = state.treeListSet(e.childPermissions)
          }
          return item
        })
      },
    })

    onMounted(() => {
      if (permissionFormDatas.userFormData) {
        state.permissionForm = permissionFormDatas.userFormData
        if (permissionFormDatas.userFormData.id) {
          getPermissionDetail(permissionFormDatas.userFormData.id).then((res) => {
            state.permissionForm = res
          })
        }
      }
      if (permissionFormDatas.permissionOptions?.length) {
        // const { permissionOptions } = permissionFormDatas
        // state.permissionOptions = permissionFormDatas.permissionOptions
        state.permissionOptions = state.treeListSet(permissionFormDatas.permissionOptions)
      }
    })

    return {
      ...toRefs(state),
      permissionFormDatas,
    }
  },
})
</script>

<style scoped>
.el-radio-group {
  transform: translateY(-4px);
}
</style>
