<template>
  <div class="common-form-module materialsListDetail">
    <div class="flex justify-between">
      <div v-if="RESEARCHCENTER_INFOS.stateVal === 1">编辑</div>
      <div v-if="RESEARCHCENTER_INFOS.stateVal === 2">映射关系</div>
      <div>
        <el-button plain :loading="loading" @click="routerGo">返回</el-button>
        <el-button type="primary" :loading="loading" @click="saveData">保存</el-button>
      </div>
    </div>
    <div v-if="RESEARCHCENTER_INFOS.stateVal === 1" class="materials-box">
      <el-form :model="ruleForm" label-position="top">
        <div class="flex">
          <el-form-item label="类型" class="margin-2" style="width: 30%">
            <el-input
              v-model.trim="ruleForm.materialTypeText"
              class="w-full"
              disabled
            />
          </el-form-item>
          <el-form-item label="物资ID" class="margin-2" style="width: 30%">
            <el-input
              v-model.trim="ruleForm.omsMaterialId"
              class="w-full"
              disabled
            />
          </el-form-item>
          <el-form-item label="随机发药" style="width: 30%">
            <el-select
              v-model="ruleForm.isRandom"
              placeholder=""
              disabled
            >
              <el-option
                v-for="item in isRandomOptions"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="物资名称" style="width: 62%;margin-right: 30px;">
            <el-input
              v-model.trim="ruleForm.materialName"
              class="w-full"
              disabled
            />
          </el-form-item>
          <el-form-item label="是否回收">
            <el-select
              v-model="ruleForm.recoverResult"
              placeholder="请选择"
            >
              <el-option
                v-for="item in recoverResultOptions"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item
          v-for="item in ruleForm.materialSpecLists"
          :key="item.id"
          label="规格"
          style="width: 30%"
          class="mr-6"
        >
          <el-input
            v-model="item.spec"
            class="w-full"
            disabled
          />
        </el-form-item>
        <el-form-item style="width: 100%">
          <template #label>
            <div>
              物资图片
              <span style="color:#aaa">（支持上传JPG、PNG格式文件，最大不超过100KB）</span>
            </div>
          </template>
          <MyUpload
            :upload-img-falg="true"
            :upload-img="icfFileUrl"
            :custom-information-form-data="customInformationFormData"
            :request="postStudyFile"
            :request-fun="fileImg"
            :file-size="1 / 10.24"
          />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="RESEARCHCENTER_INFOS.stateVal === 2" class="materials-box">
      <div class="apply-centre-center mb-5">
        <el-button type="primary" class="mr-4" @click="applyFlagShow = true">适用中心</el-button>
        <span>已选择 {{ shuttleValue.length }}/{{ shuttleData.length }} 家中心</span>
      </div>
      <div>
        <div class="ft-15 flex items-center justify-between">
          <span>OMS渠道映射</span>
          <el-button type="primary" class="mr-4" @click="issueRule(1)">批量设置</el-button>
        </div>
        <!-- 表格 -->
        <trial-table
          ref="materialsListDetailRef"
          title=""
          :request="getResearchCenterApplyList"
          :columns="columns"
          :showbtnfalg="true"
          @selectionChange="handleSelectionChange"
        >
          <!-- :requestExport="getResearchCenterExport" -->
          <template #operate="scope">
            <span class="editBtnBlue" @click="issueRule(0, scope.row, scope.$index)">
              设置
            </span>
          </template>
        </trial-table>
      </div>
    </div>
    <trial-dialog v-model="applyFlagShow" :my-dialog-body-style="myDialogBodyStyle">
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="中心名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :left-default-checked="leftDefaultChecked"
            :right-default-checked="rightDefaultChecked"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain @click="shuttleCancel">取消</el-button>
          <el-button type="primary" @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="flagShow" class="myDialog">
      <template #footer>
        <div class="font-semibold title">{{ myTitle }}</div>
        <el-form
          ref="materialsListFormRef"
          class="mt-5"
          :model="setForm"
          :rules="setRules"
          label-position="top"
          style="width: 50%"
        >
          <el-form-item label="OMS渠道映射" prop="omsChannelId">
            <el-select
              v-model="setForm.omsChannelId"
              class="w-full"
              placeholder="请选择"
            >
              <el-option
                v-for="item in omsMapList"
                :key="item.omsChannelId"
                :label="item.omsChannelName"
                :value="item.omsChannelId"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="flex justify-center footer-btn">
          <el-button plain @click="flagShow = false">取消</el-button>
          <el-button type="primary" @click="setConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import MyUpload from '@/components/Upload/index.vue'
import { getCustomTasksTransfer } from '@/api/customTask'
import {
  getMaterial,
  getMaterialMapping,
  getOMSMaterialMapping,
  putMaterialMapping,
  postMaterialInfo,
} from '@/api/materialsManagement'
import { postStudyFile } from '@/api/home'

export default defineComponent({
  name: 'MaterialsListDetail', // 物资列表-新建/编辑
  components: {
    MyUpload,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state: any = reactive({
      postStudyFile,
      loading: false,
      beforeFileUploadType: ['.jpg', '.png'],
      customInformationFormData: {
        customInformationFormDataObj: {
          icfFilePath: '', // 文件声明路径
          icfFileName: '', // 文件名
          icfFileUrl: '', // 文件访问路径
        },
      },
      icfFileUrl: '',
      ruleForm: {}, // 编辑页面的数据
      materialsListDetailRef: null, // 表格ref
      shuttleData: [], // 总的
      shuttleValue: [], // 选中的
      shuttleValueCopy: [], // 选中的copy
      applyFlagShow: false, // 适用中心的弹出窗
      leftDefaultChecked: [], // 初始状态下选中的
      rightDefaultChecked: [], // 初始状态下选中的
      multipleSelection: [], // 勾选选中的数组
      shuttleProps: {
        key: 'siteId',
        label: 'statusStrAndsiteName'
      },
      myDialogBodyStyle: {
        width: '60%'
      },
      columns: [
        { type: 'selection' }, // table勾选框
        { label: '中心信息', prop: 'addObj.dctSiteName', width: 300 },
        { label: 'OMS渠道映射', prop: 'addObj.omsChannelName', width: 150 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      materialsListFormRef: null, // 批量设置ref弹窗
      setForm: {
        omsChannelId: null,
        omsChannelName: ''
      },
      recoverResultOptions: [
        {
          value: 1,
          label: '是',
        },
        {
          value: 0,
          label: '否',
        },
      ],
      isRandomOptions: [
        {
          value: true,
          label: '是',
        },
        {
          value: false,
          label: '否',
        },
      ],
      setRules: {
        omsChannelId: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      omsMapList: [], // oms映射渠道
      flagShow: false, // 设置的弹窗
      setNum: -1, // 是否点击批量设置
      pitchOnTabData: -1, // 选中表格的第几项
      myTitle: '', // 设置弹窗上部的表头
      maoAllMesg: {},
      // 设置
      issueRule: (num, row, index) => {
        state.setNum = num
        if (state.setNum === 0) {
          state.myTitle = row.addObj.dctSiteName
          state.setForm.omsChannelId = row.addObj.omsChannelId
          state.setForm.omsChannelName = row.addObj.omsChannelName
          state.pitchOnTabData = index
        } else if (state.setNum === 1) {
          if (!state.multipleSelection.length) {
            ElMessage.warning({
              showClose: true,
              message: '请选择内容',
            })
            return
          }
          state.setForm.omsChannelId = null
          state.setForm.omsChannelName = ''
          state.myTitle = '批量设置'
        }
        state.flagShow = true
      },
      async getResearchCenterApplyList() {
        return {
          data: []
        }
      },
      // 多选框
      handleSelectionChange: (val) => {
        state.multipleSelection = val
      },
      // 上传图片
      fileImg: (res) => {
        state.customInformationFormData = res
        state.ruleForm.materialPic = res.thumbnailUrl
      },
      routerGo: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      // 总的保存
      saveData: () => {
        if (RESEARCHCENTER_INFOS.stateVal === 1) {
          state.loading = true
          const data = {
            recoverResult: state.ruleForm.recoverResult,
            materialPic: state.ruleForm.materialPic
          }
          // 是否回收的字段ruleForm.recoverResult
          // postMaterialInfo保存物资信息
          postMaterialInfo(studyId, RESEARCHCENTER_INFOS.researchContent?.id, data)
            .then(() => {
              ElMessage.success('保存成功')
              state.loading = false
              RESEARCHCENTER_INFOS.resetMethod.handleReset()
              RESEARCHCENTER_INFOS.contentVisible = false
            }).catch(() => {
              state.loading = false
            })
        } else if (RESEARCHCENTER_INFOS.stateVal === 2) {
          if (!state.shuttleValue.length) {
            ElMessage.warning({
              showClose: true,
              message: '请选择中心',
            })
            return
          }
          const materialMappingRelations = []
          let selectAllOmsChannel = false
          state.materialsListDetailRef.tableData.forEach((item) => {
            materialMappingRelations.push(item.addObj)
          })
          for (const i of materialMappingRelations) {
            if (!i.omsChannelId) {
              selectAllOmsChannel = true
              ElMessage.warning({
                showClose: true,
                message: '请设置已选中心的信息',
              })
              break
            }
          }
          if (selectAllOmsChannel) return
          state.loading = true
          const data = {
            materialMappingRelations,
            materialId: state.maoAllMesg?.materialId,
            dctStudyId: state.maoAllMesg?.dctStudyId
          }
          putMaterialMapping(studyId, data).then(() => {
            ElMessage.success('保存成功')
            RESEARCHCENTER_INFOS.resetMethod.handleReset()
            RESEARCHCENTER_INFOS.contentVisible = false
            state.loading = false
          }).catch(() => {
            state.loading = false
          })
        }
      },
      // 穿梭框的确定
      shuttleConfirm: () => {
        state.shuttleValueCopy = state.shuttleValue
        state.tab()
        state.applyFlagShow = false
      },
      // 穿梭框取消
      shuttleCancel: () => {
        state.shuttleValue = state.shuttleValueCopy
        state.applyFlagShow = false
      },
      // 设置弹窗的确定
      setConfirm: () => {
        // 校验
        state.materialsListFormRef.validate((valid) => {
          if (valid) {
            state.omsMapList.forEach((ite: any) => {
              if (ite.omsChannelId === state.setForm.omsChannelId) {
                state.setForm.omsChannelName = ite.omsChannelName
              }
            })
            if (state.setNum === 1) {
              state.materialsListDetailRef.tableData.forEach((it: any) => {
                state.multipleSelection.forEach((item: any) => {
                  if (it.addObj.dctSiteId === item.siteId) {
                    it.addObj.omsChannelId = state.setForm.omsChannelId
                    it.addObj.omsChannelName = state.setForm.omsChannelName
                  }
                })
              })
            } else if (state.setNum === 0) {
              state.materialsListDetailRef.tableData[state.pitchOnTabData].addObj.omsChannelId = state.setForm.omsChannelId
              state.materialsListDetailRef.tableData[state.pitchOnTabData].addObj.omsChannelName = state.setForm.omsChannelName
            }
            state.flagShow = false
          }
        })
      },
      // 将数据转化到表格
      tab: () => {
        const tableList = []
        state.shuttleData.forEach((ite: any) => {
          state.shuttleValue.forEach((item) => {
            if (ite.addObj.dctSiteId === item) {
              tableList.push(ite)
            }
          })
        })
        if (state.materialsListDetailRef.tableData) {
          state.materialsListDetailRef.tableData = []
        }
        state.materialsListDetailRef.tableData = tableList
      },
      // 进入页面加载，写在了onMounted中
      onLoad: async() => {
        if (RESEARCHCENTER_INFOS.stateVal === 1) {
          getMaterial(studyId, RESEARCHCENTER_INFOS.researchContent.id).then((res: any) => {
            if (res.materialType === '0') {
              res.materialTypeText = '实物'
            }
            if (!res?.recoverResult && res?.recoverResult !== 0) {
              res.recoverResult = 0
            }
            state.ruleForm = res
            if (state.ruleForm.materialPic) {
              state.icfFileUrl = state.ruleForm.materialPic
            }
          })
        } else if (RESEARCHCENTER_INFOS.stateVal === 2) {
          // oms映射渠道
          getOMSMaterialMapping(RESEARCHCENTER_INFOS.researchContent.omsMaterialId).then((res: any) => {
            if (res?.length) {
              res.forEach((res) => {
                res.omsChannelId = res.id
                res.omsChannelName = res.name
              })
              state.omsMapList = res
            }
          })

          const rest: any = await getCustomTasksTransfer(studyId, {})
          if (rest?.customTaskSiteRuleList) {
            rest.customTaskSiteRuleList.forEach((item) => {
              rest.studySiteList.push(item)
            })
          }
          state.shuttleData = rest.studySiteList
          // 接口表格的
          const res = await getMaterialMapping(studyId, RESEARCHCENTER_INFOS.researchContent.id)
          state.maoAllMesg = res
          state.shuttleData.forEach((ite: any) => {
            ite.addObj = {
              dctSiteName: ite.siteName,
              omsChannelName: '',
              omsChannelId: null,
              dctSiteId: ite.siteId
            }
            ite.statusStrAndsiteName = `${ite.edcSiteCode}-(${ite.siteStatusStr})  ${ite.siteName}`
            if (state.maoAllMesg?.materialMappingRelations.length) {
              state.maoAllMesg.materialMappingRelations.forEach((item) => {
                if (ite.siteId === item.dctSiteId) {
                  state.shuttleValue.push(item.dctSiteId)
                  ite.addObj = item
                }
              })
            }
          })
          state.tab()
        }
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.materialsListDetail {
  min-height: 800px;
  .materials-box {
    padding: 10px;
  }
  .margin-2 {
    margin-right: 2%;
  }
  :deep(.myDialog) {
    .my-dialog-body {
      padding: 10px 20px 20px 20px;
    }
    .title {
      padding: 10px 0;
      border-bottom: 2px solid #ccc;
    }
    .footer-btn {
      margin-top: 200px;
    }
  }
}
:deep(.my-el-table) {
  padding: 0 !important;
}
</style>
