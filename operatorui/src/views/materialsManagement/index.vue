<template>
  <div class="customTask">
    <div v-if="!RESEARCHCENTER_INFOS?.contentVisible">
      <trial-table
        ref="materialsManagementRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :showbtnfalg="true"
        :hide-center="true"
      >
        <!-- :requestExport="getResearchCenterExport" -->
        <template #isRandomStr="scope">
          <span>
            {{ !scope.row?.isRandom ? '否' : '是' }}
          </span>
        </template>
        <template #operate="scope">
          <span class="editBtnBlue mr-3" @click="editCustomTaskInfoItem(scope.row, 1)">
            编辑
          </span>
          <span class="editBtnBlue mr-3" @click="editCustomTaskInfoItem(scope.row, 2)">
            映射关系
          </span>
          <!-- <span class="editBtnBlue mr-3" @click="editCustomTaskInfoItem(scope.row, 3)">
            人工补单
          </span> -->
        </template>
        <template #hideCenter>
          <div class="researchCenter-hideCenter">
            <div class="my-5 flex justify-between items-center">
              <span>物资列表</span>
              <div>
                <el-button
                  type="primary"
                  size
                  :loading="loading"
                  @click="showVisitClFlag = true"
                >同步OMS</el-button>
              </div>
            </div>
          </div>
        </template>
      </trial-table>
    </div>
    <MaterialsListDetail v-if="RESEARCHCENTER_INFOS?.contentVisible" />
    <trial-dialog v-model="showVisitClFlag" title="提示" :my-dialog-body-style="myDialogBodyStyle">
      <template #DialogBody>
        是否确认同步OMS系统中物资信息？
      </template>
      <template #footer>
        <div class="mt-10 text-center">
          <el-button size="large" :loading="loading" @click="showVisitClFlag = false">取 消</el-button>
          <el-button
            size="large"
            type="primary"
            :loading="loading"
            @click="syncOMS"
          >确 定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="artificialShow" :my-dialog-body-style="artificialDialogBodyStyle" class="dialog">
      <template #DialogBody>
        <div class="mb-5">
          人工补单
        </div>
        <!-- 表单 -->
        <el-form ref="materialsFormRef" :model="ruleForm" :rules="rules" label-position="top">
          <div class="flex">
            <el-form-item label="中心" prop="dctSiteId" class="mr-[1%]" style="width: 24%">
              <el-select
                v-model="ruleForm.dctSiteId"
                class="w-full"
                placeholder="请选择"
                @change="dctSiteChange"
              >
                <el-option
                  v-for="(item, index) in labourRep.sites"
                  :key="index"
                  :label="item.siteName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="OMS渠道映射" prop="omsChannelId" class="mr-[1%]" style="width: 24%">
              <el-select
                v-model="ruleForm.omsChannelId"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in labourRep.omsChannel"
                  :key="index"
                  :label="item.omsChannelName"
                  :value="item.omsChannelId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="发放对象" class="mr-[1%]" style="width: 24%">
              <el-input v-model="distributionTarget" class="w-full" disabled />
              <!-- <el-select
                v-model="ruleForm.distributionTarget"
                class="w-full"
                placeholder="请选择"
              >
                <el-option label="受试者端" :value="2" />
              </el-select> -->
            </el-form-item>
            <el-form-item label="选择用户" prop="patientId" style="width: 24%">
              <el-select
                v-model="ruleForm.patientId"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in patientsUserInfo"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item
              label="物资"
              prop="replenishmentMaterial.materialId"
              class="mr-[1%]"
              style="width: 49%"
            >
              <el-select
                v-model="ruleForm.replenishmentMaterial.materialId"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in labourRep.materialArr"
                  :key="index"
                  :label="item.materialName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="规格" class="mr-[1%]" style="width: 24%">
              <!-- <el-select
                v-model="ruleForm.gui"
                class="w-full"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in options"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select> -->
              <el-input v-model.trim="materialSpecs" disabled />
            </el-form-item>
            <el-form-item
              label="数量"
              prop="replenishmentMaterial.num"
              style="width: 24%"
            >
              <el-input
                v-model.trim="ruleForm.replenishmentMaterial.num"
                placeholder="请输入"
                maxlength="200"
              />
            </el-form-item>
          </div>
          <el-form-item label="订单备注">
            <el-input
              v-model.trim="ruleForm.remark"
              class="w-full"
              placeholder="请输入"
              maxlength="200"
            />
          </el-form-item>
          <div class="mb-5">
            收件信息
          </div>
          <div class="flex">
            <el-form-item label="联系人" prop="rcptName" class="mr-[1%]" style="width: 30%">
              <el-input
                v-model.trim="ruleForm.rcptName"
                class="w-full"
                placeholder="请输入"
                maxlength="200"
              />
            </el-form-item>
            <el-form-item label="联系人电话" prop="rcptPhoneNo" class="mr-[1%]" style="width: 30%">
              <el-input
                v-model.trim="ruleForm.rcptPhoneNo"
                class="w-full"
                placeholder="请输入"
                maxlength="11"
              />
            </el-form-item>
          </div>
          <el-form-item label="所在地区" prop="address" style="width: 100%">
            <el-cascader
              v-model="ruleForm.address"
              clearable
              style="width: 95%"
              :options="cityOptions"
              :props="{
                expandTrigger: 'hover',
              }"
            />
          </el-form-item>
          <el-form-item label="详细地址" prop="rcptAddressDetail" class="mr-[1%]" style="width: 100%">
            <el-input
              v-model.trim="ruleForm.rcptAddressDetail"
              class="w-full"
              placeholder="请输入"
              maxlength="200"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="mt-10 text-center">
          <el-button size="large" :loading="loading" @click="artificialClick(0)">取 消</el-button>
          <el-button
            size="large"
            type="primary"
            :loading="loading"
            @click="artificialClick(1)"
          >确 定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, provide, reactive, toRefs } from 'vue'
import MaterialsListDetail from '@/views/materialsManagement/MaterialsListDetail.vue'
import { useStore } from 'vuex'
import { getCitys } from '@/api/home'
import { getManualReplenishment, getMaterials, getPullOmsMaterial, postManualReplenishment } from '@/api/materialsManagement'

export default defineComponent({
  name: 'MaterialsManagement', // 物资列表
  components: {
    MaterialsListDetail,
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
      stateVal: null
    })
    const state = reactive({
      loading: false,
      materialsManagementRef: null,
      showVisitClFlag: false,
      myDialogBodyStyle: {
        width: '30%',
        minHeight: '0',
      },
      artificialDialogBodyStyle: {
        width: '60%',
      },
      materialSpecs: '', // 规格 点击的时候带来的
      distributionTarget: '受试者端', // 写死的
      labourRep: {}, // 人工补单内的所有数据
      patientsUserInfo: [],
      columns: [
        { label: '类型', prop: 'materialTypeText', width: 150 },
        { label: '物资ID', prop: 'omsMaterialId', width: 200 },
        { label: '物资', prop: 'materialName', width: 350, },
        { label: '随机发药', width: 120, tdSlot: 'isRandomStr', },
        { label: '更新时间', prop: 'lastUpdatetime', width: 180, },
        { label: '更新人', prop: 'lastUpdator', width: 180, },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          minWidth: 160,
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      artificialShow: false, // 人工补单的弹窗
      materialsFormRef: null, // 人工补单的ref
      ruleForm: {
        dctSiteId: '',
        omsChannelId: '',
        patientId: '',
        remark: '',
        rcptName: '',
        rcptPhoneNo: '',
        address: [],
        rcptProvinceCode: '',
        rcptProvinceName: '',
        rcptCityCode: '',
        rcptCityName: '',
        rcptAreaCode: '',
        rcptAreaName: '',
        rcptAddressDetail: '',
        replenishmentMaterial: {
          materialId: '',
          num: null
        }
      },
      rules: {
        dctSiteId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        omsChannelId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        patientId: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        'replenishmentMaterial.materialId': [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        'replenishmentMaterial.num': [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^[1-9]\d*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ],
        rcptName: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        rcptPhoneNo: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        address: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        rcptAddressDetail: [
          { required: true, message: '请输入', trigger: 'blur' },
        ]
      },
      cityOptions: [], // 省市区
      // 编辑/新建
      editCustomTaskInfoItem: (row, index) => {
        if (index === 1 || index === 2) {
          RESEARCHCENTER_INFOS.researchContent = { ...row }
          RESEARCHCENTER_INFOS.contentVisible = true
          RESEARCHCENTER_INFOS.resetMethod = state.materialsManagementRef
          RESEARCHCENTER_INFOS.stateVal = index
        } else if (index === 3) {
          // 调用人工补单的接口
          getManualReplenishment(store.state.studyItem.studyId, row?.id).then((res: any) => {
            res.materialArr = []
            res.materialArr.push(res.material)
            state.labourRep = res
            state.materialSpecs = row.materialSpecs
            state.artificialShow = true
          })
          // 城市
          getCitys().then((res: any) => {
            if (Array.isArray(res) && res?.length) {
              state.cityOptions = res
            }
          })
        }
      },
      // 同步oms
      syncOMS: () => {
        state.loading = true
        getPullOmsMaterial(store.state.studyItem.studyId).then(() => {
          ElMessage.success('同步成功')
          state.materialsManagementRef.refresh()
          state.showVisitClFlag = false
          state.loading = false
        }).catch(() => {
          state.showVisitClFlag = false
          state.loading = false
        })
      },
      // 中心选择的dctSiteChange
      dctSiteChange: (val) => {
        state.patientsUserInfo = []
        state.ruleForm.patientId = ''
        if (state.labourRep?.patients?.length) {
          state.labourRep.patients.forEach((item) => {
            if (val === item.dctSiteId) {
              state.patientsUserInfo.push(item)
            }
          })
        }
      },
      // 人工补单确定
      artificialClick: (index) => {
        if (index === 0) {
          state.artificialShow = false
          state.materialsFormRef.resetFields()
        } else if (index === 1) {
          // 可能会处理城市
          state.materialsFormRef.validate((valid) => {
            if (valid) {
              // 处理城市
              if (state.ruleForm.address) {
                state.cityOptions.forEach((item: any) => {
                  item.children.forEach((itemClild: any) => {
                    itemClild.children.forEach((itemClildClild: any) => {
                      if (itemClildClild.value === state.ruleForm.address[2]) {
                        state.ruleForm.rcptProvinceName = item.label
                        state.ruleForm.rcptCityName = itemClild.label
                        state.ruleForm.rcptAreaName = itemClildClild.label
                        return
                      }
                    })
                  })
                })
                state.ruleForm.rcptProvinceCode = state.ruleForm.address[0]
                state.ruleForm.rcptCityCode = state.ruleForm.address[1]
                state.ruleForm.rcptAreaCode = state.ruleForm.address[2]
              }
              if (state.ruleForm?.replenishmentMaterial?.num) {
                state.ruleForm.replenishmentMaterial.number = state.ruleForm.replenishmentMaterial.num / 1
              }
              state.ruleForm.replenishmentMaterial.omsMaterialId = state.labourRep.materialArr[0].omsMaterialId
              state.labourRep.sites.forEach((item) => {
                if (item.id === state.ruleForm.dctSiteId) {
                  state.ruleForm.dctSiteName = item.siteName
                }
              })
              const data = {
                ...state.ruleForm,
                orderType: 2,
                dctPatientMaterialDistributionActionId: '',
              }
              postManualReplenishment(store.state.studyItem.studyId, data).then(() => {
                ElMessage.success('保存成功')
                state.materialsFormRef.resetFields()
                state.artificialShow = false
              })
            }
          })
        }
      },
      // 表格数据
      async getResearchCenterList() {
        try {
          const rest: any = await getMaterials(store.state.studyItem.studyId)
          rest.forEach((item) => {
            if (item.materialType === '0') {
              item.materialTypeText = '实物'
            }
          })
          return {
            data: rest || []
          }
        } catch (e) {
          return {
            data: []
          }
        }
      },
    })

    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>
