<template>
  <div class="releaseplan">
    <div v-if="!RESEARCHCENTER_INFOS?.contentVisible">
      <trial-table
        ref="releaseplanRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :showbtnfalg="true"
        :hide-center="true"
      >
        <!-- :requestExport="getResearchCenterExport" -->
        <template #operate="scope">
          <span class="editBtnBlue mr-3" @click="editCustomTaskInfoItem(scope.row, 1)">
            编辑
          </span>
        </template>
        <template #hideCenter>
          <div class="researchCenter-hideCenter">
            <div class="my-5 flex justify-between items-center">
              <span>计划列表</span>
              <div>
                <el-button
                  type="primary"
                  size
                  :loading="loading"
                  @click="editCustomTaskInfoItem(null ,0)"
                >新建</el-button>
              </div>
            </div>
          </div>
        </template>
      </trial-table>
    </div>
    <ReleasePlanDetail v-if="RESEARCHCENTER_INFOS?.contentVisible" />
  </div>
</template>

<script lang='ts'>
import { defineComponent, onMounted, provide, reactive, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import ReleasePlanDetail from '@/views/materialsManagement/releaseplan/ReleasePlanDetail.vue'
import { getMaterialDistributionPlans } from '@/api/materialsManagement'

export default defineComponent({
  name: 'Releaseplan',
  components: {
    ReleasePlanDetail
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
      stateVal: null
    })
    const state = reactive({
      loading: false,
      releaseplanRef: null,
      columns: [
        { label: '计划名称', prop: 'planName', width: 260 },
        { label: '发放对象', prop: 'distribution', width: 150 },
        { label: '适用中心', prop: 'siteNum', width: 150, },
        { label: '创建时间', prop: 'createTime', width: 180, },
        { label: '创建人', prop: 'creator', width: 150, },
        { label: '更新时间', prop: 'lastUpdatetime', width: 180, },
        { label: '更新人', prop: 'lastUpdator', width: 150, },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 编辑/新建
      editCustomTaskInfoItem: (row, index) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.releaseplanRef
        RESEARCHCENTER_INFOS.stateVal = index
      },
      async getResearchCenterList() {
        const rest: any = await getMaterialDistributionPlans(store.state.studyItem.studyId)
        rest.forEach((res) => {
          if (res.distributionTarget === 1) {
            res.distribution = '受试者'
          }
        })
        return {
          data: rest
        }
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
      // 
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">

</style>
