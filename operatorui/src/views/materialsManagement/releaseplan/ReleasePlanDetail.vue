<template>
  <div class="common-form-module releasePlanDetail">
    <div class="flex justify-between">
      <div>{{ RESEARCHCENTER_INFOS.stateVal ? '编辑' : '新建' }}</div>
      <div class="flex">
        <el-button class="mr-3" plain @click="routerGo">返回</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          @click="saveData"
        >保存</el-button>
      </div>
    </div>
    <el-form ref="releasePlanDetailRef" class="mt-5" :model="ruleForm" :rules="rules" label-position="top">
      <div class="flex">
        <el-form-item label="计划名称" prop="planName" class="mr-[20px]" style="width: 30%">
          <el-input
            v-model.trim="ruleForm.planName"
            class="w-full"
            placeholder="请输入"
            maxlength="999"
          />
        </el-form-item>
        <el-form-item label="发放对象" prop="distributionTarget" class="mr-[20px]" style="width: 20%">
          <el-select
            v-model="ruleForm.distributionTarget"
            class="w-full"
            placeholder="请选择"
          >
            <el-option label="受试者" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" style="width: 20%">
          <el-select
            v-model="ruleForm.tagArr"
            clearable
            multiple
            class="w-full"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in tagOptions"
              :key="index"
              :label="item.armName"
              :value="item.armCode"
            />
          </el-select>
        </el-form-item>
      </div>
      <div class="my-5">
        <div style="font-size: 14px; margin-bottom: 5px" class="flex">
          <div class="mr-3"><span style="color: red">*</span>发放规则</div>
          <el-icon color="#1296db" class="cursor-pointer" :size="20" @click="addruleClick"><CirclePlus /></el-icon>
        </div>
      </div>
      <div v-for="(item, index) in ruleForm.materialDistributionRules" :key="index" class="flex items-center mb-3">
        <div class="mb-[18px] mr-2">本次发放名称</div>
        <el-form-item style="width: 20%" :prop="`materialDistributionRules[${index}].ruleName`" :rules="{ required: true, message: '请输入', trigger: 'blur' }">
          <el-input
            v-model.trim="item.ruleName"
            class="w-full"
            placeholder="请输入"
            maxlength="999"
          />
        </el-form-item>
        <div class="mb-[18px] ml-7 mr-2">基于</div>
        <el-form-item style="width: 13%" :prop="`materialDistributionRules[${index}].ruleBaseOn`" :rules="{ required: true, message: '请选择', trigger: 'change' }">
          <el-select
            v-model="item.ruleBaseOn"
            class="w-full"
            placeholder="请选择"
          >
            <el-option label="入组日期" :value="1" />
            <!-- <el-option label="指定时间" :value="2" /> -->
          </el-select>
        </el-form-item>
        <div class="mb-[18px] mx-2">第</div>
        <el-form-item
          style="width: 13%"
          :prop="`materialDistributionRules[${index}].theDaysAfterBaseOn`"
          :rules="[{ required: true, message: '请输入', trigger: 'blur' },{ pattern: /^[1-9]\d*$/,message: '请填写正整数',trigger: 'blur' }]"
        >
          <el-input
            v-model.trim="item.theDaysAfterBaseOn"
            class="w-full"
            placeholder="请输入"
            maxlength="999"
          />
        </el-form-item>
        <div class="mb-[18px] ml-2">天</div>
        <div class="mb-[18px] ml-7 mr-2">状态</div>
        <el-switch
          v-model="item.isEnable"
          class="mb-[18px]"
          :active-text="item.isEnable ? '启用' : '禁用'"
        />
        <el-icon v-if="ruleForm.materialDistributionRules.length !== 1" color="#d81e06" class="mb-[18px] ml-7 cursor-pointer" @click="deleteRule(index)"><CloseBold /></el-icon>
      </div>
    </el-form>

    <!-- 选择物资 -->
    <div class="apply-centre-center">
      <el-button type="primary" class="mr-4" @click="applyClick(0)">选择物资</el-button>
    </div>
    <trial-table
      ref="releaseplanSubstanceRef"
      class="tab-pro mb-5"
      title=""
      :request="getSubstanceList"
      :columns="substanceColumns"
      :showbtnfalg="true"
    >
      <template #materialTypeStr="scope">
        <span>
          {{ scope.row.materialType === '0' ? '实物' : '' }}
        </span>
      </template>
      <template #isRandomStr="scope">
        <span>
          {{ !scope.row?.isRandom ? '否' : '是' }}
        </span>
      </template>
      <template #operate="scope">
        <span class="editBtnBlue mr-3" @click="substanceClick(scope.row, scope.$index)">
          设置
        </span>
      </template>
    </trial-table>
    <!-- 适用中心 -->
    <div class="apply-centre-center">
      <el-button type="primary" class="mr-4" @click="applyClick(1)">适用中心</el-button>
      <span>已选择 {{ applyValue.length }}/{{ applyDate.length }} 家中心</span>
    </div>
    <trial-table
      ref="releaseplanApplyRef"
      class="tab-pro"
      title=""
      :request="getApplyList"
      :columns="applyColumns"
      :showbtnfalg="true"
    />

    <trial-dialog v-if="applyFlagShow || substanceFlagShow" :my-dialog-body-style="myDialogBodyStyle">
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            :filter-placeholder="filterPlaceholder"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :left-default-checked="leftDefaultChecked"
            :right-default-checked="rightDefaultChecked"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain @click="shuttleCancel">取消</el-button>
          <el-button type="primary" @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="substanceSetMes" :my-dialog-body-style="substanceMyDialogBodyStyle">
      <template #footer>
        <div class="substanceSetMes-title mb-5">{{ substanceForm.materialName }}</div>
        <el-form ref="substanceFormRef" :model="substanceForm" :rules="substanceRules" label-position="top">
          <el-form-item label="规格" prop="materialSpecs">
            <el-input
              v-model.trim="substanceForm.materialSpecs"
              class="w-full"
              maxlength="666"
              disabled
            />
          </el-form-item>
          <el-form-item label="数量" prop="number">
            <el-input
              v-model.trim="substanceForm.number"
              class="w-full"
              placeholder="请输入"
              maxlength="666"
            />
          </el-form-item>
        </el-form>
        <div class="flex justify-center">
          <el-button plain @click="substanceSetMes = false">取消</el-button>
          <el-button type="primary" @click="substanceConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, inject, onMounted, reactive, toRefs, nextTick } from 'vue';
// import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { CirclePlus, CloseBold } from '@element-plus/icons-vue'
import { getCustomTasksTransfer } from '@/api/customTask'
import { getMaterialDistributionPlan, getMaterials, postMaterialDistributionPlan } from '@/api/materialsManagement'
import { deepClone } from '@trialdata/common-fun-css'
import { ReleasePlanDetailRootObject } from '@/type/views/materialsManagement'

export default defineComponent({
  name: 'ReleasePlanDetail', // 发放计划-新建/编辑
  components: {
    CirclePlus,
    CloseBold,
  },
  setup() {
    // const router = useRouter()
    // const route = useRoute()
    const store = useStore()
    const { studyId } = store.state.studyItem
    const RESEARCHCENTER_INFOS: any = inject('RESEARCHCENTER_INFOS')
    const state: ReleasePlanDetailRootObject = reactive({
      saveLoading: false,
      releasePlanDetailRef: null,
      ruleForm: {
        planName: '',
        distributionTarget: 1,
        tag: '',
        tagArr: [],
        materialDistributionRules: [
          {
            id: '',
            materialDistributionPlanId: '',
            ruleName: '',
            ruleBaseOn: 1,
            theDaysAfterBaseOn: '',
            isEnable: true,
          }
        ],
        materialDistributionPlanAndMaterialConfigs: [],
        materialDistributionPlanAndSiteConfigs: []
      },
      rules: {
        planName: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        distributionTarget: [
          { required: true, message: '请选择', trigger: 'change' },
        ]
      },
      tagOptions: [],
      myDialogBodyStyle: {
        width: '60%'
      },
      filterPlaceholder: '',
      // 适用中心适用的
      applyFlagShow: false,
      applyValue: [],
      applyDate: [],
      applyColumns: [
        { label: '中心信息', prop: 'siteName' },
      ],
      releaseplanApplyRef: null,
      // 物资发放
      substanceFlagShow: false,
      substanceVlaue: [],
      substanceDate: [],
      substanceColumns: [
        { label: '类型', width: '150', tdSlot: 'materialTypeStr' },
        { label: '物资ID', prop: 'omsMaterialId', width: '200' },
        { label: '物品名称', prop: 'materialName', width: '260' },
        { label: '随机发药', width: '120', tdSlot: 'isRandomStr' },
        { label: '规格', prop: 'materialSpecs', width: '200' },
        { label: '数量', prop: 'number', width: '150' },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      releaseplanSubstanceRef: null,
      substanceObj: {}, // 选中的某行物资
      substanceSetMes: false, // 物资修改弹窗
      // 物资表单
      substanceMyDialogBodyStyle: {
        width: '40%'
      },
      substanceFormRef: null,
      substanceForm: {},
      substanceRules: {
        materialSpecs: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        number: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^[1-9]\d*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ]
      },
      // 穿梭框用的数据
      shuttleValue: [],
      shuttleData: [],
      leftDefaultChecked: [],
      rightDefaultChecked: [],
      shuttleProps: {},
      ruleFormObj: {},
      routerGo: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      addruleClick: () => {
        state.ruleForm.materialDistributionRules.push(
          {
            id: '',
            materialDistributionPlanId: '',
            ruleName: '',
            ruleBaseOn: 1,
            theDaysAfterBaseOn: '',
            isEnable: true,
          }
        )
      },
      deleteRule: (index) => {
        state.ruleForm.materialDistributionRules.splice(index, 1)
      },
      saveData: () => {
        //
        state.releasePlanDetailRef.validate((valid) => {
          if (valid) {
            if (state.ruleForm.tagArr?.length) {
              state.ruleForm.tag = state.ruleForm.tagArr.join(',') // 数组转为字符串
            }
            let selectAllOmsChannel = false
            state.ruleForm.materialDistributionPlanAndSiteConfigs = [] // 保存清空重新赋值
            state.ruleForm.materialDistributionPlanAndMaterialConfigs = [] // 保存清空重新赋值
            // 为空停止执行
            // 没写数量停止执行
            state.releaseplanApplyRef.tableData.forEach((item: any) => {
              const data = {
                siteId: item.siteId,
                siteName: item.siteName
              }
              state.ruleForm.materialDistributionPlanAndSiteConfigs.push(data)
            })
            state.releaseplanSubstanceRef.tableData.forEach((item) => {
              state.ruleForm.materialDistributionPlanAndMaterialConfigs.push(item)
            })
            if (!state.ruleForm?.materialDistributionPlanAndSiteConfigs.length) {
              ElMessage.warning({
                showClose: true,
                message: '请选择适用中心',
              })
              return
            }
            if (!state.ruleForm?.materialDistributionPlanAndMaterialConfigs.length) {
              ElMessage.warning({
                showClose: true,
                message: '请选择物资',
              })
              return
            }
            // 重新赋值操作 materialDistributionRules
            for (const i of state.ruleForm?.materialDistributionPlanAndMaterialConfigs) {
              if (!i.number) {
                selectAllOmsChannel = true
                ElMessage.warning({
                  showClose: true,
                  message: '请设置已选物资的信息',
                })
                break
              }
            }
            if (selectAllOmsChannel) {
              reject()
              return
            }
            state.ruleForm.materialDistributionRules.forEach((item: any) => {
              if (item.isEnable) {
                item.materialDistributionRuleState = 1
              } else {
                item.materialDistributionRuleState = 2
              }
            })
            const data = {
              studyId,
              ...state.ruleForm
            }
            data.materialDistributionPlanAndMaterialConfigs.forEach((item: any) => {
              if (state.ruleFormObj?.materialDistributionPlanAndMaterialConfigs) {
                state.ruleFormObj.materialDistributionPlanAndMaterialConfigs.forEach((ite) => {
                  if (item.id === ite.id) {
                    item.id = ite.planAndMaterialConfigId
                  }
                })
              }
            })
            state.saveLoading = true
            postMaterialDistributionPlan(studyId, data)
              .then(() => {
                state.saveLoading = false
                ElMessage.success({
                  message: '保存成功',
                })
                RESEARCHCENTER_INFOS.resetMethod.handleReset()
                RESEARCHCENTER_INFOS.contentVisible = false
              }).catch(() => {
                state.saveLoading = false
              })
          }
        })
      },
      // 穿梭框的取消
      shuttleCancel: () => {
        if (state.applyFlagShow) {
          state.applyFlagShow = false
        } else if (state.substanceFlagShow) {
          state.substanceFlagShow = false
        }
      },
      shuttleConfirm: () => {
        if (state.applyFlagShow) {
          state.applyValue = state.shuttleValue
          state.applyFlagShow = false
          state.applyTab()
        } else if (state.substanceFlagShow) {
          state.substanceVlaue = state.shuttleValue
          state.substanceFlagShow = false
          state.substanceTab()
        }
      },
      // 物资/适用中心
      applyClick: (index) => {
        if (index === 0) {
          state.filterPlaceholder = '物资名称'
          state.substanceFlagShow = true
          state.shuttleProps = {
            key: 'id',
            label: `label`,
            // label: 'materialName'
          }
          state.shuttleValue = state.substanceVlaue
          state.shuttleData = state.substanceDate
          state.shuttleData.map((item: any) => {
            item.label = `${item?.materialName || ''},${item?.materialSpecs || ''}`
          })
        } else if (index === 1) {
          state.filterPlaceholder = '中心名称'
          state.applyFlagShow = true
          state.shuttleProps = {
            key: 'siteId',
            label: 'statusStrAndsiteName'
          }
          state.shuttleValue = state.applyValue
          state.shuttleData = state.applyDate
        }
      },
      // 适用中心的表格
      async getApplyList() {
        return {
          data: []
        }
      },
      // 处理适用中心的表格数据
      applyTab: () => {
        const tableList = []
        state.applyDate.forEach((ite: any) => {
          state.applyValue.forEach((item) => {
            if (ite.siteId === item) {
              tableList.push(ite)
            }
          })
        })
        nextTick(() => {
          if (state.releaseplanApplyRef?.tableData) {
            state.releaseplanApplyRef.tableData = []
          }
          state.releaseplanApplyRef.tableData = tableList
        })
      },
      // 物资处理表格数据
      substanceTab: () => {
        const tableList = []
        state.substanceDate.forEach((ite: any) => {
          state.substanceVlaue.forEach((item) => {
            if (ite.id === item) {
              ite.materialSepcId = item
              tableList.push(ite)
            }
          })
        })
        nextTick(() => {
          if (state.releaseplanSubstanceRef?.tableData) {
            state.releaseplanSubstanceRef.tableData = []
          }
          state.releaseplanSubstanceRef.tableData = tableList
        })
      },
      substanceClick: (row) => {
        state.substanceObj = row
        state.substanceForm = { ...row }
        state.substanceSetMes = true
      },
      substanceConfirm: () => {
        state.substanceFormRef.validate((valid) => {
          if (valid) {
            state.substanceObj.number = state.substanceForm?.number
            state.substanceSetMes = false
          }
        })
      },
      async getSubstanceList() {
        return {
          data: []
        }
      },
      // 进入页面加载，写在了onMounted中
      onLoad: async() => {
        // 得到适用中心的数据
        const rest: any = await getCustomTasksTransfer(studyId, {})
        if (rest?.customTaskSiteRuleList) {
          rest.customTaskSiteRuleList.forEach((item) => {
            rest.studySiteList.push(item)
          })
        }
        state.applyDate = rest.studySiteList
        state.applyDate.forEach((ite: any) => {
          ite.statusStrAndsiteName = `${ite.edcSiteCode}-(${ite.siteStatusStr})  ${ite.siteName}`
        })

        // 物资发放出的
        const subrest: any = await getMaterials(studyId, {
          isPlan: true
        })
        // subrest.forEach((item) => {
        //   if (item.materialType === '0') {
        //     item.materialTypeText = '实物'
        //   }
        // })
        state.substanceDate = subrest

        let planId = ''
        if (RESEARCHCENTER_INFOS.researchContent?.id) {
          planId = RESEARCHCENTER_INFOS.researchContent?.id
        }
        const ruleForm: any = await getMaterialDistributionPlan(studyId, { planId })
        state.ruleFormObj = deepClone(ruleForm)
        // 计划列表
        state.tagOptions = ruleForm.materialDistributionArms
        if (RESEARCHCENTER_INFOS.stateVal === 1) { // 编辑的时候调用
          if (ruleForm.tag) {
            ruleForm.tagArr = ruleForm.tag.split(',') // 分割为数组
          }
          ruleForm.materialDistributionRules.forEach((item) => {
            if (item.materialDistributionRuleState === 1) {
              item.isEnable = true
            } else if (item.materialDistributionRuleState === 2) {
              item.isEnable = false
            }
          })
          ruleForm.materialDistributionPlanAndSiteConfigs.forEach((ite) => {
            state.applyValue.push(ite.siteId)
          })
          state.applyTab()
          state.substanceDate = subrest.map((item:any) => {
            const ite = ruleForm.materialDistributionPlanAndMaterialConfigs.find((ite) => {
              if (item.id === ite.id) {
                state.substanceVlaue.push(ite.id)
                return ite
              }
            })
            return ite || item
          })
          state.substanceTab()
          state.ruleForm = ruleForm
        }
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.tab-pro {
  :deep(.my-el-table) {
    padding: 0;
  }
}
.substanceSetMes-title {
  border-bottom: 2px solid #d7d7d7;
}
</style>
