<template>
  <div class="group w-full h-full box-border">
    <div class="flex justify-between mb-5">
      <span class="group-title font-bold">主流程入组方式</span>
      <div>
        <el-button plain size :loading="loading" @click="routerGo">返回</el-button>
        <el-button v-if="false" type="primary" size :loading="loading" @click="saveData">保存</el-button>
      </div>
    </div>
    <div>
      暂未使用
    </div>
    <div v-if="false">
      <el-radio-group v-model="radio1" @change="radioChange">
        <el-radio class="w-full" label="1">DCT入组</el-radio>
        <el-radio disabled class="mt-2.5" label="2">手工登记入组信息</el-radio>
      </el-radio-group>
      <!-- 第二个选中的 -->
      <el-checkbox-group
        v-if="radio1 === '2'"
        v-model="checkbox1"
        class="ml-4 mt-3"
      >
        <el-checkbox
          v-for="item in manualOptions"
          :key="item.value"
          :label="item.value"
          class="block"
          style="width: 10%;"
        >{{ item.name }}</el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script lang='ts'>
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'GroupingProcess',
  setup() {
    const router = useRouter()
    const state = reactive({
      loading: false,
      radio1: '1',
      checkbox1: [],
      checkbox1Copy: [],
      manualOptions: [
        {
          name: '受试者编号',
          value: 1
        },
        {
          name: '入组日期',
          value: 2
        },
        {
          name: '组别',
          value: 3
        },
        {
          name: '随机号',
          value: 4
        },
        {
          name: '手机号',
          value: 5
        },
      ],
      routerGo: () => {
        router.replace('/home')
      },
      saveData: () => {
        console.log(state.radio1, state.checkbox1)
      },
      radioChange: () => {
        console.log(state.radio1)
        if (state.radio1 === '1') {
          state.checkbox1 = state.checkbox1Copy
        }
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
      // 
        state.checkbox1Copy = []
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.group {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  .group-title {
    font-size: 20px;
  }
}
</style>