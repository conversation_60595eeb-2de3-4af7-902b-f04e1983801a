<template>
  <div class="subjectSetting">
    <div class="head">
      <div class="headTitle">
        <h3>项目信息</h3>
        <div>
          <span @click="synchronization('crf')">同步CRF版本</span>
          <span @click="synchronization('topic')">同步项目信息</span>
        </div>
      </div>
      <div class="head-bottom">
        <div>
          <span>项目名称</span>
          <span v-html="subjectSetting.studyName" />
        </div>
        <div class="head-boxBottom">
          <div>
            <span>项目代号</span>
            <span v-html="subjectSetting.studyCode" />
          </div>
          <div>
            <span>研究对象</span>
            <span v-html="subjectSetting.studyObject" />
          </div>
          <div>
            <span />
            <span />
          </div>
          <div>
            <span>分组设计</span>
            <span v-html="subjectSetting.groupingDesign" />
          </div>
          <div>
            <span>盲法设置</span>
            <span v-html="subjectSetting.blindType" />
          </div>
          <div>
            <span>随机方式</span>
            <span v-html="subjectSetting.randomType" />
          </div>
          <div>
            <span>最新版本</span>
            <span v-html="subjectSetting.currentVersion" />
          </div>
          <div>
            <span>当前版本 </span>
            <span v-html="subjectSetting.lastVersion" />
          </div>
          <div>
            <span />
            <span />
          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="content" style="display:none">
        <div class="content-top">自定义信息</div>
        <div class="content-bottom">
          <p>可自定义项目LOGO、项目介绍、</p>
          <p>项目结束语</p>
        </div>
      </div>
      <div class="content" style="display:none">
        <div class="content-top">研究中心</div>
        <div class="content-bottom">
          <p>共10个研究中心</p>
          <p>9个激活，1个失活</p>
        </div>
      </div>
      <div
        v-if="subjectSetting.ieQuestStatus.lastOfficalVersion"
        class="content"
        @click="routerGo('/home/<USER>')"
      >
        <div class="content-top">
          编辑初筛问卷
          <span
            v-if="subjectSetting.ieQuestStatus.hasActivateVersion"
            >待升级</span
          >
        </div>
        <div class="content-bottom">
          <p>
            最新正式版本：{{
              subjectSetting.ieQuestStatus.lastOfficalVersion
            }}
          </p>
          <p>
            提交于{{
              subjectSetting.ieQuestStatus.publishDate
            }}
          </p>
        </div>
      </div>
      <div class="content" v-else @click="routerGo('/home/<USER>')">
        <div class="content-top">编辑初筛问卷</div>
        <div class="content-bottom">
          <p>暂无正式版本信息</p>
          <p>请先同步入排问卷版本并提交测试版本</p>
        </div>
      </div>
      <div
        class="content"
        @click="routerGo('/home/<USER>')"
        v-if="subjectSetting.crfStatus.lastOfficalVersion"
      >
        <div class="content-top">
          编辑CRF
          <span
            v-if="subjectSetting.crfStatus.hasActivateVersion"
            >待升级</span
          >
        </div>
        <div class="content-bottom">
          <p>
            最新正式版本：{{
              subjectSetting.crfStatus?.lastOfficalVersion
            }}
          </p>
          <p>
            提交于{{
              subjectSetting.crfStatus?.publishDate
            }}
          </p>
        </div>
      </div>
      <div
        class="content"
        v-else
        @click="routerGo('/home/<USER>')"
      >
        <div class="content-top">编辑CRF</div>
        <div class="content-bottom">
          <p>暂无正式版本信息</p>
          <p>请先同步CRF版本并提交测试版本</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import {
  getDropStudyId, // 课题设置信息
  getDropStudySyn, // 同步课题信息
  postDropStudySyncVersion, // 同步CRF版本
} from '@/api/home'
export default defineComponent({
  name: 'SubjectSetting',
  setup() {
    const store = useStore()
    const router = useRouter()
    // const route = useRoute();
    const state = reactive({
      subjectSetting: {
        studyId: '',
        edcStudyId: 0,
        studyName: '',
        studyCode: '',
        currentVersion: '',
        lastVersion: '',
        groupingDesign: '',
        studyObject: '',
        blindType: '',
        randomType: '',
        studyVersions: [],
        studyExecutedStatus: 0,
        questPublishStatus: 0,
        ieQuestStatus: {
          hasActivateVersion: Boolean,
          lastOfficalVersion: '',
          publishDate: null,
        },
        crfStatus: {
          hasActivateVersion: Boolean,
          lastOfficalVersion: '',
          publishDate: null,
        },
      },
      // 同步信息
      synchronization: (flag) => {
        if (flag === 'crf') {
          if (
            state.subjectSetting.lastVersion ===
            state.subjectSetting.currentVersion
          ) {
            ElMessage.warning('已是最新版本,无需同步')
          } else {
            if (
              state.subjectSetting.ieQuestStatus.hasUpgradeActivateVersion ||
              state.subjectSetting.crfStatus.hasUpgradeActivateVersion
            ) {
              ElMessageBox.confirm(
                `当前存在未提交的测试版本，再次同步后可能会导致测试版本部分数据丢失，是否确认？`,
                '提示',
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                }
              )
                .then(() => {
                  ElMessageBox.confirm(`同步中，请稍后`, {
                    showCancelButton: false,
                    showConfirmButton: false,
                    showClose: false,
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                  })
                  postDropStudySyncVersion(store.state.studyItem.studyId).then(
                    () => {
                      ElMessageBox.close()
                      ElMessage.success('同步成功')
                      state.onLoad()
                    }
                  )
                })
                .catch(() => {})
            } else {
              ElMessageBox.confirm(`是否确认同步CRF版本？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
              })
                .then(() => {
                  ElMessageBox.confirm(`同步中，请稍后`, {
                    showCancelButton: false,
                    showConfirmButton: false,
                    showClose: false,
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                  })
                  postDropStudySyncVersion(store.state.studyItem.studyId).then(
                    () => {
                      ElMessageBox.close()
                      ElMessage.success('同步成功')
                      state.onLoad()
                    }
                  )
                })
                .catch(() => {})
            }
          }
          // ElMessageBox.confirm(
          //   `提交后，将升级为正式版本，部分数据将无法编辑，是否确认提交测试版本？`,
          //   '提示',
          //   {
          //     confirmButtonText: '确定',
          //     cancelButtonText: '取消',
          //     // type: 'warning',
          //   }
          // )
          //   .then(() => {
          //     // addOcrDataMap(row.id).then(() => {
          //     //   ElMessage.success(`提交成功!`)
          //     //   state.refresh()
          //     // })
          //   })
          //   .catch(() => {})
        } else if (flag === 'topic') {
          ElMessageBox.confirm(
            `是否确认同步项目基本信息、中心信息，账号信息？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
            }
          )
            .then(() => {
              ElMessageBox.confirm(`同步中，请稍后`, {
                showCancelButton: false,
                showConfirmButton: false,
                showClose: false,
                closeOnClickModal: false,
                closeOnPressEscape: false,
              })
              getDropStudySyn(store.state.studyItem.studyId).then(() => {
                ElMessageBox.close()
                ElMessage.success('同步成功')
                state.onLoad()
              })
            })
            .catch(() => {})
        }
      },
      // 跳转
      routerGo: (path) => {
        router.push({
          path,
        })
      },
      onRefresh: () => {},
      // 获取数据
      onLoad: () => {
        getDropStudyId(store.state.studyItem.studyId).then((res: any) => {
          state.subjectSetting = res
        })
      },
    })

    onMounted(() => {
      state.onLoad()
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
<style lang="scss" scoped>
.subjectSetting {
  width: 100%;
  .head {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 0;
    box-sizing: border-box;
    .headTitle {
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      h3 {
        padding: 0;
        margin: 0;
      }
      div {
        display: flex;
        span {
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          border-radius: 5px;
          color: #fff;
          font-size: 12px;
          &:first-child {
            background: orange;
            margin-right: 20px;
          }
          &:last-child {
            background: skyblue;
          }
        }
      }
    }
    .head-bottom {
      padding: 0 80px;
      margin-top: 20px;
      div {
        span {
          &:first-child {
            color: rgb(156, 152, 152);
          }
          &:last-child {
            margin-left: 10px;
          }
        }
      }
      .head-boxBottom {
        margin: 0;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        div {
          width: 30%;
          margin-top: 20px;
        }
      }
    }
  }
  .container {
    width: 50%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    flex-wrap: wrap;
    .content {
      width: 48%;
      border: 1px solid black;
      margin-top: 20px;
      &:hover{
        cursor: pointer;
      }
      .content-top {
        width: 100%;
        height: 50px;
        line-height: 50px;
        text-align: center;
        background: rgb(55, 66, 81);
        color: #fff;
        position: relative;
        span {
          display: block;
          padding: 0 10px;
          height: 20px;
          line-height: 20px;
          font-size: 10px;
          position: absolute;
          top: 0;
          right: 0;
          background: rgb(255, 102, 0);
          border-radius: 2px;
        }
      }
      .content-bottom {
        box-sizing: border-box;
        padding: 20px 20px;
        background: #fff;
        text-align: center;
      }
    }
  }
}
</style>
