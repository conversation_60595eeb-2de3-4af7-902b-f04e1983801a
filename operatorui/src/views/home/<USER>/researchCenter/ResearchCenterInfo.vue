<template>
  <div class="ResearchCenterInfo">
    <el-tabs
      v-model="activeName"
      type="card"
      class="demo-tabs"
      @tab-click="tabsHandleClick"
    >
      <el-tab-pane label="基本信息" name="Infos">
        <el-form
          ref="researchCenterInfoRef"
          label-position="top"
          :model="researchContent"
          :rules="rules"
        >
          <el-form-item
            label="中心编号"
            :label-width="formLabelWidth"
            prop="edcSiteCode"
            style="width: 30%"
          >
            <el-input
              v-model.trim="researchContent.edcSiteCode"
              autocomplete="off"
              disabled
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="中心类别"
            :label-width="formLabelWidth"
            prop="siteCategory"
            style="width: 30%"
          >
            <el-input
              v-model.trim="researchContent.siteCategory"
              autocomplete="off"
              disabled
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="EDC中心状态"
            :label-width="formLabelWidth"
            prop="edcSiteStatus"
            style="width: 30%"
          >
            <el-input
              :placeholder="
                researchContent.edcSiteStatus == '1' ? '激活' : '失活'
              "
              autocomplete="off"
              disabled
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="EDC中心名称"
            :label-width="formLabelWidth"
            prop="edcSiteName"
            disabled
            style="width: 60%"
          >
            <el-input
              v-model.trim="researchContent.edcSiteName"
              disabled
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="EDC计划入组例数"
            :label-width="formLabelWidth"
            prop="edcPlanNumber"
            style="width: 30%"
          >
            <el-input
              v-model.trim="researchContent.edcPlanNumber"
              autocomplete="off"
              disabled
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="中心名称"
            :label-width="formLabelWidth"
            prop="siteName"
            disabled
            style="width: 60%"
          >
            <el-input
              v-model.trim="researchContent.siteName"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="计划入组例数"
            :label-width="formLabelWidth"
            prop="planNumber"
            style="width: 30%"
          >
            <el-input
              v-model.trim="researchContent.planNumber"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
        </el-form>
        <!-- 中心成员列表table -->
        <trial-table
          ref="centerMemberRef"
          title="中心成员列表"
          :request="centerMemberGetList"
          :columns="columns"
          :search="false"
          :pagination="false"
        >
          <template #avatarUrl="scope">
            <img
              :src="scope.row.avatarUrl"
              style="width: 70px; border-radius: 50%"
              alt=""
            >
          </template>
          <template #isDeleteSlot="scope">
            <div>{{ scope.row?.isDelete ? '未授权' : '已授权' }}</div>
          </template>
          <template #operate="scope">
            <el-button size="small" type="primary" text @click="editDctRoleForm(scope.row)">
              设置
            </el-button>
          </template>
        </trial-table>
        <DctRoleForm
          ref="DctRoleFormRef"
          :request="centerMemberRefRefresh"
        />
      </el-tab-pane>

      <el-tab-pane label="联络信息" name="Relevance">
        <el-form
          ref="contactObjRef"
          label-position="top"
          :model="contactObj"
          :rules="rules"
        >
          <el-form-item
            label="联络人姓名"
            :label-width="formLabelWidth"
            prop="name"
            style="width: 30%; minwidth: 400px; margin: 0 50% 0 0"
          >
            <el-input
              v-model.trim="contactObj.name"
              placeholder="请填写姓名"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="联络人手机号"
            :label-width="formLabelWidth"
            prop="mobile"
            style="width: 30%; minwidth: 400px; margin: 0 50% 0 0"
          >
            <el-input
              v-model.trim="contactObj.mobile"
              placeholder="请填写手机号"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="省市区"
            :label-width="formLabelWidth"
            prop="citys"
            style="width: 30%; minwidth: 400px; margin: 0 50% 0 0"
          >
            <el-cascader
              v-model="contactObj.citys"
              clearable
              style="width: 95%"
              :options="cityOptions"
              :props="{
                expandTrigger: 'hover',
              }"
            />
          </el-form-item>
          <el-form-item
            label="详细地址"
            :label-width="formLabelWidth"
            prop="addressDetail"
            style="width: 50%; minwidth: 600px"
          >
            <el-input
              v-model.trim="contactObj.addressDetail"
              placeholder="请填写地址"
              type="textarea"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="伦理信息" name="EthicInfo">
        <el-form
          ref="ethicInfoRef"
          label-position="top"
          :model="ethicsCommitteeInfo"
          :rules="rules"
        >
          <el-form-item
            label="中心伦理电话（按需合成知情同意书）"
            :label-width="formLabelWidth"
            prop="name"
            style="width: 30%; minwidth: 400px; margin: 0 50% 0 0"
          >
            <el-input
              v-model.trim="ethicsCommitteeInfo.ethicsCommitteeMobile"
              placeholder="请输入"
              autocomplete="off"
              maxlength="11"
            />
          </el-form-item>
        </el-form>
        <el-button
          style="margin-top: 100px;display: none;"
          type="primary"
          @click="setQuestionnaire()"
        >绑定知情版本</el-button>
      </el-tab-pane>
    </el-tabs>

    <!-- 保存按钮 -->
    <div class="delete-pos-tr">
      <el-button size="large" @click="backRefresh">返回</el-button>
      <el-button
        size="large"
        type="primary"
        :loading="loading"
        @click="submitInfos"
      >保存</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  getCitys,
  getSiteDetail,
  postSite,
  postRelationBuilding,
} from '@/api/home'
import { useStore } from 'vuex'
import DctRoleForm from '@/views/home/<USER>/researchCenter/DctRoleForm.vue'
import { deepClone } from '@trialdata/common-fun-css/index'
import { getRoles } from '@/api/approvalProcess'

export default defineComponent({
  name: 'ResearchCenterInfo',
  components: {
    DctRoleForm
  },
  // props: {
  //   // 请求数据的方法
  //   request: {
  //     type: Function,
  //     default: () => {},
  //   },
  // },
  setup() {
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      DctRoleFormRef: null,
      researchContent: {
        dctSort: null,
        fieldLabel: '',
        id: null,
        siteName: '',
        planNumber: null,
        addressInfo: {
          // 联络信息
          addressId: null,
          name: null,
          mobile: null,
          province: null,
          city: null,
          area: null,
          provinceCode: null,
          cityCode: null,
          areaCode: null,
          addressDetail: null,
        },
      },
      // oldQuestionnaireContent: {}, // 未保存前的基本信息对象
      formLabelWidth: '140px',
      handleResetRef: null,
      researchCenterInfoRef: null, // 基本信息ref
      centerMemberRef: null, // 中心成员列表Ref
      contactObjRef: null, // 联络信息Ref
      ethicInfoRef: null, // 伦理信息Ref
      cityOptions: [
        // 城市
      ],
      loading: false,
      activeName: 'Infos', // tab高亮
      // 联络信息对象
      contactObj: {},
      // 伦理信息对象
      ethicsCommitteeInfo: {},
      members: [],
      // 表格列配置
      /* avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/UdZhlPq6VxKtZ3fWjzofMPhBia8TboNa6TuRbEwA3E0jPNpRIHe597M4JEHp3nxWLvicXutPs93ntiamqrQBG9hHg/132"
      id: "0005d40a-6372-42b5-0000-000000000000"
      realName: "DemoPI"
      userName: "testPI2"
      userRole: null*/
      columns: [
        { label: '微信头像', width: 120, tdSlot: 'avatarUrl' },
        { label: '姓名', prop: 'realName', width: 200 },
        { label: 'EDC账号', prop: 'userName', width: 200 },
        { label: 'EDC角色', prop: 'userRole', width: 200 },
        { label: 'DCT角色', prop: 'dctUserRole', width: 200 },
        { label: '授权状态', width: 120, tdSlot: 'isDeleteSlot' },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      rules: {
        siteName: [
          { required: true, message: '请填写中心名称', trigger: 'blur' },
        ],
        // 计划入组例数
        planNumber: [
          { required: true, message: '请填写整数', trigger: 'blur' },
          {
            pattern: /^-?[1-9]\d*$|^0$/,
            message: '请填写整数',
            trigger: 'blur',
          },
        ],
      },
      DctRoleList: [],
      editDctRoleForm: (row) => {
        state.dctRoleFormData = row
        state.DctRoleFormRef.DctRoleFormData = deepClone(row)
        if (row?.dctUserRole) {
          state.DctRoleFormRef.DctRoleFormData.dctUserRole = row?.dctUserRole.split(',')
        }
        state.DctRoleFormRef.DctRoleDialogVisible = true
        getRoles(studyId).then((res) => {
          state.DctRoleFormRef.DctRoleList = res
        })
      },
      // 点击tab切换
      tabsHandleClick: (tab: string, event: Event) => {
        switch (tab.index) {
          case '0':
            break
          case '1':
            break
          // default: //
        }
      },
      setQuestionnaire: () => {
        postRelationBuilding(state.researchContent.id).then((res) => {
          ElMessage.success('绑定成功')
        })
      },
      // 刷新中心成员列表
      centerMemberRefRefresh: () => {
        state.centerMemberRef.refresh()
      },
      // 获取中心成员列表
      centerMemberGetList: async(params) => {
        try {
          // 必须要返回一个对象，包含data数组和total总数
          state.researchContent = RESEARCHCENTER_INFOS.researchContent
          const { members, addressInfo, ethicsCommitteeInfo, roles } =
            await getSiteDetail(studyId, state.researchContent.id)
          state.ethicsCommitteeInfo = ethicsCommitteeInfo
          state.members = { ...members }
          // state.DctRoleList = roles || []
          // state.ethicsCommitteeInfo =
          state.contactObj = addressInfo || {
            addressId: null,
            name: null,
            mobile: null,
            province: null,
            city: null,
            area: null,
            provinceCode: null,
            cityCode: null,
            areaCode: null,
            addressDetail: null,
          }
          if (
            state?.contactObj?.provinceCode &&
            state?.contactObj?.cityCode &&
            state?.contactObj?.areaCode
          ) {
            const { provinceCode, cityCode, areaCode } = state.contactObj
            state.contactObj.citys = [provinceCode, cityCode, areaCode]
          }

          return {
            data: members,
            total: +members.length,
          }
        } catch (e) {
          // console.log(e)
        }
      },

      // 基本信息or联络信息的保存
      submitInfos: () => {
        state.researchCenterInfoRef.validate((valid) => {
          if (valid) {
            // 手机号验证
            const zz = /^1[3-9][0-9]\d{8}$/
            if (
              state?.contactObj?.mobile &&
              !zz.test(state.contactObj.mobile)
            ) {
              ElMessage.error(`请输入正确的手机号`)
              return
            }
            // 取省市区label
            if (
              state?.contactObj?.citys &&
              state.contactObj.citys.length === 3
            ) {
              state.cityOptions.forEach((item) => {
                item.children.forEach((itemClild) => {
                  itemClild.children.forEach((itemClildClild) => {
                    if (itemClildClild.value === state.contactObj.citys[2]) {
                      state.contactObj.province = item.label
                      state.contactObj.city = itemClild.label
                      state.contactObj.area = itemClildClild.label
                      state.contactObj.provinceCode = state.contactObj.citys[0]
                      state.contactObj.cityCode = state.contactObj.citys[1]
                      state.contactObj.areaCode = state.contactObj.citys[2]
                      return
                    }
                  })
                })
              })
            }
            const paramsObj = {
              id: state.researchContent.id,
              siteName: state.researchContent.siteName,
              planNumber: state.researchContent.planNumber / 1,
              addressInfo: { ...state.contactObj },
              ethicsCommitteeInfo: { ...state.ethicsCommitteeInfo },
            }
            // mobile字段如果为空的话只能为null不能为空字符串
            if (!paramsObj.addressInfo?.mobile) {
              paramsObj.addressInfo.mobile = null
            }
            state.loading = true
            // 保存接口调用
            postSite(studyId, paramsObj)
              .then((res) => {
                ElMessage.success('保存成功')
                state.loading = false
              })
              .catch(() => {
                state.loading = false
              })
          } else if (state.activeName !== 'Infos') {
            ElMessage.error(`请先完善基本信息`)
          }
        })
      },
      // 返回刷新
      backRefresh: () => {
        RESEARCHCENTER_INFOS.contentVisible = false
        RESEARCHCENTER_INFOS.method.handleReset()
      },

      onLoad: () => {
        // 获取城市
        getCitys().then((res) => {
          if (res && res?.length && Array.isArray(res)) {
            state.cityOptions = res
          }
        })
        if (JSON.stringify(RESEARCHCENTER_INFOS.researchContent) !== '{}') {
          state.researchContent = RESEARCHCENTER_INFOS.researchContent
          state.contactObj = { ...RESEARCHCENTER_INFOS.researchContent }
        }
      },
    })

    onMounted(() => {
      state.onLoad()
    })

    return {
      ...toRefs(state),
      RESEARCHCENTER_INFOS,
    }
  },
})
</script>

<style lang="less" scoped>
.ResearchCenterInfo {
  position: relative;
  ::v-deep .title {
    font-size: 16px !important;
    font-weight: 300 !important;
  }
  .demo-tabs > .el-tabs__content {
    padding: 32px;
    background-color: #f4f5f7;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
  }
  .delete-pos-tr {
    position: absolute;
    top: 0px;
    right: 0;
  }
  ::v-deep .el-tabs {
    // min-height: 800px;
    min-height: calc(100vh - 120px);
    background: #fff;
  }
  ::v-deep .el-tabs__nav-scroll {
    background: #f0f2f5;
  }
  ::v-deep .el-tabs__nav is-top,
  ::v-deep [role='tablist'] {
    background: #fff;
  }
  ::v-deep .el-tabs__item,
  ::v-deep .is-top,
  ::v-deep .is-focus {
    min-width: 97px;
    text-align: center;
  }
  ::v-deep .el-tabs__content {
    padding: 30px;
  }
  .el-form,
  .el-form--default {
    display: flex;
    flex-wrap: wrap;
    .el-input,
    .el-select {
      margin: 0 20px 0 0;
    }
    .el-select {
      width: 100%;
    }
  }
  ::v-deep .el-table__cell,
  ::v-deep .is-leaf {
    border: none !important;
  }
  ::v-deep .el-table__inner-wrapper::before {
    height: 0;
  }
}
</style>
