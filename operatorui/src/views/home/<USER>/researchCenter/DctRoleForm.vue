<template>
  <trial-dialog
    v-model="DctRoleDialogVisible"
    :title="DctRoleFormData.realName"
    my-title-class="flex my-1 mb-8 real-name-br"
  >
    <template #DialogBody>
      <el-form
        ref="DctRoleFormRef"
        label-position="top"
        :model="DctRoleFormData"
        :rules="DctRoleRules"
      >
        <el-form-item
          label="DCT角色"
          :label-width="DctRoleFormLabelWidth"
          prop="dctUserRole"
        >
          <el-select
            v-model="DctRoleFormData.dctUserRole"
            class="w-8/12"
            placeholder="请选择"
            multiple
          >
            <el-option
              v-for="item in DctRoleList"
              :key="item.id"
              :label="item.roleName"
              :value="item.roleId"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="centerflex mt-36">
        <el-button plain @click="closeDialogVisible">取消</el-button>
        <el-button
          :loading="DctRoleLoading"
          type="primary"
          @click="DctRoleSubmit"
        >确定</el-button>
      </div>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { postDctUserRole } from '@/api/home'

export default defineComponent({
  name: 'DctRoleForm',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const store = useStore()
    const { studyId } = store.state.studyItem

    const state = reactive({
      DctRoleFormData: {
        id: '',
        realName: '',
        dctUserRole: '',
      },
      DctRoleList: [],
      DctRoleFormRef: null,
      DctRoleDialogVisible: false,
      DctRoleFormLabelWidth: '86px',
      DctRoleRules: {
        dctUserRole: [{ required: true, message: '请选择', trigger: 'blur' }],
      },
      DctRoleLoading: false,
      closeDialogVisible: () => {
        state.DctRoleDialogVisible = false
      },
      DctRoleSubmit: () => {
        state.DctRoleFormRef.validate((valid) => {
          if (valid) {
            state.DctRoleLoading = true
            const RoleFormData = {
              userId: state.DctRoleFormData.id,
              roleIds: state.DctRoleFormData.dctUserRole,
              siteId: state.DctRoleFormData.siteId,
            }
            postDctUserRole(studyId, RoleFormData)
              .then(() => {
                props.request()
                state.DctRoleLoading = false
                ElMessage.success('设置成功')
                state.closeDialogVisible()
              }).catch(() => {
                state.DctRoleLoading = false
              })
          }
        })
      },
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>
