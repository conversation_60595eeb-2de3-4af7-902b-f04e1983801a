<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="researchCenter-headTitle">
        <h3>研究中心</h3>
        <el-button
          size="large"
          @click="routerGo('back')"
        >返回</el-button>
      </div>
      <!-- @selectionChange="handleSelectionChange" -->
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
      >
        <template #edcSiteStatusSlot="scope">
          <span>
            {{ scope?.row?.edcSiteStatus ? '激活' : '失活' }}
          </span>
        </template>
        <template #operate="scope">
          <span class="editBtn" @click="editResearchCenterInfoItem(scope.row)">
            编辑
          </span>
        </template>
      </trial-table>
    </div>

    <ResearchCenterInfo v-if="RESEARCHCENTER_INFOS?.contentVisible" />
  </div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, provide, reactive, toRefs } from 'vue'
import ResearchCenterInfo from '@/views/home/<USER>/researchCenter/ResearchCenterInfo.vue'
import { getSites } from '@/api/home'

export default defineComponent({
  name: 'ResearchCenter', // 研究中心
  components: {
    ResearchCenterInfo
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      method: {}, // 获取重置方法
    })

    const state = reactive({
      researchCenterRef: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        { label: '中心编号', prop: 'edcSiteCode', width: 180 },
        { label: '中心类别', prop: 'siteCategory', width: 200 },
        { label: '中心名称', prop: 'siteName', width: 400 },
        { label: '计划入组列数', prop: 'planNumber', width: 180 },
        { label: '中心状态', width: 200, tdSlot: 'edcSiteStatusSlot' },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'select',
            label: '中心名称',
            name: 'siteName',
            defaultValue: null,
            options: store.state.studyItem.sites,
            filterable: true,
          },
          {
            label: '中心状态',
            name: 'status',
            type: 'select',
            defaultValue: null,
            options: [
              {
                value: 0,
                name: '失活'
              },
              {
                value: 1,
                name: '激活'
              },
            ],
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // researchCenterList: [],

      // 选择
      // handleSelectionChange(arr) {
      //   state.researchCenterList = arr
      // },
      // 请求研究中心列表
      async getResearchCenterList(params) {
        try {
          const { studyId } = store.state.studyItem
          const myParams = { ...params }
          if (!myParams.status && myParams.status !== 0) { // -1代表所有状态
            myParams.status = -1
          }
          const rest = await getSites(studyId, myParams)

          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.pagedSites.items,
            total: +rest.pagedSites.totalItemCount
          }
        } catch (e) {
          // console.log(e)
        }
      },
      // 编辑某项中心
      editResearchCenterInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.method = state.researchCenterRef
      },
      // 跳转
      routerGo: (path) => {
        if (path === 'back') {
          router.replace('/home')
          store.state.refreshFlag = true
          return
        }
        router.push({
          path,
        })
      },
      // 获取数据pageSize: 0时可取到课题数组
      onLoad: async() => {
        const { studyId } = store.state.studyItem
        const myParams = { pageIndex: 0, pageSize: 0 }
        try {
          const { allSiteNames, allStatus } = await getSites(studyId, myParams)
          const newAllSiteNames = []
          const newAllStatus = []
          if (Array.isArray(allSiteNames) && allSiteNames.length) {
            allSiteNames.forEach((item) => {
              newAllSiteNames.push({
                name: item,
                value: item
              })
            })
            state.searchConfig.fields[0].options = newAllSiteNames
          }
          if (Array.isArray(allStatus) && allStatus.length) {
            allStatus.forEach((item) => {
              newAllStatus.push({
                name: item.value,
                value: item.key
              })
            })
            state.searchConfig.fields[1].options = newAllStatus
          }
        } catch (e) {
          // console.log(e)
        }
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)

    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.researchCenter {
  width: 100%;
  min-height: 100%;
  .researchCenter-headTitle {
    width: 100%;
    margin: 0 0 10px 0;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3 {
      padding: 0;
      margin: 0;
    }
  }
  .editBtn{
    color: #409eff;
    &:hover {
      cursor: pointer; /*小手*/
    }
  }
}
</style>
