<template>
  <div>
    <div
      v-if="!editionList.length"
      style="display: flex; justify-content: space-between; align-items: center"
    >
      暂无版本信息，请先同步CRF版本
      <el-button plain @click="setAddVersionsOrpreview('back')">返回</el-button>
    </div>
    <!-- 如果版本有信息则显示表格 -->
    <div v-else class="SupervisionManagement">
      <div v-if="stateChange.stateChangeFalg">
        <nav class="nav-module">
          <div class="nav-btn-le">
            <el-select
              v-model="versionsValue"
              placeholder="请选择版本号"
              class="!w-auto"
              @change="getCrfSurface"
            >
              <el-option
                v-for="(item, index) in editionList"
                :key="index"
                :label="item.label"
                :value="item.studyVersionId"
                @click="editionListBtn(item, index)"
              />
            </el-select>
          </div>
          <div class="nav-btn-ri">
            <el-button
              plain
              @click="setAddVersionsOrpreview('back')"
            >返回</el-button>
            <el-button
              v-if="crfSurface?.activateState === 2"
              type="primary"
              @click="setAddVersionsOrpreview('add')"
            >提交测试版本</el-button>
            <el-button
              v-if="crfSurface?.activateState === 2"
              type="danger"
              @click="setAddVersionsOrpreview('reset')"
            >重置</el-button>
          </div>
        </nav>
        <!-- :header-row-class-name="tableHeaderColor"
    :header-cell-style
    :header-row-style
    -->
        <div class="infos-body">
          <el-select v-model="versionsFrom" class="!w-auto">
            <el-option label="全局" value="全局" />
          </el-select>
          <el-button type="primary" class="ml-[12px]" @click="addRowColumn(0)">添加问卷</el-button>
          <!-- <el-button v-if="versionStatus === 1" type="danger" @click="addRowColumn(3)">删除问卷</el-button> -->
          <el-button type="primary" @click="addRowColumn(1)">添加访视</el-button>
          <!-- <el-button v-if="versionStatus === 1" type="danger" @click="addRowColumn(2)">删除访视</el-button> -->
          <el-button type="primary" @click="addRowColumn(4)">建立联系</el-button>
          <el-button type="danger" @click="addRowColumn(5)">删除联系</el-button>
          <!-- height="700" -->
          <el-table
            class="table"
            :style="{'width': myWidth}"
            :data="tableData"
            :height="tableHeight"
            :header-cell-style="tableHeaderColor"
            :header-cell-class-name="headerClassName"
            :cell-style="cellStyle"
            @header-click="headerClick"
          >
            <!-- 如果父子相同, 那么显示子的名字 一遍修改 -->
            <!-- 如果父子相同, 那么用子的property, 这样, 点击就生效-->
            <!-- 9.20要求修改 -->
            <!-- :prop="item.parentIsChild == 1 ? item.clid[0].prop : item.prop"
              :label="item.parentIsChild == 1 ? item.clid[0].label : item.label" -->
            <el-table-column
              v-for="(item, index) in tableLabel"
              :key="index"
              :prop="item.prop"
              :label="item.label"
              show-overflow-tooltip
              :fixed="index === 0 ? 'left' : false"
            >
              <template #header>
                <div class="tableHeaderbBox">
                  {{ item?.fatherVisitName }}
                  <img
                    v-if="!item?.dctVisitDisplay && Array.isArray(item.clid) && item?.clid[0]?.dctVisitDisplay === 0"
                    class="childColumnShowEyes"
                    src="@/assets/svg/eye.svg"
                  >
                  <div v-else-if="Array.isArray(item.clid) && (cellStyleArr.includes(item.dctVisitDisplay) || cellStyleArr.includes(item?.clid[0]?.dctVisitDisplay))" class="childColumnShowMoves" />
                </div>
              </template>
              <!--如果父子相同,根据子是否需要显示研究来设置眼睛标记 -->
              <template v-if="!item?.clid?.length" #default="scope">
                <div
                  class="tableBox"
                  @click="leftClick(scope.row, index, false, $event)"
                >
                  <!-- <el-icon><timer /></el-icon> -->
                  <!-- {{item.prop}} -->
                  <span
                    v-if="index === 0 "
                    class="tableOne"
                    :class="{ befo: scope.row.checked }"
                  >{{ scope.row[item.prop] }}
                  </span>
                </div>
                <img v-if="scope.row.questTemplateType == 1 || scope.row.questTemplateType == 2" src="@/assets/svg/eye.svg" class="tableThree">
                <!-- 如果父子相同, check由父来控制-->
                <div v-if="item?.parentIsChild == 1" class="tableBox">
                  <span class="tableTow">{{ scope.row.crfValues[+item.clid[0].prop.substr(1)] ? '√' : '' }}</span>
                </div>
              </template>
              <!-- 如果父子相同, check由父来控制-->
              <!-- <template v-if="item?.parentIsChild == 1" #default="scope">
                <div  class="tableBox">
                  <span class="tableTow">{{ scope.row.crfValues[+item.clid[0].prop.substr(1)] ? '√' : '' }}</span>
                </div>
              </template> -->
              <!--如果父子相同则不显示子-->
              <!-- v-if="item.parentIsChild != 1"-->
              <el-table-column
                v-for="(clitem, clindex) in item.clid"
                :key="clindex"
                :prop="clitem.prop"
                :label="clitem.label"
                :formatter="Formatter"
              >
                <template #header>
                  <div class="tableHeaderbBox">
                    {{ clitem?.visitName }}
                    <img
                      v-if="!clitem.dctVisitDisplay"
                      class="childColumnShowEyes"
                      src="@/assets/svg/eye.svg"
                    >
                    <div v-else-if="cellStyleArr.includes(clitem.dctVisitDisplay)" class="childColumnShowMoves" />
                  </div>
                </template>
              </el-table-column>
              <!-- 根据标记显示眼睛与否 -->
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div v-if="!stateChange.stateChangeFalg">
        <VisitManagement :state-change="stateChange" :request-load="onLoad" />
      </div>

      <!-- 添加行列的弹出框 -->
      <trial-dialog v-model="addFalg" :title="addCrfTitle">
        <template #DialogBody>
          <div v-show="addFalg" :my-title="addCrfTitle">
            <el-form
              ref="addCrfDesignationRef"
              :model="addFrom"
              :rules="addCrfDesignationRules"
              :label-width="labelWidth"
            >
              <!-- 添加行或列 -->
              <el-form-item v-if="addRowColumnNumber === 1 || addRowColumnNumber === 0" label="添加名称" prop="fatherVisitName">
                <el-input v-model.trim="addFrom.fatherVisitName" autocomplete="off" />
              </el-form-item>
              <el-form-item v-if="addRowColumnNumber === 1 || addRowColumnNumber === 0" :label="addRowColumnNumber === 0 ? '问卷排序' : '访视排序' " prop="dctSort">
                <el-input v-model.trim="addFrom.dctSort" autocomplete="off" />
              </el-form-item>
              <!-- 删除列 -->
              <el-form-item
                v-if="addRowColumnNumber === 2"
                label="删除访视"
                prop="deleteLineId"
                style="width: 50%"
              >
                <el-select
                  v-model="addFrom.deleteLineId"
                  placeholder="请选择删除访视"
                >
                  <!-- item.parentIsChild == 1 ? item.clid[0].label : item.label -->
                  <el-option
                    v-for="item in tableLabel.slice(1)"
                    :key="item.id"
                    :label="item.label"
                    :value="item?.id"
                  />
                </el-select>
              </el-form-item>
              <!-- 删除行 -->
              <el-form-item
                v-if="addRowColumnNumber === 3"
                label="删除问卷"
                prop="deleteRowId"
                style="width: 50%"
              >
                <el-select v-model="addFrom.deleteRowId" placeholder="请选择删除问卷">
                  <el-option
                    v-for="item in tableData"
                    :key="item.id"
                    :label="item?.crfName"
                    :value="item?.moduleContainer?.id"
                  />
                </el-select>
              </el-form-item>
              <!-- 建立联系 -->
              <el-form-item
                v-if="addRowColumnNumber === 4 || addRowColumnNumber === 5"
                label="选择联系访视"
                prop="deleteLineChildId"
                style="width: 50%"
              >
                <el-select
                  v-model="addFrom.deleteLineChildId"
                  placeholder="请选择联系访视"
                >
                  <el-option
                    v-for="item in tableLabelVisitTemplates"
                    :key="item.id"
                    :label="item?.visitName"
                    :value="item?.id"
                  />
                </el-select>
              </el-form-item>
              <!-- 删除行 -->
              <el-form-item
                v-if="addRowColumnNumber === 4 || addRowColumnNumber === 5"
                label="选择联系问卷"
                prop="deleteRowChildId"
                style="width: 50%"
              >
                <el-select v-model="addFrom.deleteRowChildId" placeholder="请选择联系问卷">
                  <el-option
                    v-for="item in tableData"
                    :key="item.id"
                    :label="item?.crfName"
                    :value="item?.id"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </template>
        <template #footer>
          <div class="footer-flex-end">
            <el-button size="large" @click="cancle">取消</el-button>
            <el-button size="large" type="primary" @click="addDeleteCrf(addRowColumnNumber)">确定</el-button>
          </div>
        </template>
      </trial-dialog>
    </div>
  </div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  provide,
  onBeforeUnmount,
  nextTick
} from 'vue'
import {
  getDropStudyVersionsCRF, // 获取课题版本
  getStudyCrf, // 获取CRF表
  postCrfPublish, // 提交测试版本
  resetCrfPublish, // 重置测试版本
  postVisitContainer, // 添加列
  addModuleContainer, // 添加行
  buildCrfRelation, // 建立联系
  deleteBuildCrfRelation, // 删除联系
} from '@/api/home'
import VisitManagement from '@/views/home/<USER>/VisitManagement.vue'

export default defineComponent({
  name: 'SupervisionManagement', // 编辑CRF模块管理
  components: {
    VisitManagement,
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const QUESTIONNAIRE_INFOS = reactive({
      questionnaireInfo: {
        name: '入排问卷',
        nature: '患者问卷',
        synchronization: '同步至EDC',
        style: '向导式问卷',
        type: '入排',
        explain: '可填写与该问卷相关的说明内容，帮助用户更好地完成问卷',
      },
      dialogVisible: false,
    })

    const state = reactive({
      cellStyleArr: [1, 8, 9],
      editionList: [], // CRF版本
      newVisitTemplates: [],
      versionsFrom: '',
      tableHeight: 700,
      stateChange: {
        stateChangeFalg: true,
        tabChangeFalg: true,
        id: '',
        dctVisitContainerId: '',
        dctModuleContainerId: '',
        label: '',
        prop: '',
        patientVisitType: '',
        edcQuestName: '',
        versionsValue: '',
        versionStatus: 0,
        index: 0,
      },
      // CRF表
      crfSurface: {
        activateState: '',
        crfFatherColumns: [],
        crfRow: [],
        crfValues: [],
      },

      // 列表头 top
      tableLabel: [],
      // 行表头
      tableData: [],

      versionsValue: '',
      myWidth: '99%',
      addCrfTitle: '',
      tableLabelVisitTemplates: [], // 上面第二排
      addCrfDesignationRules: {
        fatherVisitName: [
          {
            required: true,
            message: '请输入名称',
            trigger: 'blur',
          },
        ],
        deleteLineId: [
          {
            required: true,
            message: '请选择要删除的访视',
            trigger: 'blur',
          }
        ],
        deleteRowId: [
          {
            required: true,
            message: '请选择要删除的问卷',
            trigger: 'blur',
          }
        ],
        deleteLineChildId: [
          {
            required: true,
            message: '请选择访视',
            trigger: 'blur',
          }
        ],
        deleteRowChildId: [
          {
            required: true,
            message: '请选择问卷',
            trigger: 'blur',
          }
        ],
        dctSort: [
          {
            required: true,
            message: '请输入序号',
            trigger: 'blur',
          },
          {
            pattern: /^[1-9]\d*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ]
      },
      labelWidth: '',
      buildCrfRelationLable: '',
      addCrfDesignationRef: null,
      // 控制表单的显示
      addFalg: false,
      // 表单
      addFrom: {
        fatherVisitName: '',
        deleteLineId: '',
        deleteRowId: '',
        deleteLineChildId: '',
        deleteRowChildId: '',
        dctSort: ''
      },
      addRowColumnNumber: 0,
      versionStatus: 0,
      addRowColumn: (number) => {
        state.addFalg = true
        if (number === 0) {
          state.addCrfTitle = '添加问卷'
          state.labelWidth = '80px'
        } else if (number === 1) {
          state.addCrfTitle = '添加访视'
          state.labelWidth = '80px'
        } else if (number === 4) {
          state.addCrfTitle = '建立联系'
          state.labelWidth = '120px'
        } else if (number === 5) {
          state.addCrfTitle = '删除联系'
          state.labelWidth = '120px'
        }
        state.addRowColumnNumber = number
      },
      // 添加删除行列
      addDeleteCrf: (number) => {
        state.addCrfDesignationRef.validate((valid) => {
          if (valid) {
            if (number === 0) {
              const data = {
                dctStudyVersionId: state.versionsValue,
                questName: state.addFrom?.fatherVisitName,
                dctSort: state.addFrom?.dctSort
              }
              addModuleContainer(data).then(() => {
                ElMessage.success('保存成功')
                state.resetFrom()
                state.onLoad()
              }).catch(() => {
                // state.loading = false
                state.resetFrom()
              })
            } else if (number === 1) {
              const data = {
                dctStudyVersionId: state.versionsValue,
                fatherVisitName: state.addFrom?.fatherVisitName,
                dctSort: state.addFrom?.dctSort
              }
              postVisitContainer(data).then(() => {
                ElMessage.success('保存成功')
                state.resetFrom()
                state.onLoad()
              }).catch(() => {
                // state.loading = false
                state.resetFrom()
              })
            } else if (number === 4) {
              buildCrfRelation(state.addFrom.deleteRowChildId, state.addFrom.deleteLineChildId).then((res) => {
                ElMessage.success(`建立成功!`)
                state.resetFrom()
                state.onLoad()
              })
            } else if (number === 5) {
              ElMessageBox.confirm(`是否确认删除？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              }).then(() => {
                deleteBuildCrfRelation(state.addFrom.deleteRowChildId, state.addFrom.deleteLineChildId).then((res) => {
                  ElMessage.success(`删除成功!`)
                  state.resetFrom()
                  state.onLoad()
                })
              }).catch(() => {
                state.resetFrom()
              })
            }
          }
        })
      },
      // 关闭
      handleClose: () => {
        state.resetFrom()
      },
      // 取消
      cancle: () => {
        state.resetFrom()
      },
      // 重置
      resetFrom: () => {
        state.addFalg = false
        state.addCrfDesignationRef.resetFields()
      },
      // 子单元格中的值
      Formatter: (row, column) => {
        // if(column)
        // column.property 就是绑定上去的prop的值， 这个值必须是字符串，所以放了前置_
        // 其实代表的是列的索引 （不包含列头）。
        // 我们用subStr排除第一个字符（也可以用unshift等其他方法）
        // 取出后是一个数字， 我们用+来强制转换为数字
        // 这个数字对应的就是crfValues里面对应的序号。
        const ret =
          row.crfValues[+column.property.substr(1)] === 'Checkd' ? '√' : ''
        return ret
        // return row[column.property] == 'Checkd' ? '√' : ''
      },
      // 表头style设置
      tableHeaderColor: ({ row, column, rowIndex, columnIndex }) => {
        if (column.level === 2) {
          return {
            'background-color': '#E3EAF5',
            'text-align': 'center',
            color: '#000',
            'font-size': '10px',
            // 'white-space': 'nowrap',
            // 'overflow': 'hidden',
            // 'text-overflow': 'ellipsis'
          }
        } else {
          return {
            'background-color': '#C7D5ED',
            'text-align': 'center',
            color: '#000',
            'font-size': '10px',
            // 'white-space': 'nowrap',
            // 'overflow': 'hidden',
            // 'text-overflow': 'ellipsis'
          }
        }
        // return 'skyblue-he'
      },
      // 表头class
      headerClassName: ({ row, column, rowIndex, columnIndex }) => {
        // console.log(row, column, rowIndex, columnIndex)
        // if (
        //   row[columnIndex].label ===
        //   state.tableLabel[columnIndex]?.fatherVisitName
        // ) {
        // }

        if (rowIndex > 0 && columnIndex > 0) {
          const allColumns = state.tableLabel.reduce(function(a, b) {
            if (b && b.clid) {
              return a.concat(b.clid)
            }
            return a
          }, [])
          if (allColumns[columnIndex].patientVisitType === 1) {
            // document // 这个弃用了, 直接在表格上加样式
            //   .getElementsByClassName(`${column.id}`)[0]
            //   ?.classList?.add('eys')
          }
        }

        const cellArr = document.getElementsByClassName(`cell`)
        const cellArrFrom = Array.from([...cellArr])
        for (let i = 0; i < cellArrFrom.length; i++) {
          cellArrFrom[i].classList?.add('hove-tooltip')
        }

        // 千万不要使用column的id去做判断，这个id是会改变的
        // console.log(document.getElementsByClassName(`cell`))
        // }
        // return 'eys' 'befo'
        // return 'el-tooltip'
      },
      // 单元格样式
      cellStyle: ({ row, column, rowIndex, columnIndex }) => {
        if (columnIndex === 0) {
          return {
            'background-color': '#C7D5ED',
            'text-align': 'center',
            color: '#000',
            'font-size': '10px',
          }
        }
      },
      editionListBtn: (val, index) => {
        state.stateChange.index = index
        state.stateChange.edcQuestName =
          val.versionStatus === 1
            ? 'V' + val.versionNumber + '测试版本'
            : val.versionStatus === 2
              ? 'V' + val.versionNumber + '正式版本'
              : val.versionNumber
        state.onLoad()
      },
      // 点击表头事件
      headerClick: (e, l) => {
        state.crfSurface.crfFatherColumns.forEach((item, index) => {
          if (index && item?.clid?.length) {
            const { id, patientVisitType, dctVisitContainerId } = item
            if (e.level === 1 && index === e.no) {
              // console.log(item, index)
              state.stateChange.id = id
              state.stateChange.dctVisitContainerId = id
              state.stateChange.patientVisitType = patientVisitType
              state.stateChange.label = e.label
              state.stateChange.stateChangeFalg = false
              state.stateChange.tabChangeFalg = false
              return
            }
            if (item?.clid?.length) {
              item.clid.forEach((idx) => {
                if (idx.prop === e.property) {
                  state.stateChange.id = item.id
                  state.stateChange.dctVisitContainerId =
                    idx.dctVisitContainerId
                  state.stateChange.label = e.label
                  state.stateChange.stateChangeFalg = false
                  state.stateChange.tabChangeFalg = false
                  state.stateChange.patientVisitType = idx.patientVisitType
                }
              })
              return
            } else {
              if (item.prop === e.property) {
                state.stateChange.id = id
                state.stateChange.dctVisitContainerId = dctVisitContainerId
                state.stateChange.label = e.label
                state.stateChange.patientVisitType = patientVisitType
                state.stateChange.stateChangeFalg = false
                state.stateChange.tabChangeFalg = false
              }
            }
          }
        })
      },
      // 左头点击事件
      leftClick: (row, index, flag, e) => {
        if (index === 0) {
          // 点第一列表头时生效
          state.stateChange.id = row.id
          state.stateChange.dctModuleContainerId = row.dctModuleContainerId
          state.stateChange.prop = row.edcQuestName
          state.stateChange.label = ''
          state.stateChange.stateChangeFalg = false
          state.stateChange.tabChangeFalg = flag
        }
      },
      onLoad: () => {
        getDropStudyVersionsCRF(store.state.studyItem.studyId)
          .then((res: any) => {
            if (res?.length) {
              if (res[state?.stateChange?.index]?.versionStatus === 1) {
                state.stateChange.edcQuestName = 'V' +
                    res[state.stateChange.index].versionNumber +
                    '测试版本'
              } else if (res[state?.stateChange?.index]?.versionStatus === 2) {
                state.stateChange.edcQuestName = 'V' +
                    res[state.stateChange.index].versionNumber +
                    '正式版本'
              } else {
                state.stateChange.edcQuestName = res[state?.stateChange?.index]?.versionNumber || ''
              }
              // 生成label
              res.forEach(element => {
                if (element?.versionStatus === 1) {
                  element.label = `V${element.versionNumber}测试版本`
                } else if (element?.versionStatus === 2) {
                  element.label = `V${element.versionNumber}正式版本`
                } else {
                  element.label = ''
                }
              })
              state.editionList = res
              state.versionsValue =
                res[state.stateChange.index]?.studyVersionId || []
              state.stateChange.versionsValue = state.versionsValue
              state.versionStatus =
                res[state.stateChange.index]?.versionStatus || 0
              state.stateChange.versionStatus = state.versionStatus
              state.getCrfSurface()
            }
          })
      },
      getCrfSurface: async() => {
        try {
          const res = await getStudyCrf(state.versionsValue)
          let x = 0
          res.crfFatherColumns.forEach((rowitem: any, rowindex) => {
            rowitem.label = rowitem?.fatherVisitName || ''
            rowitem.prop =
              rowitem?.visitTemplates?.length > 1 ? '' : 'prop' + rowindex
            rowitem.clid = rowitem?.visitTemplates || []
            rowitem.clid.forEach((cildItem) => {
              cildItem.label = cildItem?.visitName || ''
              // cildItem.prop 不能是数字，所以我们强制在前面加上'_'
              // 这个是对应的列的索引，我们通过列序号去crfValues查看绑定状态。
              cildItem.prop = '_' + x++ // 'prop'+ rowindex+'clid'+ cildIndex
            })
          })
          // 添加左侧头
          if (res?.crfFatherColumns && Array.isArray(res.crfFatherColumns)) {
            res.crfFatherColumns.unshift({
              label: '',
              prop: 'left',
            })
          }
          state.crfSurface = res
          const newCrfColumnsArr = [...res.crfRow]
          newCrfColumnsArr.forEach((item: any, index) => {
            item.left = res.crfRow[index].crfName // crfName // edcQuestName
            item.crfValues = res.crfValues[index]
            item.crfValues.forEach((valitem: any, valindex) => {
              res.crfFatherColumns.forEach((rowitem: any, rowindex) => {
                if (!rowitem?.clid?.length) {
                  item[`prop${rowindex}`] = valitem
                } else {
                  rowitem.clid.forEach((cildItem, cildIndex) => {
                    item[`prop${rowindex}clid${cildIndex}`] = valitem
                  })
                }
              })
            })
          })
          state.crfSurface.newCrfColumnsArr = newCrfColumnsArr
          state.tableLabel = res.crfFatherColumns
          state.tableData = state.crfSurface.newCrfColumnsArr
          // 得到所有的子项
          state.tableLabelVisitTemplates = []
          state.tableLabel.slice(1).forEach((ite) => {
            ite.visitTemplates.forEach((itm) => {
              state.tableLabelVisitTemplates.push(itm)
            })
          })
          nextTick(() => {
            state.onResize()
            setTimeout(() => {
              state.myWidth = '100%'
              state.onResize()
            }, 100)
          })
        } catch (e) {
          console.log(e)
        }
      },
      setAddVersionsOrpreview: (flag) => {
        if (flag === 'add') {
          ElMessageBox.confirm(
            `提交后，将升级为正式版本，部分数据将无法编辑，是否确认提交测试版本？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              // type: 'warning',
            }
          )
            .then(() => {
              postCrfPublish(state.versionsValue).then(() => {
                ElMessage.success(`提交成功!`)
                state.onLoad()
                store.state.refreshFlag = true
              })
            })
            .catch(() => {})
        } else if (flag === 'back') {
          router.replace('/home')
          store.state.refreshFlag = true
        } else if (flag === 'reset') {
          ElMessageBox.confirm(
            `是否确认初始化当前测试版本？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              // type: 'warning',
            }
          )
            .then(() => {
              resetCrfPublish(state.versionsValue).then(() => {
                ElMessage.success(`重置成功!`)
                state.onLoad()
                store.state.refreshFlag = true
              })
            })
            .catch(() => {})
        }
      },
      onResize: () => {
        if ((document.body.clientWidth - 300) > 700) {
          if (state.tableHeight >= 700) {
            state.tableHeight = document.body.clientHeight - 300
          } else {
            state.tableHeight = 700
          }
        }
      }
    })

    onMounted(() => {
      window.addEventListener('resize', state.onResize)
      state.onLoad()
    })
    onBeforeUnmount(() => {
      window.removeEventListener('resize', state.onResize)
    })

    // 传值
    provide('QUESTIONNAIRE_INFOS', QUESTIONNAIRE_INFOS)

    return {
      QUESTIONNAIRE_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.el-select {
  min-width: 150px;
}
.SupervisionManagement {
  color: #333;
  .nav-module {
    margin: 10px 0 0 0;
    display: flex;
    .nav-btn-le {
      width: 50%;
      display: flex;
      align-items: center;
      h4 {
        margin: 0 30px 0 0;
      }
    }
    .nav-btn-ri {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .infos-body {
    width: 100%;
    margin: 20px 0;
    padding: 20px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 5px;
    min-width: 1200px;
    overflow-x: scroll;
    .table {
      margin-top: 20px;
      width: 100%;
      max-width: calc(100vw - 300px);
      .tableBox {
        width: 100%;
        display: flex;
        align-items: center;
        text-align: center;
        span {
          width: 100%;
          display: block;
          text-align: center;
          position: relative;
        }
      }
    }
  }
  .befo::before {
    content: '*';
    font-size: 18px;
    position: absolute;
    color: #fff;
    top: 0;
    right: 0;
  }
// 没有去掉过去的eys样式, 新增一个, 一定是用于cell上,
  // :deep(.cell.childColumnShowEyes) {
  //   &::before {
  //     content: '';
  //     position: absolute;
  //     top: 0;
  //     right: 0;
  //     width: 16px;
  //     height: 16px;
  //     background: url('@/assets/svg/eye.svg');
  //     overflow: hidden;
  //   }
  // }
  .tableHeaderbBox {
    position: relative;
    .childColumnShowEyes {
      position: absolute !important;
      right: -12px !important;
      top: 0 !important;
      width: 16px;
      height: 16px;
      overflow: hidden;
    }
    .childColumnShowMoves {
      &::before {
        content: '动';
        position: absolute;
        color: #fff;
        top: 0;
        right: 0;
        width: 16px;
        height: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        background:#f59a23;
        border-radius: 5px;
        overflow: hidden;
      }
    }
  }
  :deep(.cell.childColumnShowMoves) {
    &::before {
      content: '动';
      position: absolute;
      color: #fff;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      background:#f59a23;
      border-radius: 5px;
      overflow: hidden;
    }
  }
  :deep(.eys) {
    &::before {
      content: '';
      // font-size: 18px;
      position: absolute;
      color: #fff;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      background: url('@/assets/svg/eye.svg');
      overflow: hidden;
      // font-size: 50px 30px;
    }
  }
  .is-group {
    background: #333 !important;
    tr {
      background: #333 !important;
      &:first-child {
        th {
          &:first-child {
            background: #333 !important;
          }
        }
      }
    }
  }
}
.tableBox {
  box-sizing:border-box;
  margin: 8px 0;
}
.tableThree{
  position: absolute !important;
  right: 0 !important;
  top: 2px !important;
}
.tableOne {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.hove-tooltip) {
  display: -webkit-box !important;
  word-break: break-all;
  word-wrap: break-word;
  overflow: hidden;
  /*…省略形式*/
  text-overflow: ellipsis;
  /*从上向下垂直排列子元素*/
  -webkit-box-orient: vertical;
  /*文本可分两行*/
  -webkit-line-clamp: 2;
}
</style>
