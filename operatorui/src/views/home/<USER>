<template>
  <div class="text-[#333]">
    <h3 class="mt-0">隐私协议</h3>
    <div class="w-full px-[20px] box-border">
      <div class="flex justify-between items-end">
        <div class="flex items-center">
          <el-form-item label="是否必读" class="mr-[40px]">
            <el-select v-model="privacyMustBeRead" placeholder="请选择">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="privacyMustBeRead" label="阅读时长">
            <el-input
              v-model="privacyMustBeReadSeconds"
              placeholder="请输入"
              maxlength="15"
              style="width: 120px;"
              @input="(val) => {
                if (!/^[1-9][0-9]*$/.test(val)) {
                  privacyMustBeReadSeconds = val.replace(/[^0-9]/g, '').replace(/^0+/, '');
                }
              }"
            />
            <span class="ml-2">秒</span>
          </el-form-item>
        </div>
        <div>
          <span class="mr-[20px]">请先保存数据后再扫码预览效果</span>
          <vue-qr v-if="siteUrl" :text="siteUrl" class="w-[140px] h-[140px] mr-[20px]" />
          <img v-else class="w-[140px] h-[140px] mr-[20px]" src="@/assets/logocd.png" alt="">
        </div>
      </div>
      <trial-wang-editor ref="editorPrivacy" :toolbar-config="toolbarConfig" class="mt-[20px]" />
      <div class="w-full flex justify-center">
        <el-button
          class="w-[120px] mt-[30px]"
          type="primary"
          :loading="saveLoading"
          @click="save"
        >保存</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { studyIcfPrivacySLA, studyPrivacy } from '@/api/home'
import { useStore } from 'vuex'
import VueQr from 'vue-qr/src/packages/vue-qr.vue'
// import { PrivacyAgreement } from '@/type/views/home'

export default defineComponent({
  name: 'PrivacyAgreement', // 隐私协议
  components: {
    VueQr,
  },
  setup() {
    const store = useStore()

    const state: any = reactive({
      editorPrivacy: null,
      contentHtml: '',
      saveLoading: false,
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          // 'headerSelect', // 正文
          'insertTable', // 插入表格
          'blockquote', // 引用
          'uploadImage', // 上传图片
          'uploadVideo', // 上传视频
          // 'group-video' // 排除菜单组，写菜单组 key 的值即可
        ],
      },
      siteUrl: '',
      privacyMustBeRead: false,
      privacyMustBeReadSeconds: '',
      // 保存
      save: () => {
        // 必读 要填阅读时长的秒数
        state.contentHtml = state.editorPrivacy.gainTxt('set')
        state.saveLoading = true
        // 是否必填、阅读时长
        studyIcfPrivacySLA(store.state.studyItem.studyId, {
          studyTextType: '2',
          studyText: state.contentHtml,
          privacyMustBeRead: state.privacyMustBeRead,
          privacyMustBeReadSeconds: state.privacyMustBeReadSeconds,
        })
          .then(() => {
            state.saveLoading = false
            ElMessage.success({
              message: '保存成功',
            })
          })
          .catch(() => {
            state.saveLoading = false
          })
      },
    })

    onMounted(() => {
      if (
        store?.state?.studyItem?.studyId &&
        store?.getters?.app?.authorization
      ) {
        state.siteUrl = `${window.location.origin}/doctorui/#/privacyAgreementTermsOfService
        ?type=2==${store.state.studyItem.studyId}==${store.getters.app.authorization}
        `
      }
      studyPrivacy(store.state.studyItem.studyId).then((res: any) => {
        state.editorPrivacy.gainTxt('get', res.studyText)
        state.privacyMustBeRead = res.privacyMustBeRead
        state.privacyMustBeReadSeconds = res.privacyMustBeReadSeconds
      })
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
