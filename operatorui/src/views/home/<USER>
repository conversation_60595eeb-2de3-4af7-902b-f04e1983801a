<template>
  <div class="">
    <div class="flex justify-between items-center">
      <span>问卷审阅</span>
      <el-button type="primary" :loading="saveLoading" @click="handleSave">保存</el-button>
    </div>
    <!-- 卡片 -->
    <div
      v-for="(item,index) in [formDataList[0]]"
      :key="index"
      class="bg-white p-4 rounded mt-4"
    >
      <div class="mb-4">
        <span class="font-bold">审阅机制（面相受试者端、研究端填写的问卷）</span>
        <!-- <span v-if="item.questTemplateType === 1" class="font-bold">患者端填写的问卷</span>
        <span v-if="item.questTemplateType === 2" class="font-bold">研究端填写的问卷</span> -->
        <span class="ml-[30px] text-[#F59A23] text-[14px]">请在问卷侧，配置适用的问卷范围</span>
      </div>
      <el-form
        ref="electronicInfosDetailsRef"
        :model="item"
        label-position="left"
      >
        <el-form-item label="签署方式" prop="qusetSignatureType">
          <el-radio-group v-model="item.qusetSignatureType">
            <el-radio :value="1">手写板签名</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签署意愿核验方式" prop="qusetSignatureCheckType">
          <el-radio-group v-model="item.qusetSignatureCheckType" class="min-w-[220px]">
            <el-radio :value="1">手机验证码</el-radio>
            <el-radio :value="2">人脸识别</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import { getQuestSigntureSet, postSetQuestSignture } from '@/api/home'
import { ElMessage } from 'element-plus'
import { defineComponent, onBeforeMount, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'QuestionnaireReviewMethod', // 问卷审阅
  setup() {
    const store = useStore()
    const studyId = store.state.studyItem.studyId
    const state: any = reactive({
      formDataList: [],
      saveLoading: false,
      // 点击保存
      handleSave: () => {
        state.saveLoading = true
        state.formDataList[0].questTemplateType = 1
        state.formDataList[1].questTemplateType = 2
        state.formDataList[1].qusetSignatureCheckType = state.formDataList[0].qusetSignatureCheckType
        postSetQuestSignture(state.formDataList).then(() => {
          ElMessage.success('保存成功')
          state.saveLoading = false
        }).catch(() => {
          state.saveLoading = false
        })
      },
      // 获取数据
      onLoad: () => {
        getQuestSigntureSet(studyId, 2).then((res) => {
          if (Array.isArray(res) && res.length) {
            state.formDataList = res
          }
        })
      },
    })
    onBeforeMount(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>
