<template>
  <div class="informed">
    <div class="informed-body">
      <h3>知情同意书</h3>
      <div>
        最短阅读时长设置:
        <el-input
          v-model="studyKnowReadingTime"
          class="inputNumber"
          type="number"
          min="10"
          max="6000"
        />
        秒
      </div>
      <div class="margin-t-10">
        知情视频Url:
        <el-input v-model="studyVideoUrl" />
      </div>
      <div class="margin-t-10">
        知情视频封面Url:
        <el-input v-model="studyVideoCoverUrl" />
      </div>
      <div class="margin-t-10">
        最短视频时长设置:
        <el-input
          v-model="studyVideoViewSeconds"
          class="inputNumber"
          type="number"
          min="10"
          max="6000"
        />
        秒
      </div>

      <div class="margin-t-10">录制个人声明视频中需朗读的内容（按需配置）</div>
      <div class="margin-t-10 margin-b-10">
        <trial-wang-editor ref="reciteEditor" class="reciteEditor" :toolbar-config="toolbarConfig" />
      </div>

      <vue-qr v-if="siteUrl" :text="siteUrl" class="site" />
      <img v-else class="site margin-t-10" src="@/assets/logocd.png" alt="">
      <span>请先保存数据后再扫码预览效果</span>

      <trial-wang-editor ref="editorHome" class="editorHome" :toolbar-config="toolbarConfig" />

      <div class="btn">
        <el-button type="primary" @click="save">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
} from 'vue'
import { studyIcfPrivacySLA, studyKnow } from '@/api/home'
import { useStore } from 'vuex'
import VueQr from 'vue-qr/src/packages/vue-qr.vue'
import { Informed } from '@/type/views/home'

export default defineComponent({
  name: 'Informed', // 知情同意书
  components: {
    VueQr,
  },
  setup() {
    const store = useStore()

    const state: Informed = reactive({
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          'uploadImage', // 上传图片
          'uploadVideo' // 上传视频
        ],
      },
      studyKnowReadingTime: 10,
      studyVideoViewSeconds: 10,
      studyVideoUrl: '',
      studyVideoCoverUrl: '',
      editorHome: null,
      reciteEditor: null,
      contentHtml: '',
      reciteHtml: '',
      siteUrl: '',
      // 保存
      save: () => {
        state.contentHtml = state.editorHome.gainTxt('set')
        state.reciteHtml = state.reciteEditor.gainTxt('set')

        studyIcfPrivacySLA(store.state.studyItem.studyId, {
          studyTextType: '4',
          studyText: state.contentHtml,
          studyKnowReadingTime: state.studyKnowReadingTime,
          studyVideoViewSeconds: state.studyVideoViewSeconds,
          studyVideoUrl: state.studyVideoUrl,
          studyVideoCoverUrl: state.studyVideoCoverUrl,
          studyReciteText: state.reciteHtml,
        }).then(() => {
          ElMessage({
            message: `保存成功`,
            type: 'success',
          })
        })
      },
    })

    onMounted(() => {
      if (
        store?.state?.studyItem?.studyId &&
        store?.getters?.app?.authorization
      ) {
        // state.siteUrl = `http://192.168.2.5:8002/doctorui/#/privacyAgreementTermsOfService==type=4==${store.state.studyItem.studyId}==${store.getters.app.authorization}`
        state.siteUrl = `${
          import.meta.env.VITE_APP_BASE_URL
        }doctorui/#/privacyAgreementTermsOfService==type=4==${
          store.state.studyItem.studyId
        }==${store.getters.app.authorization}`
      }
      studyKnow(store.state.studyItem.studyId)
        .then((res: any) => {
          state.editorHome.gainTxt('get', res.studyText)
          state.reciteEditor.gainTxt('get', res.studyReciteText)
          state.studyKnowReadingTime = res.studyKnowReadingTime
          state.studyVideoViewSeconds = res.studyVideoViewSeconds
          state.studyVideoUrl = res.studyVideoUrl
          state.studyVideoCoverUrl = res.studyVideoCoverUrl
        })
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.informed {
  color: #333;
  .informed-body {
    width: 100%;
    padding: 0 40px 40px;
    box-sizing: border-box;
    .site {
      width: 140px;
      height: 140px;
      margin: 0 20px 0 0;
    }
    .editorHome {
      margin: 10px 0 0 0;
    }
    .reciteEditor {
      margin-top: 10px;
    }
  }
  .el-input {
    width: 600px;
  }
  .inputNumber {
    width: 80Px;
  }
  .btn {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
  }
}
</style>
