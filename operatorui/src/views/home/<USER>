<template>
  <div>
    <trial-table
      ref="CustomLabelRef"
      title=""
      :request="getRequestList"
      :columns="columns"
      :search="searchConfig"
      showbtnfalg
      hide-center
      :pagination="paginationConfig"
    >
      <template #effectiveRuleStr="scope">
        <span
          v-if="scope.row.effectiveRule === 1"
        >
          人工打标签
        </span>
      </template>
      <template #operate="scope">
        <span
          class="text-[#409eff] cursor-pointer"
          @click="editAddItem(scope.row)"
        >
          编辑
        </span>
        <span
          class="text-[#f56c6c] cursor-pointer ml-3"
          @click="deleteItem(scope.row)"
        >删除</span>
      </template>
      <template #hideCenter>
        <div class="my-5 flex justify-end items-center">
          <el-button type="primary" @click="editAddItem(null)">新增</el-button>
        </div>
      </template>
    </trial-table>

    <trial-dialog
      v-model="customLabelDialogVisible"
      title="标签"
      my-title-class="flex mb-3 mt-0"
    >
      <template #DialogBody>
        <el-form
          ref="customLabelFormRef"
          label-position="top"
          class="px-3"
          :model="customLabelFormData"
          :rules="customLabelRules"
        >
          <el-form-item
            label="标签名称"
            prop="tagName"
          >
            <el-input
              v-model.trim="customLabelFormData.tagName"
              max-length="100"
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="标签说明"
            prop="tagDesc"
          >
            <el-input
              v-model.trim="customLabelFormData.tagDesc"
              autocomplete="off"
              max-length="999"
              type="textarea"
            />
          </el-form-item>
          <!-- 标签生效规则 -->
          <el-form-item
            label="标签生效规则"
            prop="effectiveRule"
          >
            <el-select
              v-model="customLabelFormData.effectiveRule"
              placeholder="请选择"
              size="large"
              style="width: 240px"
            >
              <!-- v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
             -->
              <el-option
                label="人工打标签"
                :value="1"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-center">
          <el-button plain @click="closeDialogVisible">取消</el-button>
          <el-button
            :loading="customLabelLoading"
            type="primary"
            @click="customLabelSubmit"
          >确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { getPagedList, postPatientTag, deletePatientTag } from '@/api/home'
import { defineComponent, reactive, toRefs } from 'vue'
// import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'CustomLabel', // 用户标签
  setup() {
    const store = useStore()
    const state = reactive({
      CustomLabelRef: null,
      columns: [
        { label: '标签名称', prop: 'tagName', minWidth: 200 },
        { label: '标签说明', prop: 'tagDesc', minWidth: 280 },
        { label: '标签生效规则', prop: 'effectiveRule', minWidth: 120, tdSlot: 'effectiveRuleStr' },
        {
          label: '更新时间',
          prop: 'lastUpdateTime',
          minWidth: 200,
        },
        { label: '更新人', prop: 'lastUpdateUserName', minWidth: 100 },
        {
          label: '创建时间',
          prop: 'createTime',
          minWidth: 200,
        },
        { label: '创建人', prop: 'createUserName', minWidth: 100 },
        {
          minWidth: 160,
          label: '操作',
          fixed: 'right',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            label: '标签名称',
            name: 'tagName',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        // pageSize: 5, // 每页条数
        // pageSizes: [5, 10, 20],
        style: { textAlign: 'left' },
      },
      // 编辑/新建
      editAddItem: (row) => {
        // console.log(row)
        if (row) {
          state.customLabelFormData = {
            id: row.id,
            tagName: row.tagName,
            tagDesc: row.tagDesc,
            effectiveRule: row.effectiveRule,
          }
        } else {
          state.customLabelFormData = {
            id: null,
            tagName: '',
            tagDesc: '',
            effectiveRule: 1,
          }
        }
        state.customLabelDialogVisible = true
      },
      deleteItem: (e) => {
        ElMessageBox.confirm(
          `若有用户已关联了该标签，删除后该标签将自动失效，是否确认删除？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          }
        )
          .then(() => {
            deletePatientTag(e.id).then(() => {
              ElMessage.success(`删除成功!`)
              state.CustomLabelRef.refresh()
            })
          })
          .catch(() => {})
      },
      async getRequestList(params) {
        // 获取数据
        const rest: any = await getPagedList(
          store.state.studyItem.studyId,
          params
        )
        return {
          data: rest.items,
          total: rest.totalItemCount,
        }
      },
      // 弹窗数据
      customLabelFormData: {
        id: null,
        tagName: '',
        tagDesc: '',
        effectiveRule: 1,
      },
      customLabelFormRef: null,
      customLabelDialogVisible: false,
      customLabelRules: {
        tagName: [{ required: true, message: '请输入', trigger: 'blur' }],
        effectiveRule: [{ required: true, message: '请选择', trigger: 'blur' }],
      },
      customLabelLoading: false,
      closeDialogVisible: () => {
        state.customLabelDialogVisible = false
      },
      customLabelSubmit: () => {
        state.customLabelFormRef.validate((valid) => {
          if (valid) {
            state.customLabelLoading = true
            const data = {
              ...state.customLabelFormData,
              studyId: store.state.studyItem.studyId,
            }
            postPatientTag(data).then(() => {
              state.CustomLabelRef.refresh()
              state.customLabelLoading = false
              state.closeDialogVisible()
            }).catch(() => {
              state.customLabelLoading = false
            })
          }
        })
      },
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>
