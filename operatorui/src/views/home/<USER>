<template>
  <div class="commonProblem">
    <div>
      <!-- @selectionChange="handleSelectionChange" -->
      <trial-table
        ref="commonProblemRef"
        title="FAQ列表"
        :request="getList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
      >
        <template #visibleTo="scope">
          <span v-if="scope?.row?.visibleTo == 0"> 都不可见 </span>
          <span v-if="scope?.row?.visibleTo == 1"> 患者可见 </span>
          <span v-if="scope?.row?.visibleTo == 2"> 医生可见 </span>
          <span v-if="scope?.row?.visibleTo == 3"> 都可见 </span>
        </template>
        <template #toolbar>
          <el-button type="primary" @click="editResearchCenterInfoItem('add')"
            >新建
          </el-button>
        </template>
        <template #operate="scope">
          <span
            class="editBtn"
            @click="editResearchCenterInfoItem('edit', scope.row)"
          >
            编辑
          </span>
          <span
            class="deleteBtn"
            @click="editResearchCenterInfoItem('delete', scope.row)"
          >
            删除
          </span>
        </template>
      </trial-table>
    </div>
    <!-- 选项弹窗 -->
    <trial-dialog
      v-model="contentVisible"
      :title="commonProblemInfo.id ? '编辑' : '新建'"
    >
      <template #DialogBody>
        <el-form
          ref="commonProblemRefAlert"
          label-position="top"
          :model="commonProblemInfo"
          :rules="rules"
          style="display: flex; justify-content: space-between; flex-wrap: wrap"
        >
          <el-form-item
            label="排序值"
            :label-width="formLabelWidth"
            prop="seqNum"
            style="width: 48%"
          >
            <el-input
              v-model.trim="commonProblemInfo.seqNum"
              autocomplete="off"
              placeholder="请填写"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="可见性"
            :label-width="formLabelWidth"
            prop="visibleTo"
            style="width: 48%"
          >
            <el-select
              v-model="commonProblemInfo.visibleTo"
              placeholder="请选择可见性"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in visibleToList"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="问题"
            :label-width="formLabelWidth"
            prop="question"
            style="width: 100%; minwidth: 600px"
          >
            <el-input
              v-model.trim="commonProblemInfo.question"
              placeholder="请填写"
              type="textarea"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="回答"
            :label-width="formLabelWidth"
            prop="answer"
            style="width: 100%; minwidth: 600px"
          >
            <el-input
              v-model.trim="commonProblemInfo.answer"
              placeholder="请填写"
              type="textarea"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="footer">
          <el-button size="large" @click="contentVisible = false"
            >取消</el-button
          >
          <el-button type="primary" size="large" @click="submitOptionTab"
            >保存</el-button
          >
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import {
  getStudyFaqs, // 获取
  postStudyFaq, // 保存
  deleteStudyFaq, // 删除
} from '@/api/home'

export default defineComponent({
  name: 'CommonProblem', // 常见问题
  setup() {
    const store = useStore()
    const state = reactive({
      contentVisible: false,
      commonProblemInfo: {
        id: '',
        dctStudyId: '',
        visibleToInput: null,
        seqNum: '',
        visibleTo: 0,
        question: '',
        answer: '',
      },
      listData: {},
      visibleToList: [
        {
          name: '都不可见',
          value: 0,
        },
        {
          name: '患者可见',
          value: 1,
        },
        {
          name: '医生可见',
          value: 2,
        },
        {
          name: '都可见',
          value: 3,
        },
      ],
      formLabelWidth: '140px',
      rules: {
        seqNum: [
          { required: true, message: '请填写', trigger: 'blur' },
          {
            pattern: /^-?[1-9]\d*$|^0$/,
            message: '请填写整数',
            trigger: 'blur',
          },
        ],
        visibleTo: [{ required: true, message: '请选择', trigger: 'blur' }],
        question: [{ required: true, message: '请填写', trigger: 'blur' }],
        answer: [{ required: true, message: '请填写', trigger: 'blur' }],
      },
      commonProblemRefAlert: null,
      // researchCenter: {},
      commonProblemRef: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//seqNumable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        { label: '排序值', prop: 'seqNum', width: 80 },
        { label: '问题', prop: 'question', width: 300 },
        { label: '回答', prop: 'answer', width: 300 },
        { label: '可见性', width: 180, tdSlot: 'visibleTo' },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            label: '可见性',
            name: 'visibleTo',
            type: 'select',
            defaultValue: null,
            options: [
              {
                name: '都不可见',
                value: 0,
              },
              {
                name: '患者可见',
                value: 1,
              },
              {
                name: '医生可见',
                value: 2,
              },
              {
                name: '都可见',
                value: 3,
              },
            ],
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // researchCenterList: [],
      // 选择
      // handleSelectionChange(arr) {
      //   state.researchCenterList = arr
      // },
      // 请求短信列表函数
      async getList(params) {
        try {
          // const { data } = await getNoteList(params)
          const { studyId } = store.state.studyItem
          const rest = await getStudyFaqs(studyId, params)
          // getSites(studyId, myParams)
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          //
        }
      },
      editResearchCenterInfoItem: (type, row) => {
        if (type === 'add') {
          // 新建
          state.contentVisible = true
          state.commonProblemInfo = {
            id: '',
            dctStudyId: store.state.studyItem.studyId,
            visibleToInput: null,
            seqNum: '',
            visibleTo: '',
            question: '',
            answer: '',
          }
        } else if (type === 'edit') {
          // 编辑
          state.commonProblemInfo = { ...row }
          state.contentVisible = true
        } else if (type === 'delete') {
          // 删除
          ElMessageBox.confirm(`是否确认删除`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          })
            .then(() => {
              deleteStudyFaq(row.id)
                .then((res) => {
                  ElMessage.success('删除成功')
                  state.commonProblemRef.handleReset()
                })
                .catch(() => {})
            })
            .catch(() => {})
        }
      },
      submitOptionTab: () => {
        state.commonProblemRefAlert.validate((valid) => {
          if (valid) {
            postStudyFaq(state.commonProblemInfo)
              .then((res) => {
                ElMessage.success('保存成功')
                state.contentVisible = false
                state.commonProblemRef.handleReset()
              })
              .catch(() => {
                state.contentVisible = false
              })
          }
        })
      },
      // 获取数据
      onLoad: () => {},
    })

    onMounted(() => {
      state.onLoad()
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.commonProblem {
  width: 100%;
  min-height: 100%;
  .commonProblem-headTitle {
    width: 100%;
    margin: 0 0 10px 0;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3 {
      padding: 0;
      margin: 0;
    }
  }
  .editBtn {
    color: #409eff;
    &:hover {
      cursor: pointer; /*小手*/
    }
  }
  .deleteBtn {
    color: red;
    margin-left: 10px;
    &:hover {
      cursor: pointer; /*小手*/
    }
  }
}
</style>
