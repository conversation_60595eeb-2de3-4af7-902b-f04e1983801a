<template>
  <div>
    <div>医患绑定方式</div>
    <div class="mb-2 items-center text-sm">
      <el-radio-group v-model="myBondingRadio" class="w-full mt-3" @change="myRadioChange">
        <el-radio value="1" size="small">患者扫中心二维码后与本中心下所有医生绑定</el-radio>
        <!-- 第一个可选的 -->
        <div class="w-full">
          <el-radio-group v-model="myBondingRadio2" class="ml-4 mt-3">
            <el-radio value="3" :disabled="myBondingRadio === '2'" size="small">入组时患者与本中心下所有医生绑定</el-radio>
            <el-radio value="4" :disabled="myBondingRadio === '2'" size="small">入组时按患者所在分组绑定医生</el-radio>
          </el-radio-group>
        </div>
        <el-radio class="mt-2.5" value="2" size="small">患者扫医生出示的中心二维码后与该医生绑定</el-radio>
      </el-radio-group>
    </div>
    <el-button
      class="mt-3"
      :loading="saveLoadingFlag"
      type="primary"
      @click="saveBonding"
    >保 存</el-button>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted } from 'vue'
import { useStore } from 'vuex'
import { getDPAutomaticBind, postDPAutomaticBind } from '@/api/home'

export default defineComponent({
  name: 'DoctorPatientBonding', // 医患绑定方式
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    // console.log(store)
    const state = reactive({
      // 0 = 不绑定, 1 = 中心, 2 = 分组, 3 = 医生二维码
      myBondingRadio: '0', // 1
      myBondingRadio2: '0', // 3
      saveLoadingFlag: false,
      // 请求函数
      async onLoad() {
        // params是从组件接收的，包含分页和搜索字段。
        try {
          const mydata = await getDPAutomaticBind(studyId)
          if (mydata / 1 === 1) {
            state.myBondingRadio = '1'
            state.myBondingRadio2 = '3'
          } else if (mydata / 1 === 2) {
            state.myBondingRadio = '1'
            state.myBondingRadio2 = '4'
          } else if (mydata / 1 === 3) {
            state.myBondingRadio = '2'
          }
        } catch (e) {
          console.log(e)
        }
      },
      //
      myRadioChange: (e) => {
        if (e === '2') {
          state.myBondingRadio2 = '0'
        } else if (e === '1') {
          state.myBondingRadio2 = '3'
        }
      },
      // 保存
      saveBonding: () => {
        let dpAutomaticBind = 0
        if (state.myBondingRadio / 1 === 1) {
          if (state.myBondingRadio2 / 1 === 3) {
            dpAutomaticBind = '1'
          } else if (state.myBondingRadio2 / 1 === 4) {
            dpAutomaticBind = '2'
          }
        } else if (state.myBondingRadio / 1 === 2) {
          dpAutomaticBind = '3'
        }
        state.saveLoadingFlag = true
        postDPAutomaticBind(studyId, {
          dpAutomaticBind
        }).then(() => {
          // 绑定成功后更新页面
          state.saveLoadingFlag = false
          ElMessage.success('绑定成功')
        }).catch(() => {
          state.saveLoadingFlag = false
        })
      },
    })

    onMounted(() => {
      state.onLoad()
    })

    return {
      ...toRefs(state)
    }
  }
})
</script>
