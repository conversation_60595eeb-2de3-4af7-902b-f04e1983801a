<template>
  <div class="">
    <div class="flex justify-between items-center">
      <span>问卷签署</span>
      <el-button type="primary" :loading="saveLoading" @click="handleSave">保存</el-button>
    </div>
    <!-- 患者端卡片 -->
    <div class="bg-white p-4 rounded mt-4">
      <div class="mb-4">
        <span class="font-bold">受试者端填写的问卷</span>
        <span class="ml-[30px] text-[#F59A23] text-[14px]">请在问卷侧，配置适用的问卷范围</span>
      </div>
      <el-form
        ref="electronicInfosDetailsRef"
        :model="formData"
        :rules="rules"
        label-position="left"
      >
        <el-form-item label="签署声明" prop="signatureRemark">
          <el-input
            v-model="formData.signatureRemark"
            maxlength="3999"
            class="max-w-[70%]"
            type="textarea"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="签署方式" prop="qusetSignatureType">
          <el-radio-group v-model="formData.qusetSignatureType">
            <el-radio :value="1">手写板签名</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签署意愿核验方式" prop="qusetSignatureCheckType">
          <el-radio-group
            v-model="formData.qusetSignatureCheckType"
            class="min-w-[220px]"
          >
            <el-radio :value="1">手机验证码</el-radio>
            <el-radio :value="2">人脸识别</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签署提醒" prop="enableReminder">
          <el-radio-group v-model="formData.enableReminder" class="min-w-[220px]">
            <el-radio :value="0">不提醒</el-radio>
            <el-radio :value="1">提醒</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="formData.enableReminder === 1"
          label="提醒周期"
          prop="daysOfWeek"
        >
          <el-checkbox-group v-model="formData.daysOfWeek">
            <el-checkbox
              v-for="day in daysOfWeekList"
              :key="day.value"
              :value="day.value"
            >每周{{ day.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item
          v-if="formData.enableReminder === 1"
          label="提醒时间"
          prop="reminderTime"
        >
          <!-- :clearable="false" -->
          <el-time-picker
            v-model="formData.reminderTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择时间"
          />
        </el-form-item>
      </el-form>
    </div>
    <!-- 研究端卡片 -->
    <div class="bg-white p-4 rounded mt-4">
      <div class="mb-4">
        <span class="font-bold">研究端填写的问卷</span>
        <span class="ml-[30px] text-[#F59A23] text-[14px]">请在问卷侧，配置适用的问卷范围</span>
      </div>
      <div class="mb-6 flex justify-center">
        <span class="ml-4 text-[14px]">暂不适用</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, reactive, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'
import { getQuestSigntureSet, postSetQuestSignture } from '@/api/home'

export default defineComponent({
  name: 'QuestionnaireSignature', // 问卷签署
  setup() {
    const store = useStore()
    const studyId = store.state.studyItem.studyId
    const validateDaysOfWeekValidator = (rule, value, callback) => {
      console.log('Validating daysOfWeek:', value);
      if (value.length === 0) {
        console.log('请选择')
        callback(new Error('请选择'))
      } else {
        callback()
      }
    }
    const state = reactive({
      electronicInfosDetailsRef: null,
      daysOfWeekList: [{
        value: '1',
        label: '一',
      },
      {
        value: '2',
        label: '二',
      },
      {
        value: '3',
        label: '三',
      },
      {
        value: '4',
        label: '四',
      },
      {
        value: '5',
        label: '五',
      },
      {
        value: '6',
        label: '六',
      },
      {
        value: '0',
        label: '日',
      },
      ],
      formData: {
        id: '',
        questTemplateType: 1,
        qusetSignatureType: 1,
        signatureRemark: '我确认数据由本人根据实际情况填写',
        dctStudyId: studyId,
        qusetSignatureCheckType: 1,
        purposeOfSignature: 1,
        questTemplateId: '',
        enableReminder: 1,
        daysOfWeek: ['0'],
        reminderTime: '12:00',
      },
      rules: {
        daysOfWeek: [
          { required: true, message: '请选择', trigger: 'blur' },
          {
            validator: validateDaysOfWeekValidator,
            trigger: 'change',
            message: '请选择',
          }
        ],
        reminderTime: [{ required: true, message: '请选择', trigger: 'blur' }],
        signatureRemark: [{ required: true, message: '请输入', trigger: 'blur' }],
      },
      saveLoading: false,
      //
      handleSave: () => {
        state.electronicInfosDetailsRef.validate((valid) => {
          if (valid) {
            state.saveLoading = true
            const formData = { ...state.formData, daysOfWeek: state.formData.daysOfWeek.join(',') }
            postSetQuestSignture([formData]).then(() => {
              ElMessage.success('保存成功')
              state.saveLoading = false
            }).catch(() => {
              state.saveLoading = false
            })
          }
        })
      },
      // 获取数据
      onLoad: () => {
        getQuestSigntureSet(studyId, 1).then((res) => {
          if (res?.length) {
            res[0].daysOfWeek = res[0].daysOfWeek?.length ? res[0].daysOfWeek?.split(',') : []
            state.formData = res[0]
          }
        })
      },
    })
    onBeforeMount(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>
