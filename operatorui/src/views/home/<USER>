<template>
  <div class="text-[#333]">
    <h3 class="mt-0">服务条款</h3>
    <div class="w-full px-[20px] box-border">
      <div class="flex justify-between items-end">
        <div class="flex items-center">
          <el-form-item label="是否必读" class="mr-[40px]">
            <el-select v-model="slaMustBeRead" placeholder="请选择">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="slaMustBeRead" label="阅读时长">
            <el-input
              v-model="slaMustBeReadSeconds"
              placeholder="请输入"
              maxlength="15"
              style="width: 120px;"
              @input="(val) => {
                if (!/^[1-9][0-9]*$/.test(val)) {
                  slaMustBeReadSeconds = val.replace(/[^0-9]/g, '').replace(/^0+/, '');
                }
              }"
            />
            <span class="ml-2">秒</span>
          </el-form-item>
        </div>
        <div>
          <span class="mr-[20px]">请先保存数据后再扫码预览效果</span>
          <vue-qr v-if="siteUrl" :text="siteUrl" class="w-[140px] h-[140px] mr-[20px]" />
          <img v-else class="w-[140px] h-[140px] mr-[20px]" src="@/assets/logocd.png" alt="">
        </div>
      </div>
      <trial-wang-editor ref="editorTermsService" :toolbar-config="toolbarConfig" class="mt-[20px]" />
      <div class="w-full flex justify-center">
        <el-button
          class="w-[120px] mt-[30px]"
          type="primary"
          :loading="saveLoading"
          @click="save"
        >保存</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import VueQr from 'vue-qr/src/packages/vue-qr.vue'
import { useStore } from 'vuex'
import { studyIcfPrivacySLA, studyService } from '@/api/home'
// import { TermsService } from '@/type/views/home'

export default defineComponent({
  name: 'TermsService', // 服务条款
  components: {
    VueQr,
  },
  setup() {
    const store = useStore()
    const state: any = reactive({
      editorTermsService: null,
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          'uploadImage', // 上传图片
          'uploadVideo', // 上传视频
        ],
      },
      contentHtml: '',
      // value: `https://dctest.trialdata.cn/doctorui/#/privacyAgreementTermsOfService`,
      siteUrl: '',
      slaMustBeRead: false,
      slaMustBeReadSeconds: '',
      saveLoading: false,
      // 保存
      save: () => {
        state.contentHtml = state.editorTermsService.gainTxt('set')
        state.saveLoading = true
        studyIcfPrivacySLA(store.state.studyItem.studyId, {
          studyTextType: '3',
          studyText: state.contentHtml,
          slaMustBeRead: state.slaMustBeRead,
          slaMustBeReadSeconds: state.slaMustBeReadSeconds,
        })
          .then(() => {
            state.saveLoading = false
            ElMessage.success({
              message: '保存成功',
            })
          })
          .catch(() => {
            state.saveLoading = false
          })
      },
    })

    onMounted(() => {
      if (
        store?.state?.studyItem?.studyId &&
        store?.getters?.app?.authorization
      ) {
        state.siteUrl = `${window.location.origin}/doctorui/#/privacyAgreementTermsOfService
        ?type=3==${store.state.studyItem.studyId}==${store.getters.app.authorization}
        `
      }
      studyService(store.state.studyItem.studyId).then((res: any) => {
        state.editorTermsService.gainTxt('get', res.studyText)
        state.slaMustBeReadSeconds = res.slaMustBeReadSeconds
        state.slaMustBeRead = res.slaMustBeRead
      })
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
