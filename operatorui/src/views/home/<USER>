<template>
  <div>
    <div class="flex justify-between">
      <span>过程身份核验</span>
      <el-button type="primary" :loading="saveLoading" @click="handleSave"
        >保存</el-button
      >
    </div>
    <!-- 患者端卡片 -->
    <div class="bg-white p-4 rounded mt-4">
      <div class="font-bold mb-4">患者端</div>
      <div class="flex flex-wrap">
        <span class="ml-4 text-[14px]">触发事件</span>
        <div class="">
          <el-checkbox
            v-model="processIdentityVerificationList[0].enable"
            class="ml-6"
            >访问患者端</el-checkbox
          >
          <div class="flex">
            <el-radio-group v-model="verificationValidity" class="ml-[40px]">
              <el-radio :value="true">单事件核验有效期</el-radio>
            </el-radio-group>
            <el-input
              v-model.number="processIdentityVerificationList[0].expiredTime"
              class="!w-[110px] ml-[20px]"
              placeholder="请输入"
              maxlength="15"
              @input="
                (e) => {
                  let n = e.replace(/^\D*(0|([1-9]\d*))$/, '$1')
                  if (isNaN(parseInt(n))) {
                    n = '0'
                  } else {
                    n = parseInt(n)
                  }
                  processIdentityVerificationList[0].expiredTime = n
                }
              "
            />
            <el-select
              v-model="processIdentityVerificationList[0].timeUnit"
              placeholder="请选择"
              style="min-width: 40px; width: 110px"
              class="ml-[10px]"
            >
              <el-option label="天" :value="1" />
              <el-option label="小时" :value="2" />
              <el-option label="分钟" :value="3" />
            </el-select>
            <div class="min-w-[80px] ml-[20px] mt-[6px] text-[14px]">
              核验方式
            </div>
            <div class="flex items-center">
              <el-radio-group
                v-model="processIdentityVerificationList[0].mode"
                class="min-w-[220px]"
              >
                <el-radio :value="1">手机验证码</el-radio>
                <el-radio :value="2">人脸识别</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="w-full ml-[70px]">
          <el-checkbox
            v-model="processIdentityVerificationList[1].enable"
            class="ml-6"
            >参加会议</el-checkbox
          >
          <div class="flex">
            <el-radio-group v-model="verificationValidity" class="ml-[40px]">
              <el-radio :value="true">单事件核验有效期</el-radio>
            </el-radio-group>
            <el-input
              v-model.number="processIdentityVerificationList[1].expiredTime"
              class="!w-[110px] ml-[20px]"
              placeholder="请输入"
              maxlength="15"
              @input="
                (e) => {
                  let n = e.replace(/^\D*(0|([1-9]\d*))$/, '$1')
                  if (isNaN(parseInt(n))) {
                    n = '0'
                  } else {
                    n = parseInt(n)
                  }
                  processIdentityVerificationList[1].expiredTime = n
                }
              "
            />
            <el-select
              v-model="processIdentityVerificationList[1].timeUnit"
              placeholder="请选择"
              style="min-width: 40px; width: 110px"
              class="ml-[10px]"
            >
              <el-option label="天" :value="1" />
              <el-option label="小时" :value="2" />
              <el-option label="分钟" :value="3" />
            </el-select>
            <div class="min-w-[80px] ml-[20px] mt-[6px] text-[14px]">
              核验方式
            </div>
            <div class="flex items-center">
              <el-radio-group
                v-model="processIdentityVerificationList[1].mode"
                class="min-w-[220px]"
              >
                <el-radio :value="1">手机验证码</el-radio>
                <el-radio :value="2">人脸识别</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 研究端卡片 -->
    <div class="bg-white p-4 rounded mt-4">
      <div class="font-bold mb-4">研究端</div>
      <div class="flex flex-wrap">
        <span class="ml-4 text-[14px]">触发事件</span>
        <div class="">
          <el-checkbox
            v-model="processIdentityVerificationList[2].enable"
            class="ml-6"
            >参加会议</el-checkbox
          >
          <div class="flex">
            <el-radio-group v-model="verificationValidity" class="ml-[40px]">
              <el-radio :value="true">单事件核验有效期</el-radio>
            </el-radio-group>
            <el-input
              v-model.number="processIdentityVerificationList[2].expiredTime"
              class="!w-[110px] ml-[20px]"
              placeholder="请输入"
              maxlength="15"
              @input="
                (e) => {
                  let n = e.replace(/^\D*(0|([1-9]\d*))$/, '$1')
                  if (isNaN(parseInt(n))) {
                    n = '0'
                  } else {
                    n = parseInt(n)
                  }
                  processIdentityVerificationList[2].expiredTime = n
                }
              "
            />
            <el-select
              v-model="processIdentityVerificationList[2].timeUnit"
              placeholder="请选择"
              style="min-width: 40px; width: 110px"
              class="ml-[10px]"
            >
              <el-option label="天" :value="1" />
              <el-option label="小时" :value="2" />
              <el-option label="分钟" :value="3" />
            </el-select>
            <div class="min-w-[80px] ml-[20px] mt-[6px] text-[14px]">
              核验方式
            </div>
            <div class="flex items-center">
              <el-radio-group
                v-model="processIdentityVerificationList[2].mode"
                class="min-w-[220px]"
              >
                <el-radio :value="1">手机验证码</el-radio>
                <el-radio :value="2">人脸识别</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, reactive, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getProcessIdentityVerification,
  postProcessIdentityVerification,
} from '@/api/home'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'ProcessIdentityVerification',
  setup() {
    const store = useStore()
    const studyId = store.state.studyItem.studyId

    const state = reactive({
      verificationValidity: true,
      processIdentityVerificationList: [
        {
          configType: 1,
          triggerEvent: 1,
          enable: false,
          expiredTime: 7,
          timeUnit: 1,
          mode: 1,
        },
        {
          configType: 1,
          triggerEvent: 2,
          enable: false,
          expiredTime: 30,
          timeUnit: 3,
          mode: 1,
        },
        {
          configType: 2,
          triggerEvent: 2,
          enable: false,
          expiredTime: 30,
          timeUnit: 3,
          mode: 1,
        },
      ],
      saveLoading: false,
      handleSave: () => {
        state.saveLoading = true
        postProcessIdentityVerification(studyId, state.processIdentityVerificationList)
          .then(() => {
            ElMessage.success('保存成功')
            state.saveLoading = false
            state.onLoad()
          })
          .catch(() => {
            state.saveLoading = false
          })
      },
      onLoad: async () => {
        try {
          const res: any = await getProcessIdentityVerification(studyId)
          if (res?.length) {
            state.processIdentityVerificationList = res
          }
        } catch {
          // ElMessage.error('获取核验配置失败')
        }
      },
    })

    onBeforeMount(() => {
      state.onLoad()
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>
