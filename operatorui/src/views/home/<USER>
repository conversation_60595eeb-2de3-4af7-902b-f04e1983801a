<template>
  <div class="personal-container">
    <div class="personal-name">
      <p>昵称 :</p>
      <i v-if="infoForm.name">{{ infoForm.name }}</i>
    </div>

    <div class="personal-name">
      <p>角色 :</p>
      <i v-if="infoForm.role">{{ infoForm.role }}</i>
    </div>

    <div class="personal-name">
      <p>手机号 :</p>
      <i v-if="infoForm.mobile">{{ infoForm.mobile }}</i>
    </div>

    <div class="personal-profile-picture">
      <p>头像 :</p>
      <!-- 图片可预览 -->
      <el-image
        v-if="infoForm.avatar"
        style="maxWidth: 200px"
        hide-on-click-modal
        lazy
        :src="infoForm.avatar"
        :preview-src-list="[infoForm.avatar]"
      />
      <div v-else class="personal-nopicture">
        暂无头像,可以点击修改个人信息-上传头像
      </div>
    </div>

    <div class="personal-edit" @click="editPersonal">
      <el-link type="primary" href="javascript:;" icon="el-icon-edit">
        修改个人信息
      </el-link>
    </div>
    <!-- 修改个人信息 编辑框 -->
    <el-dialog v-model="infoForm.editPersonalFalg" title="修改个人信息">
      <el-form
        ref="editInfoFormRef"
        :model="editInfoForm"
        label-width="80px"
        :rules="rules"
      >
        <el-form-item label="昵称" prop="name">
          <el-input v-model="editInfoForm.name" maxlength="100" />
        </el-form-item>

        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="editInfoForm.mobile" maxlength="11" />
        </el-form-item>

        <el-form-item label="头像">
          <YyUpload :upload-img-falg="true" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="onSubmit">
            保存
          </el-button>
          <el-button
            @click="
              () => {
                infoForm.editPersonalFalg = false
              }
            "
          >
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, provide, toRefs } from 'vue'
import { useStore } from 'vuex'
import YyUpload from '@/components/Upload/index.vue'

export default defineComponent({
  name: 'Personal',
  components: {
    YyUpload,
  },
  setup() {
    const store = useStore()
    const infoForm = reactive({
      editPersonalFalg: false,
      ...store.state.account.userinfo,
    }) // 是否开启修改
    const editInfoForm = reactive({ ...store.state.account.userinfo })
    const state = reactive({
      editInfoFormRef: null,
      loading: false,
      rules: {
        name: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
      },

      // 打开修改个人信息编辑框
      editPersonal: () => {
        infoForm.editPersonalFalg = true
      },

      // 保存-修改
      onSubmit: () => {
        state.editInfoFormRef.validate((valid) => {
          if (valid) {
            state.loading = true
            ElMessage.success('修改成功')
            state.loading = false
            infoForm.editPersonalFalg = false
          }
        })
      }
    })

    provide('uploadInfoImg', editInfoForm.avatar ?? '')

    return {
      infoForm,
      editInfoForm,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.personal-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  .personal-name {
    display: flex;
    align-items: center;
    p {
      min-width: 70px;
      text-align: right;
      color: blue;
      font-size: 18px;
      font-weight: 700;
    }
    i {
      margin-left: 30px;
      color: pink;
      font-size: 18px;
      font-weight: 700;
      &:hover {
        font-size: 24px;
      }
    }
  }
  .personal-profile-picture {
    display: flex;
    p {
      min-width: 70px;
      text-align: right;
      color: blue;
      font-size: 18px;
      font-weight: 700;
      margin-right: 30px;
    }
    .personal-nopicture {
      display: flex;
      align-items: center;
      color: rgba(
        242,
        74,
        12,
        1
      ); //rgba(96, 105, 105, 1);//rgba(169, 209, 248, 1)
    }
  }
  .personal-edit {
    position: absolute;
    right: 50px;
    top: 0px;
    &:hover {
      cursor: pointer;
    }
    :deep(.el-link.el-link--primary.is-underline) {
      font-size: 16px;
      font-weight: 700;
    }
  }
}
</style>
