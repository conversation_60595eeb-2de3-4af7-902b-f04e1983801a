<template>
  <div class="EditQuestionnaireContent">
    <el-tabs
      v-model="activeName"
      type="card"
      class="demo-tabs"
      @tab-click="tabsHandleClick"
    >
      <el-tab-pane label="基本信息" name="Infos">
        <el-form
          ref="questionnaireOptionInfosRef"
          label-position="top"
          :model="questionnaireContent"
          :rules="rules"
        >
          <el-form-item
            label="可见性"
            :label-width="formLabelWidth"
            prop="dctQuestItemDisplayType"
            style="width: 30%"
          >
            <el-select
              v-model="questionnaireContent.dctQuestItemDisplayType"
              placeholder="请选择可见性"
            >
              <el-option
                v-for="item in QUESTIONNAIRE_CONTENT.DropInfos
                  .questItemDisplayType"
                :key="item.id"
                :label="item.itemName"
                :value="item.itemCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="排序值"
            :label-width="formLabelWidth"
            prop="dctSort"
            style="width: 30%"
          >
            <el-input
              v-model="questionnaireContent.dctSort"
              placeholder="请填写正整数"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="DCT编码"
            :label-width="formLabelWidth"
            prop="dctCode"
            style="width: 30%"
          >
            <el-input
              v-model.trim="questionnaireContent.dctCode"
              placeholder="系统自动生成，带出"
              disabled
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="EDC中题目描述"
            :label-width="formLabelWidth"
            prop="style"
            style="width: 100%"
          >
            <el-input
              v-model="questionnaireContent.edcFieldLabel"
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="DCT中题目名称"
            :label-width="formLabelWidth"
            prop="fieldLabel"
            style="width: 100%"
          >
            <!-- <el-input
              v-model.trim="questionnaireContent.fieldLabel"
              placeholder="请填写题目名称"
              autocomplete="off"
              maxlength="666"
            /> -->
            <trial-wang-editor
              ref="fieldLabelRef"
              :toolbar-config="toolbarConfig"
              :editor-config="editorConfig"
            />
          </el-form-item>

          <div class="w-full flex">
            <el-form-item
              label="是否必填"
              :label-width="formLabelWidth"
              prop="isRequired"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.isRequired"
                placeholder="请选择是否必填"
                :disabled="questionnaireContent?.crfFieldType === 3"
              >
                <el-option
                  v-for="item in isRequiredOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="控件类型"
              :label-width="formLabelWidth"
              prop="crfFieldControl"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.crfFieldControl"
                placeholder="请选择控件类型"
                :disabled="questionnaireContent?.crfFieldType === 3"
                @change="() => {
                  if (questionnaireContent.crfFieldControl !== 6) {
                    questionnaireContent.dctDateControlDefault = false;
                  }
                }"
              >
                <el-option
                  v-for="item in QUESTIONNAIRE_CONTENT.DropInfos
                    .questFieldControl"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemCode"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="数据类型"
              :label-width="formLabelWidth"
              prop="crfFieldType"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.crfFieldType"
                placeholder="请选择数据类型"
                @change="crfFieldTypeChange"
              >
                <el-option
                  v-for="item in QUESTIONNAIRE_CONTENT.DropInfos.questFieldType"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemCode"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="flex w-full">
            <el-form-item
              v-if="
                questionnaireContent.crfFieldControl === 6 ||
                questionnaireContent.crfFieldControl === 7 ||
                questionnaireContent.crfFieldControl === 8 ||
                questionnaireContent.crfFieldControl === 11
              "
              label="UK录入设置"
              :label-width="formLabelWidth"
              prop="isAllowUK"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.isAllowUK"
                placeholder="请选择"
                :disabled="questionnaireContent?.specialFieldType === 3"
              >
                <el-option label="不支持录入UK" :value="0" />
                <el-option label="支持录入UK" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="
                questionnaireContent.crfFieldControl === 6 ||
                questionnaireContent.crfFieldControl === 8 ||
                questionnaireContent.crfFieldControl === 11
              "
              label="未来日期"
              :label-width="formLabelWidth"
              prop="isFutureDate"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.isFutureDate"
                placeholder="请选择"
              >
                <el-option label="不支持" :value="0" />
                <el-option label="支持" :value="1" />
              </el-select>
            </el-form-item>
            <div
              v-if="
                questionnaireContent.crfFieldControl === 6 ||
                questionnaireContent.crfFieldControl === 8 ||
                questionnaireContent.crfFieldControl === 11
              "
              style="width: 32%"
              class="flex"
            >
              <el-form-item
                label="年份区间"
                :label-width="formLabelWidth"
                class="flex-1"
                prop="minimumYear"
              >
                <el-input
                  v-model.trim="questionnaireContent.minimumYear"
                  placeholder="请输入"
                />
              </el-form-item>
              <div class="mr-5 mt-30-px">-</div>
              <div class="flex-1 mt-30-px">
                <el-form-item prop="maximumYear">
                  <el-input
                    v-model.trim="questionnaireContent.maximumYear"
                    placeholder="请输入"
                  />
                </el-form-item>
              </div>
            </div>
          </div>
          <el-form-item
            label="单位"
            :label-width="formLabelWidth"
            prop="dctQuestUnit"
            style="width: 32%"
          >
            <el-input
              v-model.trim="questionnaireContent.dctQuestUnit"
              placeholder="请填写单位"
              :disabled="
                (questionnaireContent.crfFieldControl !== 3 ||
                  questionnaireContent.crfFieldType === 3) &&
                (questionnaireContent.crfFieldControl !== 5 ||
                  questionnaireContent.crfFieldType === 5)
              "
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="字段特殊类型"
            :label-width="formLabelWidth"
            prop="specialFieldType"
            style="width: 32%"
          >
            <el-select
              v-model="questionnaireContent.specialFieldType"
              placeholder="请选择字段特殊类型"
              clearable
              @change="() => {
                if (questionnaireContent.specialFieldType === 3) {
                  questionnaireContent.isAllowUK = 0
                }
              }"
              :disabled="
                questionnaireContent?.crfFieldType === 3 ||
                !!questionnaireContent?.parentId
              "
            >
              <el-option
                v-for="item in QUESTIONNAIRE_CONTENT.DropInfos
                  .questSpecialFieldType"
                :key="item.id"
                :label="item.itemName"
                :value="item.itemCode"
              />
            </el-select>
          </el-form-item>
          <!-- 是否只读 -->
          <el-form-item
            label="是否只读"
            :label-width="formLabelWidth"
            prop="isReadOnly"
            style="width: 32%"
          >
            <el-select
              v-model="questionnaireContent.isReadOnly"
              placeholder="请选择"
            >
              <el-option
                v-for="item in isRequiredOptions"
                :key="item.id"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="questionnaireContent.parentId"
            label="列表默认展示"
            :label-width="formLabelWidth"
            prop="specialFieldType"
            style="width: 32%"
          >
            <el-select
              v-model="questionnaireContent.extensionShow"
              placeholder="请选择是否显示"
              clearable
            >
              <el-option
                v-for="exitem in isRequiredOptions"
                :key="exitem.value"
                :label="exitem.label"
                :value="exitem.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="填写指南"
            :label-width="formLabelWidth"
            prop="fieldDescription"
            style="width: 100%"
          >
            <!-- <el-input
              v-model.trim="questionnaireContent.fieldDescription"
              placeholder="请填写答题指南"
              type="textarea"
              autocomplete="off"
              maxlength="666"
            /> -->
            <trial-wang-editor
              ref="fieldDescriptionRef"
              :toolbar-config="toolbarConfig"
              :editor-config="editorConfig"
            />
          </el-form-item>
          <div class="synchronization">
            <el-form-item
              v-if="questionnaireContent.edcFieldCode"
              label="数据同步默认值"
              :label-width="formLabelWidth"
              prop="EDCFiledDefaultValue"
              style="width: 60%"
            >
              <el-input
                v-model.trim="questionnaireContent.edcFiledDefaultValue"
                placeholder="请填写数据同步默认值，若控件类型为选项时，请填写DCT选项序列值"
              />
            </el-form-item>
            <div
              v-show="questionnaireContent.crfFieldControl == 6"
              class="radio"
            >
              <el-checkbox v-model="questionnaireContent.dctDateControlDefault"
                >以首次提交日期为默认值</el-checkbox
              >
            </div>
          </div>
          <!-- 自动计算 -->
          <div class="centerflex mx-1">
            <el-switch v-model="questionnaireContent.hasCalculationFormula" />
            <span class="px-3">自动计算</span>
          </div>
          <!-- 入排标准 -->
          <div
            v-if="
              QUESTIONNAIRE_INFOS?.questionnaireInfo
                ?.questPatientTemplateType === 4
            "
            class="synchronization mt-5"
          >
            <el-form-item
              class="standard"
              label="入排标准"
              prop="dischargeStandard"
            >
              <el-radio-group v-model="questionnaireContent.dischargeStandard">
                <el-radio class="mr-10" :label="1">入选标准</el-radio>
                <el-radio :label="2">排除标准</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </el-form>
        <!-- 按钮 -->
        <div class="footer">
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="submitInfosRelevanceOption('I')"
            >保存</el-button
          >
        </div>
      </el-tab-pane>

      <el-tab-pane
        v-if="questionnaireContent.edcFieldCode"
        label="EDC信息"
        name="EdcInfo"
      >
        <el-form
          ref="questionnaireOptionRef"
          label-position="top"
          :model="questionnaireContent"
          :rules="rules"
        >
          <el-form-item
            label="对应EDC字段ID"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              :placeholder="
                EdcList.edcFieldId !== 0 ? EdcList.edcFieldId + '' : ''
              "
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="对应EDC分组ID"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              v-model.trim="EdcList.edcArmIds"
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="EDC中字段顺序"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              v-model.trim="EdcList.edcSort"
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="对应EDC父字段ID"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              :placeholder="
                EdcList.edcpFieldId !== 0 ? EdcList.edcpFieldId : ''
              "
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="对应EDC关联字段CODE"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              v-model.trim="EdcList.edcRefFieldCode"
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="对应EDC关联字段的选项"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              v-model.trim="EdcList.edcRefItemValue"
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="对应EDC关联字段显示还是隐藏"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input v-model="EdcList.edcRefType" placeholder="" disabled />
          </el-form-item>
          <el-form-item
            label="字段类型"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              v-model.trim="EdcList.edcElementType"
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="EDC中的FieldCode"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 30%"
          >
            <el-input
              v-model.trim="EdcList.edcFieldCode"
              disabled
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            label="EDC中填写指南"
            :label-width="formLabelWidth"
            prop="synchronization"
            style="width: 90%"
          >
            <el-input
              v-model.trim="EdcList.edcFieldGuideLines"
              disabled
              autocomplete="off"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane
        v-if="questionnaireContent.id"
        label="关联条件"
        name="Relevance"
      >
        <el-form
          ref="questionnaireRelevanceRef"
          label-position="top"
          :model="questionnaireContent"
          :rules="rules"
        >
          <el-radio v-model="radio" label="1">题目答案关联</el-radio>
          <el-radio v-model="radio" label="2">特殊关联</el-radio>
          <!-- <br /> -->
          <div v-show="radio === '1'" style="width: 100%">
            <el-form-item
              label="关联题目"
              :label-width="formLabelWidth"
              prop="refDctCode"
              style="width: 80%"
            >
              <el-select
                v-model="relevanceObj.refDctCode"
                placeholder="请选择关联题目"
                clearable
                @change="changeFieldLabel"
              >
                <el-option
                  v-for="(item, index) in questTemplateArr"
                  :key="index"
                  :label="item.removeHtmlTagOfFieldLabel"
                  :value="item.dctCode"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="关联选项值"
              :label-width="formLabelWidth"
              prop="refItemValue"
              style="width: 51%"
            >
              <el-select
                v-if="
                  questTemplateArr[questTemplateOptionIndex]
                    ?.refQuestTemplateItemOption
                "
                v-model="relevanceObj.refItemValue"
                placeholder="请选择关联选项值"
                multiple
                clearable
              >
                <el-option
                  v-for="item in questTemplateArr[questTemplateOptionIndex]
                    .refQuestTemplateItemOption"
                  :key="item.value"
                  :label="item.itemName"
                  :value="item.itemValue"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="关联行为"
              :label-width="formLabelWidth"
              prop="refType"
              style="width: 51%"
            >
              <el-select
                v-model="relevanceObj.refType"
                placeholder="请选择关联行为"
                clearable
              >
                <el-option
                  v-for="item in refTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div v-show="radio === '2'" style="width: 100%">
            <div style="display: flex">
              <div class="cycle">当</div>
              <el-form-item
                :label-width="formLabelWidth"
                prop="type"
                style="width: 15%"
              >
                <el-select v-model="newGender" placeholder="请选择" clearable>
                  <el-option value="性别">性别</el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :label-width="formLabelWidth"
                prop="type"
                style="width: 12%"
              >
                <el-select v-model="newEqual" placeholder="请选择">
                  <el-option value="等于">等于</el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :label-width="formLabelWidth"
                prop="type"
                style="width: 15%"
              >
                <el-select v-model="newBoy" placeholder="请选择" clearable>
                  <el-option label="男" value="1">男</el-option>
                  <el-option label="女" value="2">女</el-option>
                </el-select>
              </el-form-item>
            </div>
            <el-form-item
              label="关联行为"
              :label-width="formLabelWidth"
              prop="type"
              style="width: 51%"
            >
              <el-select
                v-model="newShow"
                placeholder="请选择关联行为"
                clearable
              >
                <el-option label="显示" :value="1">显示</el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form>
        <div class="footer">
          <el-button
            type="primary"
            plain
            size="large"
            class="clRelevance"
            @click="clRelevance"
            >清空关联</el-button
          >
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="submitInfosRelevanceOption('R')"
            >保存</el-button
          >
        </div>
      </el-tab-pane>

      <el-tab-pane
        v-if="
          questionnaireContent.id &&
          (questionnaireContent.crfFieldControl === 9 ||
            questionnaireContent.crfFieldControl === 10) &&
          (oldQuestionnaireContent.crfFieldControl === 9 ||
            oldQuestionnaireContent.crfFieldControl === 10)
        "
        label="选项"
        name="Option"
      >
        <el-button type="primary" plain @click="submitOptionTab('a')"
          >新建选项</el-button
        >
        <el-table
          :data="questTemplateOptionArr"
          style="width: 100%; margintop: 10px"
          :header-cell-style="{
            background: '#f2f2f2',
          }"
        >
          <el-table-column
            v-if="questionnaireContent.edcFieldCode"
            prop="edcItemValue"
            label="EDC序列值"
            width="220"
          />
          <el-table-column
            v-if="questionnaireContent.edcFieldCode"
            prop="edcItemName"
            label="EDC文本内容"
            width="220"
          />
          <el-table-column
            v-if="questionnaireContent.edcFieldCode"
            prop="itemValue"
            label="DCT序列值"
            width="220"
          />
          <el-table-column
            v-if="!questionnaireContent.edcFieldCode"
            fixed
            prop="itemValue"
            label="DCT序列值"
            width="220"
          />
          <el-table-column prop="itemName" label="DCT文本内容" width="360" />
          <el-table-column prop="sort" label="DCT排序" width="220" />
          <el-table-column prop="gaugeValue" label="对应分值" width="220" />
          <el-table-column
            v-if="questionnaireContent.crfFieldControl === 10"
            prop="mutualExclusionText"
            label="互斥"
            width="220"
          />
          <el-table-column width="150" fixed="right" label="操作">
            <template #default="scope">
              <el-button
                text
                type="primary"
                size="small"
                @click="submitOptionTab('e', scope.row)"
                >编辑</el-button
              >
              <el-button
                text
                type="danger"
                size="small"
                style="color: #ff0000"
                @click="submitOptionTab('d', scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <!-- 自动计算 -->
      <el-tab-pane
        v-if="
          questionnaireContent.id && questionnaireContent?.hasCalculationFormula
        "
        label="自动计算"
        name="AutomaticCalculation"
      >
        <div class="my-3">计算公式</div>
        <el-select
          v-if="QUESTIONNAIRE_CONTENT?.DropInfos?.questItemCalculationFormula"
          v-model="questionnaireContent.calculationFormula"
          placeholder="请选择"
        >
          <el-option
            v-for="item in QUESTIONNAIRE_CONTENT.DropInfos
              .questItemCalculationFormula"
            :key="item.value"
            :label="item.itemName"
            :value="item.itemCode"
          />
        </el-select>
        <div class="footer mt-96">
          <el-button
            type="primary"
            size="large"
            :loading="saveBtnLoading"
            @click="submitInfosRelevanceOption('A')"
            >保存</el-button
          >
        </div>
      </el-tab-pane>
    </el-tabs>
    <!-- 选项弹窗 -->
    <trial-dialog
      v-model="contentDialogShow"
      :title="contentDialogObj.id ? '编辑选项' : '新建选项'"
    >
      <template #DialogBody>
        <el-form
          ref="contentDialogRef"
          :model="contentDialogObj"
          :rules="contentDialogRules"
          label-position="top"
          style="display: flex; flex-warp: warp"
        >
          <el-form-item
            label="DCT选项序列值"
            :label-width="formLabelWidth"
            prop="itemValue"
            style="width: 50%"
          >
            <el-input
              v-model.trim="contentDialogObj.itemValue"
              placeholder="请填写DCT选项序列值"
              autocomplete="off"
              maxlength="666"
              :disabled="contentDialogObj.id ? true : false"
            />
          </el-form-item>
          <el-form-item
            label=" DCT选项排序"
            :label-width="formLabelWidth"
            prop="sort"
            style="width: 50%"
          >
            <el-input
              v-model.trim="contentDialogObj.sort"
              placeholder="请填写DCT选项排序"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="DCT选项文本内容"
            :label-width="formLabelWidth"
            prop="itemName"
            style="width: 100%"
          >
            <el-input
              v-model.trim="contentDialogObj.itemName"
              placeholder="请填写DCT选项文本内容"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="对应分值"
            :label-width="formLabelWidth"
            prop="gaugeValue"
            style="width: 50%"
          >
            <el-input
              v-model.trim="contentDialogObj.gaugeValue"
              placeholder="请填写对应分值"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <!-- 互斥 -->
          <div
            v-if="questionnaireContent?.crfFieldControl === 10"
            class="w-full centerflex-h"
          >
            <el-switch v-model="contentDialogObj.mutualExclusion" />
            <span class="px-3">互斥</span>
          </div>
          <div style="width: 20%" />
          <div
            v-if="questionnaireContent.edcFieldCode"
            style="width: 100%; display: flex; flex-wrap: wrap"
          >
            <el-form-item label="EDC选项序列值" style="width: 50%">
              <el-input v-model="contentDialogObj.edcItemValue" disabled />
            </el-form-item>
            <el-form-item label="EDC选项排序" style="width: 50%">
              <el-input v-model="contentDialogObj.edcSort" disabled />
            </el-form-item>
            <el-form-item label="EDC选项文本内容" style="width: 100%">
              <el-input v-model="contentDialogObj.edcItemName" disabled />
            </el-form-item>
            <el-form-item label="EDC中选项CODE" style="width: 50%">
              <el-input v-model="contentDialogObj.edcFieldItemId" disabled />
            </el-form-item>
          </div>
        </el-form>
      </template>
      <template #footer>
        <div class="footer">
          <el-button size="large" @click="contentDialogShow = false"
            >取 消</el-button
          >
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="submitOptionTab('s')"
            >保 存</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <!-- 删除按钮 -->
    <div class="delete-pos-tr">
      <el-button size="default" @click="backRefresh">返回</el-button>
      <el-button
        v-if="!questionnaireContent.edcFieldCode"
        size="default"
        type="danger"
        :loading="loading"
        @click="deletePresent"
        >删除</el-button
      >
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  deleteQuestTemplateItem,
  postQuestTemplateItem,
  putQuestTemplateItemBaseInfo,
  putQuestTemplateItemRef,
  getQuestTemplateOption,
  postQuestTemplateOption,
  deleteQuestTemplateOption,
  getQuestTemplateItem,
  getDropRefQuestTemplateItem,
  getDropRefQuestTemplateItemTable,
  putQuestTemplateItemBaseInfoTable,
  putQuestTemplateItemRefTable,
  postQuestTemplateItemTable,
} from '@/api/home'
import { optionSequentialValue } from '@/utils'

export default defineComponent({
  name: 'EditQuestionnaireContent',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => { },
    },
  },
  setup({ request }) {
    const QUESTIONNAIRE_CONTENT = inject('QUESTIONNAIRE_CONTENT')
    const QUESTIONNAIRE_INFOS = inject('QUESTIONNAIRE_INFOS')
    const base64LimitSizeNum = 0.5 * 1024 * 1024
    const yearVerify = (rule, value, callback) => {
      if (Number(state.questionnaireContent.maximumYear) < Number(state.questionnaireContent.minimumYear)) {
        return callback(new Error(rule.message))
      }
      return callback()
    }
    const state = reactive({
      editorConfig: {
        MENU_CONF: {
          uploadImage: {
            server: '/api', // 没有配置地址
            base64LimitSize: base64LimitSizeNum, // 5kb
            // 单个文件上传失败
            onError() {
              ElMessage.error(`上传图片最大${base64LimitSizeNum}kb`)
            },
          }
        }
      },
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          // 'uploadImage', // 上传图片
          'uploadVideo' // 上传视频
        ],
      },
      clearTips: false,
      fieldDescriptionRef: null, // 填写指南ref
      fieldLabelRef: null, // 题目ref
      labelPosition: 'top',
      newGender: '',
      newEqual: '等于',
      newBoy: '',
      newShow: '',
      EdcList: {},
      questionnaireContent: {
        crfFieldControl: null,
        crfFieldType: null,
        dctCode: '',
        dctQuestItemDisplayType: 3,
        dctQuestTemplateId: null,
        dctQuestUnit: null,
        dctSort: null,
        fieldDescription: '',
        fieldLabel: '',
        id: null,
        isRequired: 0,
        questTemplateOption: [],
        refDctCode: '',
        refItemValue: '',
        refType: 0,
        specialFieldType: null,
        EDCFiledDefaultValue: '',
        dctDateControlDefault: false,
        dischargeStandard: 0,
        maximumYear: '2099',
        minimumYear: '1900',
        isAllowUK: 0,
        isFutureDate: 1
      },
      oldQuestionnaireContent: {}, // 未保存前的基本信息对象
      formLabelWidth: '140px',
      questionnaireOptionInfosRef: null, // 基本信息ref
      questionnaireOptionRef: null, // EDC信息ref
      relevanceObjRef: null, // 关联条件Ref
      questionnaireRelevanceRef: null, // 关联条件
      contentDialogShow: false, // 选项弹窗显示隐藏
      contentDialogObj: {
        dctQuestItemId: '',
        gaugeValue: '0',
        id: null,
        itemContent: '',
        itemName: '',
        itemValue: '',
        sort: null,
        mutualExclusion: false
      },
      radio: '1',
      contentDialogRef: null, // 选项新增编辑Ref
      loading: false,
      saveBtnLoading: false,
      activeName: 'Infos', // tab高亮
      questTemplateOptionIndex: 0, // 关联选项值下标
      questTemplateArr: [], // 关联题目用的数组
      // 关联对象
      relevanceObj: {
        questTemplateItemId: '',
        refDctCode: '',
        refItemValue: '',
        refType: null,
        // relevanceSpecial: 1
      },
      // 是否必填
      isRequiredOptions: [
        {
          value: 1,
          label: '是',
        },
        {
          value: 0,
          label: '否',
        },
      ],
      // 关联行为
      refTypeOptions: [
        {
          value: 1,
          label: '显示',
        },
        {
          value: 2,
          label: '隐藏',
        },
      ],
      sexOptions: [
        {
          value: 1,
          label: '男',
        },
        {
          value: 2,
          label: '女',
        },
      ],
      questTemplateOptionArr: [], // 选项tabel表
      rules: {
        fieldLabel: [
          { required: true, message: '请填写DCT中题目名称', trigger: 'blur' },
        ],
        dctSort: [{ required: true, message: '请填写正整数', trigger: 'blur' }],
        dctQuestItemDisplayType: [
          { required: true, message: '请选择问卷可见性', trigger: 'blur' },
        ],
        isRequired: [
          { required: true, message: '请选择是否必填', trigger: 'blur' },
        ],
        crfFieldControl: [
          { required: true, message: '请选择控件类型', trigger: 'blur' },
        ],
        crfFieldType: [
          { required: true, message: '请选择数据类型', trigger: 'blur' },
        ],
        // specialFieldType: [
        //   { required: true, message: '请选择字段特殊类型', trigger: 'blur' },
        // ],
        dischargeStandard: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        isAllowUK: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        isFutureDate: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        minimumYear: [
          { required: true, message: '请输入年份', trigger: 'blur' },
          {
            pattern: /^[0-9]{4}$/,
            message: '请填写正确年份',
            trigger: 'blur',
          },
          { validator: yearVerify, message: '开始年份不能超过结束年份', trigger: 'blur' }
        ],
        maximumYear: [
          { required: true, message: '请输入年份', trigger: 'blur' },
          {
            pattern: /^[0-9]{4}$/,
            message: '请填写正确年份',
            trigger: 'blur',
          },
          { validator: yearVerify, message: '结束年份不能小于开始年份', trigger: 'blur' }
        ]
      },
      contentDialogRules: {
        itemName: [
          { required: true, message: '请填写DCT选项文本内容', trigger: 'blur' },
        ],
        sort: [
          { required: true, message: '请填写DCT选项排序', trigger: 'blur' },
          {
            pattern: /^[0-9]\d*$/,
            message: '请填写非负整数',
            trigger: 'blur',
          },
        ],
        itemValue: [
          { required: true, message: '请填写DCT选项序列值', trigger: 'blur' },
          // {
          //   pattern: /^[0-9]\d*$/,
          //   message: '请填写非负整数',
          //   trigger: 'blur',
          // },
        ],
      },
      // 数据类型控件发生值改变
      crfFieldTypeChange: (e) => {
        if (e === 3) {
          // console.log(e) // 列表类型时
          state.questionnaireContent.crfFieldControl = 1
          state.questionnaireContent.isRequired = 0
          state.questionnaireContent.dctQuestUnit = ''
          state.questionnaireContent.specialFieldType = null
        }
      },
      // 点击tab
      tabsHandleClick: (tab: string, event: Event) => {
        switch (tab.index) {
          case '0':
            break
          case '1':
            state.getQuestTemplateOptionList()
            if (state.relevanceObj?.refType === 0) {
              state.relevanceObj.refType = null
            }
            break
          case '2':
            state.getQuestTemplateOptionList()
            if (state.relevanceObj?.refType === 0) {
              state.relevanceObj.refType = null
            }
            break
          case '3':
            state.getQuestTemplateOptionList()
            if (state.questTemplateOptionArr.length) {
              break
            }
            break
          // default: //
        }
      },
      // 获取选项列表
      getQuestTemplateOptionList: () => {
        getQuestTemplateOption(state.questionnaireContent.id).then((res) => {
          res.forEach(el => {
            if (el?.mutualExclusion) {
              el.mutualExclusionText = '是'
            } else {
              el.mutualExclusionText = '否'
            }
          })
          // mutualExclusionText
          state.questTemplateOptionArr = res
          /*
          dctQuestItemId: "0005d416-9292-c825-0000-000000000000"
          gaugeValue: 0
          id: "0005d416-9292-c82e-0000-000000000000"
          itemContent: ""
          itemName: "男"
          itemValue: "1"
          sort: 1
          */
        })
        // 关联题目的数组
        const getQuestTemplateFun = (QUESTIONNAIRE_CONTENT.editOrAddList === 'editList' || QUESTIONNAIRE_CONTENT.editOrAddList === 'addList')
          ? getDropRefQuestTemplateItemTable : getDropRefQuestTemplateItem
        getQuestTemplateFun(state.questionnaireContent.id).then(
          (res) => {
            state.questTemplateArr = res
            const relevanceObj = { ...state.relevanceObj }
            state.questTemplateArr.forEach((item, index) => {
              if (item.dctCode === state.relevanceObj.refDctCode) {
                state.questTemplateOptionIndex = index
                if (state.relevanceObj.refItemValue.length && !Array.isArray(state.relevanceObj.refItemValue)) {
                  state.relevanceObj.refItemValue = relevanceObj.refItemValue.split(',')
                } else if (!state.relevanceObj.refItemValue) {
                  state.relevanceObj.refItemValue = []
                }
              }
            })
          }
        )

        getQuestTemplateItem(state.questionnaireContent.id).then((res) => {
          state.EdcList = res
        })
      },
      // 清空关联
      clRelevance: () => {
        state.relevanceObj.refDctCode = '' // 性别
        state.relevanceObj.refItemValue = '' // 1 2
        state.relevanceObj.refType = null // 显示隐藏
        state.newGender = ''
        state.newBoy = ''
        state.newShow = ''
      },
      // 基本信息or关联 的保存
      submitInfosRelevanceOption: (flag) => {
        // I信息 R关联
        if (!state.questionnaireContent?.hasCalculationFormula) {
          // 关闭自动计算时
          state.questionnaireContent.calculationFormula = 0
        }
        if (flag === 'I') {
          // 富文本 传入接口
          state.questionnaireContent.fieldLabel = state.fieldLabelRef.gainTxt('set')
          state.questionnaireContent.fieldDescription = state.fieldDescriptionRef.gainTxt('set')
          if (state.questionnaireContent?.fieldLabel?.length > 1048576) {
            ElMessage.error('DCT中题目名称富文本内容过大，请限制在1MB以内（请压缩图片）')
            return
          } else if (state.questionnaireContent?.fieldDescription?.length > 1048576) {
            ElMessage.error('填写指南富文本内容过大，请限制在1MB以内（请压缩图片）')
            return
          }
          state.questionnaireOptionInfosRef.validate((valid) => {
            if (valid) {
              state.loading = true
              const studyVersionId = QUESTIONNAIRE_CONTENT.versionsValue || null
              // 转换特殊类型
              const questionnaireContentObj = { ...state.questionnaireContent }
              if (state.questionnaireContent.dctCode === '') {
                state.questionnaireContent.id = null
                // questionnaireContentObj.dctQuestTemplateId = studyVersionId;
                questionnaireContentObj.id = null
              }
              if (questionnaireContentObj.specialFieldType === null) {
                questionnaireContentObj.specialFieldType = 0
              }
              questionnaireContentObj.specialFieldType === ''
                ? (questionnaireContentObj.specialFieldType = 0)
                : questionnaireContentObj.specialFieldType
              questionnaireContentObj.calculationFormula = questionnaireContentObj.hasCalculationFormula
              let savefun
              // 列表时改变
              if (QUESTIONNAIRE_CONTENT.editOrAddList === 'editList' ||
                QUESTIONNAIRE_CONTENT.editOrAddList === 'addList') {
                savefun = state.questionnaireContent.id
                  ? putQuestTemplateItemBaseInfoTable(questionnaireContentObj)
                  : postQuestTemplateItemTable(studyVersionId, questionnaireContentObj)
              } else {
                savefun = state.questionnaireContent.id
                  ? putQuestTemplateItemBaseInfo(questionnaireContentObj)
                  : postQuestTemplateItem(studyVersionId, questionnaireContentObj)
              }
              // 保存或者修改-接口调用
              savefun
                .then((res) => {
                  if (!state.questionnaireContent?.id) {
                    state.relevanceObj.id = res.id
                    state.relevanceObj.dctCode = res.dctCode
                    state.questionnaireContent.id = res.id
                    state.questionnaireContent.dctCode = res.dctCode
                    if (res?.parentId) {
                      state.questionnaireContent.parentId = res.parentId
                    }
                    state.oldQuestionnaireContent = { ...res }
                  }
                  ElMessage.success('保存成功')
                  state.oldQuestionnaireContent = res
                  state.loading = false
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        } else if (flag === 'R') {
          // console.log(state.relevanceObj)
          const relevanceObj = { ...state.relevanceObj }
          if (state.questTemplateArr[state.questTemplateOptionIndex]?.crfFieldControl === 9 ||
            state.questTemplateArr[state.questTemplateOptionIndex]?.crfFieldControl === 10) {
            if (Array.isArray(relevanceObj.refItemValue) && relevanceObj.refItemValue.length) {
              relevanceObj.refItemValue = relevanceObj.refItemValue.join(',')
            } else {
              relevanceObj.refItemValue = ''
            }
          }
          relevanceObj.questTemplateItemId = relevanceObj.id
          if (!relevanceObj?.refType) {
            relevanceObj.refType = 0
          }
          if (state.radio === '2' && state.newGender && state.newBoy && state.newShow) {
            relevanceObj.refDctCode = state.newGender // 性别
            relevanceObj.refItemValue = state.newBoy // 1 2
            relevanceObj.refType = 1 // 显示隐藏
          }
          state.loading = true
          const putQuestTemplateItemRefFun = QUESTIONNAIRE_CONTENT.editOrAddList === 'editList'
            ? putQuestTemplateItemRefTable : putQuestTemplateItemRef
          putQuestTemplateItemRefFun(relevanceObj)
            .then((res) => {
              ElMessage.success('保存成功')
              state.loading = false
            })
            .catch(() => {
              state.loading = false
            })
        } else if (flag === 'A') {
          const questionnaireContentObj = { ...state.questionnaireContent }
          if (questionnaireContentObj.specialFieldType === null) {
            questionnaireContentObj.specialFieldType = 0
          }
          state.saveBtnLoading = true
          delete questionnaireContentObj.hasCalculationFormula
          putQuestTemplateItemBaseInfo(questionnaireContentObj)
            .then((res) => {
              ElMessage.success('保存成功')
              state.saveBtnLoading = false
            })
            .catch(() => {
              state.saveBtnLoading = false
            })
        }
      },
      // 返回刷新
      backRefresh: () => {
        request()
        QUESTIONNAIRE_CONTENT.contentVisible = false
        setTimeout(() => {
          const mainScoll = document.querySelector('.mainScoll')
          if (mainScoll) {
            mainScoll.scrollTop = QUESTIONNAIRE_CONTENT?.scrollTop || 0
          }
        }, 0)
      },
      // 选项表格某项保存
      submitOptionTab: (falg, item) => {
        // 保存
        if (falg === 's') {
          if (!state.contentDialogObj?.id) {
            const nextStep = optionSequentialValue(state.questTemplateOptionArr || [], state.contentDialogObj?.itemValue || '')
            if (nextStep) {
              ElMessage.warning('DCT序列值不可以重复')
              return
            }
          }
          state.contentDialogRef.validate((valid) => {
            if (valid) {
              state.contentDialogObj.dctQuestItemId =
                state.questionnaireContent.id
              state.loading = true
              // 保存或者修改-接口调用
              postQuestTemplateOption(state.contentDialogObj)
                .then(() => {
                  ElMessage.success('保存成功')
                  state.getQuestTemplateOptionList()
                  state.loading = false
                  state.contentDialogShow = false
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        } else if (falg === 'e') {
          // 编辑
          state.contentDialogObj = { ...item }
          state.contentDialogShow = true
        } else if (falg === 'a') {
          // 新增
          state.contentDialogObj = {
            dctQuestItemId: '',
            gaugeValue: null,
            id: null,
            itemContent: '',
            itemName: '',
            itemValue: '',
            sort: null,
            mutualExclusion: false
          }
          state.contentDialogShow = true
        } else if (falg === 'd') {
          // 删除
          ElMessageBox.confirm(`是否确认删除？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              deleteQuestTemplateOption(item.id).then(() => {
                ElMessage.success(`删除成功!`)
                state.getQuestTemplateOptionList()
              })
            })
            .catch(() => { })
        }
      },
      // 删除当前问卷
      deletePresent: () => {
        ElMessageBox.confirm(`是否确认删除？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deleteQuestTemplateItem(state.questionnaireContent.id).then(() => {
              ElMessage.success('删除成功')
              state.backRefresh()
            })
          })
          .catch(() => { })
      },
      // 切换当前关联选项值数组
      changeFieldLabel: (e) => {
        state.questTemplateArr.map(
          (item, index) => {
            if (item.dctCode === e) {
              state.questTemplateOptionIndex = index
              if (Array.isArray(state.relevanceObj.refItemValue)) {
                state.relevanceObj.refItemValue = []
              } else {
                state.relevanceObj.refItemValue = ''
              }
            }
          }
        )
      },
      // 关联题目数组生成
      cleanQuestTemplateArr: (id) => {
        const { questTemplateItem } =
          QUESTIONNAIRE_CONTENT.questionnaireContentViews
        for (var i = 0; i < questTemplateItem.length; i++) {
          if (questTemplateItem[i].id === id) {
            break
          }
          if (
            questTemplateItem[i].crfFieldControl === 9 ||
            questTemplateItem[i].crfFieldControl === 10
          ) {
            state.questTemplateArr.push(questTemplateItem[i])
          }
        }
      },
    })
    onMounted(() => {
      if (JSON.stringify(QUESTIONNAIRE_CONTENT.questionnaireContent) !== '{}') {
        state.questionnaireContent = QUESTIONNAIRE_CONTENT.questionnaireContent
        state.oldQuestionnaireContent = {
          ...QUESTIONNAIRE_CONTENT.questionnaireContent,
        }
        // 题目-填写指南-富文本
        state.fieldLabelRef.gainTxt('get', state.questionnaireContent?.fieldLabel)
        state.fieldDescriptionRef.gainTxt('get', state.questionnaireContent?.fieldDescription)
        // 关联对象
        state.relevanceObj = { ...QUESTIONNAIRE_CONTENT.questionnaireContent }
        if (QUESTIONNAIRE_CONTENT.questionnaireContent.refDctCode === '性别') {
          state.newGender = state.relevanceObj.refDctCode
          state.newBoy = state.relevanceObj.refItemValue
          state.newShow = state.relevanceObj.refType
          state.radio = '2'
          state.relevanceObj.refDctCode = ''
          state.relevanceObj.refItemValue = ''
          state.relevanceObj.refType = null
        }
        state.changeFieldLabel(state.relevanceObj.refDctCode)
        // state.cleanQuestTemplateArr(state.questionnaireContent.id)
        if (!QUESTIONNAIRE_CONTENT.DropInfos.questSpecialFieldType) {
          QUESTIONNAIRE_CONTENT.DropInfos.questSpecialFieldType = 1
        }
        state.questionnaireContent.crfFieldControl === 0
          ? (state.questionnaireContent.crfFieldControl = null)
          : state.questionnaireContent.crfFieldControl
        state.questionnaireContent.crfFieldType === 0
          ? (state.questionnaireContent.crfFieldType = null)
          : state.questionnaireContent.crfFieldType
        state.questionnaireContent.specialFieldType === 0
          ? (state.questionnaireContent.specialFieldType = null)
          : state.questionnaireContent.specialFieldType
      }
    })

    return {
      ...toRefs(state),
      QUESTIONNAIRE_CONTENT,
      QUESTIONNAIRE_INFOS
    }
  },
})
</script>

<style lang="less" scoped>
.EditQuestionnaireContent {
  position: relative;
  div[data-we-id] {
    width: calc(100% - 18px);
  }
  .demo-tabs > .el-tabs__content {
    padding: 32px;
    background-color: #f4f5f7;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
  }
  .delete-pos-tr {
    position: absolute;
    top: 0px;
    right: 0;
  }
  ::v-deep .el-tabs {
    min-height: 800px;
    // height: calc(100vh - 200px);
    background: #fff;
  }
  ::v-deep .el-tabs__nav-scroll {
    background: #f0f2f5;
  }
  ::v-deep .el-tabs__nav is-top,
  ::v-deep [role='tablist'] {
    background: #fff;
  }
  ::v-deep .el-tabs__item,
  ::v-deep .is-top,
  ::v-deep .is-focus {
    min-width: 97px;
    text-align: center;
  }
  ::v-deep .el-tabs__content {
    padding: 30px;
  }
  .el-form,
  .el-form--default {
    display: flex;
    flex-wrap: wrap;
    .el-input,
    .el-select {
      margin: 0 20px 0 0;
    }
    .el-select {
      width: 100%;
    }
  }
  ::v-deep .el-table__cell,
  ::v-deep .is-leaf {
    border: none !important;
  }
  ::v-deep .el-table__inner-wrapper::before {
    height: 0;
  }
  .radio {
    margin-top: 30px;
    ::v-deep .el-tabs__item,
    ::v-deep .is-top,
    ::v-deep .is-focus {
      min-width: 0;
    }
  }
  .standard {
    ::v-deep .is-focus {
      min-width: 0;
    }
  }
}
.cycle {
  margin-top: 3px;
  margin-right: 10px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
.synchronization {
  width: 100%;
  display: flex;
}
.clRelevance {
  // background: #fff;
  // border-color: #409eff;
  // &:active{
  //   color: #409eff;
  // }
  // &:hover{
  //   background: #409eff;
  //   color:  #fff;
  // }
}
</style>
