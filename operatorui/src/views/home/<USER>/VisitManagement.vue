<template>
  <div class="visit">
    <!-- 编辑访视 -->
    <div v-if="labelName">
      <div
        v-show="!QUESTIONNAIRE_CONTENT?.contentVisible"
        class="visitcontainer"
      >
        <nav class="nav-module">
          <!-- <div class="nav-btn-le">{{ edcQuestName + '——' + visitContainerObj.fatherVisitName }}</div> -->
          <div class="nav-btn-le">{{ edcQuestName + '——' + labelName }}</div>
          <div class="nav-btn-ri">
            <el-button plain @click="routerGo('back')">返回</el-button>
            <el-button
              type="primary"
              :loading="loading"
              @click="routerGo('addFaVisit')"
              >保存</el-button
            >
            <el-button type="danger" :loading="loading" @click="addRowColumn(0)"
              >删除</el-button
            >
            <!-- <el-button v-if="versionStatus === 1" type="danger" :loading="loading" @click="addRowColumn(0)">删除</el-button> -->
          </div>
        </nav>
        <div class="container">
          <el-form
            ref="detailsForm"
            :model="visitContainerObj"
            :label-position="labelPosition"
            :rules="rules"
            style="width: 100%"
          >
            <el-row>
              <el-col :span="13">
                <el-form-item label="DCT访视名称" prop="fatherVisitName">
                  <el-input
                    v-model.trim="visitContainerObj.fatherVisitName"
                    placeholder="请填访视名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="可见性" prop="dctVisitDisplay">
                  <el-select
                    v-model="visitContainerObj.dctVisitDisplay"
                    placeholder="请选择可见性"
                  >
                    <el-option
                      v-for="item in fatherVisitDispalyList"
                      :key="item.id"
                      :label="item.itemName"
                      :value="item.itemCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="13">
                <el-form-item label="DCT访视排序" prop="dctSort">
                  <el-input
                    v-model.trim="visitContainerObj.dctSort"
                    placeholder="请填访视序号"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div
        v-show="!QUESTIONNAIRE_CONTENT?.contentVisible"
        class="visitcontainer-cl"
      >
        <el-button v-if="versionStatus === 1" type="primary" @click="addVisitCl"
          >添加访视</el-button
        >
        <div v-if="visitContainerObj?.visitTemplates?.length">
          <div
            v-for="(visitItem, visitIndex) in visitContainerObj.visitTemplates"
            :key="visitIndex"
            class="visitItems"
          >
            <div class="visitItems-on" @click="changeVisitCl(visitItem)">
              <div>
                <span>DCT访视名称：</span> {{ visitItem?.visitName || '' }}
              </div>
              <div class="centerflex-h visit-display">
                <span>可见性：</span> {{ visitItem.dctVisitDisplayLabel }}
              </div>
            </div>
            <el-button
              v-if="versionStatus === 1"
              class="delete-son-vinterview"
              size="small"
              type="danger"
              @click="deleteSonInterview(visitItem)"
              >删除</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑CRF模块 -->
    <div v-else>
      <div v-show="!QUESTIONNAIRE_CONTENT?.contentVisible" class="modular">
        <nav class="nav-module">
          <div class="nav-btn-le">{{ edcQuestName + '——' + propName }}</div>
          <div class="nav-btn-ri">
            <el-button @click="routerGo('back')">返回</el-button>
            <el-button
              type="primary"
              plain
              @click="
                () => {
                  previewDialogVisible = true
                }
              "
              >预览</el-button
            >
          </div>
        </nav>

        <div class="infos-body">
          <div class="infos-body-title">
            <h4>基本信息</h4>
            <div>
              <el-button
                type="primary"
                @click="
                  () => {
                    QUESTIONNAIRE_INFOS.dialogVisible = true
                  }
                "
                >编辑</el-button
              >
              <el-button
                type="danger"
                :loading="loading"
                @click="addRowColumn(1)"
                >删除</el-button
              >
              <!-- <el-button v-if="versionStatus === 1" type="danger" :loading="loading" @click="addRowColumn(1)">删除</el-button> -->
            </div>
          </div>
          <div class="infos-body-content">
            <div class="infos-body-content-items">
              <div class="infos-body-content-items-label">DCT问卷名称</div>
              <span
                v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfName"
                v-html="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfName"
              />
            </div>
            <div class="infos-body-content-items">
              <div class="infos-body-content-items-paralleling">
                <div class="infos-body-content-items-label">DCT问卷排序</div>
                <span>{{ QUESTIONNAIRE_INFOS?.questionnaireInfo?.dctSort }}</span>
              </div>
              <div class="infos-body-content-items-paralleling">
                <div class="infos-body-content-items-label">显示规则</div>
                <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.dctQuestDisplay === 0">显示</span>
                <span v-else-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.dctQuestDisplay === 8">按公式显示</span>
                <span v-else-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.dctQuestDisplay === 9">按公式隐藏</span>
                <span v-else-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.dctQuestDisplay === 10">隐藏</span>
                <span v-else-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.dctQuestDisplay === 11">按公式当前访视显示</span>
                <span v-else-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.dctQuestDisplay === 12">按公式当前访视隐藏</span>
              </div>
              <div class="infos-body-content-items-paralleling">
                <div class="infos-body-content-items-label">填写对象</div>
                <!-- 0 = 未知, 1 = 客户问卷, 2 = 医生问卷, 3 = 非客户问卷 -->
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questTemplateType === 1
                  "
                  >受试者问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questTemplateType === 2
                  "
                  >研究者问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questTemplateType === 3
                  "
                  >非客户问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questTemplateType === 4
                  "
                  >上门医护问卷</span
                >
              </div>
            </div>
            <div class="infos-body-content-items">
              <div class="infos-body-content-items-paralleling">
                <!-- 0 = 不同步, 1 = 同步EDC -->
                <div class="infos-body-content-items-label">数据同步</div>
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.isSynchronization
                  "
                  >同步至EDC</span
                >
                <span v-else>不同步</span>
              </div>
              <div class="infos-body-content-items-paralleling">
                <div class="infos-body-content-items-label">问卷样式</div>
                <!-- 0 = 未知, 1 = 问卷完整列表, 2 = 问卷向导列表, 3 = 问卷Url, 4 = 上传图片的问卷 -->
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType ===
                      1
                  "
                  >完整问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType ===
                      2
                  "
                  >向导式问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType ===
                      3
                  "
                  >问卷URL</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType &&
                    QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType ===
                      4
                  "
                  >上传图片的问卷</span
                >
              </div>
              <div class="infos-body-content-items-paralleling">
                <div class="infos-body-content-items-label">问卷类型</div>
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 1
                  "
                  >普通问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 2
                  "
                  >不良事件</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 3
                  "
                  >合并用药</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 4
                  "
                  >入排标准</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 5
                  "
                  >访视日期</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 6
                  "
                  >初筛问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 7
                  "
                  >随机问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 8
                  "
                  >量表问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 9
                  "
                  >图片问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 10
                  "
                  >知情问卷</span
                >
                <span
                  v-if="
                    QUESTIONNAIRE_INFOS?.questionnaireInfo
                      ?.questPatientTemplateType === 11
                  "
                  >服药问卷</span
                >
              </div>
            </div>
            <div class="infos-body-content-items">
              <div class="infos-body-content-items-label">填写说明</div>
              <span
                v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfGuideline"
                v-html="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfGuideline"
              />
            </div>
          </div>
        </div>
        <!-- 核查规则（适用于单个问卷下的字段逻辑核查） -->
        <div v-if="QUESTIONNAIRE_INFOS.questionnaireInfo?.enableLogicalVerification" class="questionnaire-body">
          <div class="questionnaire-body-title">
            <div class="centerflex"><h4>核查规则</h4><div class="text-[#7F7F7F]">（适用于单个问卷下的字段逻辑核查）</div></div>
            <el-button type="primary" @click="addEditVerificationRule('add')">新增规则</el-button>
          </div>
          <trial-table
            ref="TableVerificationRef"
            border
            :pagination="false"
            :request="getVerificationList"
            :columns="tableVerificationColumns"
            class="table-verification"
          >
            <template #verificationRuleLevel="scope">
              <span v-if="scope.row.verificationRuleLevel === 1">强制修改</span>
              <span v-else-if="scope.row.verificationRuleLevel === 2">非强制修改</span>
            </template>
            <template #isEnable="scope">
              <span v-if="scope.row.isEnable">已启用</span>
              <span v-else>未启用</span>
            </template>
            <template #operate="scope">
              <el-button text size="small" type="primary" @click="addEditVerificationRule('edit',scope.row)">
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                text
                @click="addEditVerificationRule('delete',scope.row)"
              >删除</el-button>
            </template>
          </trial-table>
        </div>
        <VerificationRule ref="VerificationRuleFormRef" :request="onLoad" />

        <div class="questionnaire-body">
          <div class="questionnaire-body-title">
            <h4>问卷内容</h4>
            <div class="flex">
              <el-button style="display: none" type="primary" @click="ADD_TOPIC.addTopicShow = true">快捷新建</el-button>
              <el-button type="primary" @click="setQuestionnaire('add')">新建题目</el-button>
            </div>
          </div>
          <div class="questionnaire-body-content">
            <div
              v-for="(item, index) in QUESTIONNAIRE_CONTENT
                .questionnaireContentViews.questTemplateItem"
              :key="index"
              class="questionnaire-body-content-module"
            >
              <div class="questionnaire-body-content-module-head">
                <div class="sort">
                  ({{ item.dctSort }})
                  <span v-if="item.isRequired">(必填)</span>
                  <span
                    v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questPatientTemplateType === 4 && item.dischargeStandard === 1"
                    class="btnRP btnRP-back-o"
                  >
                    入选标准
                  </span>
                  <span
                    v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questPatientTemplateType === 4 && item.dischargeStandard === 2"
                    class="btnRP btnRP-back-t"
                  >
                    排除标准
                  </span>
                </div>
                <!-- 0 = 未知, 1 = 仅患者端显示, 2 = 仅医生端显示, 3 = 医生患者都显示 -->
                <div v-if="item?.dctQuestItemDisplayType" class="check">
                  <span v-if="item.dctQuestItemDisplayType === 1"
                    >仅患者端显示</span
                  >
                  <span v-if="item.dctQuestItemDisplayType === 2"
                    >仅医生端显示</span
                  >
                  <span v-if="item.dctQuestItemDisplayType === 3"
                    >医生患者都显示</span
                  >
                </div>
                <div class="btns">
                  <span v-if="item.edcFieldCode" />
                  <span
                    v-if="item.crfFieldType === 3"
                    class="btns-edit"
                    @click.stop="setQuestionnaire('addList', item)"
                    >添加列表字段&nbsp;&nbsp;&nbsp;&nbsp;</span
                  >
                  <span
                    v-if="!item.edcFieldCode"
                    class="btns-delete"
                    @click="setQuestionnaire('delete', item)"
                    >删除</span
                  >
                  <span
                    class="btns-edit"
                    @click="setQuestionnaire('edit', item)"
                    >编辑</span
                  >
                </div>
              </div>
              <div class="questionnaire-body-content-module-items">
                <div class="questionnaire-body-content-module-item">
                  <div class="questionnaire-body-content-module-item-lable">
                    EDC中题目名称
                  </div>
                  <span v-if="item.edcFieldLabel" v-html="item.edcFieldLabel" />
                </div>
                <div class="questionnaire-body-content-module-item">
                  <div class="questionnaire-body-content-module-item-lable">
                    DCT中题目名称
                  </div>
                  <span v-if="item.fieldLabel" v-html="item.fieldLabel" />
                </div>
                <div class="questionnaire-body-content-module-item">
                  <div class="questionnaire-body-content-module-item-lable">
                    控件类型
                  </div>
                  <!-- 0 = 未知, 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件,
                  5 = 数字控件, 6 = 日期控件, 7 = 时间控件
                  , 8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件, 11 = 年月控件 -->
                  <div v-for="(ite, idx) in QUESTIONNAIRE_CONTENT?.DropInfos?.questFieldControl" :key="idx">
                    <span v-if="ite.itemCode === item?.crfFieldControl">{{ ite.itemName }}</span>
                  </div>
                </div>
              </div>
              <!-- 列表时 -->
              <div v-if="item.crfFieldType === 3">
                <div
                  v-for="listItem in item.children"
                  :key="listItem.id"
                  class="questionnaire-list-module"
                >
                  <div class="questionnaire-body-content-module-head">
                    <div class="sort">
                      ({{ listItem.dctSort }})
                      <span v-if="listItem.isRequired">(必填)</span>
                    </div>
                    <!-- 0 = 未知, 1 = 仅患者端显示, 2 = 仅医生端显示, 3 = 医生患者都显示 -->
                    <div v-if="listItem?.dctQuestItemDisplayType" class="check">
                      <span v-if="listItem.dctQuestItemDisplayType === 1"
                        >仅患者端显示</span
                      >
                      <span v-if="listItem.dctQuestItemDisplayType === 2"
                        >仅医生端显示</span
                      >
                      <span v-if="listItem.dctQuestItemDisplayType === 3"
                        >医生患者都显示</span
                      >
                    </div>
                    <div class="btns">
                      <span v-if="listItem.edcFieldCode" />
                      <span
                        v-if="!listItem.edcFieldCode"
                        class="btns-delete"
                        @click="setQuestionnaire('delete', listItem)"
                        >删除</span
                      >
                      <span
                        class="btns-edit"
                        @click="setQuestionnaire('editList', listItem)"
                        >编辑</span
                      >
                    </div>
                  </div>
                  <div class="questionnaire-body-content-module-items">
                    <div class="questionnaire-body-content-module-item">
                      <div class="questionnaire-body-content-module-item-lable">
                        DCT中题目名称
                      </div>
                      <span
                        v-if="listItem.fieldLabel"
                        v-html="listItem.fieldLabel"
                      />
                    </div>
                    <div class="questionnaire-body-content-module-item margin0">
                      <div class="questionnaire-body-content-module-item-lable">
                        控件类型
                      </div>
                      <div v-for="(ite, idx) in QUESTIONNAIRE_CONTENT?.DropInfos?.questFieldControl" :key="idx">
                        <span v-if="ite.itemCode === listItem.crfFieldControl">{{ ite.itemName }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 扫码预览 -->
    <el-dialog v-model="previewDialogVisible" title="扫码预览">
      <div style="display: flex; justify-content: center; padding: 50px 0">
        <vue-qr
          v-if="
            siteUrl &&
            QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType &&
            QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType < 3
          "
          :text="siteUrl"
        />
        <div
          v-else-if="
            QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType === 3
          "
        >
          非用户问卷～不支持填写。
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">返回</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 基本信息弹窗 -->
    <VisitManagementInfo
      v-if="QUESTIONNAIRE_INFOS?.dialogVisible"
      :request="onLoad"
    />

    <!-- CRF新建题目 -->
    <VisitManagementContent
      v-if="QUESTIONNAIRE_CONTENT?.contentVisible"
      :request="onLoad"
    />
    <trial-dialog v-model="showVisitClFlag" :title="myTitle">
      <template #DialogBody>
        <el-form
          ref="visitClRef"
          :model="VisitManagementFrom"
          :rules="VisitManagementRules"
          label-position="top"
        >
          <el-form-item label="DCT访视名称" prop="visitName">
            <el-input
              v-model.trim="VisitManagementFrom.visitName"
              placeholder="请填访视名称"
            />
          </el-form-item>
          <div class="flex">
            <el-form-item
              class="mr-5"
              :label="`可见性（检测到该访视${
                VisitManagementFrom.hasQuestion ? '有' : '无'
              }填写任务）`"
              prop="dctVisitDisplay"
            >
              <el-select
                v-model="VisitManagementFrom.dctVisitDisplay"
                placeholder="请选择可见性"
              >
                <el-option
                  v-for="item in dctVisitDisplay"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="mr-5" label="访视性质" prop="visitCategory">
              <el-select
                v-model="VisitManagementFrom.visitCategory"
                placeholder="请选择"
              >
                <el-option label="筛选访视（唯一）" :value="2" />
                <el-option label="入组后访视" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item class="mr-5" label="子访视排序" prop="dctSort">
              <el-input
                v-model.trim="VisitManagementFrom.dctSort"
                placeholder="请输入序号"
              />
            </el-form-item>
          </div>
          <el-form-item v-if="VisitManagementFrom.visitCategory === 2" style="width: 100%" label="访视任务填写时效（受试者端）" prop="validityPeriod">
            入组后
            <div class="radioBtn-input">
              <el-input
                v-model.trim="VisitManagementFrom.validityPeriod "
                oninput="value=value.replace(/^[^\d]+/g,'')"
                placeholder="请输0或正整数"
              />
            </div>
            天内可填写（含入组当天，为0则代表入组后不可填写）
          </el-form-item>
          <div>
            <el-checkbox
              v-model="VisitManagementFrom.conductVisit"
              label="上门访视"
              size="large"
            />
          </div>
          <!-- 不是筛选器显示的 -->
          <div v-if="VisitManagementFrom.visitCategory === 1">
            <el-checkbox
              v-model="VisitManagementFrom.hasExpectedDay"
              label="预期发生日期"
              size="large"
            />
            <el-row v-if="VisitManagementFrom.hasExpectedDay">
              <el-col :span="6">
                <el-form-item label="随访基线" prop="visitBaseline">
                  <el-icon class="label-warning-visit" @click="visitHintDialogVisit = true"><Warning /></el-icon>
                  <el-select
                    v-model="VisitManagementFrom.visitBaseline"
                    placeholder="请选择随访基线"
                    @change="handleSelectchange"
                  >
                    <el-option label="入组日期" :value="1" />
                    <el-option label="人工指定" :value="2" />
                    <!-- <el-option label="首次知情日期" value="3" />
                    <el-option label="首次用药日期" value="4" /> -->
                    <el-option label="指定子访视" :value="5" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="1" />
              <el-col v-if="VisitManagementFrom.visitBaseline === 5" :span="6">
                <el-form-item label="子访视信息" prop="refVisitTempId">
                  <el-select
                    v-model="VisitManagementFrom.refVisitTempId"
                    placeholder="请选择子访视"
                  >
                    <el-option
                      v-for="item in refVisitTempList"
                      :key="item.id"
                      :label="item.visitName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="VisitManagementFrom.hasExpectedDay">
              <!-- 不是人工指定隐藏掉 -->
              <el-col v-if="VisitManagementFrom.visitBaseline !== 2" :span="6">
                <el-form-item label="这个访视预期第几天发生" prop="baseLineDay">
                  <el-input v-model="VisitManagementFrom.baseLineDay" />
                </el-form-item>
              </el-col>
              <el-col v-if="VisitManagementFrom.visitBaseline !== 2" :span="1" />
              <el-col :span="6" style="maxwidth: 100%">
                <el-form-item
                  label=" 这个访视可以比预期早发生多少天"
                  prop="minDay"
                >
                  <el-input
                    v-model.trim="VisitManagementFrom.minDay"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="1" />
              <el-col :span="6" style="maxwidth: 100%">
                <el-form-item
                  label=" 这个访视可以比预期晚发生多少天"
                  prop="maxDay"
                >
                  <el-input
                    v-model.trim="VisitManagementFrom.maxDay"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="visit-hint">
              假设1月1日入组，此为基线日期
              <br />若项目定义基线日为D0，下个访视预期在D10发生，则填11，预期发生日期为1月11日
              <br />若项目定义基线日为D1，下个访视预期在D10发生，则填10，预期发生日期为1月10日
            </div>

            <div v-if="VisitManagementFrom.hasExpectedDay" class="remind-the-way">
              <el-radio
                v-model="VisitManagementFrom.visitRemindType"
                :label="1 / 1"
                @click="clearVisitType"
                >到院随访提醒</el-radio
              >
              <el-radio
                v-model="VisitManagementFrom.visitRemindType"
                :label="2 / 1"
                @click="clearVisitType"
                >电话随访提醒</el-radio
              >
            </div>
            <div
              v-if="
                VisitManagementFrom.hasExpectedDay &&
                VisitManagementFrom.visitRemindType
              "
            >
              <el-form-item label="基于访视预期发生日期推算" prop="reminderDate">
                <el-icon class="label-warning" @click="visitHintDialog = true"
                  ><Warning
                /></el-icon>
                <el-input
                  v-model.trim="VisitManagementFrom.reminderDate"
                  style="width: 40%"
                  maxlength="999"
                  placeholder="请填写"
                />
              </el-form-item>
              <el-form-item label="提醒内容" prop="remindContent">
                <el-input
                  v-model.trim="VisitManagementFrom.remindContent"
                  maxlength="9999"
                  type="textarea"
                  rows="4"
                  placeholder="请填写提醒内容"
                />
              </el-form-item>
            </div>
          </div>
        </el-form>
      </template>

      <template #footer>
        <div class="footer-flex-end">
          <el-button size="large" @click="showVisitClFlag = false"
            >取 消</el-button
          >
          <el-button
            v-if="submitVisitManagement"
            size="large"
            type="primary"
            :loading="loading"
            @click="submitVisitManagementFrom"
            >添 加</el-button
          >
          <el-button
            v-else
            size="large"
            type="primary"
            :loading="loading"
            @click="submitVisitManagementFrom"
            >保 存</el-button
          >
        </div>
      </template>
    </trial-dialog>
    <el-dialog v-model="visitHintDialog" title="提示">
      <div>举例</div>
      <div class="emphasis-text">希望提前5天，提前1天，当天进行提醒</div>
      <div class="margin-t20">则填写</div>
      <div class="emphasis-text">-5,-1,0</div>
      <div class="margin-t20 centerflex">
        <el-button size="large" @click="visitHintDialog = false"
          >关闭</el-button
        >
      </div>
    </el-dialog>
    <el-dialog v-model="visitHintDialogVisit" title="提示">
      <div>随访基线：由人工指定</div>
      <div class="emphasis-text">需要人工（CRC、研究者）维护指定计划随访日期</div>
      <div class="margin-t20">随访基线：某个访视</div>
      <div class="emphasis-text">如果该访视没有确认实际随访日期，将会根据计划随访日期推算</div>
      <div class="emphasis-text">如果该访视有实际随访日期，将会根据实际随访日期推算</div>
      <div class="margin-t20 centerflex">
        <el-button size="large" @click="visitHintDialogVisit = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 自用键题目的 -->
    <AddNewTopic v-if="ADD_TOPIC?.addTopicShow" :topic-length="QUESTIONNAIRE_INFOS.questionnaireInfo?.questTemplateItem?.length" :dct-quest-template-id="id" :request="onLoad" />
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  onMounted,
  reactive,
  toRefs,
  provide,
  nextTick,
} from 'vue'
// import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import VueQr from 'vue-qr/src/packages/vue-qr.vue'

import {
  getVisitTemplateDropInfo, // 获取CRF访视特殊任务字段
  getVisitTemplateId, // 获取访视模板
  getVisitTemplateDrop, // 保存访视模板
  getQuestTemplateDropInfo, // 基本信息下拉
  getQuestTemplateItemDropInfo, // 所有下拉框
  getQuestTemplateInfoId, // 获取问卷
  deleteQuestTemplateItem, // 删除
  getVisitContainer,
  postVisitContainer,
  deleteVisitTemplateId, // 删除CRF访视模板
  deleteVisitContainer, // 删除父访视
  deleteModuleContainer, // 删除父问卷
  getVisitTemplate, // 获取关联子方式
} from '@/api/home'
import { deleteQuestTemplateVerificationRule } from '@/api/customTask'
import VisitManagementInfo from '@/views/home/<USER>/VisitManagementInfo.vue'
import VisitManagementContent from '@/views/home/<USER>/VisitManagementContent.vue'
import AddNewTopic from '@/views/home/<USER>/AddNewTopic.vue'
import { Warning } from '@element-plus/icons-vue'
import { deepClone } from '@/utils'

export default defineComponent({
  name: 'VisitManagement', // 编辑CRF访视和模块
  components: {
    VisitManagementInfo, // CRF编辑模块
    VisitManagementContent, // CRF新建题目
    VueQr,
    Warning,
    AddNewTopic // 自己加题目用的
  },
  props: {
    stateChange: {
      type: Object,
    },
    requestLoad: {
      type: Function,
    },
  },
  setup(props) {
    const ADD_TOPIC = reactive({
      addTopicShow: false,
      versionsValue: props.stateChange?.versionsValue || '',
    })
    // console.log(EE)
    // const router = useRouter()
    const store = useStore()
    // const route = useRoute()
    const QUESTIONNAIRE_INFOS = reactive({
      questionnaireInfo: {
        crfGuideline: '',
        crfName: '',
        dctModuleContainerId: null,
        id: null,
        isSynchronization: 0,
        moduleContainer: null,
        questDisplayType: 2,
        questPatientTemplateType: 6,
        questTemplateItem: [],
        questTemplateType: 1,
        editOrAddList: '', // 是否编辑|添加列表
      },
      dialogVisible: false,
      DropInfos: {},
      templateDropInfos: {},
    })
    const QUESTIONNAIRE_CONTENT = reactive({
      questionnaireContent: {},
      questionnaireContentViews: {},
      contentVisible: false,
      DropInfos: {},
      templateDropInfos: {},
      versionsValue: '',
      scrollTop: 0,
    })
    const state = reactive({
      siteUrl: '', // 预览二维码地址
      previewDialogVisible: false, // 扫码预览显示隐藏
      labelName: '',
      propName: '',
      dctVisitContainerId: '',
      id: '',
      versionsValue: '',
      edcQuestName: '',
      questionnaireContent: '',
      detailsForm: '',
      MyStateChangeFalg: false,
      labelPosition: 'top',
      VisitManagementFrom: {
        // 子访视
        id: '',
        dctVisitContainerId: '', // 访视ID
        dctModuleContainerId: '',
        visitName: '',
        baseLineDay: 0,
        minDay: 0,
        maxDay: 0,
        publishDate: '',
        patientVisitType: '',
        // 提醒字段
        remindContent: null,
        reminderDate: null,
        visitRemindType: 0,
        hasExpectedDay: false,
        dctVisitDisplay: '',
        dctSort: '',
        visitBaseline: 1,
        refVisitTempId: null,
        visitCategory: 2,
        validityPeriod: null,
        conductVisit: false
      },
      // VisitManagementList: [],
      dctVisitDisplay: [],
      fatherVisitDispalyList: [],
      formLabelWidth: '120px',
      visitClRef: null,
      visitContainerObj: {}, // 父访视对象
      showVisitClFlag: false, // 显示子访视的
      loading: false,
      visitHintDialog: false,
      visitHintDialogVisit: false,
      refVisitTempList: [], // 子方式数据
      rules: {
        fatherVisitName: [
          { required: true, message: '请填写访视名称', trigger: 'blur' },
        ],
        dctVisitDisplay: [
          { required: true, message: '请选择可见性', trigger: 'blur' },
        ],
        dctSort: [
          { required: true, message: '请填写访视序号', trigger: 'blur' },
          {
            pattern: /^[1-9]\d*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ],
      },
      submitVisitManagement: false, // 保存时调用不同的参数
      versionStatus: 0, // 控制添加和删除按钮的显示和隐藏
      myTitle: '',
      // 反选随访
      clearVisitType: (e) => {
        if (state.VisitManagementFrom.visitRemindType === e.target._value) {
          nextTick(() => {
            state.VisitManagementFrom.visitRemindType = 0
          })
        }
      },
      VisitManagementRules: {
        visitName: [
          { required: true, message: '请填写访视名称', trigger: 'blur' },
        ],
        dctVisitDisplay: [
          { required: true, message: '请选择可见性', trigger: 'blur' },
        ],
        baseLineDay: [
          {
            required: true,
            message: '请填写访视预期第几天发生',
            trigger: 'blur',
          },
        ],
        minDay: [
          {
            required: true,
            message: '请填写访视可以比预期早发生多少天',
            trigger: 'blur',
          },
        ],
        maxDay: [
          {
            required: true,
            message: '请填写这个访视可以比预期晚发生多少天',
            trigger: 'blur',
          },
        ],
        reminderDate: [{ required: true, message: '请填写', trigger: 'blur' }],
        remindContent: [
          { required: true, message: '请填写提醒内容', trigger: 'blur' },
        ],
        dctSort: [
          { required: true, message: '请填写子访视序号', trigger: 'blur' },
          {
            pattern: /^[1-9]\d*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ],
        visitBaseline: [
          { required: true, message: '请选择随访基线', trigger: 'blur' },
        ],
        refVisitTempId: [
          { required: true, message: '请选择子访视', trigger: 'blur' },
        ],
        visitCategory: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
        validityPeriod: [
          { required: true, message: '请输入', trigger: 'blur' },
        ]
      },
      addRowColumn: (number) => {
        if (number === 0) {
          // 删除访视
          ElMessageBox.confirm(`是否确认删除？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              deleteVisitContainer(state.visitContainerObj.id).then(() => {
                ElMessage.success(`删除成功!`)
                props.stateChange.stateChangeFalg = true
                props.requestLoad()
              })
            })
            .catch(() => {})
        } else if (number === 1) {
          // 删除问卷
          ElMessageBox.confirm(`是否确认删除？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              // console.log(QUESTIONNAIRE_INFOS.questionnaireInfo)
              deleteModuleContainer(
                QUESTIONNAIRE_INFOS.questionnaireInfo.moduleContainer.id
              ).then(() => {
                ElMessage.success(`删除成功!`)
                props.stateChange.stateChangeFalg = true
                props.requestLoad()
              })
            })
            .catch(() => {})
        }
      },
      // 删除子访视
      deleteSonInterview: (e) => {
        // 获取访视模板
        getVisitTemplateId(e.id).then((res) => {
          ElMessageBox.confirm(`是否确认删除？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              deleteVisitTemplateId(e.id).then(() => {
                ElMessage.success(`删除成功!`)
                state.onLoad()
              })
            })
            .catch(() => {})
        })
      },
      // 保存子访视
      submitVisitManagementFrom: () => {
        state.visitClRef.validate((valid) => {
          if (valid) {
            state.loading = true
            const VisitManagementFromCopy = deepClone(
              state.VisitManagementFrom
            )
            if (state.submitVisitManagement) {
              VisitManagementFromCopy.dctVisitContainerId =
                state?.dctVisitContainerId
            }
            getVisitTemplateDrop(VisitManagementFromCopy)
              .then((res) => {
                state.loading = false
                ElMessage.success('保存成功')
                state.resetFrom()
                state.onLoad()
              })
              .catch(() => {
                state.loading = false
              })
          }
        })
      },
      // 重置表单
      resetFrom: () => {
        state.visitClRef.resetFields()
        state.showVisitClFlag = false
      },
      // 随访计划
      handleSelectchange: () => {
        if (state.VisitManagementFrom?.hasExpectedDay && state.VisitManagementFrom?.visitBaseline === 2) {
          state.VisitManagementFrom.baseLineDay = 0
        }
      },
      // 改变子访视内容
      changeVisitCl: (e) => {
        state.submitVisitManagement = false
        state.myTitle = '编辑子访视'
        // 获取访视模板
        getVisitTemplateId(e.id).then((res) => {
          state.VisitManagementFrom = res
          res.visitSpecialTask === 1
            ? (res.visitSpecialTask = '需要上传照片')
            : res.visitSpecialTask === 0
            ? (res.visitSpecialTask = '无')
            : res.visitSpecialTask
          state.showVisitClFlag = true
        })
        state.getVisitTemplateCon(e.id)
      },
      // 获取子访视信息
      getVisitTemplateCon: (visitTemplateId) => {
        // 获取子访视
        getVisitTemplate(state.visitContainerObj.dctStudyVersionId, {
          visitTemplateId
        }).then((res) => {
          // console.log(res)
          state.refVisitTempList = res
        })
      },
      // 添加子访视
      addVisitCl: () => {
        state.VisitManagementFrom = {
          // 子访视
          id: '',
          dctVisitContainerId: '', // 访视ID
          dctModuleContainerId: '',
          visitName: '',
          baseLineDay: 0,
          minDay: 0,
          maxDay: 0,
          publishDate: '',
          patientVisitType: '',
          // 提醒字段
          remindContent: null,
          reminderDate: null,
          visitRemindType: 0,
          hasExpectedDay: false,
          dctVisitDisplay: '',
          dctSort: '',
          visitBaseline: 1,
          refVisitTempId: null,
          visitCategory: 2,
          validityPeriod: null,
        }
        state.myTitle = '添加子访视'
        state.getVisitTemplateCon('')
        state.showVisitClFlag = true
        state.submitVisitManagement = true
      },
      // 跳转
      routerGo: (type) => {
        if (type === 'back') {
          props.stateChange.stateChangeFalg = true
          props.requestLoad()
          // 你把父组件的onLoad传进来在这里调用
        } else if (type === 'addFaVisit') {
          state.detailsForm.validate((valid) => {
            if (valid) {
              state.loading = true
              const visitContainerObj = { ...state.visitContainerObj }
              delete visitContainerObj.visitTemplates
              postVisitContainer(visitContainerObj)
                .then(() => {
                  state.loading = false
                  state.onLoad()
                  ElMessage.success('保存成功')
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        }
      },
      setQuestionnaire: (flag, item) => {
        if (flag === 'delete') {
          ElMessageBox.confirm(`是否确认删除？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              deleteQuestTemplateItem(item.id).then(() => {
                ElMessage.success(`删除成功!`)
                state.onLoad()
              })
            })
            .catch(() => {})
        } else if (flag === 'edit' || flag === 'editList') {
          // 编辑
          QUESTIONNAIRE_CONTENT.questionnaireContent = { ...item }
          QUESTIONNAIRE_CONTENT.editOrAddList = flag
          if (
            QUESTIONNAIRE_CONTENT.questionnaireContent.specialFieldType === 0
          ) {
            QUESTIONNAIRE_CONTENT.questionnaireContent.specialFieldType = null
          }
          QUESTIONNAIRE_CONTENT.contentVisible = true
        } else if (flag === 'add' || flag === 'addList') {
          // 新增
          QUESTIONNAIRE_CONTENT.questionnaireContent = {
            crfFieldControl: null,
            crfFieldType: null,
            dctCode: '',
            dctQuestItemDisplayType: 3,
            dctQuestTemplateId: state.id, // 问卷id
            dctQuestUnit: null,
            dctSort: null,
            fieldDescription: '',
            fieldLabel: '',
            id: null,
            isRequired: 0,
            questTemplateOption: [],
            refDctCode: '',
            refItemValue: '',
            refType: 0,
            specialFieldType: null,
            parentId: item?.id || '',
            minimumYear: '1900',
            maximumYear: '2099',
            isAllowUK: 0,
            isFutureDate: 1,
          }
          QUESTIONNAIRE_CONTENT.editOrAddList = flag
          QUESTIONNAIRE_CONTENT.contentVisible = true
        }
        const mainScoll = document.querySelector('.mainScoll')
        if (flag !== 'delete' && mainScoll) {
          QUESTIONNAIRE_CONTENT.scrollTop = mainScoll.scrollTop
          mainScoll.scrollTop = 0
        }
      },
      //
      tableVerificationColumns: [
        { label: '规则名称', prop: 'ruleName', minWidth: 280 },
        { label: '规则效力', tdSlot: 'verificationRuleLevel', minWidth: 120 },
        { label: '状态', tdSlot: 'isEnable', minWidth: 120 },
        {
          label: '操作',
          fixed: 'right',
          width: 140,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      async getVerificationList(params) {
        // params是从组件接收的，包含分页和搜索字段。
        try {
          const list = QUESTIONNAIRE_INFOS.questionnaireInfo?.verificationRules
          const { data } = await new Promise((rs) => {
            setTimeout(() => {
              rs({
                // code: 200,
                data: {
                  list,
                  total: list.length,
                },
              })
            }, 200)
          })
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: data.list,
            total: +data.total,
          }
        } catch (e) {
          console.log(e)
        }
      },
      VerificationRuleFormRef: null, // 弹窗
      TableVerificationRef: null, // 表格
      // 新增-编辑 核查规则
      addEditVerificationRule: (flag, row) => {
        if (flag === 'add') {
          state.VerificationRuleFormRef.VerificationRuleDialogVisible = true
          state.VerificationRuleFormRef.VerificationRuleFormData = {
            id: '',
            dctQuestTemplateId: QUESTIONNAIRE_CONTENT.questionnaireContentViews?.id,
            ruleName: '',
            verificationRuleCode: '',
            isEnable: false,
          }
        } else if (flag === 'edit') {
          state.VerificationRuleFormRef.VerificationRuleDialogVisible = true
          state.VerificationRuleFormRef.VerificationRuleFormData = row
        } else {
          ElMessageBox.confirm(
            '是否确认删除？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              deleteQuestTemplateVerificationRule(row.id).then(() => {
                state.onLoad()
                ElMessage.success('删除成功')
              })
            })
            .catch(() => {})
        }
      },
      onLoad: () => {
        if (state.labelName) {
          // 获取CRF访视特殊任务字段
          getVisitTemplateDropInfo().then((res) => {
            // state.VisitManagementList = res.visitSpecialTask
            state.dctVisitDisplay = res.dctVisitDisplay
            state.fatherVisitDispalyList = res.fatherVisitDispalyList
          })
          // 获取父访视模板
          getVisitContainer(state.id).then((res) => {
            state.visitContainerObj = res
          })
          return
        }
        // 基本信息下拉
        getQuestTemplateDropInfo().then((res) => {
          QUESTIONNAIRE_INFOS.templateDropInfos = res
          QUESTIONNAIRE_CONTENT.templateDropInfos = res
        })
        // 所有下拉框
        getQuestTemplateItemDropInfo().then((res) => {
          QUESTIONNAIRE_INFOS.DropInfos = res
          QUESTIONNAIRE_CONTENT.DropInfos = res
        })
        // 获取问卷
        getQuestTemplateInfoId(state.id).then((re) => {
          QUESTIONNAIRE_INFOS.questionnaireInfo = re
          if (state?.TableVerificationRef) {
            state.TableVerificationRef.refresh()
          }
          QUESTIONNAIRE_CONTENT.questionnaireContentViews = re
          if (!state.id) {
            QUESTIONNAIRE_INFOS.questionnaireInfo.id = state.id
          }
          if (
            QUESTIONNAIRE_CONTENT?.questionnaireContentViews?.id &&
            store?.getters?.app?.authorization
          ) {
            state.siteUrl = `${
              window.location.origin
            }/doctorui/#/questionnairePreview==type=${
              QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType
            }==${QUESTIONNAIRE_CONTENT.questionnaireContentViews.id}==${
              store.getters.app.authorization
            }`
          }
        })
      },
    })
    // 获取药品编辑信息
    onMounted(() => {
      state.questionnaireContent = { ...props.stateChange }
      state.labelName = props.stateChange?.label || ''
      state.propName = props.stateChange.prop
      state.dctVisitContainerId = props.stateChange.dctVisitContainerId
      state.dctModuleContainerId = props.stateChange.dctModuleContainerId
      state.id = props.stateChange.id
      state.edcQuestName = props.stateChange.edcQuestName
      QUESTIONNAIRE_CONTENT.versionsValue = props.stateChange.versionsValue
      state.versionStatus = props.stateChange.versionStatus
      state.onLoad()
    })
    // 传值
    provide('QUESTIONNAIRE_INFOS', QUESTIONNAIRE_INFOS)
    provide('QUESTIONNAIRE_CONTENT', QUESTIONNAIRE_CONTENT)
    provide('ADD_TOPIC', ADD_TOPIC)

    return {
      ADD_TOPIC,
      QUESTIONNAIRE_INFOS,
      QUESTIONNAIRE_CONTENT,
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less">
@import '@/style/questionnaire.less';
</style>
<style lang="scss" scoped>
.el-select {
  min-width: 200px;
}
.margin-t20 {
  margin-top: 20px;
}
.emphasis-text {
  color: rgb(227, 42, 42);
}
.el-form--label-top {
  white-space: nowrap;
}
.visit-hint {
  color: #f59a23;
}
.label-warning {
  font-size: 18px;
  position: absolute;
  top: -28px;
  left: 200px;
}
.label-warning-visit {
  font-size: 18px;
  position: absolute;
  top: -28px;
  left: 80px;
  cursor: pointer;
}
.remind-the-way {
  margin: 20px 0;
}
.visit {
  width: 100%;
  .nav-module {
    margin: 10px 0 0 0;
    display: flex;
    .nav-btn-le {
      width: 50%;
      display: flex;
      align-items: center;
      h4 {
        margin: 0 30px 0 0;
      }
    }
    .nav-btn-ri {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  // 编辑访视
  .visitcontainer {
    width: 100%;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    .container {
      width: 94%;
      margin: 0 auto;
      margin-top: 20px;
      span {
        margin-left: 10px;
      }
    }
  }
  .visitcontainer-cl {
    width: 100%;
    margin: 30px 0 0 0;
    padding: 10px 20px 20px;
    box-sizing: border-box;
    background: #fff;
    .visitItems {
      position: relative;
      padding: 20px;
      margin: 10px 0 0 0;
      box-sizing: border-box;
      background: #f7f7f7;
      border-radius: 5px;
      overflow: hidden;
      display: flex;
      .visitItems-on {
        width: 100%;
        height: 100%;
      }
      .delete-son-vinterview {
        position: absolute;
        top: 0;
        right: 0;
      }
      .visit-display {
        margin: 10px 0 0 0;
      }
      span {
        font-weight: 700;
      }
      &::before {
        position: absolute;
        left: -0px;
        top: -0px;
        width: 30px;
        content: '子';
        font-size: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        background: #f59a23;
        border-radius: 5px;
      }
    }
  }
  // 编辑CRF模块
  .modular {
    width: 100%;
    .infos-body {
      width: 100%;
      // height: 200px;
      margin: 20px 0;
      padding: 0 20px 10px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 5px;
      .infos-body-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .infos-body-content {
        .infos-body-content-items {
          display: flex;
          align-items: center;
          margin: 0px 0 20px 0;
          span {
            max-width: 80%;
            display: -webkit-box;
            word-break: break-all;
            word-wrap: break-word;
            overflow: hidden;
            /*…省略形式*/
            text-overflow: ellipsis;
            /*从上向下垂直排列子元素*/
            -webkit-box-orient: vertical;
            /*文本可分两行*/
            -webkit-line-clamp: 3;
          }
          .infos-body-content-items-paralleling {
            width: 400px;
            display: flex;
            align-items: center;
          }
          .infos-body-content-items-label {
            width: 100px;
            text-align: right;
            color: #999;
            margin: 0 20px 0 0;
          }
        }
      }
    }
    .questionnaire-body {
      width: 100%;
      // height: 200px;
      margin: 20px 0;
      padding: 0 20px 30px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 5px;
      .questionnaire-body-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .questionnaire-body-content {
        width: 100%;
        padding: 0 40px;
        box-sizing: border-box;
        .questionnaire-body-content-module {
          width: 100%;
          padding: 12px 20px;
          margin: 0 0 20px 0;
          box-sizing: border-box;
          min-height: 130px;
          border-radius: 5px;
          background: #f2f2f2;
          .questionnaire-body-content-module-head {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .sort {
              width: 117px;
              color: #fe664e;
              span {
                margin: 0 0 0 16px;
              }
            }
            .check {
              width: 200px;
              text-align: center;
              span {
                color: #fe664e;
              }
            }
            .btns {
              min-width: 120px;
              display: flex;
              justify-content: space-between;
              .btns-delete {
                color: #fd004e;
              }
              .btns-edit {
                color: #409eff;
                margin: 0 0 0 20px;
              }
            }
          }
          .questionnaire-body-content-module-items {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            margin: 20px 0 0 0;
            .questionnaire-body-content-module-item {
              width: 100%;
              margin: 0 0 20px 0;
              display: flex;
              align-items: center;
              .questionnaire-body-content-module-item-lable {
                width: 150px;
                margin: 0 20px 0 0;
                text-align: right;
                color: #666;
              }
            }
          }
        }
      }
    }
  }
}
.active {
  font-weight: 400;
  border-bottom: 2px solid rgb(0, 124, 240);
}
.hidde {
  display: none;
}
::v-deep .radioBtn {
  justify-content: space-between;
  .el-radio__label {
    display: flex;
    .radioBtn-input {
      margin: 0 5px;
    }
  }
}
// 入选标准或排除标准按钮颜色
.btnRP {
  display: inline-block;
  padding: 2px 3px;
  color: #fff;
  font-size: 13px;
  border-radius: 3px;
}
.btnRP-back-o {
  background-color: #4b7902;
}
.btnRP-back-t {
  background-color: #d9001b;
}
</style>
