<template>
  <trial-dialog v-model="QUESTIONNAIRE_INFOS.dialogVisible" title="编辑基本信息">
    <template #DialogBody>
      <el-form ref="questionnaireInfoRef" :model="questionnaireInfo" :rules="rules" label-position="top">
        <el-form-item
          label="DCT问卷名称"
          :label-width="formLabelWidth"
          prop="crfName"
          style="width: 100%"
        >
          <el-input
            v-model.trim="questionnaireInfo.crfName"
            placeholder="请填写问卷名称"
            autocomplete="off"
            maxlength="666"
          />
        </el-form-item>
        <el-form-item
          label="DCT问卷排序"
          :label-width="formLabelWidth"
          prop="dctSort"
          style="width: 33%"
        >
          <el-input
            v-model.trim="questionnaireInfo.dctSort"
            placeholder="请填写问卷序号"
            autocomplete="off"
            maxlength="666"
          />
        </el-form-item>
        <el-form-item
          label="显示规则"
          :label-width="formLabelWidth"
          prop="dctQuestDisplay"
          style="width: 33%"
        >
          <el-select v-model="questionnaireInfo.dctQuestDisplay" placeholder="请选择">
            <el-option
              v-for="item in displayRuleList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="填写对象"
          :label-width="formLabelWidth"
          prop="questTemplateType"
          style="width: 33%"
        >
          <el-select v-model="questionnaireInfo.questTemplateType" placeholder="请选择问卷类型">
            <el-option
              v-for="item in QUESTIONNAIRE_INFOS.templateDropInfos.questTemplateType"
              :key="item.id"
              :label="item.itemName"
              :value="item.itemCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="数据同步"
          :label-width="formLabelWidth"
          prop="synchronization"
          style="width: 33%"
        >
          <el-select v-model="questionnaireInfo.isSynchronization" placeholder="请选择问卷类型">
            <el-option
              v-for="item in isSynchronizationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="问卷样式"
          :label-width="formLabelWidth"
          prop="style"
          style="width: 33%"
        >
          <el-select
            v-model="questionnaireInfo.questDisplayType"
            placeholder="请选择问卷类型"
            @change="(e) => {
              if (e === 3) {
                questionnaireInfo.qusetSubmitSignatureType = 0
                questionnaireInfo.qusetReviewSignatureType = 0
              }
            }"
          >
            <el-option
              v-for="item in QUESTIONNAIRE_INFOS.templateDropInfos.questStyleType"
              :key="item.id"
              :label="item.itemName"
              :value="item.itemCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="问卷类型"
          :label-width="formLabelWidth"
          prop="questPatientTemplateType"
          style="width: 33%"
        >
          <!-- <el-select v-model="questionnaireInfo.questPatientTemplateType" disabled placeholder="请选择问卷类型"> -->
          <el-select v-model="questionnaireInfo.questPatientTemplateType" placeholder="请选择问卷类型">
            <el-option
              v-for="item in QUESTIONNAIRE_INFOS.templateDropInfos.questPatientTemplateType"
              :key="item.value"
              :label="item.itemName"
              :value="item.itemCode"
              :disabled="item.itemCode === 6 || item.itemCode === 10 || item.itemCode === 12"
            />
          </el-select>
        </el-form-item>
        <!-- 问卷URL时-上传HTML -->
        <el-form-item
          v-if="questionnaireInfo.questDisplayType === 3"
          label=""
          :label-width="formLabelWidth"
          prop="questUrl"
          style="width: 100%"
        >
          <div>
            <MyUpload
              upload-file-falg
              :request-fun="fileFun"
              :deletefile="deletefile"
              :limit="1"
              :file-list="fileList"
              :file-size="100"
            />
          </div>
        </el-form-item>
        <el-form-item
          v-if="questionnaireInfo?.showQuestSignatureType"
          label="问卷签署（目前单个签署节点，由提交人签署）"
          :label-width="formLabelWidth"
          prop="qusetSubmitSignatureType"
          style="width: 40%"
        >
          <el-select
            v-model="questionnaireInfo.qusetSubmitSignatureType"
            placeholder="请选择问卷类型"
            :disabled="questionnaireInfo.questDisplayType === 3"
          >
            <el-option
              label="无需签署"
              :value="0"
            />
            <el-option
              label="需签署"
              :value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="questionnaireInfo?.showQuestReviewType"
          label="问卷审阅（目前单个审阅节点，由PI或SUBI审阅）"
          :label-width="formLabelWidth"
          prop="qusetReviewSignatureType"
          style="width: 40%"
        >
          <el-select v-model="questionnaireInfo.qusetReviewSignatureType" placeholder="请选择问卷类型" :disabled="questionnaireInfo.questDisplayType === 3">
            <el-option
              label="无需审阅"
              :value="0"
            />
            <el-option
              label="需审阅"
              :value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="填写说明"
          :label-width="formLabelWidth"
          prop="explain"
          style="width: 100%"
        >
          <el-input
            v-model.trim="questionnaireInfo.crfGuideline"
            type="textarea"
            autocomplete="off"
            maxlength="666"
          />
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <div class="footer-flex-end">
        <el-button size="large" @click="QUESTIONNAIRE_INFOS.dialogVisible = false">取 消</el-button>
        <el-button size="large" type="primary" :loading="loading" @click="submit">保 存</el-button>
      </div>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  postQuestTemplate,
  postQuestFileHTML
} from '@/api/home'
import MyUpload from '@/components/Upload/index.vue'

export default defineComponent({
  name: 'EditQuestionnaireInfo',
  components: {
    MyUpload
  }, // 编辑基本信息
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    },
  },
  setup({ request }) {
    const QUESTIONNAIRE_INFOS = inject('QUESTIONNAIRE_INFOS')
    const state = reactive({
      questionnaireInfo: {
        crfGuideline: '',
        crfName: '',
        dctModuleContainerId: null,
        id: null,
        isSynchronization: 0,
        moduleContainer: null,
        questDisplayType: 2,
        questPatientTemplateType: 6, // 6为客户入排
        questTemplateItem: [],
        questTemplateType: 1,
        dctSort: null,
        dctQuestDisplay: 0,
        qusetSubmitSignatureType: null,
        qusetReviewSignatureType: null,
        showQuestSignatureType: false,
        showQuestReviewType: false,
      },
      formLabelWidth: '120px',
      questionnaireInfoRef: null,
      loading: false,
      // 数据同步
      isSynchronizationOptions: [
        {
          value: 1,
          label: '同步至EDC',
        },
        {
          value: 0,
          label: '不同步',
        },
      ],
      // 显示规则
      displayRuleList: [
        {
          value: 0,
          name: '显示'
        },
        {
          value: 10,
          name: '隐藏'
        },
        {
          value: 8,
          name: '按公式显示'
        },
        {
          value: 9,
          name: '按公式隐藏'
        },
        {
          value: 11,
          name: '按公式当前访视显示'
        },
        {
          value: 12,
          name: '按公式当前访视隐藏'
        },
      ],
      rules: {
        crfName: [
          { required: true, message: '请输入问卷名称', trigger: 'blur' },
          // {
          //   pattern: /^[0-9]*$/,
          //   message: '请输入数字',
          //   trigger: 'blur',
          // },
        ],
        dctSort: [
          { required: true, message: '请输入问卷序号', trigger: 'blur' },
          {
            pattern: /^[0-9]*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ]
      },
      // 上传文件回显
      fileList: [],
      // 上传
      fileFun: (fileObj) => {
        const myFormDataObj = new FormData()
        const fileName = fileObj.file.name
        const pos = fileName.lastIndexOf('.')
        const lastName = fileName.substring(pos, fileName.length)
        const limitFileType = lastName.toLowerCase()
        if (
          limitFileType !== '.html'
        ) {
          ElMessage.warning({
            showClose: true,
            message: '文件仅支持html格式，请重新上传!',
          })
          state.fileList = []
        } else {
          // questTemplateId
          const loading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          // questUrl
          myFormDataObj.append('CheckImageFiles', fileObj.file)
          postQuestFileHTML(
            state.questionnaireInfo.id,
            myFormDataObj
          )
            .then((res) => {
              loading.close()
              state.questionnaireInfo.questUrl = res.questFilePath
              state.questionnaireInfo.questUrlFileName = res.questFileName
              ElMessage.success('上传成功')
            })
            .catch(() => {
              state.fileList = []
              loading.close()
            })
        }
      },
      // 删除文件
      deletefile: () => {
        // console.log('deletefile')
      },

      submit: () => {
        state.questionnaireInfoRef.validate((valid) => {
          if (valid) {
            state.loading = true
            if (!state.questionnaireInfo.questPatientTemplateType) {
              state.questionnaireInfo.questPatientTemplateType = 0
            }
            postQuestTemplate(state.questionnaireInfo)
              .then(() => {
                request()
                ElMessage.success('保存成功')
                state.loading = false
                QUESTIONNAIRE_INFOS.dialogVisible = false
              })
              .catch(() => {
                state.loading = false
              })
          }
        })
      },
    })

    onMounted(() => {
      if (JSON.stringify(QUESTIONNAIRE_INFOS.questionnaireInfo) !== '{}') {
        state.questionnaireInfo = { ...QUESTIONNAIRE_INFOS.questionnaireInfo }
        if (!state.questionnaireInfo.questPatientTemplateType) {
          state.questionnaireInfo.questPatientTemplateType = null
        }
        if (state.questionnaireInfo?.questUrl) {
          state.fileList.push({
            name: state.questionnaireInfo?.questUrlFileName || '',
            url: state.questionnaireInfo?.questUrl || '',
          })
        }
      }
    })

    return {
      ...toRefs(state),
      QUESTIONNAIRE_INFOS,
    }
  },
})
</script>

<style lang="less" scoped>
::v-deep .el-textarea__inner{
  height: 130px;
}
.el-form,.el-form--default{
  display: flex;
  flex-wrap: wrap;
  .el-input,.el-select{
      margin: 0 20px 0 0;
  }
  .el-select{
      width: 100%;
  }
}
</style>
