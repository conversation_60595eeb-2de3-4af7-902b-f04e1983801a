<template>
  <trial-dialog>
    <template #DialogBody>
      <div v-if="!contentShow" class="flex">
        <el-input v-model="password" placeholder="输入用户" />
        <el-button size="large" type="primary" :loading="loading" @click="passwordClick">确定</el-button>
      </div>
      <div v-else>
        <el-button size="large" type="primary" :loading="loading" @click="submitAll(1)">一键生成</el-button>
        <div>生成3-11各一题，不要在点下边的了</div>
        <el-form :model="addTopicObj">
          <div v-for="(item, index) in addTopicObj.topicArr" :key="index" class="mb-3">
            <el-form-item label="DCT中题目名称">
              <el-input v-model="item.fieldLabel" placeholder="默认" />
            </el-form-item>
            <el-form-item label="控件类型">
              <el-input v-model="item.crfFieldControl" placeholder="默认3" />
            </el-form-item>
            <div>
              <div>别输其他的</div>
              3-单行文本 4-多行文本 5-数字控件 6-日期控件 7-时间控件 8-日期时间控件 9-单选 10-多选 11-年月控件
            </div>
            <el-button type="primary" :loading="loading" @click="addArr(item, index)">添加</el-button>
            <el-button type="primary" :loading="loading" @click="deleteArr(item, index)">删除</el-button>
          </div>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div class="footer-flex-end">
        <el-button size="large" :loading="loading" @click="ADD_TOPIC.addTopicShow = false">取 消</el-button>
        <el-button v-if="contentShow" size="large" type="primary" :loading="loading" @click="submitAll">保 存</el-button>
      </div>
    </template>
  </trial-dialog>
</template>

<script lang='ts'>
import { postQuestTemplateItem } from '@/api/home'
import { deepClone } from '@/utils'
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'

export default defineComponent({
  name: 'AddNewTopic',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    },
    dctQuestTemplateId: {
      type: String,
      default: ''
    },
    topicLength: {
      type: Number,
      default: 0
    }
  },
  setup(props) {
    const ADD_TOPIC = inject('ADD_TOPIC')
    const state = reactive({
      loading: false,
      password: '',
      passwordList: ['wanghongtao', 'yinjun', '王宏涛1', '殷俊1', '19491001'],
      contentShow: false,
      addTopicObj: {
        topicArr: [
          {
            crfFieldControl: 3, // 可修改
            crfFieldType: 4, // 数据类型都是普通字段
            dctCode: '',
            dctQuestItemDisplayType: 3,
            dctQuestTemplateId: props?.dctQuestTemplateId || '', // 问卷id
            dctQuestUnit: null,
            dctSort: null, // 可修改
            fieldDescription: '',
            fieldLabel: '',
            id: null,
            isRequired: 0,
            questTemplateOption: [],
            refDctCode: '',
            refItemValue: '',
            refType: 0,
            specialFieldType: 0,
            parentId: '',
            minimumYear: '1900',
            maximumYear: '2099',
            isAllowUK: 0,
          }
        ]
      },
      addArr: (item, index) => {
        const data = {
          crfFieldControl: 3, // 可修改
          crfFieldType: 4, // 数据类型都是普通字段
          dctCode: '',
          dctQuestItemDisplayType: 3,
          dctQuestTemplateId: props?.dctQuestTemplateId || '', // 问卷id
          dctQuestUnit: null,
          dctSort: null, // 可修改
          fieldDescription: '',
          fieldLabel: '',
          id: null,
          isRequired: 0,
          questTemplateOption: [],
          refDctCode: '',
          refItemValue: '',
          refType: 0,
          specialFieldType: 0,
          parentId: '',
          minimumYear: '1900',
          maximumYear: '2099',
          isAllowUK: 0,
        }
        state.addTopicObj.topicArr.splice(index + 1, 0, data)
      },
      deleteArr: (item, index) => {
        state.addTopicObj.topicArr.splice(index, 1)
      },
      passwordClick: () => {
        if (state.password && state.passwordList.includes(state.password)) {
          state.contentShow = true
        } else {
          ADD_TOPIC.addTopicShow = false
        }
      },
      submitAll: (index) => {
        let num = 0
        state.loading = true
        const addLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        let addArr: any = []
        if (index === 1) {
          for (let i = 0; i < 9; i++) {
            const crfFieldControlnum = 3 + i
            addArr.push({
              crfFieldControl: crfFieldControlnum, // 可修改
              crfFieldType: 4, // 数据类型都是普通字段
              dctCode: '',
              dctQuestItemDisplayType: 3,
              dctQuestTemplateId: props?.dctQuestTemplateId || '', // 问卷id
              dctQuestUnit: null,
              dctSort: null, // 可修改
              fieldDescription: '',
              fieldLabel: '',
              id: null,
              isRequired: 0,
              questTemplateOption: [],
              refDctCode: '',
              refItemValue: '',
              refType: 0,
              specialFieldType: 0,
              parentId: '',
              minimumYear: '1900',
              maximumYear: '2099',
              isAllowUK: 0,
            })
          }
        } else {
          addArr = deepClone(state.addTopicObj.topicArr)
        }
        addArr.forEach((item, index) => {
          item.dctSort = props.topicLength + index + 1
          if (!item?.crfFieldControl) {
            item.crfFieldControl = 3
          }
          item.crfFieldControl = Number(item.crfFieldControl)
          // 3-单行文本 4-多行文本 5-数字控件 6-日期控件 7-时间控件 8-日期时间控件 9-单选 10-多选 11-年月控件
          if (!item?.fieldLabel) {
            if (item.crfFieldControl === 3) {
              item.fieldLabel = `单行文本${index}`
            } else if (item.crfFieldControl === 4) {
              item.fieldLabel = `多行文本${index}`
            } else if (item.crfFieldControl === 5) {
              item.fieldLabel = `数字控件${index}`
            } else if (item.crfFieldControl === 6) {
              item.fieldLabel = `日期控件${index}`
            } else if (item.crfFieldControl === 7) {
              item.fieldLabel = `时间控件${index}`
            } else if (item.crfFieldControl === 8) {
              item.fieldLabel = `日期时间控件${index}`
            } else if (item.crfFieldControl === 9) {
              item.fieldLabel = `单选${index}`
            } else if (item.crfFieldControl === 10) {
              item.fieldLabel = `多选${index}`
            } else if (item.crfFieldControl === 11) {
              item.fieldLabel = `年月控件${index}`
            }
          }
          postQuestTemplateItem(ADD_TOPIC.versionsValue, { ...item }).then(() => {
            num++
            if (num === state.addTopicObj.topicArr.length) {
              state.loading = false
              addLoading.close()
              ADD_TOPIC.addTopicShow = false
              props.request()
            }
          })
        })
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
      // 
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ADD_TOPIC,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">

</style>
