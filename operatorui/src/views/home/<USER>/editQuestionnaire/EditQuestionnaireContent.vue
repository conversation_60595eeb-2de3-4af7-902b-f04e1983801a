<template>
  <div class="EditQuestionnaireContent">
    <el-tabs
      v-model="activeName"
      type="card"
      class="demo-tabs"
      @tab-click="tabsHandleClick"
    >
      <el-tab-pane label="基本信息" name="Infos">
        <el-form
          ref="questionnaireOptionRef"
          label-position="top"
          :model="questionnaireContent"
          :rules="rules"
        >
          <el-form-item
            label="可见性"
            :label-width="formLabelWidth"
            prop="dctQuestItemDisplayType"
            style="width:30%;"
          >
            <el-select
              v-model="questionnaireContent.dctQuestItemDisplayType"
              placeholder="请选择可见性"
            >
              <el-option
                v-for="item in QUESTIONNAIRE_CONTENT.DropInfos.questItemDisplayType"
                :key="item.id"
                :label="item.itemName"
                :value="item.itemCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="排序值"
            :label-width="formLabelWidth"
            prop="dctSort"
            style="width:30%;"
          >
            <el-input
              v-model.trim="questionnaireContent.dctSort"
              placeholder="请填写正整数"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="DCT编码"
            :label-width="formLabelWidth"
            prop="dctCode"
            disabled
            style="width:30%;"
          >
            <el-input
              v-model.trim="questionnaireContent.dctCode"
              placeholder="系统自动生成，带出"
              disabled
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="DCT中题目名称"
            :label-width="formLabelWidth"
            prop="fieldLabel"
            style="width:100%;"
          >
            <trial-wang-editor v-if="QUESTIONNAIRE_CONTENT.customShow" ref="customFieldLabelRef" :toolbar-config="toolbarConfig" :editor-config="editorConfig" />
            <el-input
              v-else
              v-model.trim="questionnaireContent.fieldLabel"
              placeholder="请填写题目名称"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <div class="w-full flex">
            <el-form-item
              label="是否必填"
              :label-width="formLabelWidth"
              prop="isRequired"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.isRequired"
                placeholder="请选择是否必填"
                :disabled="questionnaireContent.crfFieldType === 3"
              >
                <el-option
                  v-for="item in isRequiredOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="控件类型"
              :label-width="formLabelWidth"
              prop="crfFieldControl"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.crfFieldControl"
                placeholder="请选择控件类型"
                :disabled="questionnaireContent.crfFieldType === 3"
              >
                <el-option
                  v-for="item in QUESTIONNAIRE_CONTENT.DropInfos.questFieldControl"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemCode"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              label="数据类型"
              :label-width="formLabelWidth"
              prop="crfFieldType"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.crfFieldType"
                placeholder="请选择数据类型"
                @change="crfFieldTypeChange"
              >
                <el-option
                  v-for="item in QUESTIONNAIRE_CONTENT.DropInfos.questFieldType"
                  :key="item.id"
                  :label="item.itemName"
                  :value="item.itemCode"
                />
              </el-select>
            </el-form-item>

          </div>
          <div class="flex w-full">
            <el-form-item
              v-if="questionnaireContent.crfFieldControl === 6 ||
                questionnaireContent.crfFieldControl === 7 ||
                questionnaireContent.crfFieldControl === 8 ||
                questionnaireContent.crfFieldControl === 11"
              label="UK录入设置"
              :label-width="formLabelWidth"
              prop="isAllowUK"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.isAllowUK"
                placeholder="请选择"
                :disabled="questionnaireContent?.specialFieldType === 3"
              >
                <el-option label="不支持录入UK" :value="0" />
                <el-option label="支持录入UK" :value="1" />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="questionnaireContent.crfFieldControl === 6 ||
                questionnaireContent.crfFieldControl === 8 ||
                questionnaireContent.crfFieldControl === 11"
              label="未来日期"
              :label-width="formLabelWidth"
              prop="isFutureDate"
              style="width: 32%"
            >
              <el-select
                v-model="questionnaireContent.isFutureDate"
                placeholder="请选择"
              >
                <el-option label="不支持" :value="0" />
                <el-option label="支持" :value="1" />
              </el-select>
            </el-form-item>

            <div
              v-if="questionnaireContent.crfFieldControl === 6 ||
                questionnaireContent.crfFieldControl === 8 ||
                questionnaireContent.crfFieldControl === 11"
              style="width: 32%"
              class="flex"
            >
              <el-form-item
                label="年份区间"
                :label-width="formLabelWidth"
                class="flex-1"
                prop="minimumYear"
              >
                <el-input v-model.trim="questionnaireContent.minimumYear" placeholder="请输入" />
              </el-form-item>
              <div class="mr-5 mt-30-px">-</div>
              <div class="flex-1 mt-30-px">
                <el-form-item prop="maximumYear">
                  <el-input v-model.trim="questionnaireContent.maximumYear" placeholder="请输入" />
                </el-form-item>
              </div>
            </div>
          </div>

          <el-form-item
            label="单位"
            :label-width="formLabelWidth"
            prop="dctQuestUnit"
            style="width: 32%"
          >
            <el-input
              v-model.trim="questionnaireContent.dctQuestUnit"
              placeholder="请填写单位"
              :disabled="(questionnaireContent.crfFieldControl !== 3 || questionnaireContent.crfFieldType === 3) && (questionnaireContent.crfFieldControl !== 5 || questionnaireContent.crfFieldType === 5)"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>

          <el-form-item
            label="字段特殊类型"
            :label-width="formLabelWidth"
            prop="specialFieldType"
            style="width: 32%"
          >
            <el-select
              v-model="questionnaireContent.specialFieldType"
              placeholder="请选择字段特殊类型"
              clearable
              @change="() => {
                if (questionnaireContent.specialFieldType === 3) {
                  questionnaireContent.isAllowUK = 0
                }
              }"
              :disabled="questionnaireContent.crfFieldType === 3 || !!questionnaireContent.parentId "
            >
              <el-option
                v-for="item in QUESTIONNAIRE_CONTENT.DropInfos.questSpecialFieldType"
                :key="item.id"
                :label="item.itemName"
                :value="item.itemCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="questionnaireContent.parentId"
            label="列表默认展示"
            :label-width="formLabelWidth"
            prop="specialFieldType"
            style="width: 32%"
          >
            <el-select
              v-model="questionnaireContent.extensionShow"
              placeholder="请选择是否显示"
              clearable
            >
              <el-option
                v-for="exitem in isRequiredOptions"
                :key="exitem.value"
                :label="exitem.label"
                :value="exitem.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="填写指南"
            :label-width="formLabelWidth"
            prop="fieldDescription"
            style="width:100%;"
          >
            <trial-wang-editor v-if="QUESTIONNAIRE_CONTENT.customShow" ref="customFieldDescriptionRef" :toolbar-config="toolbarConfig" :editor-config="editorConfig" />
            <el-input
              v-else
              v-model.trim="questionnaireContent.fieldDescription"
              placeholder="请填写答题指南"
              type="textarea"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
                    <!-- 自动计算 -->
          <div class="centerflex mx-1">
            <el-switch v-model="questionnaireContent.hasCalculationFormula" />
            <span class="px-3">自动计算</span>
          </div>
        </el-form>
        <!-- 按钮 -->
        <div class="footer" :class="{'pos-rb': !QUESTIONNAIRE_CONTENT.customShow}">
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="submitInfosRelevanceOption('I')"
          >保存</el-button>
        </div>
      </el-tab-pane>

      <el-tab-pane v-if="questionnaireContent.id" label="关联条件" name="Relevance">
        <!-- <div style="marginBottom: 20px">
          <el-radio v-model="relevanceObj.relevanceSpecial" label="1" size="large">题目答案关联</el-radio>
          <el-radio v-model="relevanceObj.relevanceSpecial" label="2" size="large">特殊关联</el-radio>
        </div> -->
        <el-form
          ref="relevanceObjRef"
          label-position="top"
          :model="relevanceObj"
          :rules="rules"
        >
          <el-form-item
            label="关联题目"
            :label-width="formLabelWidth"
            prop="refDctCode"
            style="width:80%"
          >
            <el-select
              v-model="relevanceObj.refDctCode"
              placeholder="请选择关联题目"
              clearable
              @change="changeFieldLabel"
            >
              <el-option
                v-for="item in questTemplateArr"
                :key="item.id"
                :label="item.removeHtmlTagOfFieldLabel"
                :value="item.dctCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="关联选项值"
            :label-width="formLabelWidth"
            prop="refItemValue"
            style="width:51%"
          >
            <el-select
              v-if="questTemplateArr[questTemplateOptionIndex]?.refQuestTemplateItemOption"
              v-model="relevanceObj.refItemValue"
              placeholder="请选择关联选项值"
              multiple
              clearable
            >
              <el-option
                v-for="item in questTemplateArr[questTemplateOptionIndex]
                  .refQuestTemplateItemOption"
                :key="item.value"
                :label="item.itemName"
                :value="item.itemValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="关联行为"
            :label-width="formLabelWidth"
            prop="refType"
            style="width:51%"
          >
            <el-select
              v-model="relevanceObj.refType"
              placeholder="请选择关联行为"
              clearable
            >
              <el-option
                v-for="item in refTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 特殊关联 -->
        <!-- <div v-else class="relevance-s">
          <div>
            当性别等于
            <el-select
              v-model="relevanceObj.refItemValue"
              placeholder="请选择关联行为"
              style="margin: 0 15px"
            >
              <el-option
                v-for="item in sexOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            时
          </div>
          <div style="marginTop: 15px">
            <div>关联行为</div>
            <el-select
              v-model="relevanceObj.refType"
              placeholder="请选择关联行为"
              style="marginTop: 10px; width: 300px"
            >
              <el-option
                v-for="item in refTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div> -->
        <div class="footer" style="margin: 400px 0 0 0;">
          <el-button
            type="primary"
            plain
            size="large"
            class="clRelevance"
            @click="clRelevance"
          >清空关联</el-button>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="submitInfosRelevanceOption('R')"
          >保存</el-button>
        </div>
      </el-tab-pane>
      <!-- 编辑状态&&单选控件时可显示 -->
      <el-tab-pane v-if="questionnaireContent.id && (((questionnaireContent.crfFieldControl === 9 || questionnaireContent.crfFieldControl === 10) && (oldQuestionnaireContent.crfFieldControl === 9 || oldQuestionnaireContent.crfFieldControl === 10)))" label="选项" name="Option">
        <el-button
          type="primary"
          plain
          @click="submitOptionTab('a')"
        >新建选项</el-button>
        <el-table
          :data="questTemplateOptionArr"
          style="width: 100%;marginTop:10px"
          :header-cell-style="{
            background: '#f2f2f2'
          }"
        >
          <el-table-column fixed prop="itemValue" label="DCT序列值" width="220" show-overflow-tooltip />
          <el-table-column prop="itemName" label="DCT文本内容" width="360" show-overflow-tooltip />
          <el-table-column prop="sort" label="DCT排序" width="220" show-overflow-tooltip />
          <el-table-column prop="gaugeValue" label="对应分值" width="220" show-overflow-tooltip />
          <el-table-column fixed="right" label="操作">
            <template #default="scope">
              <el-button
                text
                type="primary"
                size="small"
                @click="submitOptionTab('e',scope.row)"
              >编辑</el-button>
              <el-button text type="primary" size="small" style="color: #FF0000;" @click="submitOptionTab('d',scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!-- 选项弹窗 -->
    <trial-dialog
      v-model="contentDialogShow"
      :title="contentDialogObj.id ? '编辑选项' :'新建选项'"
    >
      <template #DialogBody>
        <el-form
          ref="contentDialogRef"
          :model="contentDialogObj"
          :rules="contentDialogRules"
          label-position="top"
          style="display: flex; flex-warp: warp"
        >
          <el-form-item
            label="DCT选项序列值"
            :label-width="formLabelWidth"
            prop="itemValue"
            style="width: 50%"
          >
            <el-input
              v-model.trim="contentDialogObj.itemValue"
              placeholder="请填写DCT选项序列值"
              autocomplete="off"
              maxlength="666"
              :disabled="contentDialogObj.id ? true : false"
            />
          </el-form-item>
          <el-form-item
            label=" DCT选项排序"
            :label-width="formLabelWidth"
            prop="sort"
            style="width: 50%"
          >
            <el-input
              v-model.trim="contentDialogObj.sort"
              placeholder="请填写DCT选项排序"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="DCT选项文本内容"
            :label-width="formLabelWidth"
            prop="itemName"
            style="width: 100%"
          >
            <el-input
              v-model.trim="contentDialogObj.itemName"
              placeholder="请填写DCT选项文本内容"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
          <el-form-item
            label="对应分值"
            :label-width="formLabelWidth"
            prop="gaugeValue"
            style="width: 50%"
          >
            <el-input
              v-model.trim="contentDialogObj.gaugeValue"
              placeholder="请填写对应分值"
              autocomplete="off"
              maxlength="666"
            />
          </el-form-item>
        </el-form>
      </template>

      <template #footer>
        <div class="footer" style="marginTop: 300px">
          <el-button
            size="large"
            @click="contentDialogShow = false"
          >取 消</el-button>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="submitOptionTab('s')"
          >保 存</el-button>
        </div>
      </template>
    </trial-dialog>
    <!-- 删除按钮 -->
    <div class="delete-pos-tr">
      <el-button
        size="large"
        @click="backRefresh"
      >返回</el-button>
      <el-button
        v-if="questionnaireContent?.id"
        size="default"
        type="danger"
        :loading="loading"
        @click="deletePresent"
      >删除</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  deleteQuestTemplateItem,
  postQuestTemplateItem,
  putQuestTemplateItemBaseInfo,
  putQuestTemplateItemRef,
  getQuestTemplateOption,
  postQuestTemplateOption,
  deleteQuestTemplateOption,
  getDropRefQuestTemplateItemTable,
  putQuestTemplateItemBaseInfoTable,
  putQuestTemplateItemRefTable,
  postQuestTemplateItemTable,
  getDropRefQuestTemplateItem
} from '@/api/home'
import { useStore } from 'vuex'
import { optionSequentialValue } from '@/utils'

export default defineComponent({
  name: 'EditQuestionnaireContent',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
    getCustomTaskQuests: {
      type: Function,
      default: () => {}
    }
  },
  setup({ request, getCustomTaskQuests }) {
    const store = useStore()
    const base64LimitSizeNum = 0.5 * 1024 * 1024
    const QUESTIONNAIRE_CONTENT = inject('QUESTIONNAIRE_CONTENT')
    const yearVerify = (rule, value, callback) => {
      if (Number(state.questionnaireContent.maximumYear) < Number(state.questionnaireContent.minimumYear)) {
        return callback(new Error(rule.message))
      }
      return callback()
    }
    const state = reactive({
      customFieldLabelRef: null, // 自定义题目ref
      customFieldDescriptionRef: null, // 自定义填写指南ref
      editorConfig: {
        MENU_CONF: {
          uploadImage: {
            server: '/api', // 没有配置地址
            base64LimitSize: base64LimitSizeNum, // 5kb
            // 单个文件上传失败
            onError() {
              ElMessage.error(`上传图片最大${base64LimitSizeNum}kb`)
            },
          }
        }
      },
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          // 'uploadImage', // 上传图片
          'uploadVideo' // 上传视频
        ],
      },
      questionnaireContent: {
        crfFieldControl: null,
        crfFieldType: null,
        dctCode: '',
        dctQuestItemDisplayType: 3,
        dctQuestTemplateId: null,
        dctQuestUnit: null,
        dctSort: null,
        fieldDescription: '',
        fieldLabel: '',
        id: null,
        isRequired: 0,
        questTemplateOption: [],
        refDctCode: '',
        refItemValue: '',
        refType: 0,
        specialFieldType: null,
        maximumYear: '2099',
        minimumYear: '1900',
        isAllowUK: 0,
        isFutureDate: 1,
      },
      oldQuestionnaireContent: {}, // 未保存前的基本信息对象
      formLabelWidth: '140px',
      questionnaireOptionRef: null, // 基本信息ref
      relevanceObjRef: null, // 关联条件Ref
      questionnaireRelevanceRef: null, // 关联条件
      contentDialogShow: false, // 选项弹窗显示隐藏
      contentDialogObj: {
        dctQuestItemId: '',
        gaugeValue: null,
        id: null,
        itemContent: '',
        itemName: '',
        itemValue: '',
        sort: null
      },
      contentDialogRef: null, // 选项新增编辑Ref
      loading: false,
      activeName: 'Infos', // tab高亮
      questTemplateOptionIndex: 0, // 关联选项值下标
      questTemplateArr: [], // 关联题目用的数组
      // 关联对象
      relevanceObj: {
        questTemplateItemId: '',
        refDctCode: '',
        refItemValue: '',
        refType: null,
        // relevanceSpecial: 1
      },
      // 是否必填
      isRequiredOptions: [
        {
          value: 1,
          label: '是',
        },
        {
          value: 0,
          label: '否',
        },
      ],
      // 关联行为
      refTypeOptions: [
        {
          value: 1,
          label: '显示',
        },
        {
          value: 2,
          label: '隐藏',
        },
      ],
      sexOptions: [
        {
          value: 1,
          label: '男',
        },
        {
          value: 2,
          label: '女',
        },
      ],
      questTemplateOptionArr: [], // 选项tabel表
      rules: {
        // dctCode: '',
        // dctQuestTemplateId: null,
        // dctQuestUnit: null,
        // fieldDescription: '',
        fieldLabel: [
          { required: true, message: '请填写DCT中题目名称', trigger: 'blur' },
        ],
        dctSort: [
          { required: true, message: '请填写整数', trigger: 'blur' },
          {
            pattern: /^-?[1-9]\d*$|^0$/,
            message: '请填写整数',
            trigger: 'blur',
          },
        ],
        dctQuestItemDisplayType: [
          { required: true, message: '请选择问卷可见性', trigger: 'blur' },
        ],
        isRequired: [
          { required: true, message: '请选择是否必填', trigger: 'blur' },
        ],
        crfFieldControl: [
          { required: true, message: '请选择控件类型', trigger: 'blur' },
        ],
        crfFieldType: [
          { required: true, message: '请选择数据类型', trigger: 'blur' },
        ],
        // specialFieldType: [
        //   { required: true, message: '请选择字段特殊类型', trigger: 'blur' },
        // ],
        isAllowUK: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        isFutureDate: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        minimumYear: [
          { required: true, message: '请输入年份', trigger: 'blur' },
          {
            pattern: /^[0-9]{4}$/,
            message: '请填写正确年份',
            trigger: 'blur',
          },
          { validator: yearVerify, message: '开始年份不能超过结束年份', trigger: 'blur' }
        ],
        maximumYear: [
          { required: true, message: '请输入年份', trigger: 'blur' },
          {
            pattern: /^[0-9]{4}$/,
            message: '请填写正确年份',
            trigger: 'blur',
          },
          { validator: yearVerify, message: '结束年份不能小于开始年份', trigger: 'blur' }
        ]
      },
      contentDialogRules: {
        itemName: [
          { required: true, message: '请填写DCT选项文本内容', trigger: 'blur' },
        ],
        sort: [
          { required: true, message: '请填写DCT选项排序', trigger: 'blur' },
          {
            pattern: /^-?[1-9]\d*$|^0$/,
            message: '请填写整数',
            trigger: 'blur',
          },
        ],
        itemValue: [
          { required: true, message: '请填写DCT选项序列值', trigger: 'blur' },
        ],
        // gaugeValue: [
        //   { required: true, message: '请填写对应分值', trigger: 'blur' },
        // ],
      },
      // 数据类型控件发生值改变
      crfFieldTypeChange: (e) => {
        if (e === 3) {
          state.questionnaireContent.crfFieldControl = 1
          state.questionnaireContent.isRequired = 0
          state.questionnaireContent.dctQuestUnit = ''
          state.questionnaireContent.specialFieldType = null
        }
      },
      // 点击tab
      tabsHandleClick: (tab: string, event: Event) => {
        switch (tab.index) {
          case '0':
            // console.log('0')
            break
          case '1':
            if (state.relevanceObj?.refType === 0) {
              state.relevanceObj.refType = null
            }
            state.cleanQuestTemplateArr(state.questionnaireContent.id)
            break
          case '2':
            if (state.questTemplateOptionArr.length) {
              break
            }
            state.getQuestTemplateOptionList()
            break
            // default: //
        }
      },
      // 获取选项列表
      getQuestTemplateOptionList: () => {
        getQuestTemplateOption(state.questionnaireContent.id).then((res) => {
          state.questTemplateOptionArr = res
          /*
          dctQuestItemId: "0005d416-9292-c825-0000-000000000000"
          gaugeValue: 0
          id: "0005d416-9292-c82e-0000-000000000000"
          itemContent: ""
          itemName: "男"
          itemValue: "1"
          sort: 1
          */
        })
      },
      // 清空关联
      clRelevance: () => {
        state.relevanceObj.refDctCode = ''
        state.relevanceObj.refItemValue = ''
        state.relevanceObj.refType = null
      },
      // 基本信息or关联 的保存
      submitInfosRelevanceOption: (flag) => {
        // I信息 R关联
        if (flag === 'I') {
          if (QUESTIONNAIRE_CONTENT.customShow) {
            state.questionnaireContent.fieldLabel = state.customFieldLabelRef.gainTxt('set')
            state.questionnaireContent.fieldDescription = state.customFieldDescriptionRef.gainTxt('set')
            if (state.questionnaireContent?.fieldLabel?.length > 1048576) {
              ElMessage.error('DCT中题目名称富文本内容过大，请限制在1MB以内（请压缩图片）')
              return
            } else if (state.questionnaireContent?.fieldDescription?.length > 1048576) {
              ElMessage.error('填写指南富文本内容过大，请限制在1MB以内（请压缩图片）')
              return
            }
          }
          state.questionnaireOptionRef.validate((valid) => {
            if (valid) {
              state.loading = true
              let studyVersionId = QUESTIONNAIRE_CONTENT.versionsValue || null
              // 转换特殊类型
              const questionnaireContentObj = { ...state.questionnaireContent }
              if (QUESTIONNAIRE_CONTENT.customShow) {
                const { studyId } = store.state.studyItem
                studyVersionId = 'TASK'
                questionnaireContentObj.studyId = studyId
              }
              if (!questionnaireContentObj.specialFieldType) {
                questionnaireContentObj.specialFieldType = 0
              }
              let savefun
              questionnaireContentObj.calculationFormula = questionnaireContentObj.hasCalculationFormula
              if (QUESTIONNAIRE_CONTENT.editOrAddList === 'editList' ||
                QUESTIONNAIRE_CONTENT.editOrAddList === 'addList') {
                savefun = state.questionnaireContent.id
                  ? putQuestTemplateItemBaseInfoTable(questionnaireContentObj)
                  : postQuestTemplateItemTable(studyVersionId, questionnaireContentObj)
              } else {
                savefun = state.questionnaireContent.id
                  ? putQuestTemplateItemBaseInfo(questionnaireContentObj)
                  : postQuestTemplateItem(studyVersionId, questionnaireContentObj)
              }
              savefun
                .then((res) => {
                  if (!state.questionnaireContent?.id) {
                    state.relevanceObj.id = res.id
                    state.relevanceObj.dctCode = res.dctCode
                    state.questionnaireContent.id = res.id
                    state.questionnaireContent.dctCode = res.dctCode
                    if (res?.parentId) {
                      state.questionnaireContent.parentId = res.parentId
                    }
                    state.oldQuestionnaireContent = { ...res }
                  }
                  ElMessage.success('保存成功')
                  state.oldQuestionnaireContent = res
                  state.loading = false
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        } else if (flag === 'R') {
          const relevanceObj = { ...state.relevanceObj }
          if (state.questTemplateArr[state.questTemplateOptionIndex]?.crfFieldControl === 9 ||
            state.questTemplateArr[state.questTemplateOptionIndex]?.crfFieldControl === 10) {
            if (Array.isArray(relevanceObj.refItemValue) && relevanceObj.refItemValue.length) {
              relevanceObj.refItemValue = relevanceObj.refItemValue.join(',')
            } else {
              relevanceObj.refItemValue = ''
            }
          }
          // relevanceObj.refDctCode = QUESTIONNAIRE_CONTENT.questionnaireContentViews.questTemplateItem[state.questTemplateOptionIndex].dctCode
          relevanceObj.questTemplateItemId = relevanceObj.id
          if (!relevanceObj?.refType) {
            relevanceObj.refType = 0
          }
          state.loading = true
          const putQuestTemplateItemRefFun = QUESTIONNAIRE_CONTENT.editOrAddList === 'editList' || QUESTIONNAIRE_CONTENT.editOrAddList === 'addList'
            ? putQuestTemplateItemRefTable : putQuestTemplateItemRef
          putQuestTemplateItemRefFun(relevanceObj).then((res) => {
            ElMessage.success('保存成功')
            state.loading = false
          }).catch(() => { state.loading = false })
        }
      },
      // 返回刷新
      backRefresh: () => {
        request()
        if (QUESTIONNAIRE_CONTENT.customShow && (QUESTIONNAIRE_CONTENT.editOrAddList === 'add' || QUESTIONNAIRE_CONTENT.editOrAddList === 'addList')) {
          getCustomTaskQuests()
        }
        QUESTIONNAIRE_CONTENT.contentVisible = false
      },
      // 选项表格某项保存
      submitOptionTab: (falg, item) => {
        // 保存
        if (falg === 's') {
          if (!state.contentDialogObj?.id) {
            const nextStep = optionSequentialValue(state.questTemplateOptionArr, state.contentDialogObj?.itemValue)
            if (nextStep) {
              ElMessage.warning('DCT序列值不可以重复')
              return
            }
          }
          // 遍历查看
          state.contentDialogRef.validate((valid) => {
            if (valid) {
              state.contentDialogObj.dctQuestItemId = state.questionnaireContent.id
              state.loading = true
              // 保存或者修改-接口调用
              postQuestTemplateOption(state.contentDialogObj)
                .then(() => {
                  ElMessage.success('保存成功')
                  state.getQuestTemplateOptionList()
                  state.loading = false
                  state.contentDialogShow = false
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        } else if (falg === 'e') {
          // 编辑
          state.contentDialogObj = { ...item }
          state.contentDialogShow = true
        } else if (falg === 'a') {
          // 新增
          state.contentDialogObj = {
            dctQuestItemId: '',
            gaugeValue: null,
            id: null,
            itemContent: '',
            itemName: '',
            itemValue: '',
            sort: null
          }
          state.contentDialogShow = true
        } else if (falg === 'd') {
          // 删除
          ElMessageBox.confirm(
            `是否确认删除选项？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              deleteQuestTemplateOption(item.id).then(() => {
                ElMessage.success(`删除成功!`)
                state.getQuestTemplateOptionList()
              })
            })
            .catch(() => {})
        }
      },
      // 删除当前问卷
      deletePresent: () => {
        ElMessageBox.confirm(
          `是否确认删除？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
          .then(() => {
            deleteQuestTemplateItem(state.questionnaireContent.id).then(() => {
              ElMessage.success('删除成功')
              state.backRefresh()
            })
          })
          .catch(() => {})
      },
      // 切换当前关联选项值数组
      changeFieldLabel: (e) => {
        state.questTemplateArr.map((item, index) => {
          if (item.dctCode === e) {
            state.questTemplateOptionIndex = index
            if (Array.isArray(state.relevanceObj.refItemValue)) {
              state.relevanceObj.refItemValue = []
            } else {
              state.relevanceObj.refItemValue = ''
            }
          }
        })
      },
      // 关联题目数组生成
      cleanQuestTemplateArr: (id) => {
        // 关联题目的数组
        if (state.questionnaireContent?.id) {
          const getQuestTemplateFun = QUESTIONNAIRE_CONTENT.editOrAddList === 'editList' || QUESTIONNAIRE_CONTENT.editOrAddList === 'addList'
            ? getDropRefQuestTemplateItemTable : getDropRefQuestTemplateItem
          getQuestTemplateFun(state.questionnaireContent.id).then(
            (res) => {
              state.questTemplateArr = res
              const relevanceObj = { ...state.relevanceObj }
              state.questTemplateArr.forEach((item, index) => {
                if (item.dctCode === state.relevanceObj.refDctCode) {
                  state.questTemplateOptionIndex = index
                  // console.log(state.questTemplateArr[state.questTemplateOptionIndex].refQuestTemplateItemOption)
                  // console.log(state.questTemplateOptionIndex, state.relevanceObj.refItemValue)
                  // if (state.questTemplateArr[index]?.crfFieldControl === 10) {
                  if (state.relevanceObj.refItemValue.length && !Array.isArray(state.relevanceObj.refItemValue)) {
                    state.relevanceObj.refItemValue = relevanceObj.refItemValue.split(',')
                  } else if (!state.relevanceObj.refItemValue) {
                    state.relevanceObj.refItemValue = []
                  }
                  // }
                }
              })
            }
          )
        }
        // const { questTemplateItem } = QUESTIONNAIRE_CONTENT.questionnaireContentViews
        // for (var i = 0; i < questTemplateItem.length; i++) {
        //   if (questTemplateItem[i].id === id) {
        //     break
        //   }
        //   if (questTemplateItem[i].crfFieldControl === 9 || questTemplateItem[i].crfFieldControl === 10) {
        //     state.questTemplateArr.push(questTemplateItem[i])
        //   }
        // }
      }
    })

    onMounted(() => {
      // console.log(QUESTIONNAIRE_CONTENT.questionnaireContentViews.questTemplateItem[0].questTemplateOption)
      if (JSON.stringify(QUESTIONNAIRE_CONTENT.questionnaireContent) !== '{}') {
        state.questionnaireContent = QUESTIONNAIRE_CONTENT.questionnaireContent
        state.oldQuestionnaireContent = { ...QUESTIONNAIRE_CONTENT.questionnaireContent }
        state.relevanceObj = { ...QUESTIONNAIRE_CONTENT.questionnaireContent }
        state.changeFieldLabel(state.relevanceObj.refDctCode)
        // state.relevanceObj.relevanceSpecial = state.relevanceObj.refDctCode === '性别' ? '2' : '1'
        state.cleanQuestTemplateArr(state.questionnaireContent.id)
      }
      if (QUESTIONNAIRE_CONTENT.customShow) {
        state.customFieldLabelRef.gainTxt('get', state.questionnaireContent.fieldLabel)
        state.customFieldDescriptionRef.gainTxt('get', state.questionnaireContent.fieldDescription)
      }
    })

    return {
      ...toRefs(state),
      QUESTIONNAIRE_CONTENT,
    }
  },
})
</script>

<style lang="less" scoped>
.EditQuestionnaireContent{
    position: relative;
    div[data-we-id]{
      width: calc(100% - 18px);
    }
    .demo-tabs > .el-tabs__content {
        padding: 32px;
        background-color: #f4f5f7;
        color: #6b778c;
        font-size: 32px;
        font-weight: 600;
    }
    .delete-pos-tr{
      position: absolute;
      top: 0px;
      right: 0;
    }
    ::v-deep .el-tabs{
        min-height: 800px;
        // height: calc(100vh - 200px);
        background: #fff;
    }
    ::v-deep .el-tabs__nav-scroll{
        background: #f0f2f5;
    }
    ::v-deep .el-tabs__nav is-top,::v-deep [role="tablist"]{
        background: #fff;
    }
    ::v-deep .el-tabs__item,::v-deep .is-top,::v-deep .is-focus{
        min-width: 97px;
        text-align: center;
    }
    ::v-deep .el-tabs__content{
      padding: 30px;
    }
    .el-form,.el-form--default{
        display: flex;
        flex-wrap: wrap;
        .el-input,.el-select{
            margin: 0 20px 0 0;
        }
        .el-select{
            width: 100%;
        }
    }
    ::v-deep .el-table__cell,::v-deep .is-leaf{
      border: none !important;
    }
    ::v-deep .el-table__inner-wrapper::before{
      height: 0;
    }
}
.footer{
  display: flex;
  justify-content: flex-end
}
.clRelevance{
  // background: #fff;
  // border-color: #409eff;
  // &:hover{
  //   background: #409eff;
  //   color:  #fff;
  // }
}
</style>
