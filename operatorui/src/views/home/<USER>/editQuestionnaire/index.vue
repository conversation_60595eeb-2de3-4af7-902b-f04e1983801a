<template>
  <div class="EditQuestionnaire">
    <!-- 主内容 -->
    <div v-if="!versionsOptions.length" style="display: flex;justify-content: space-between;align-items: center;">暂无版本信息，请先同步CRF版本
      <el-button plain @click="setAddVersionsOrpreview('back')">返回</el-button>
    </div>
    <div v-show="!QUESTIONNAIRE_CONTENT?.contentVisible && versionsOptions?.length" class="EditQuestionnaire-module">
      <nav class="nav-module">
        <div class="nav-btn-le">
          <!-- :label="item.versionNumber" -->
          <el-select v-model="versionsValue" placeholder="请选择版本号" @change="getIEQuestTemplates">
            <el-option
              v-for="(item,index) in versionsOptions"
              :key="index"
              :label="item.versionStatus === 1 ?
                'V' + item.versionNumber + '测试版本' :
                (item.versionStatus === 2 ? 'V' + item.versionNumber + '正式版本' : item.versionNumber)"
              :value="item.studyVersionId"
              @click="editionListBtn(item,index)"
            />
          </el-select>
        </div>
        <div class="nav-btn-ri">
          <!-- v-if="versionsObj?.versionStatus && versionsObj?.versionStatus === 1" -->
          <el-button plain size="large" @click="setAddVersionsOrpreview('back')">返回</el-button>
          <el-button type="primary" plain size="large" @click="setAddVersionsOrpreview('preview')">预览</el-button>
          <el-button v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.activateState === 2" size="large" type="primary" @click="setAddVersionsOrpreview('add')">提交测试版本</el-button>
          <el-button v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.activateState === 2" size="large" type="danger" @click="setAddVersionsOrpreview('reset')">重置</el-button>
        </div>
      </nav>

      <div class="infos-body">
        <div class="infos-body-title">
          <h4>基本信息</h4>
          <el-button
            type="primary"
            @click="() => {
              QUESTIONNAIRE_INFOS.dialogVisible = true
            }"
          >编辑</el-button>
        </div>
        <div class="infos-body-content">
          <div class="infos-body-content-items">
            <div class="infos-body-content-items-label">DCT问卷名称</div>
            <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfName" v-html="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfName" />
          </div>
          <div class="infos-body-content-items">
            <div class="infos-body-content-items-paralleling">
              <div class="infos-body-content-items-label">问卷性质</div>
              <!-- 0 = 未知, 1 = 客户问卷, 2 = 医生问卷, 3 = 非客户问卷 -->
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType === 1">受试者问卷</span>
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType === 2">研究者问卷</span>
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType === 3">非用户问卷</span>
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType === 4">上门医护问卷</span>
            </div>
            <div class="infos-body-content-items-paralleling">
              <!-- 0 = 不同步, 1 = 同步EDC -->
              <div class="infos-body-content-items-label">数据同步</div>
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.isSynchronization">同步至EDC</span>
              <span v-else>不同步</span>
            </div>
          </div>
          <div class="infos-body-content-items">
            <div class="infos-body-content-items-paralleling">
              <div class="infos-body-content-items-label">问卷样式</div>
              <!-- 0 = 未知, 1 = 问卷完整列表, 2 = 问卷向导列表, 3 = 问卷Url, 4 = 上传图片的问卷 -->
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType === 1">完整问卷</span>
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType === 2">向导式问卷</span>
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType === 3">问卷URL</span>
              <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questDisplayType === 4">上传图片的问卷</span>
            </div>
            <div class="infos-body-content-items-paralleling">
              <div class="infos-body-content-items-label">问卷类型</div>
              <!-- v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questPatientTemplateType" -->
              <span>初筛问卷</span>
            </div>
          </div>
          <div class="infos-body-content-items">
            <div class="infos-body-content-items-label">填写说明</div>
            <span v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfGuideline" v-html="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfGuideline" />
          </div>
        </div>
      </div>

      <div class="questionnaire-body">
        <div class="questionnaire-body-title">
          <h4>问卷内容</h4>
          <el-button type="primary" @click="setQuestionnaire('add')">新建题目</el-button>
        </div>
        <div class="questionnaire-body-content">
          <div v-for="(item,index) in QUESTIONNAIRE_CONTENT.questionnaireContentViews.questTemplateItem" :key="index" class="questionnaire-body-content-module">
            <div class="questionnaire-body-content-module-head">
              <div class="sort">
                ({{ item.dctSort }})
                <span v-if="item.isRequired">(必填)</span>
              </div>
              <!-- 0 = 未知, 1 = 仅患者端显示, 2 = 仅医生端显示, 3 = 医生患者都显示 -->
              <div v-if="item?.dctQuestItemDisplayType" class="check">
                <span v-if="item.dctQuestItemDisplayType === 1">仅患者端显示</span>
                <span v-if="item.dctQuestItemDisplayType === 2">仅医生端显示</span>
                <span v-if="item.dctQuestItemDisplayType === 3">医生患者都显示</span>
              </div>
              <div class="btns">
                <span
                  v-if="item.crfFieldType === 3"
                  class="btns-edit"
                  @click="setQuestionnaire('addList', item)"
                >添加列表字段&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <span class="btns-delete" @click="setQuestionnaire('delete',item)">删除</span>
                <span class="btns-edit" @click="setQuestionnaire('edit',item)">编辑</span>
              </div>
            </div>
            <div class="questionnaire-body-content-module-items">
              <div class="questionnaire-body-content-module-item">
                <div class="questionnaire-body-content-module-item-lable">DCT中题目名称</div>
                <span v-if="item.fieldLabel" v-html="item.fieldLabel" />
              </div>
              <div class="questionnaire-body-content-module-item">
                <div class="questionnaire-body-content-module-item-lable">控件类型</div>
                <!-- 0 = 未知, 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件,
                5 = 数字控件, 6 = 日期控件, 7 = 时间控件
                , 8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件, 11 = 年月控件 -->
                <div v-for="(ite, idx) in QUESTIONNAIRE_CONTENT?.DropInfos?.questFieldControl" :key="idx">
                  <span v-if="ite.itemCode === item?.crfFieldControl">{{ ite.itemName }}</span>
                </div>
              </div>
            </div>
            <!-- 列表时 -->
            <div v-if="item.crfFieldType === 3">
              <div
                v-for="(listItem) in item.children"
                :key="listItem.id"
                class="questionnaire-list-module"
              >
                <div class="questionnaire-body-content-module-head">
                  <div class="sort">
                    ({{ listItem.dctSort }})
                    <span v-if="listItem.isRequired">(必填)</span>
                  </div>
                  <!-- 0 = 未知, 1 = 仅患者端显示, 2 = 仅医生端显示, 3 = 医生患者都显示 -->
                  <div v-if="listItem?.dctQuestItemDisplayType" class="check">
                    <span v-if="listItem.dctQuestItemDisplayType === 1">仅患者端显示</span>
                    <span v-if="listItem.dctQuestItemDisplayType === 2">仅医生端显示</span>
                    <span v-if="listItem.dctQuestItemDisplayType === 3">医生患者都显示</span>
                  </div>
                  <div class="btns">
                    <span v-if="listItem.edcFieldCode" />
                    <span
                      v-if="!listItem.edcFieldCode"
                      class="btns-delete"
                      @click="setQuestionnaire('delete', listItem)"
                    >删除</span>
                    <span
                      class="btns-edit"
                      @click="setQuestionnaire('editList', listItem)"
                    >编辑</span>
                  </div>
                </div>
                <div class="questionnaire-body-content-module-items">
                  <div class="questionnaire-body-content-module-item">
                    <div class="questionnaire-body-content-module-item-lable">
                      DCT中题目名称
                    </div>
                    <span v-if="listItem.fieldLabel" v-html="listItem.fieldLabel" />
                  </div>
                  <div class="questionnaire-body-content-module-item margin0">
                    <div class="questionnaire-body-content-module-item-lable">
                      控件类型
                    </div>
                    <div v-for="(ite, idx) in QUESTIONNAIRE_CONTENT?.DropInfos?.questFieldControl" :key="idx">
                      <span v-if="ite.itemCode === listItem.crfFieldControl">{{ ite.itemName }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 扫码预览 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="扫码预览"
    >
      <div style="display: flex;justify-content: center;padding:50px 0">
        <vue-qr v-if="siteUrl && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType && QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType < 3" :text="siteUrl" />
        <div v-else-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType === 3">非用户问卷～不支持填写。</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="previewDialogVisible = false"
          >返回</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 基本信息弹窗 -->
    <EditQuestionnaireInfo
      v-if="QUESTIONNAIRE_INFOS?.dialogVisible"
      :request="onLoad"
    />
    <!-- 新建编辑课题 -->
    <EditQuestionnaireContent
      v-if="QUESTIONNAIRE_CONTENT?.contentVisible"
      :request="onLoad"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, provide } from 'vue'
import EditQuestionnaireInfo from '@/views/home/<USER>/editQuestionnaire/EditQuestionnaireInfo.vue'
import EditQuestionnaireContent from '@/views/home/<USER>/editQuestionnaire/EditQuestionnaireContent.vue'
// import { studyIcfPrivacySLA, studyPrivacy } from '@/api/home'
import { useStore } from 'vuex'
import VueQr from 'vue-qr/src/packages/vue-qr.vue'
import {
  getQuestTemplateItemDropInfo,
  getDropStudyVersionsIE,
  getIEQuestTemplateInfo,
  deleteQuestTemplateItem,
  getQuestTemplateDropInfo,
  postIEQuestTemplateInfo,
  resetIEQuestTemplateInfo
} from '@/api/home'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'EditQuestionnaire', // 编辑初筛问卷
  components: {
    EditQuestionnaireInfo,
    EditQuestionnaireContent,
    VueQr
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const QUESTIONNAIRE_INFOS = reactive({
      questionnaireInfo: {
        crfGuideline: '',
        crfName: '',
        dctModuleContainerId: null,
        id: null,
        isSynchronization: 0,
        moduleContainer: null,
        questDisplayType: 2,
        questPatientTemplateType: 6,
        questTemplateItem: [],
        questTemplateType: 1,
        editOrAddList: ''
      },
      dialogVisible: false,
      DropInfos: {},
      templateDropInfos: {}
    })
    const QUESTIONNAIRE_CONTENT = reactive({
      questionnaireContent: {},
      questionnaireContentViews: {},
      contentVisible: false,
      DropInfos: {},
      templateDropInfos: {},
      versionsValue: ''
    })

    const state = reactive({
      editionIndex: 0,
      versionsValue: '',
      versionsOptions: [],
      versionsObj: {}, // 当前版本对象
      siteUrl: '', // 预览二维码地址
      previewDialogVisible: false, // 扫码预览显示隐藏
      // 预览-提交测试版本
      setAddVersionsOrpreview: (flag) => {
        if (flag === 'add') {
          ElMessageBox.confirm(
            `提交后，将升级为正式版本，部分数据将无法编辑，是否确认提交测试版本？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              // type: 'warning',
            }
          )
            .then(() => {
              if (state?.versionsValue) {
                postIEQuestTemplateInfo(state.versionsValue).then(() => {
                  ElMessage.success(`提交成功!`)
                  state.onLoad()
                  store.state.refreshFlag = true
                })
              }
            })
            .catch(() => {})
        } else if (flag === 'preview') {
          state.previewDialogVisible = true
        } else if (flag === 'back') {
          router.replace('/home')
          store.state.refreshFlag = true
        } else if (flag === 'reset') {
          ElMessageBox.confirm(
            `是否确认初始化当前测试版本？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              // type: 'warning',
            }
          )
            .then(() => {
              if (state?.versionsValue) {
                resetIEQuestTemplateInfo(state.versionsValue).then(() => {
                  ElMessage.success(`提交成功!`)
                  state.onLoad()
                  store.state.refreshFlag = true
                })
              }
            })
            .catch(() => {})
        }
      },
      editionListBtn: (val, index) => {
        state.editionIndex = index
        // 不明白为什么要这样做, 因为下拉框出现的就是选项显示的内容, 目前这样做会影响发布和重置, 注释掉. 简单测试没有什么问题, 
        // state.versionsValue = val.versionStatus === 1
        //   ? 'V' + val.versionNumber + '测试版本'
        //   : val.versionStatus === 2
        //     ? 'V' + val.versionNumber + '正式版本'
        //     : val.versionNumber
      },
      setQuestionnaire: (flag, item) => {
        if (flag === 'delete') {
          ElMessageBox.confirm(
            `是否确认删除？`,
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              deleteQuestTemplateItem(item.id).then(() => {
                ElMessage.success(`删除成功!`)
                state.onLoad()
              })
            })
            .catch(() => {})
        } else if (flag === 'edit' || flag === 'editList') {
          // 编辑
          QUESTIONNAIRE_CONTENT.questionnaireContent = item
          QUESTIONNAIRE_CONTENT.editOrAddList = flag
          if (QUESTIONNAIRE_CONTENT.questionnaireContent.specialFieldType === 0) {
            QUESTIONNAIRE_CONTENT.questionnaireContent.specialFieldType = null
          }
          QUESTIONNAIRE_CONTENT.contentVisible = true
        } else if (flag === 'add' || flag === 'addList') {
          // 新增
          QUESTIONNAIRE_CONTENT.editOrAddList = flag
          QUESTIONNAIRE_CONTENT.questionnaireContent = {
            crfFieldControl: null,
            crfFieldType: null,
            dctCode: '',
            dctQuestItemDisplayType: null, // 3
            dctQuestTemplateId: QUESTIONNAIRE_CONTENT.questionnaireContentViews?.id || '', // 问卷id
            dctQuestUnit: null,
            dctSort: null,
            fieldDescription: '',
            fieldLabel: '',
            id: null,
            isRequired: 0,
            questTemplateOption: [],
            refDctCode: '',
            refItemValue: '',
            refType: 0,
            specialFieldType: null,
            parentId: item?.id || '',
            minimumYear: '1900',
            maximumYear: '2099',
            isAllowUK: 0,
            isFutureDate: 1,
          }
          QUESTIONNAIRE_CONTENT.contentVisible = true
        }
      },
      // 获取数据
      onLoad: () => {
        // 获取版本信息
        getDropStudyVersionsIE(store.state.studyItem.studyId).then((r) => {
          if (r?.length) {
            state.versionsOptions = r
            state.versionsValue = r[state.editionIndex]?.studyVersionId || []
            state.versionsObj = r[state.editionIndex] || {}
            QUESTIONNAIRE_CONTENT.versionsValue = state.versionsValue
            // 入排问卷
            state.getIEQuestTemplates()
          }
        })
        // 基本信息下拉
        getQuestTemplateDropInfo().then((res) => {
          QUESTIONNAIRE_INFOS.templateDropInfos = res
          QUESTIONNAIRE_CONTENT.templateDropInfos = res
        })
        // 所有下拉框
        getQuestTemplateItemDropInfo().then((res) => {
          QUESTIONNAIRE_INFOS.DropInfos = res
          QUESTIONNAIRE_CONTENT.DropInfos = res
        })
      },
      // 切换版本
      // 获取入排问卷
      getIEQuestTemplates: (e) => {
        // 切换后取当前版本对象
        if (e) {
          QUESTIONNAIRE_CONTENT.versionsValue = state.versionsValue
          state.versionsOptions.map((item) => {
            if (item.studyVersionId === e) {
              state.versionsObj = item
            }
          })
        }
        getIEQuestTemplateInfo(state.versionsValue).then((re) => {
          QUESTIONNAIRE_INFOS.questionnaireInfo = re
          QUESTIONNAIRE_CONTENT.questionnaireContentViews = re
          if (
            QUESTIONNAIRE_CONTENT?.questionnaireContentViews?.id &&
            store?.getters?.app?.authorization
          ) {
            state.siteUrl = `${
              window.location.origin
            }/doctorui/#/questionnairePreview==type=${QUESTIONNAIRE_INFOS?.questionnaireInfo?.questTemplateType}==${
              QUESTIONNAIRE_CONTENT.questionnaireContentViews.id
            }==${store.getters.app.authorization}`
            // http://***********:8002/doctorui/#/
          }
        })
      }
    })

    onMounted(() => {
      state.onLoad()
    })

    // 传值
    provide('QUESTIONNAIRE_INFOS', QUESTIONNAIRE_INFOS)
    provide('QUESTIONNAIRE_CONTENT', QUESTIONNAIRE_CONTENT)

    return {
      QUESTIONNAIRE_INFOS,
      QUESTIONNAIRE_CONTENT,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="less">
@import '@/style/questionnaire.less';
</style>
<style lang="less" scoped>
.EditQuestionnaire {
  color: #333;
  ::v-deep .footer{
    margin: 40px 0 0 0;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .pos-rb{
   margin: 200px 0 0 0;
  }
  .nav-module{
    margin: 10px 0 0 0;
    display: flex;
    .nav-btn-le{
      width: 50%;
      display: flex;
      align-items: center;
      h4{
        margin: 0 30px 0 0;
      }
    }
    .nav-btn-ri{
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .infos-body{
    width: 100%;
    // height: 200px;
    margin: 20px 0;
    padding: 0 20px 10px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 5px;
    .infos-body-title{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .infos-body-content{
      .infos-body-content-items{
        display: flex;
        align-items: center;
        margin: 0px 0 20px 0;
        span{
          max-width: 80%;
          display: -webkit-box;
          word-break: break-all;
          word-wrap:break-word;
          overflow: hidden;
          /*…省略形式*/
          text-overflow: ellipsis;
          /*从上向下垂直排列子元素*/
          -webkit-box-orient: vertical;
          /*文本可分两行*/
          -webkit-line-clamp: 3;
        }
        .infos-body-content-items-paralleling{
          width: 400px;
          display: flex;
          align-items: center;
        }
        .infos-body-content-items-label{
          width: 100px;
          text-align: right;
          color: #999;
          margin: 0 20px 0 0;
          white-space: nowrap;
        }
      }
    }
  }
  .questionnaire-body{
    width: 100%;
    // height: 200px;
    margin: 20px 0;
    padding: 0 20px 30px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 5px;
    .questionnaire-body-title{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .questionnaire-body-content{
      width: 100%;
      padding: 0 40px;
      box-sizing: border-box;
      .questionnaire-body-content-module{
        width: 100%;
        padding: 12px 20px;
        margin: 0 0 20px 0;
        box-sizing: border-box;
        min-height: 130px;
        border-radius: 5px;
        background: #f2f2f2;
        .questionnaire-body-content-module-head{
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .sort{
            width: 117px;
            color: #FE664E;
            span{
              margin: 0 0 0 16px;
            }
          }
          .check{
            width: 200px;
            text-align: center;
            color: #FE664E;
          }
          .btns{
            display: flex;
            align-content: center;
            .btns-delete{
              margin: 0 40px 0 0;
              color: #FD004E;
            }
            .btns-edit{
              color: #409EFF;
            }
            &:hover{
              cursor: pointer;
            }
          }
        }
        .questionnaire-body-content-module-items{
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin: 20px 0 0 0;
          .questionnaire-body-content-module-item{
            width: 100%;
            margin: 0 0 20px 0;
            display: flex;
            align-items: center;
            .questionnaire-body-content-module-item-lable{
              width: 150px;
              margin: 0 20px 0 0;
              text-align: right;
              color: #666;
            }
          }
        }
      }
    }
  }
}
</style>
