<template>
  <trial-dialog v-model="QUESTIONNAIRE_INFOS.dialogVisible" title="编辑基本信息">
    <template #DialogBody>
      <el-form ref="questionnaireInfoRef" :model="questionnaireInfo" :rules="rules" label-position="top">
        <el-form-item
          label="DCT问卷名称"
          :label-width="formLabelWidth"
          prop="crfName"
          style="width: 100%"
        >
          <el-input
            v-model.trim="questionnaireInfo.crfName"
            placeholder="请填写问卷名称"
            autocomplete="off"
            maxlength="666"
          />
        </el-form-item>
        <el-form-item
          label="问卷性质"
          :label-width="formLabelWidth"
          prop="questTemplateType"
          style="width: 50%"
        >
          <el-select v-model="questionnaireInfo.questTemplateType" placeholder="请选择问卷类型">
            <el-option
              v-for="item in QUESTIONNAIRE_INFOS.templateDropInfos.questTemplateType"
              :key="item.id"
              :label="item.itemName"
              :value="item.itemCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="数据同步"
          :label-width="formLabelWidth"
          prop="synchronization"
          style="width: 50%"
        >
          <el-select v-model="questionnaireInfo.isSynchronization" placeholder="请选择问卷类型">
            <el-option
              v-for="item in isSynchronizationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="问卷样式"
          :label-width="formLabelWidth"
          prop="style"
          style="width: 50%"
        >
          <el-select v-model="questionnaireInfo.questDisplayType" placeholder="请选择问卷类型">
            <el-option
              v-for="item in QUESTIONNAIRE_INFOS.templateDropInfos.questStyleType"
              :key="item.id"
              :label="item.itemName"
              :value="item.itemCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="问卷类型"
          :label-width="formLabelWidth"
          prop="questPatientTemplateType"
          style="width: 50%"
        >
          <!-- <el-select v-model="questionnaireInfo.questPatientTemplateType" disabled placeholder="请选择问卷类型"> -->
          <el-select v-model="questionnaireInfo.questPatientTemplateType" placeholder="请选择问卷类型">
            <el-option
              v-for="item in QUESTIONNAIRE_INFOS.templateDropInfos.questPatientTemplateType"
              :key="item.value"
              :label="item.itemName"
              :value="item.itemCode"
              :disabled="item.itemCode !== questPatientTemplateNumber"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="questionnaireInfo?.showQuestSignatureType && locationHerf.includes('customTaskQuestionnaire')"
          label="问卷签署（目前单个签署节点，由提交人签署）"
          :label-width="formLabelWidth"
          prop="qusetSubmitSignatureType"
          class="w-1/2"
        >
          <el-select v-model="questionnaireInfo.qusetSubmitSignatureType" placeholder="请选择问卷类型">
            <el-option
              label="无需签署"
              :value="0"
            />
            <el-option
              label="需签署"
              :value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="questionnaireInfo?.showQuestReviewType && locationHerf.includes('customTaskQuestionnaire')"
          label="问卷审阅（目前单个审阅节点，由PI或SUBI审阅）"
          :label-width="formLabelWidth"
          prop="qusetReviewSignatureType"
          class="w-1/2"
        >
          <el-select v-model="questionnaireInfo.qusetReviewSignatureType" placeholder="请选择问卷类型">
            <el-option
              label="无需审阅"
              :value="0"
            />
            <el-option
              label="需审阅"
              :value="1"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="填写说明"
          :label-width="formLabelWidth"
          prop="explain"
          style="width: 100%"
        >
          <el-input
            v-model.trim="questionnaireInfo.crfGuideline"
            type="textarea"
            autocomplete="off"
            maxlength="666"
          />
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <div class="footer">
        <el-button size="large" @click="QUESTIONNAIRE_INFOS.dialogVisible = false">取 消</el-button>
        <el-button size="large" type="primary" :loading="loading" @click="submit">保 存</el-button>
      </div>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, inject, onMounted } from 'vue'
import {
  postIEQuestTemplate
} from '@/api/home'
import { postCustomTaskQuests } from '@/api/customTask'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'EditQuestionnaireInfo',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    },
    getCustomTaskQuests: { // 调用自定义问卷的模板接口
      type: Function,
      default: () => {}
    }
  },
  setup({ request, getCustomTaskQuests }) {
    const store = useStore()
    const QUESTIONNAIRE_INFOS = inject('QUESTIONNAIRE_INFOS')
    const state = reactive({
      locationHerf: location.href,
      questionnaireInfo: {
        crfGuideline: '',
        crfName: '',
        dctModuleContainerId: null,
        id: null,
        isSynchronization: 0,
        moduleContainer: null,
        questDisplayType: 2,
        questPatientTemplateType: 6, // 6为客户入排
        questTemplateItem: [],
        questTemplateType: 1,
        qusetSubmitSignatureType: null,
        qusetReviewSignatureType: null,
        showQuestSignatureType: false,
        showQuestReviewType: false,
      },
      formLabelWidth: '120px',
      questionnaireInfoRef: null,
      loading: false,
      questPatientTemplateNumber: 6, // 除了这个禁用其他问卷
      // 数据同步
      isSynchronizationOptions: [
        {
          value: 1,
          label: '同步至EDC',
        },
        {
          value: 0,
          label: '不同步',
        },
      ],
      rules: {
        crfName: [
          { required: true, message: '请输入问卷名称', trigger: 'blur' },
          // {
          //   pattern: /^[0-9]*$/,
          //   message: '请输入数字',
          //   trigger: 'blur',
          // },
        ],
      },

      submit: () => {
        state.questionnaireInfoRef.validate((valid) => {
          if (valid) {
            state.loading = true
            // if (!state.questionnaireInfo.questPatientTemplateType) {
            //   state.questionnaireInfo.questPatientTemplateType = 0
            // }
            if (QUESTIONNAIRE_INFOS.customShow) { // 自定义问卷的保存
              const { studyId } = store.state.studyItem
              postCustomTaskQuests(studyId, state.questionnaireInfo)
                .then((res: any) => {
                  request()
                  getCustomTaskQuests(res?.id) // 调用自定义问卷模板的方法
                  ElMessage.success('保存成功')
                  state.loading = false
                  QUESTIONNAIRE_INFOS.dialogVisible = false
                })
                .catch(() => {
                  state.loading = false
                })
            } else {
              postIEQuestTemplate(state.questionnaireInfo)
                .then(() => {
                  request()
                  ElMessage.success('保存成功')
                  state.loading = false
                  QUESTIONNAIRE_INFOS.dialogVisible = false
                })
                .catch(() => {
                  state.loading = false
                })
            }
          }
        })
      },
    })

    onMounted(() => {
      if (JSON.stringify(QUESTIONNAIRE_INFOS.questionnaireInfo) !== '{}') {
        state.questionnaireInfo = { ...QUESTIONNAIRE_INFOS.questionnaireInfo }
      }
      if (QUESTIONNAIRE_INFOS.customShow) {
        state.isSynchronizationOptions = [
          {
            value: 0,
            label: '不同步',
          },
        ]
        state.questionnaireInfo.questPatientTemplateType = 12
        state.questPatientTemplateNumber = 12
      } else {
        state.questionnaireInfo.questPatientTemplateType = 6
        state.questPatientTemplateNumber = 6
      }
    })

    return {
      ...toRefs(state),
      QUESTIONNAIRE_INFOS,
    }
  },
})
</script>

<style lang="less" scoped>
::v-deep .el-textarea__inner{
  height: 130px;
}
.el-form,.el-form--default{
  display: flex;
  flex-wrap: wrap;
  .el-input,.el-select{
      margin: 0 20px 0 0;
  }
  .el-select{
      width: 100%;
  }
}
.footer{
  display: flex;
  justify-content: flex-end
}
</style>
