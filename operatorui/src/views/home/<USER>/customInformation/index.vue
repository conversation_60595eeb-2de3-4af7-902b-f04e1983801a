<template>
  <div class="customInformation">
    <div class="customInformation-headTitle">
      <h3>自定义信息</h3>
      <div>
        <el-button size="large" @click="routerGo('back')">返回</el-button>
        <el-button
          type="primary"
          size="large"
          :loading="loading"
          @click="submit"
        >保 存</el-button>
      </div>
    </div>

    <div class="customInformation-body">
      <el-form
        label-position="top"
        label-width="120px"
        :model="customInformation"
      >
        <div class="flex">
          <el-form-item label="应用名称-受试者端" style="width: 30%" class="mr-5">
            <el-input v-model="customInformation.patientApplicationName" class="w-full" />
          </el-form-item>
          <el-form-item label="应用名称-研究端" style="width: 30%">
            <el-input v-model="customInformation.doctorApplicationName" class="w-full" />
          </el-form-item>
        </div>
      </el-form>
      <div class="customInformation-body-logo">
        <div class="flex">
          <div class="w-2/5 mr-5">
            <div class="mb-5">项目介绍页banner-受试者端</div>
            <div class="flex items-end">
              <MyUpload
                :upload-img-falg="true"
                :before-file-upload-type="['.svg']"
                :upload-img="getImgFile.rotationChartUrls"
                :request="postStudyFile"
                :request-fun="requestFunBanner"
                :file-size="1 / 2.048"
              />
              <div class="ml-5 customInformation-color-tips">
                <div>已预置默认图片，按需替换最大不超过500KB</div>
                <div>建议宽750px，高368px支持svg格式</div>
              </div>
            </div>
          </div>
          <div class="w-2/5">
            <div class="mb-5">项目LOGO-研究端</div>
            <div class="flex items-end">
              <MyUpload
                :upload-img-falg="true"
                :before-file-upload-type="['.png', '.jpg', '.jpeg', '.svg']"
                :upload-img="getImgFile.thumbnailUrl"
                :request="postStudyFile"
                :request-fun="requestFun"
                :file-size="1 / 2.048"
              />
              <div class="ml-5 customInformation-color-tips">
                <div>已预置默认图片，按需替换最大不超过500KB</div>
                <div>建议宽120px，高120px支持png，jpg，jpeg，svg格式</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 课题介绍 -->
      <div class="customInformation-body-subjectIntroduce">
        <div style="margin: 0 0 10px 0">项目介绍</div>
        <trial-wang-editor ref="subjectIntroduce" class="subjectIntroduce" :toolbar-config="toolbarConfig" />
      </div>
      <!-- 完成研究结束语 -->
      <div class="customInformation-body-endLanguage">
        <div style="margin: 0 0 10px 0">完成研究结束语</div>
        <trial-wang-editor ref="endLanguage" class="endLanguage" :toolbar-config="toolbarConfig" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import MyUpload from '@/components/Upload/index.vue'
// import lodash from 'lodash'
import { getStudyInfo, postStudyFile, postStudyCustomInfo } from '@/api/home'

export default defineComponent({
  name: 'CustomInformation', // 自定义信息
  components: {
    MyUpload,
  },
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const router = useRouter()
    const state = reactive({
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          'uploadImage', // 上传图片
          'uploadVideo' // 上传视频
        ],
      },
      postStudyFile,
      customInformation: {
        avatarUrl: '',
        projectContent: '',
        completeResearchConclusion: '',
        patientApplicationName: '',
        doctorApplicationName: ''
      },
      getImgFile: {
        dctStudyFileId: '',
        thumbnailUrl: '',
        rotationChartUrls: ''
      },
      subjectIntroduce: null,
      endLanguage: null,
      subjectIntroduceHtml: '',
      endLanguageHtml: '',
      loading: false,
      requestFun: (res) => {
        state.getImgFile.dctStudyFileId =
          res.dctCommonFileId
        state.getImgFile.thumbnailUrl =
          res.thumbnailUrl
      },
      requestFunBanner: (res) => {
        state.getImgFile.rotationChartUrls =
          res.thumbnailUrl
      },
      // 跳转
      routerGo: (path) => {
        if (path === 'back') {
          router.replace('/home')
          store.state.refreshFlag = true
          return
        }
        router.push({
          path,
        })
      },
      submit: () => {
        state.endLanguageHtml = state.endLanguage.gainTxt('set')
        state.subjectIntroduceHtml = state.subjectIntroduce.gainTxt('set')
        const { dctStudyFileId, thumbnailUrl, rotationChartUrls } =
          state.getImgFile
        const paramsData = {
          studyId,
          doctorApplicationName: state.customInformation?.doctorApplicationName,
          patientApplicationName: state.customInformation?.patientApplicationName,
          projectContent: state.subjectIntroduceHtml,
          completeResearchConclusion: state.endLanguageHtml,
          dctStudyFileId,
          thumbnailUrl,
          rotationChartUrls
        }
        state.loading = true
        postStudyCustomInfo(paramsData)
          .then((res) => {
            state.loading = false
            ElMessage.success('保存成功')
          })
          .catch(() => {
            state.loading = false
          })
      },
      // 获取数据
      onLoad: () => {
        getStudyInfo(studyId).then((res: any) => {
          state.customInformation = res
          state.getImgFile.dctStudyFileId = res.dctStudyFileId
          state.getImgFile.thumbnailUrl = res.studyLogoThumbnailUrl
          state.getImgFile.rotationChartUrls = res.rotationChartUrls
          state.customInformation.patientApplicationName = res?.patientApplicationName || ''
          state.customInformation.doctorApplicationName = res?.doctorApplicationName || ''

          if (res?.projectContent) {
            state.subjectIntroduce.gainTxt('get', res.projectContent)
          }
          if (res?.completeResearchConclusion) {
            state.endLanguage.gainTxt('get', res.completeResearchConclusion)
          } else {
            // 完成研究结束语用富文本编辑器，预置文案
            state.endLanguage.gainTxt('get', `<p>恭喜您已顺利完成本次研究~</p><p>祝您早日康复，生活愉快！</p>`)
          }
        })
      },
    })

    onMounted(() => {
      state.onLoad()
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.customInformation {
  width: 100%;
  min-height: 100%;
  background: #fff;
  .customInformation-headTitle {
    width: 100%;
    background: #fff;
    margin-top: 10px;
    box-sizing: border-box;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h3 {
      padding: 0;
      margin: 0;
    }
  }
  .customInformation-body {
    padding: 0px 40px 20px 40px;
    box-sizing: border-box;
    .customInformation-body-logo {
      .customInformation-color-tips {
        color: #999;
      }
    }
    .customInformation-body-subjectIntroduce {
      margin: 20px 0;
    }
    // .customInformation-body-endLanguage{
    //   margin: 0 0 20px 0;
    // }
  }
}
</style>
