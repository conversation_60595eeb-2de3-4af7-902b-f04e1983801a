<template>
  <div class="study">
    <div class="w-[40%] nav centerflex-h justify-between">
      <div class="nav-inquire centerflex-h">
        <div class="none-warp-text">项目名称</div>
        <el-input
          v-model.trim="siteName"
          class="item-name-text min-w-[260px]"
          maxlength="999"
          clearable
          placeholder="请输入项目名称"
          @keyup.enter="handleSiteNameQuery"
        />
        <el-button type="primary" size="default" @click="handleSiteNameQuery"
          >查询</el-button
        >
      </div>
      <!-- <p class="loginout" @click="logout">退出登录</p> -->
    </div>
    <!-- <div class="study-new-edc-item centerflex-h">
      <div>项目列表</div>
      <el-button
        v-permission="['sys.study.add']"
        type="primary"
        size="default"
        @click="newItemDialogVisible = true"
      >新建</el-button>
    </div> -->
    <div class="study-body">
      <div
        v-for="(item, index) in studyList"
        :key="index"
        class="study-bodyBox"
      >
        <div class="study-name centerflex-h">
          {{ item.studyName }}
        </div>
        <div class="study-bodyBox-bottom centerflex-h">
          <!-- v-show="item.configButtonVisible" -->
          <!-- <el-button
            v-permission="['sys.study.config']"
            type="primary"
            size="default"
            :loading="checkedStudyLoadingFlag"
            @click="checkedStudy(item)"
          >配置</el-button> -->
          <!-- v-show="permissions.indexOf('5') > -1" -->
          <el-button
            type="primary"
            size="default"
            @click="administrationBtn(item)"
          >管理</el-button>
        </div>
      </div>
    </div>
    <!-- 分页 layout="prev, pager, next,total,sizes,"-->
    <el-pagination
      v-if="totalItemCount > 0"
      class="centerflex mt-4"
      layout="total, sizes, prev, pager, next, jumper"
      :current-page="pageIndex"
      :page-size="pageSize"
      :total="totalItemCount"
      :page-sizes="pageSizes"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog v-model="newItemDialogVisible" title="新建" width="40%">
      <el-form
        ref="newItemFormRef"
        :model="newItemForm"
        :rules="newItemRules"
        label-width="100px"
      >
        <el-form-item label="EDC项目ID" prop="edcStudyId">
          <el-input
            v-model.trim="newItemForm.edcStudyId"
            maxlength="999"
            placeholder="请输入EDC项目ID"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="newItemDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleNewItem"
            >确认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { getUserStudyBreifInfo, postNewStudy } from '@/api/login'
import { defineComponent, onBeforeMount, reactive, toRefs, } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'Study', // 课题列表
  setup() {
    const router = useRouter()
    const route = useRoute()
    const store = useStore()
    const state = reactive({
      // permissions: [], // 项目权限
      studyList: [],
      // userAppType: 0,
      siteName: '',
      siteNameQuery: '',
      pageIndex: 1,
      pageSize: 5,
      pageSizes: [5, 10, 20, 30, 40, 50, 100],
      totalItemCount: 0,
      newItemDialogVisible: false,
      loading: false,
      newItemFormRef: null,
      newItemForm: {
        edcStudyId: '',
      },
      newItemRules: {
        edcStudyId: [
          { required: true, message: '请输入EDC项目ID', trigger: 'blur' },
        ],
      },
      // 当前页变化
      handleCurrentChange: (e) => {
        state.pageIndex = e
        state.onLoad()
      },
      // 改变每页size数量
      handleSizeChange: (e) => {
        state.pageIndex = 1
        state.pageSize = e
        state.onLoad()
      },
      handleSiteNameQuery: () => {
        const { siteName } = state
        state.siteNameQuery = siteName
        state.onLoad()
      },
      handleNewItem: () => {
        state.newItemFormRef.validate(async(valid) => {
          if (valid) {
            state.loading = true
            try {
              await postNewStudy({
                edcStudyId: state.newItemForm.edcStudyId,
              })
              ElMessage.success('保存成功')
              await state.onLoad() // 再次获取
              state.loading = false
              state.newItemDialogVisible = false
            } catch {
              state.loading = false
            }
          }
        })
      },

      onLoad: () => {
        if (route.query.delAllTags === '1') {
          if (store.state.studyItem) {
            sessionStorage.setItem('studyInfo', '{}')
            store.commit('setStudyItem', '')
            // 清除标签栏
            // store.dispatch('tags/delAllTags')
            store.dispatch('tags/delOtherTags', {
              path: '/study',
              fullPath: '/study',
              name: 'Study',
              meta: {
                title: '我的项目',
                roles: ['admin', 'visitor'],
              },
              params: {},
              query: {},
              title: '我的项目',
            })
            store.dispatch(
              'menu/generateMenus',
              store.state.account.userinfo && store.state.account.userinfo.role
            )
            // console.log(store.state.menu, 'store.state.menu')
            // debugger
          }
          // 这里需要排除study标签tags
          // store.dispatch('tags/delAllTags')
          // store.dispatch(
          //   'menu/generateMenus',
          //   store.state.account.userinfo && store.state.account.userinfo.role
          // )
        }
        // if (!store.getters?.account?.userinfo?.systemPermissionTree?.length) {
        //   // 接口取到的权限
        //   getUserPermissionTree().then((res: any) => {
        //     store.commit('account/setUserinfo', res)
        //   })
        // }
        // state.permissions = store?.getters?.account?.userinfo?.permissions || []
        if (store?.getters?.account?.userinfo?.dctUserId) {
          getUserStudyBreifInfo(store.getters.account.userinfo.dctUserId, {
            pageSize: state.pageSize,
            pageIndex: state.pageIndex,
            studyName: state.siteNameQuery,
          })
            .then((res: any) => {
              state.studyList = res.items
              state.totalItemCount = res.totalItemCount
            })
            .catch(() => {
              state.studyList = store.getters?.account?.userinfo?.studies || []
            })
          // getPermission(store.getters.account.userinfo.dctUserId)
          //   .then((res) => {
          //     state.userAppType = res.userAppType
          //   })
          //   .catch(() => {
          //     state.studyList = store.getters?.account?.userinfo?.studies || []
          //   })
        } else {
          state.logout()
        }
      },
      // 获取权限的方法
      // getRolesFun: (item) => {
      //   // item.studyId
      //   return getUserStudyPermission(item?.studyId)
      // },
      checkedStudyLoadingFlag: false,
      // 进入某个项目——配置
      checkedStudy: async(item) => {
        sessionStorage.setItem('studyInfo', JSON.stringify(item))
        // 获取权限
        // state.checkedStudyLoadingFlag = true
        // const rolesRes = await state.getRolesFun(item)
        // store.commit('account/setUserinfo', { ...store.state.account.userinfo, ...rolesRes })
        // 清除标签栏
        store.dispatch('tags/delAllTags')
        state.setStudyItemNameValue(item)
        router.push('/home')
        // state.checkedStudyLoadingFlag = false
      },
      setStudyItemNameValue: (item) => {
        if (item?.sites?.length) {
          item.sites.forEach((el) => {
            el.name = el.siteName
            el.value = el.siteId
          })
        }
        store.commit('setStudyItem', item)
      },
      // 进入某个项目——项目管理
      administrationBtn: (item) => {
        if (state.checkedStudyLoadingFlag) {
          return
        }
        // const rolesRes = await state.getRolesFun(item)
        // store.commit('account/setUserinfo', { ...store.state.account.userinfo, ...rolesRes })
        // state.setStudyItemNameValue(item)
        //  存
        // store.commit('account/setUserinfo', res)
        if (store.getters?.account?.userinfo?.systemPermissionTree?.childSysMenus) {
          localStorage.setItem('operatoruiSystemPermissionTree', JSON.stringify(store.getters?.account?.userinfo?.systemPermissionTree))
        }
        const myLogingOutTime = localStorage.getItem('logingOutTime')
        const objList = {
          myLogingOutTime,
          studyId: item.studyId,
          token: store.state.account.userinfo.token,
          dctUserId: store.state.account.userinfo.dctUserId,
          // 分割以下可以用于请求获取
          // studyName: item.studyName,
          // name: store.state.account.userinfo.name,
          // avatar: store.state.account.userinfo.avatar,
          // sites: item.sites,
          // msg: store?.getters?.account?.userinfo?.msg || '',
          // permissions: store?.getters?.account?.userinfo?.permissions || [],
          // role: store?.getters?.account?.userinfo?.role || '',
          // roles: store?.getters?.account?.userinfo?.roles || [],
        }

        // 清除标签栏
        store.dispatch('tags/delAllTags')
        // 正常域名跳转
        window.location.href = `${window.location.origin}/OperatorManagementUI/#/home?newStore=${JSON.stringify(objList)}`
        // // 本地调试的时候把localhost换成自己的
        // window.location.href = `http://localhost:8087/OperatorManagementUI/#/home?newStore=${JSON.stringify(
        //   objList
        // )}`
      },
      // 退出登录
      logout: () => {
        localStorage.setItem('logingOutTime', '')
        // 清除token
        store.dispatch('app/clearToken')
        store.commit('setStudyItem', '')
        // 清除标签栏
        store.dispatch('tags/delAllTags')
        router.push('/login')
      },
    })
    onBeforeMount(() => {
      // console.log(store.state.studyItem, 'store.state.studyItem')
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.study {
  width: 100%;
  height: 100vh;
  padding: 20px;
  background: #f7f7f7;
  padding-bottom: 30px;
  box-sizing: border-box;
  .el-pagination {
    margin: 30px 0 0 0;
  }
  .nav {
    height: 70px;
    background: #fff;
    border-radius: 5px;
    position: relative;
    .nav-inquire {
      padding-left: 20px;
      box-sizing: border-box;
      .item-name-text {
        margin: 0 20px 0 10px;
        max-width: 500px;
      }
    }
    .loginout {
      margin-right: 30px;
      font-size: 18px;
      color: #333;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .study-new-edc-item {
    width: calc(80% + 20px);
    padding: 10px 0px 0 0;
    margin: 0 auto;
    justify-content: space-between;
    font-weight: 700;
  }
  .study-body {
    width: 100%;
    height: 75vh;
    min-height: 600px;
    overflow: auto;
    .study-bodyBox {
      // width: 80%;
      padding: 20px 20px;
      margin: 0 auto;
      border: 1px solid #e9ecef;
      margin-top: 30px;
      color: #32325d;
      display: flex;
      border-radius: 5px;
      background: #fff;
      .study-name {
        width: 50%;
      }
      .study-bodyBox-bottom {
        flex: 1;
        justify-content: flex-end;
      }
    }
  }
}
</style>
