<template>
  <div>
    <trial-table
      ref="StudyRoleSettingTable"
      title="角色列表"
      :request="getList"
      :columns="columns"
      :search="searchConfig"
      :pagination="paginationConfig"
      @selectionChange="handleSelectionChange"
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <!-- icon="el-icon-plus" -->
        <el-button type="primary" @click="editItem(null)">新增</el-button>
        <!-- icon="el-icon-refresh"
              <el-button type="primary" @click="refresh">
                刷新
              </el-button>
          -->
      </template>
      <template #statusStr="scope">
        <el-text v-if="scope.row.status === 1" type="primary">激活</el-text>
        <el-text v-else type="danger">失活</el-text>
      </template>
      <template #operate="scope">
        <el-button v-if="scope.row.roleType === 2" plain size="small" type="primary" @click="editItem(scope.row)">
          编辑
        </el-button>
        <el-button
          v-if="scope.row.status === 1"
          size="small"
          type="danger"
          @click="handleInactivationActivation(scope.row)"
        >失活</el-button>
        <el-button
          v-else
          size="small"
          type="primary"
          @click="handleInactivationActivation(scope.row)"
        >激活</el-button>
      </template>
    </trial-table>
    <!-- 编辑表单 -->
    <EditRoleForm v-if="ROLE_FORM_DATAS.dialogVisible" :request="refresh" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, provide } from 'vue'
import EditRoleForm from '@/views/login/EditRole.vue'
import { RoleFormInterface } from '@/type/views/auth'
import { getRolePagedList, setRoleStatus } from '@/api/test'
import { useRoute } from 'vue-router'
// import { useStore } from 'vuex'

export default defineComponent({
  name: 'StudyRoleSetting', // 项目角色设置
  components: {
    EditRoleForm,
  },
  setup() {
    // const store = useStore()
    // const { studyId } = store.state.studyItem
    const route = useRoute()
    const ROLE_FORM_DATAS: RoleFormInterface = reactive({
      userFormData: {},
      dialogVisible: false,
    })
    const state = reactive({
      StudyRoleSettingTable: null,
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' },
        { label: '角色分类', prop: 'roleGroup', minWidth: 180 },
        { label: '角色类型', prop: 'roleTypeName', minWidth: 180 },
        { label: '角色名称', prop: 'roleName', minWidth: 180 },
        { label: '角色描述', prop: 'description', minWidth: 180 },
        { label: '状态', tdSlot: 'statusStr', minWidth: 120 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '120px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            label: '角色名称或描述',
            name: 'roleName', // description
            defaultValue: '',
          },
          {
            type: 'select',
            label: '角色类型',
            name: 'roleType',
            defaultValue: '',
            options: [
              {
                value: '',
                name: '全部角色',
              },
              {
                name: '课题默认角色',
                value: 1,
              },
              {
                name: '课题自定义角色',
                value: 2,
              },
            ],
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 10, // 每页条数
        pageSizes: [10, 20, 50],
        style: { textAlign: 'left' },
      },
      selectedItems: [],
      // 选择
      handleSelectionChange(arr) {
        state.selectedItems = arr
      },
      async getList(params) {
        // params是从组件接收的，包含分页和搜索字段。
        try {
          params.studyId = route.query.studyId
          const data: any = await getRolePagedList(params)
          return {
            data: data.items,
            total: data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
          // 当捕获到异常时，返回默认值，确保所有代码路径都有返回值
          return {
            data: [],
            total: 0,
          }
        }
      },
      // 刷新
      refresh: () => {
        state.StudyRoleSettingTable.refresh()
      },
      // 新增-编辑
      editItem: (row) => {
        ROLE_FORM_DATAS.userFormData = { ...row }
        ROLE_FORM_DATAS.dialogVisible = true
        ROLE_FORM_DATAS.title = row ? '编辑' : '新增'
      },
      // 激活/失活
      handleInactivationActivation: (row) => {
        ElMessageBox.confirm(
          `您将${row.status === 1 ? '失' : '激'}活${
            row.roleName
          }角色, 是否继续?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: row.status === 1 ? 'warning' : '',
          }
        )
          .then(() => {
            setRoleStatus({
              ...row,
              status: row.status === 1 ? 0 : 1,
            }).then(() => {
              state.refresh()
              ElMessage.success(
                `${row.roleName}角色${row.status === 1 ? '失' : '激'}活设置成功`
              )
            })
          })
          .catch(() => {})
      },
    })

    // 传值
    provide('ROLE_FORM_DATAS', ROLE_FORM_DATAS)

    return {
      ...toRefs(state),
      ROLE_FORM_DATAS,
    }
  },
})
</script>
