<template>
  <div class="ForgetPassword">
    <div class="topBar">
      <div class="container">
        <h3>重置密码</h3>
        <div class="blank"></div>
        <div class="btn">
          <el-button type="default" @click="returnLogin">返回</el-button>
        </div>
      </div>
      <el-divider />
    </div>

    <el-form
      ref="imgVerifyRef"
      class="form"
      v-if="step === 1"
      :model="imgVerifyModel"
      :rules="imgVerifyRules"
      label-position="top"
      hide-required-asterisk="true"
    >
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model.trim="imgVerifyModel.mobile"
          class="text"
          maxlength="11"
          clearable
          placeholder="请输入"
          @keyup.enter="submitImgVerify"
        ></el-input>
      </el-form-item>
      <el-form-item label="验证码" prop="verifyCode">
        <el-input
          v-model.trim="imgVerifyModel.verifyCode"
          class="text"
          clearable
          placeholder="请输入"
          style="width: 350px"
          @keyup.enter="submitImgVerify"
        ></el-input>
        <div class="imgCode" @click="refreshImgVerifyCode">
          <img v-show="imgCodeSrc != ''" :src="imgCodeSrc" />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="loading"
          type="primary"
          class="btn"
          size="large"
          @click="submitImgVerify"
        >
          {{ verifyBtnText }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-form
      ref="mobileVerifyRef"
      class="form"
      v-if="step === 2"
      :model="mobileVerifyModel"
      :rules="mobileVerifyRules"
      label-position="top"
      hide-required-asterisk="true"
    >
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model.trim="mobileVerifyModel.mobile" class="text" disabled>
        </el-input>
      </el-form-item>
      <el-form-item label="短信验证码" prop="verifyCode">
        <div class="verifycode-modules">
          <el-input
            v-model.trim="mobileVerifyModel.verifyCode"
            class="text"
            maxlength="6"
            clearable
            placeholder="请输入"
            style="width: 320px"
            @keyup.enter="submitMobileVerify"
          >
          </el-input>
          <el-button type="primary" class="btn" @click="getVerificationCode">
            {{ VER_TIME > 59 ? '获取验证码' : VER_TIME + 's' }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="loading"
          type="primary"
          class="btn"
          size="large"
          @click="submitMobileVerify"
        >
          {{ verifyBtnText }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-form
      v-if="step === 3"
      ref="ForgetPasswordRef"
      class="form"
      :model="model"
      :rules="rules"
      label-position="top"
      hide-required-asterisk="true"
    >
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model.trim="model.mobile"
          class="text"
          maxlength="11"
          clearable
          placeholder="手机号"
          disabled
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model.trim="model.username"
          class="text"
          disabled
        ></el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="password">
        <el-input
          v-model.trim="model.password"
          class="text"
          maxlength="50"
          show-password
          clearable
          placeholder="请输入"
          @keyup.enter="submit"
        />
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPassword">
        <el-input
          v-model.trim="model.confirmPassword"
          class="text"
          maxlength="50"
          show-password
          clearable
          placeholder="请输入"
          @keyup.enter="submit"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          :loading="loading"
          class="btn"
          type="primary"
          size="large"
          @click="submit"
        >
          {{ btnText }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      v-model="resetRes"
      title="提示"
      width="500"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :align-center="true"
    >
      <div class="tips">
        <span> 您的密码已重置! </span>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="returnLogin"> 去登录 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed, onMounted } from 'vue'
import {
  getImgVerifyCode,
  imgVerifyCodeValidate,
  mobileVerifyCodeValidate,
  resetPassword,
} from '@/api/forgetPassword'
import { postSentVerifyCode } from '@/api/login'
// import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { deepClone, encode } from '@/utils'

export default defineComponent({
  name: 'ForgetPassword',
  setup() {
    onMounted(() => {
      state.refreshImgVerifyCode()
    })
    // const store = useStore()
    const router = useRouter()
    const VALIDATEPASS = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入'))
      } else if (value !== state.model.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    const state = reactive({
      step: 1, //1 图形验证码 2 短信验证码 3 密码重置
      imgVerifyRef: null,
      verifyBtnText: computed(() => (state.loading ? '验证中...' : '下一步')),
      imgCodeSrc: '',
      imgVerifyModel: {
        mobile: '',
        verifyCode: '',
        uuid: '',
      },
      imgVerifyRules: {
        mobile: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur',
          },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '手机号格式错误',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur',
          },
        ],
      },
      refreshImgVerifyCode: () => {
        getImgVerifyCode().then((res) => {
          state.imgVerifyModel.uuid = res.uuid
          state.imgCodeSrc = 'data:image/png;base64,' + res.img
        })
      },
      submitImgVerify: () => {
        if (state.loading) {
          return
        }
        state.imgVerifyRef.validate((valid) => {
          if (valid) {
            state.loading = true
            const model = deepClone(state.imgVerifyModel)
            imgVerifyCodeValidate(model)
              .then((res) => {
                state.loading = false
                state.step = 2
                state.mobileVerifyModel.mobile = model.mobile
              })
              .catch((e) => {
                state.loading = false
                state.refreshImgVerifyCode()
              })
          }
        })
      },
      mobileVerifyRef: null,
      mobileVerifyModel: {
        mobile: '',
        verifyCode: '',
      },
      mobileVerifyRules: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur',
          },
          {
            min: 6,
            max: 6,
            message: '验证码长度在6位',
            trigger: 'blur',
          },
        ],
      },
      VER_TIME: 60,
      loading: false,
      // 获取验证码
      getVerificationCode: () => {
        const MYREG = /^1[3-9][0-9]\d{8}$/
        const MOBILE = state.mobileVerifyModel.mobile
        if (state.VER_TIME / 1 < 60) {
          return
        } else if (!MYREG.test(MOBILE)) {
          ElMessage({
            message: '请输入正确的手机号',
            type: 'error',
          })
          return
        } else {
          state.VER_TIME--
          const TIMEOUT = setInterval(() => {
            state.VER_TIME--
            if (state.VER_TIME / 1 === 0) {
              state.VER_TIME = 60
              clearInterval(TIMEOUT)
            }
          }, 1000)
          postSentVerifyCode({ mobile: state.mobileVerifyModel.mobile })
        }
      },
      submitMobileVerify: () => {
        if (state.loading) {
          return
        }
        state.mobileVerifyRef.validate((valid) => {
          if (valid) {
            state.loading = true
            const model = deepClone(state.mobileVerifyModel)
            mobileVerifyCodeValidate(model)
              .then((res) => {
                state.loading = false
                state.step = 3
                state.model.mobile = res.mobile
                state.model.username = res.userName
                state.model.verifyCode = model.verifyCode
              })
              .catch((e) => {
                state.loading = false
              })
          }
        })
      },
      btnText: computed(() => (state.loading ? '修改中...' : '确认重置')),
      ForgetPasswordRef: null,
      model: {
        mobile: '',
        username: '',
        password: '',
        confirmPassword: '',
        verifyCode: '',
      },
      rules: {
        mobile: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^1[3-9][0-9]\d{8}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        password: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            min: 8,
            max: 15,
            message: '长度在 8 到 15 个字符',
            trigger: 'blur',
          },
          {
            pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9]).{8,15}$/,
            message: '密码至少包含一个大写字母，一个小写字母和一个数字',
            trigger: 'blur',
          },
        ],
        confirmPassword: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            min: 8,
            max: 15,
            message: '长度在 8 到 15 个字符',
            trigger: 'blur',
          },
          { required: true, validator: VALIDATEPASS, trigger: 'blur' },
        ],
      },
      submit: () => {
        if (state.loading) {
          return
        }
        state.ForgetPasswordRef.validate((valid) => {
          if (valid) {
            state.loading = true
            const model = deepClone(state.model)
            model.password = encode(model.password)
            model.confirmPassword = encode(model.confirmPassword)
            resetPassword(model)
              .then((res) => {
                state.loading = false
                state.resetRes = true
              })
              .catch((e) => {
                state.loading = false
              })
          }
        })
      },
      resetRes: false,
      returnLogin: () => {
        router.push('/login')
      },
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.ForgetPassword {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #2d3a4b;
  .form {
    width: 520px;
    max-width: 100%;
    padding: 0 24px;
    box-sizing: border-box;
    margin: 160px auto 0;
    :deep(.el-form-item__label) {
      font-size: 18px;
      color: white;
    }
    :deep(.el-form-item) {
      margin-bottom: 50px;
    }
    .title {
      color: #fff;
      text-align: center;
      font-size: 24px;
      margin: 0 0 24px;
    }
    .text {
      font-size: 16px;
      :deep(.el-input__inner) {
        height: 48px;
        line-height: 48px;
      }
    }
    .btn {
      width: 100%;
    }
    .verifycode-modules {
      display: flex;
      align-items: center;
      .el-button,
      .el-button--primary,
      .btn {
        flex: 1;
        width: 100px;
        height: 40px;
        margin-left: 40px;
        margin-right: 4px;
      }
    }
  }
}

.topBar {
  margin: 100px 25% 5px 25%;
  .container {
    display: flex;
    h3 {
      color: white;
    }
    .blank {
      flex: 1;
    }
    .btn {
      display: flex;
      align-items: center;
    }
  }
  :deep(.el-divider--horizontal) {
    margin: 8px 0;
  }
}

.form .imgCode {
  cursor: pointer;
  display: flex;
  flex: 1;
  justify-content: flex-end;
  img {
    margin-right: 4px;
  }
}

.tips {
  display: flex;
  height: 200px;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
}
:deep(.el-dialog__title) {
  font-weight: bold;
}
</style>
