<template>
  <!-- close-on-click-modal -->
  <trial-dialog
    v-model="ROLE_FORM_DATAS.dialogVisible"
    :title="ROLE_FORM_DATAS.title"
    :my-dialog-body-style="{
      maxHeight: 'calc(100vh - 320px)',
      overflow: 'auto'
    }"
    show-close
  >
    <template #DialogBody>
      <el-form ref="roleFormRef" :model="roleForm" :rules="rules">
        <!-- 角色分类 -->
        <el-form-item
          v-if="!roleForm.studyId"
          label="角色分类"
          :label-width="formLabelWidth"
          prop="roleType"
        >
          <el-select
            v-model="roleForm.roleType"
            placeholder="请选择"
            :disabled="!!ROLE_FORM_DATAS.userFormData?.id"
            @change="handleRoleTypeChange"
          >
            <el-option
              v-for="item in roleTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="roleForm.roleType !== 0"
          label="角色类型"
          :label-width="formLabelWidth"
          prop="roleGroup"
        >
          <el-select
            v-model="roleForm.roleGroup"
            placeholder="请选择"
            :disabled="!!ROLE_FORM_DATAS.userFormData?.id"
          >
            <el-option
              v-for="item in roleGroupOption"
              :key="item.value"
              :label="item.value"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="角色名称"
          :label-width="formLabelWidth"
          prop="roleName"
        >
          <el-input
            v-model.trim="roleForm.roleName"
            placeholder="请输入"
            maxlength="999"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item
          label="角色说明"
          :label-width="formLabelWidth"
          prop="description"
        >
          <el-input
            v-model.trim="roleForm.description"
            type="textarea"
            autocomplete="off"
            placeholder="请输入"
            maxlength="3999"
          />
        </el-form-item>
        <!-- check-strictly 断开和父级关联-->
        <el-form-item
          label="权限选择"
          :label-width="formLabelWidth"
          prop="permissionIds"
        >
          <!-- :default-expand-all="true" -->
          <el-tree
            ref="roleTree"
            v-loading="roleOptionsLoadingFlag"
            :props="propsDatas"
            :data="roleOptions"
            :default-expanded-keys="roleForm.permissionIds"
            show-checkbox
            node-key="permissionId"
            @check-change="handleCheckChange"
          />
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <span class="flex justify-center">
        <el-button
          @click="ROLE_FORM_DATAS.dialogVisible = false"
        >取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  inject,
  onMounted,
  ref,
  nextTick,
} from 'vue'
// import { useStore } from 'vuex'
import {
  getRoleDetail,
  getRolePermissionTreeList,
  saveRoleAndRelationPermission,
  getPermissionTreeList,
  getRoleGroupList
} from '@/api/test'
import { useRoute } from 'vue-router'

export default defineComponent({
  name: 'EditRole',
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {}
    }
  },
  setup(props) {
    const route = useRoute()
    // const store = useStore()
    const ROLE_FORM_DATAS: any = inject('ROLE_FORM_DATAS')
    const roleTree = ref<HTMLElement | null>(null)

    const state = reactive({
      roleGroupOption: [],
      // 0 = 系统角色, 1 = 课题默认角色, 2 = 课题自定义角色
      roleTypeOption: [
        {
          label: '系统角色',
          value: 0
        },
        {
          label: '课题默认角色',
          value: 1
        },
        // {
        //   label: '课题自定义角色',
        //   value: 2
        // },
      ],
      //
      roleOptionsLoadingFlag: false,
      // el-tree 默认展示框
      roleOptions: [
        // {
        //   id: 1,
        //   title: '权限管理',
        //   children: [
        //     {
        //       id: 10,
        //       title: '功能菜单',
        //       children: [
        //         {
        //           id: 19,
        //           title: '产品菜单',
        //         },
        //       ],
        //     },
        //   ],
        // },
      ],
      propsDatas: {
        // 勾选展示
        label: 'permissionName',
        children: 'children',
      },
      roleForm: {
        roleName: '',
        roleType: route.query?.studyId ? 2 : 0,
        studyId: route.query?.studyId || ''
      },
      roleFormRef: null,
      formLabelWidth: '86px',
      rules: {
        roleName: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        roleGroup: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
      },
      submitLoading: false,
      // 保存
      handleSubmit: () => {
        if (state.roleOptionsLoadingFlag) {
          return
        }
        state.roleFormRef.validate((valid) => {
          if (valid) {
            // console.log(state.roleForm, roleTree.value.getCheckedKeys())
            // return
            if (state.roleForm?.roleType <= 0) {
              state.roleForm.roleGroup = ''
            }
            state.submitLoading = true
            saveRoleAndRelationPermission(
              {
                ...state.roleForm,
                permissionIds: roleTree.value.getCheckedKeys()
              }
            ).then(() => {
              ElMessage.success('保存成功')
              props.request()
              state.submitLoading = false
              ROLE_FORM_DATAS.dialogVisible = false
            }).catch(() => {
              state.submitLoading = false
            })
          }
        })
      },
      // 选中时的事件
      handleCheckChange: (data, checked, indeterminate) => {
        // console.log(roleTree.value.getCheckedKeys()) // 获取到选中的所有key(不带父级的)
        // console.log(data) // console.log(checked)console.log(indeterminate)
      },
      // 树形结构数据处理
      treeListSet: (arr) => {
        return arr.map((e) => {
          const item = {
            id: e.id || '',
            permissionName: e.permissionName || '',
            permissionId: e.permissionId || '',
            pPermissionId: e.pPermissionId || '',
            children: []
          }
          if (e?.childPermissions?.length) {
            item.children = state.treeListSet(e.childPermissions)
          }
          return item
        })
      },
      handleRoleTypeChange: () => {
        state.roleOptionsLoadingFlag = true
        getPermissionTreeList({
          // isSysPermission: ROLE_FORM_DATAS.userFormData.roleType === 0 ? 1 : 0
          isSysPermission: state.roleForm.roleType === 0 ? 1 : 0
        }).then((rest: any) => {
          state.roleOptions = state.treeListSet(rest)
          state.roleOptionsLoadingFlag = false
        }).catch(() => {
          state.roleOptionsLoadingFlag = false
        })
      },
    })

    onMounted(() => {
      getRoleGroupList().then((res) => {
        state.roleGroupOption = res
      })
      state.roleOptionsLoadingFlag = true
      if (ROLE_FORM_DATAS.userFormData.id) {
        getRoleDetail(ROLE_FORM_DATAS.userFormData.id).then((res) => {
          state.roleForm = res
          if (ROLE_FORM_DATAS.userFormData.roleId) {
            getRolePermissionTreeList(
              ROLE_FORM_DATAS.userFormData.roleId,
              {
                isSysPermission: ROLE_FORM_DATAS.userFormData.roleType === 0 ? 1 : 0
              }
            ).then((rest: any) => {
              state.roleOptions = state.treeListSet(rest)
              // 设置已经勾选的->已有权限
              nextTick(() => {
                state.roleOptionsLoadingFlag = false
                // console.log(roleTree.value, 'roleTree.value', state.roleForm.permissionIds)
                roleTree.value.setCheckedKeys(state.roleForm.permissionIds)
              })
            })
          }
        }).catch(() => {
          state.roleOptionsLoadingFlag = false
        })
      } else {
        getPermissionTreeList({
          isSysPermission: state.roleForm.roleType === 0 ? 1 : 0
          // isSysPermission: state.roleForm.roleType === 0 ? 1 : 0
        }).then((rest: any) => {
          state.roleOptions = state.treeListSet(rest)
          state.roleOptionsLoadingFlag = false
        }).catch(() => {
          state.roleOptionsLoadingFlag = false
        })
      }
      // if (store.state.menu.menus && ROLE_FORM_DATAS.userFormData.id) {
      // 设置已经勾选的->已有权限
      // nextTick(() => {
      //   checkes(
      //     store.state.menu.menus.slice(1, store.state.menu.menus.length)
      //   )
      //   roleTree.value.setCheckedKeys(CheckedKeyArr)
      // })
      // }
    })

    return {
      ...toRefs(state),
      ROLE_FORM_DATAS,
      roleTree
    }
  }
})
</script>

<style lang="less" scoped>
:deep(.el-tree__empty-text) {
  width: 300px;
  transform: translate(-20%, -50%);
}
</style>
