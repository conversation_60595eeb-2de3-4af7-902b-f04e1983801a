<template>
  <div>
    <trial-table
      ref="studySettingTableRef"
      v-loading="tableLoading"
      :request="getstudySettingList"
      :columns="studySettingColumns"
      :search="studySettingSearchConfig"
      :pagination="studySettingPaginationConfig"
    >
      <!-- 工具栏 -->
      <template #toolbar>
        <el-button
          v-permission="['sys.study.add']"
          type="primary"
          size="default"
          @click="handleAddStudy"
        >
          新增
        </el-button>
      </template>
      <template #operate="scope">
        <el-button
          v-permission="['sys.study.config']"
          size="small"
          type="primary"
          plain
          :loading="checkedStudyLoadingFlag"
          @click="checkedStudy(scope.row)"
        >
          配置
        </el-button>
        <el-button
          v-permission="['sys.study.role']"
          size="small"
          type="primary"
          @click="studySettingEditItemForm(scope.row)"
        >
          角色
        </el-button>
      </template>
    </trial-table>
    <el-dialog v-model="newItemDialogVisible" title="新建" width="40%">
      <el-form
        ref="newItemFormRef"
        :model="newItemForm"
        :rules="newItemRules"
        label-width="100px"
      >
        <el-form-item label="EDC项目ID" prop="edcStudyId">
          <el-input
            v-model.trim="newItemForm.edcStudyId"
            maxlength="999"
            placeholder="请输入EDC项目ID"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="newItemDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="saveNewItemloading"
            @click="handleNewItem"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, reactive, toRefs } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { getUserStudyBreifInfo, postNewStudy } from '@/api/login'
// import { deepClone } from '@/utils'

export default defineComponent({
  name: 'StudySettingTable', // 项目设置
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()
    const state = reactive({
      pageType: 'studySetting', // 项目设置studySetting ,角色设置 roleSetting
      studySettingTableRef: null,
      studySettingColumns: [
        // { label: '客户名称', prop: 'name', minWidth: 180 },
        // { label: '研究编号', prop: 'name', minWidth: 180 },
        { label: '项目名称', prop: 'studyName', minWidth: 180 },
        // { label: '创建人', prop: 'name', minWidth: 180 },
        // { label: '创建时间', prop: 'name', minWidth: 180 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate',
        },
      ],
      // 搜索配置
      studySettingSearchConfig: {
        labelWidth: '100px', // 必须带上单位
        inputWidth: '300px', // 必须带上单位
        fields: [
          {
            type: 'text',
            // label: '项目编号、名称或者客户名称',
            label: '项目名称',
            name: 'studyName',
            defaultValue: '',
          },
        ],
      },
      // 分页配置
      studySettingPaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        pageSize: 10, // 每页条数
        pageSizes: [10, 20, 30, 50, 100],
        style: { textAlign: 'left' },
      },
      // 请求函数
      getstudySettingList: async (params) => {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const res: any = await getUserStudyBreifInfo(
            store.getters.account.userinfo.dctUserId,
            {
              pageSize: params.pageSize,
              pageIndex: params.pageIndex,
              studyName: params.studyName,
            }
          )
          return {
            data: res.items,
            total: res.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      studySettingRefresh: () => {
        state.studySettingTableRef.refresh()
      },
      newItemDialogVisible: false,
      saveNewItemloading: false,
      newItemFormRef: null,
      newItemForm: {
        edcStudyId: '',
      },
      newItemRules: {
        edcStudyId: [
          { required: true, message: '请输入EDC项目ID', trigger: 'blur' },
        ],
      },
      handleNewItem: () => {
        state.newItemFormRef.validate(async (valid) => {
          if (valid) {
            state.saveNewItemloading = true
            try {
              await postNewStudy({
                edcStudyId: state.newItemForm.edcStudyId,
              })
              ElMessage.success('保存成功')
              state.studySettingRefresh()
              state.saveNewItemloading = false
              state.newItemDialogVisible = false
            } catch {
              state.saveNewItemloading = false
            }
          }
        })
      },
      // 新增-项目
      handleAddStudy: () => {
        state.newItemDialogVisible = true
      },
      // 编辑角色
      studySettingEditItemForm: (row) => {
        if (row) {
          // console.log(row)
          // 跳转 项目角色设置页 row.studyId
          router.push({
            // path: '/studyRoleSetting',
            path: '/studyRoleSetting',
            query: { studyId: row.studyId },
          })
        }
      },
      tableLoading: false,
      checkedStudyLoadingFlag: false,
      // 进入某个项目——配置
      checkedStudy: (item) => {
        state.tableLoading = true
        sessionStorage.setItem('studyInfo', JSON.stringify(item))
        store.dispatch('tags/delAllTags')
        state.setStudyItemNameValue(item)
        store.dispatch(
          'menu/generateMenus',
          store.state.account.userinfo && store.state.account.userinfo.role,
          item?.studyId
        )
        state.setTimeoutGoHome()
        // state.checkedStudyLoadingFlag = false
      },
      setTimeoutGoHome: () => {
        setTimeout(() => {
          if (store.state.menu.menus.some((menu) => menu?.url === '/study')) {
            state.setTimeoutGoHome()
          } else {
            router.push('/home')
            state.tableLoading = false
          }
        }, 500)
      },
      setStudyItemNameValue: (item) => {
        if (item?.sites?.length) {
          item.sites.forEach((el) => {
            el.name = el.siteName
            el.value = el.siteId
          })
        }
        store.commit('setStudyItem', item)
      },
    })
    onBeforeMount(() => {
      if (store.state.studyItem) {
        sessionStorage.setItem('studyInfo', '{}')
        store.commit('setStudyItem', '')
        // 清除标签栏
        // store.dispatch('tags/delAllTags')
        store.dispatch('tags/delOtherTags', {
          path: '/studySetting',
          fullPath: '/studySetting',
          name: 'StudySetting',
          meta: {
            title: '项目设置',
            roles: ['admin', 'visitor'],
          },
          params: {},
          query: {},
          title: '项目设置',
        })
        store.dispatch(
          'menu/generateMenus',
          store.state.account.userinfo && store.state.account.userinfo.role
        )
      }
      // console.log(window.history, route.query.from, 'store.state.menu.menus')
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style scoped lang="less">
:deep(.page-box) {
  .head {
    justify-content: flex-start;
  }
}
</style>
