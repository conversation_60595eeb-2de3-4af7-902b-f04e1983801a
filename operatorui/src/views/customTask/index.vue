<template>
  <div class="customTask">
    <div v-if="!RESEARCHCENTER_INFOS?.contentVisible">
      <trial-table
        ref="customTaskRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :showbtnfalg="true"
        :hide-center="true"
      >
        <!-- :requestExport="getResearchCenterExport" -->
        <template #operate="scope">
          <span class="editBtnBlue mr-3" @click="editCustomTaskInfoItem(scope.row, 0)">
            编辑
          </span>
          <!-- <span class="editBtnBlue" @click="ereissue(1,scope.row)">
            刷新任务
          </span> -->
        </template>
        <template #hideCenter>
          <div class="researchCenter-hideCenter">
            <div class="my-5 flex justify-between items-center">
              <span>自定义任务列表</span>
              <div>
                <el-button
                  type="primary"
                  @click="editCustomTaskInfoItem(null, 1)"
                >新建</el-button>
              </div>
            </div>
          </div>
        </template>
      </trial-table>
    </div>
    <CustomTaskDetail v-if="RESEARCHCENTER_INFOS?.contentVisible" />
    <trial-dialog v-model="showVisitClFlag" title="提示" :my-dialog-body-style="myDialogBodyStyle">
      <template #DialogBody>
        刷新任务不影响已发生的历史数据，是否确认刷新？
      </template>
      <template #footer>
        <div class="mt-10 text-center">
          <el-button size="large" @click="showVisitClFlag = false">取 消</el-button>
          <el-button
            size="large"
            type="primary"
            @click="ereissue(2)"
          >保 存</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, provide, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import CustomTaskDetail from '@/views/customTask/CustomTaskDetail.vue'
import { getCustomTasks } from '@/api/customTask'

export default defineComponent({
  name: 'CustomTask', // 自定义任务-受试者
  components: {
    CustomTaskDetail,
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      stateVal: null,
      resetMethod: null,
    })
    const state = reactive({
      customTaskRef: null,
      columns: [
        { label: '任务名称', prop: 'taskName', width: 200 },
        { label: '任务对象', prop: 'userTypeStr', width: 130 },
        { label: '适用中心', prop: 'siteNum', width: 130 },
        { label: '任务窗口期', prop: 'taskWindowPeriod', width: 130, },
        { label: '子任务', prop: 'customSubtaskStr', width: 200, },
        { label: '状态', prop: 'enableState', width: 100 },
        { label: '更新时间', prop: 'lastUpdateTime', width: 180, },
        { label: '更新人', prop: 'operatorName', width: 100 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      showVisitClFlag: false,
      myDialogBodyStyle: {
        width: '30%',
        minHeight: '0',
      },
      columnsSome: {}, // 拷贝点击得到的数据

      // 编辑/新建
      editCustomTaskInfoItem: (row, idx) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.stateVal = idx
        RESEARCHCENTER_INFOS.resetMethod = state.customTaskRef
      },

      // 重新下发
      ereissue: (idx, row) => {
        if (idx === 1) {
          state.columnsSome = row
          state.showVisitClFlag = true
        } else if (idx === 2) {
          state.showVisitClFlag = false
          state.customTaskRef.handleReset()
        }
      },

      // 表格数据
      async getResearchCenterList(params) {
        const rest = await getCustomTasks(store.state.studyItem.studyId)
        return {
          data: rest
        }
      },

    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
</style>
