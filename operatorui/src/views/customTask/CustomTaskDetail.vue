<template>
  <div class="customTask-detail">
    <div
      class="flex justify-between items-center rounded-[5px] bg-white py-[20px] px-[20px] box-border"
    >
      <span>{{ RESEARCHCENTER_INFOS.stateVal ? '新建' : '编辑' }}</span>
      <div>
        <el-button plain @click="routerGo">返回</el-button>
        <el-button type="primary" :loading="loading" @click="saveData"
          >保存</el-button
        >
      </div>
    </div>
    <div class="box-border mt-5">
      <el-form
        ref="customTaskDetailForm"
        label-position="top"
        label-width="80px"
        :model="dataForm"
        :rules="rules"
      >
        <div class="bg-white p-[15px] box-border rounded-[5px] customTask-box">
          <h3 class="mt-0">任务信息</h3>
          <!-- 标题/权重值输入框 -->
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="任务名称"
                style="width: 100%"
                prop="taskName"
              >
                <el-input
                  v-model.trim="dataForm.taskName"
                  placeholder="请输入"
                  autocomplete="off"
                  maxlength="666"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="任务对象" style="width: 100%" prop="userType">
                <el-select v-model="dataForm.userType" placeholder="请选择">
                  <el-option label="受试者端" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="6">
              <el-form-item label="任务窗口期" style="width: 100%" prop="taskWindowPeriod">
                <div class="flex items-center w-full">
                  <el-input
                    v-model.trim="dataForm.taskWindowPeriod"
                    placeholder="请输入"
                    autocomplete="off"
                    maxlength="666"
                    style="width: 100%;"
                  />
                  <span class="ml-3">天</span>
                </div>
              </el-form-item>
            </el-col> -->
            <el-col :span="4">
              <div class="mb-5" style="width: 100%">
                <div class="ft-14 mr-5 mb-1.5">状态</div>
                <el-switch
                  v-model="dataForm.isEnable"
                  class="mt-1.5"
                  :active-text="dataForm.isEnable ? '启用' : '禁用'"
                />
              </div>
            </el-col>
          </el-row>
          <!-- 富文本 -->
          <div>
            <div style="font-size: 14px; margin-bottom: 5px">任务说明</div>
            <!-- <div>
              <div ref="customTaskDetailService" />
            </div> -->
            <div>
              <trial-wang-editor
                ref="customTaskDetailService"
                editorHeight="300px"
                :toolbar-config="toolbarConfig"
              />
            </div>
          </div>
          <div class="my-5">
            <el-button type="primary" class="mr-4" @click="applyFlagShow = true"
              >适用中心</el-button
            >
            <span
              >已选择 {{ shuttleValue.length }}/{{
                shuttleData.length
              }}
              家中心</span
            >
          </div>
          <!-- 任务子元素 -->
          <div class="my-5">
            <div style="font-size: 14px; margin-bottom: 5px" class="flex">
              <div class="mr-3"><span style="color: red">*</span>子任务</div>
              <el-icon
                color="#1296db"
                :size="20"
                @click="addCustomSubtasksClick"
                ><CirclePlus
              /></el-icon>
            </div>
          </div>
          <div
            v-for="(ite, indx) in dataForm.customSubtasks"
            :key="ite.id"
            class="subtask-box flex mb-5 relative"
          >
            <div class="subtask-left flex items-center justify-center py-5">
              子任务{{ indx + 1 }}
            </div>
            <div class="subtask-right flex p-5">
              <el-form-item
                style="width: 50%"
                label="子任务类型"
                :prop="`customSubtasks[${indx}].subtaskType`"
                :rules="{ required: true, message: '请选择', trigger: 'blur' }"
              >
                <el-select
                  v-model="ite.subtaskType"
                  placeholder="请选择"
                  @change="selectChange(ite)"
                >
                  <el-option label="下单寄送" :value="1" />
                  <el-option label="填写问卷" :value="2" />
                </el-select>
              </el-form-item>
              <div class="mx-5" />
              <el-form-item
                v-if="ite.subtaskType === 1"
                style="width: 50%"
                label="托寄物品类型"
                :prop="`customSubtasks[${indx}].specificItem`"
                :rules="{
                  required: true,
                  message: '请选择',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="ite.specificItem"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  placeholder="请选择"
                >
                  <el-option
                    v-for="it in options"
                    :key="it.id"
                    :label="it.goodsTypeName"
                    :value="it.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="ite.subtaskType === 2"
                style="width: 50%"
                label="关联问卷"
                :prop="`customSubtasks[${indx}].specificItems`"
                :rules="{
                  required: true,
                  message: '请选择',
                  trigger: 'change',
                }"
              >
                <el-select v-model="ite.specificItems" placeholder="请选择">
                  <el-option
                    v-for="it in questsDropoptions"
                    :key="it.questTemplateId"
                    :label="it.questName"
                    :value="it.questTemplateId"
                  />
                </el-select>
              </el-form-item>
              <div class="mx-5" />
              <el-form-item
                v-if="ite.subtaskType === 2"
                style="width: 100%"
                label="首次填写后"
                :prop="`customSubtasks[${indx}].changeDeadline`"
                :rules="{
                  required: true,
                  message: '请输入',
                  trigger: 'blur',
                }"
              >
              
              <div class="flex min-w-[270px]" >
                <div class="flex myleft" style="width: 75%">
                  <el-input
                    v-model="ite.changeDeadline"
                    oninput="value=value.replace(/[^\d]+/g,'')"
                    @blur="ite.changeDeadline = $event.target.value"
                    placeholder="请输入"
                    class="myleft"
                    style="width: 48%"
                  />
                  &nbsp;
                  <el-select
                    v-model="ite.timeUnit"
                    placeholder="请选择"
                    class="myleft"
                    style="width: 48%"
                  >
                    <el-option label="天" :value="1" />
                    <el-option label="小时" :value="2" />
                    <el-option label="分钟" :value="3" />
                  </el-select>
                </div>
                <div class="text myleft">&nbsp;内可修改</div> 
              </div>
              </el-form-item>
            </div>
            <div
              v-if="dataForm.customSubtasks.length !== 1"
              class="absolute top-0 right-0"
            >
              <el-button type="danger" @click="deleteCustomSubtasks(indx)"
                >删除</el-button
              >
            </div>
          </div>
        </div>
        <div class="bg-white p-[15px] box-border rounded-[5px] mt-[30px]">
          <h3 class="mt-0">任务窗口期</h3>
          <el-row class="flex items-center">
            <div class="mb-[18px]">下发第</div>
            <el-col :span="2" class="mx-[10px]">
              <el-form-item
                label=""
                style="width: 100%"
                prop="taskWindowStartDay"
              >
                <el-input
                  v-model="dataForm.taskWindowStartDay"
                  placeholder="请输入"
                  autocomplete="off"
                  maxlength="16"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <div class="mb-[18px]">天</div>
            <el-col :span="2" class="mx-[10px]">
              <el-form-item label="" style="width: 100%" prop="windowStartTime">
                <el-time-picker
                  v-model="dataForm.windowStartTime"
                  placeholder="请选择时间"
                  value-format="HH:mm"
                  clearable
                  format="HH:mm"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <div class="mb-[18px] mx-[20px]">至</div>
            <div class="mb-[18px]">第</div>
            <el-col :span="2" class="mx-[10px]">
              <el-form-item
                label=""
                style="width: 100%"
                prop="taskWindowPeriod"
              >
                <el-input
                  v-model.trim="dataForm.taskWindowPeriod"
                  oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                  @blur="dataForm.taskWindowPeriod = $event.target.value"
                  placeholder="请输入"
                  autocomplete="off"
                  maxlength="666"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <div class="mb-[18px]">天</div>
            <el-col :span="2" class="mx-[10px]">
              <el-form-item label="" style="width: 100%" prop="windowEndTime">
                <el-time-picker
                  v-model="dataForm.windowEndTime"
                  placeholder="请选择时间"
                  value-format="HH:mm"
                  clearable
                  format="HH:mm"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="bg-white p-[15px] box-border rounded-[5px] mt-[30px]">
          <h3 class="mt-0">下发规则</h3>
          <!-- 下发规则 -->
          <div class="subject-radio">
            <el-form-item
              label="下发规则（修改下发规则后，可按需刷新任务给用户）"
              prop="taskIssuanceType"
            >
              <el-radio-group
                v-model="dataForm.taskIssuanceType"
                class="w-full"
                @change="handleRadioChange"
              >
                <div class="flex flex-col">
                  <el-radio :label="0" class="w-full flex mb-[20px] flex-1">
                    <el-form-item prop="taskRuleType">
                      <el-select
                        v-model="dataForm.taskRuleType"
                        placeholder="请选择"
                        :disabled="dataForm.taskIssuanceType !== 0"
                      >
                        <el-option label="入组日期" :value="1" />
                      </el-select>
                    </el-form-item>
                    <span class="mx-3">第</span>
                    <el-form-item
                      style="min-width: 460px"
                      :rules="
                        dataForm.taskIssuanceType === 1
                          ? [{ required: false, message: '', trigger: 'blur' }]
                          : [
                              {
                                required: true,
                                message:
                                  '请输入，连续范围之间用英文短划线表示，如1-30，代表第1天至第30天；多组数字之间用英文逗号表示，如1-30,40，代表第1天至第30天、第40天',
                                trigger: 'blur',
                              },
                            ]
                      "
                      prop="baselineDayRange"
                      class="tips-resident"
                    >
                      <el-input
                        v-model.trim="dataForm.baselineDayRange"
                        placeholder="请输入"
                        autocomplete="off"
                        maxlength="666"
                        :disabled="dataForm.taskIssuanceType !== 0"
                      />
                      <!-- 常驻 原来的隐藏掉 -->
                      <div class="resident whitespace-normal">
                        请输入，连续范围之间用英文短划线表示，如1-30，代表第1天至第30天；多组数字之间用英文逗号表示，如1-30,40，代表第1天至第30天、第40天
                      </div>
                    </el-form-item>
                    <span class="mx-3">天</span>
                  </el-radio>
                  <el-radio :label="1">自定义脚本</el-radio>
                </div>
              </el-radio-group>
            </el-form-item>
          </div>
          <!-- 分组 -->
          <el-col
            v-if="groupingDesignShow !== 1 && dataForm.taskIssuanceType === 0"
            :span="6"
            class="mt-[20px]"
          >
            <el-form-item label="适用组别" prop="armArr">
              <el-select
                v-model="dataForm.armArr"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in groupingList"
                  :key="index"
                  :label="item.armName"
                  :value="item.armCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </div>
        <div class="bg-white p-[15px] box-border rounded-[5px] mt-[30px]">
          <h3 class="mt-0">提醒规则（面向任务对象）</h3>
          <el-form-item prop="resource">
            <el-radio-group v-model="dataForm.isRemind">
              <el-radio :label="0">不提醒</el-radio>
              <el-radio :label="1">提醒</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="dataForm.isRemind == 1">
            <div
              v-for="(item, index) in dataForm.customTaskRemindRuleForPatients"
              :key="index"
            >
              <div class="mb-[20px] text-[14px] flex items-center">
                <div
                  class="bg-[#f2f2f2] box-border p-[15px] rounded-[5px] flex-1 relative max-w-[85%]"
                >
                  <div class="flex items-center">
                    <div class="mb-[18px]">时效性</div>
                    <el-form-item
                      class="mx-[10px]"
                      :prop="`customTaskRemindRuleForPatients[${index}].remindRuleType`"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }"
                    >
                      <el-select
                        v-model="item.remindRuleType"
                        @change="remindRuleTypeChange(index)"
                      >
                        <el-option label="区间段" :value="1" />
                        <el-option label="指定某天" :value="2" />
                      </el-select>
                    </el-form-item>
                    <div
                      v-if="item.remindRuleType === 1"
                      class="flex items-center"
                    >
                      <div class="mb-[18px]">从任务下发第</div>
                      <el-form-item
                        class="mx-[10px]"
                        :prop="`customTaskRemindRuleForPatients[${index}].remindDay`"
                        :rules="{
                          required: true,
                          message: '请输入',
                          trigger: 'blur',
                        }"
                      >
                        <el-input
                          v-model.trim="item.remindDay"
                          oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                          @blur="item.remindDay = $event.target.value"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <div class="mb-[18px]">天开始，每</div>
                      <el-form-item
                        class="mx-[10px]"
                        :prop="`customTaskRemindRuleForPatients[${index}].frequencyInterval`"
                        :rules="{
                          required: true,
                          message: '请输入',
                          trigger: 'blur',
                        }"
                      >
                        <el-input
                          v-model.trim="item.frequencyInterval"
                          oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                          @blur="item.frequencyInterval = $event.target.value"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <div class="mb-[18px]">天一次（仅任务窗口期内有效）</div>
                    </div>
                    <div
                      v-else-if="item.remindRuleType === 2"
                      class="flex items-center"
                    >
                      <div class="mb-[18px]">任务下发第</div>
                      <el-form-item
                        class="mx-[10px]"
                        :prop="`customTaskRemindRuleForPatients[${index}].remindDay`"
                        :rules="{
                          required: true,
                          message: '请输入',
                          trigger: 'blur',
                        }"
                      >
                        <el-input
                          v-model.trim="item.remindDay"
                          oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                          @blur="item.remindDay = $event.target.value"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <div class="mb-[18px]">天（仅任务窗口期内有效）</div>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <div class="mb-[18px]">事件</div>
                    <el-form-item
                      class="mx-[10px]"
                      :prop="`customTaskRemindRuleForPatients[${index}].remindEventType`"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }"
                    >
                      <el-select v-model="item.remindEventType">
                        <el-option label="任务未完成" :value="1" />
                      </el-select>
                    </el-form-item>
                    <div class="mb-[18px]">独立/合并提醒</div>
                    <el-form-item
                      class="mx-[10px]"
                      :prop="`customTaskRemindRuleForPatients[${index}].multitaskMerge`"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }"
                    >
                      <el-select
                        v-model="item.multitaskMerge"
                        @change="
                          (e) => {
                            dnumChange(e, index)
                          }
                        "
                      >
                        <el-option label="单任务独立提醒" :value="0" />
                        <el-option label="多任务合并提醒" :value="1" />
                      </el-select>
                    </el-form-item>
                    <div class="mb-[18px] text-[#b3b3b3]">
                      注：多任务合并提醒，表示当前任务与访视任务、学习任务，合并至18点进行提醒。
                    </div>
                  </div>
                  <div class="flex items-center">
                    <div class="mb-[18px]">提醒时间</div>
                    <el-form-item
                      class="mx-[10px]"
                      :prop="`customTaskRemindRuleForPatients[${index}].remindTime`"
                      :rules="
                        item.multitaskMerge === 0
                          ? {
                              required: true,
                              message: '请选择',
                              trigger: 'change',
                            }
                          : { required: false }
                      "
                    >
                      <el-time-picker
                        v-model="item.remindTime"
                        placeholder="请选择时间"
                        clearable
                        value-format="HH:mm"
                        format="HH:mm"
                        :disabled="item.multitaskMerge === 1"
                      />
                    </el-form-item>
                    <div class="mb-[18px]">提醒方式</div>
                    <el-form-item
                      class="mx-[10px]"
                      :prop="`customTaskRemindRuleForPatients[${index}].remindMethod`"
                      :rules="
                        item.multitaskMerge === 0
                          ? {
                              required: true,
                              message: '请选择',
                              trigger: 'change',
                            }
                          : { required: false }
                      "
                    >
                      <el-select
                        v-model="item.remindMethod"
                        :disabled="item.multitaskMerge === 1"
                      >
                        <el-option label="微信公众号提醒" :value="1" />
                      </el-select>
                    </el-form-item>
                    <div class="mb-[18px]">提醒内容</div>
                    <el-form-item
                      class="mx-[10px] w-[300px]"
                      :prop="`customTaskRemindRuleForPatients[${index}].remindMsg`"
                      :rules="
                        item.multitaskMerge === 0
                          ? {
                              required: true,
                              message: '请输入',
                              trigger: 'blur',
                            }
                          : { required: false }
                      "
                    >
                      <el-input
                        v-model.trim="item.remindMsg"
                        class="w-full"
                        maxlength="20"
                        placeholder="最多20个中文字符"
                        :disabled="item.multitaskMerge === 1"
                        clearable
                      />
                    </el-form-item>
                  </div>
                  <img
                    v-if="index > 0"
                    class="w-[30px] absolute top-0 right-0 cursor-pointer"
                    src="@/assets/closeIcon.svg"
                    alt=""
                    @click="deleteReminderRule(1,index)"
                  />
                </div>
                <div class="h-full flex w-[30px] ml-[15px]">
                  <img
                    v-if="index == 0"
                    class="w-full cursor-pointer"
                    src="@/assets/addIcon.svg"
                    alt=""
                    @click="addReminderRule(1)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-white p-[15px] box-border rounded-[5px] mt-[30px]">
          <h3 class="mt-0">提醒规则（面向质控人员）</h3>
          <el-form-item prop="resource">
            <el-radio-group v-model="dataForm.isRemindUser">
              <el-radio :label="0">不提醒</el-radio>
              <el-radio :label="1">提醒</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="dataForm.isRemindUser == 1">
            <div
              v-for="(item, index) in dataForm.customTaskRemindRuleForUsers"
              :key="index"
            >
              <div class="mb-[20px] text-[14px] flex items-center">
                <div
                  class="bg-[#f2f2f2] box-border p-[15px] rounded-[5px] flex-1 relative max-w-[85%]"
                >
                  <div class="flex items-center">
                    <div class="mb-[18px]">事件</div>
                    <el-form-item
                      class="mx-[10px]"
                      style="min-width: 460px"
                      :prop="`customTaskRemindRuleForUsers[${index}].remindEventType`"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }"
                    >
                      <el-select v-model="item.remindEventType">
                        <el-option label="截止指定时间，任务还在窗口内，未及时完成的人数" :value="1" />
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="flex items-center">
                    <div class="mb-[18px]">提醒对象</div>
                    <el-form-item
                      class="mx-[10px] w-[300px]"
                      :prop="`customTaskRemindRuleForUsers[${index}].remindObjectRoleArr`"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }"
                    >
                      <el-checkbox-group v-model="item.remindObjectRoleArr">
                        <el-checkbox
                          v-for="(checkboxItem, checkboxIndex) in checkboxRoleArr"
                          :key="checkboxIndex"
                          :label="checkboxItem.label"
                          :value="checkboxItem.roleId"
                        />
                      </el-checkbox-group>
                    </el-form-item>
                  </div>
                  <div class="flex items-center">
                    <div class="mb-[18px]">提醒时间</div>
                    <el-form-item
                      class="mx-[10px]"
                      :prop="`customTaskRemindRuleForUsers[${index}].remindTime`"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }"
                    >
                      <el-time-picker
                        v-model="item.remindTime"
                        placeholder="请选择时间"
                        clearable
                        value-format="HH:mm"
                        format="HH:mm"
                      />
                    </el-form-item>
                    <div class="mb-[18px]">提醒方式</div>
                    <el-form-item
                      class="mx-[10px]"
                      :prop="`customTaskRemindRuleForUsers[${index}].remindMethod`"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }"
                    >
                      <el-select
                        v-model="item.remindMethod"
                      >
                        <el-option label="微信公众号提醒" :value="1" />
                      </el-select>
                    </el-form-item>
                  </div>
                  <img
                    v-if="index > 0"
                    class="w-[30px] absolute top-0 right-0 cursor-pointer"
                    src="@/assets/closeIcon.svg"
                    alt=""
                    @click="deleteReminderRule(2,index)"
                  />
                </div>
                <div class="h-full flex w-[30px] ml-[15px]">
                  <img
                    v-if="index == 0"
                    class="w-full cursor-pointer"
                    src="@/assets/addIcon.svg"
                    alt=""
                    @click="addReminderRule(2)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <trial-dialog
      v-model="applyFlagShow"
      :my-dialog-body-style="myDialogBodyStyle"
    >
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="中心名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :left-default-checked="leftDefaultChecked"
            :right-default-checked="rightDefaultChecked"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain @click="shuttleCancel">取消</el-button>
          <el-button type="primary" @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import {
  getCustomTaskDetail,
  getCustomTaskQuestsDrop,
  getCustomTasksTransfer,
  // getRoles,
  postCustomTask,
} from '@/api/customTask'
import { CirclePlus } from '@element-plus/icons-vue'
import { getGoodsType } from '@/api/logisticsManagement'
import { studyStudyArmsInfo } from '@/api/dosageRegimenManagement'
import { deepClone } from '@/utils'

export default defineComponent({
  name: 'CustomTaskDetail', // 自定义任务详情
  components: {
    CirclePlus,
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          'uploadImage', // 上传图片
          'uploadVideo', // 上传视频
        ],
      },
      loading: false,
      customTaskDetailService: null, // 富文本ref
      customTaskDetailForm: null, // 表单ref
      dataForm: {
        id: '',
        windowStartTime: '00:00',
        windowEndTime: '23:59',
        taskName: '',
        isEnable: true,
        taskRuleType: 1,
        baselineDayRange: '',
        userType: 2,
        taskWindowStartDay: '1',
        taskWindowPeriod: '',
        taskDescription: '', // 富文本内容
        customSubtasks: [
          {
            id: '',
            subtaskType: 1,
            specificItem: [],
            specificItems: '',
            changeDeadline: '7',
            timeUnit: 1,
          },
        ],
        taskIssuanceType: 0, // 0默认规则 1自定义脚本
        arm: '', // 使用组别
        armArr: [],
        isRemind: 1, // 提醒患者
        isRemindUser: 1, // 提醒质控人员
        customTaskRemindRuleForPatients: [
          {
            id: '',
            remindObjectType:1,
            remindRuleType: 1,
            remindDay: null,
            frequencyInterval: null,
            remindEventType: 1,
            multitaskMerge: 0,
            remindTime: '',
            remindMethod: 1,
            remindMsg: '',
          },
        ],
        customTaskRemindRuleForUsers: [
          {
            id: '',
            remindObjectType:2,
            remindObjectRoles:'',
            remindObjectRoleArr:[],
            remindEventType: 1,
            remindTime: '',
            remindMethod: 1,
            remindMsg: '',
          },
        ],
      },
      options: [],
      questsDropoptions: [], // 问卷得数据
      rules: {
        taskName: [
          {
            required: true,
            message: '请输入/任务名称不可重复',
            trigger: 'blur',
          },
        ],
        // baselineDayRange: [
        //   { required: true, message: '请输入，连续范围之间用英文短划线表示，如1-30，代表第1天至第30天；多组数字之间用英文逗号表示，如1-30,40，代表第1天至第30天、第40天', trigger: 'blur' },
        // ],
        taskWindowPeriod: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^[1-9]\d*$/,
            message: '请输入正整数',
            trigger: 'blur',
          },
        ],
        taskIssuanceType: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        userType: [{ required: true, message: '请选择', trigger: 'change' }],
        armArr: [{ required: true, message: '请选择', trigger: 'change' }],
        windowEndTime: [{ required: true, message: '请选择', trigger: 'blur' }],
        windowStartTime: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
      },
      applyFlagShow: false, // 适用中心弹窗
      myDialogBodyStyle: {
        width: '60%',
      },
      shuttleProps: {
        key: 'siteId',
        label: 'statusStrAndsiteName',
      },
      shuttleValue: [],
      shuttleValueCopy: [], // 拷贝一份选中项绑定值
      leftDefaultChecked: [],
      rightDefaultChecked: [],
      shuttleData: [],
      groupingList: [],
      groupingDesignShow: store.state.studyItem.groupingDesign, // 单臂没有组别
      dnumChange: (e, index) => {
        if (e === 1) {
          state.customTaskDetailForm.clearValidate(
            `customTaskRemindRuleForPatients[${index}].remindTime`
          )
          state.customTaskDetailForm.clearValidate(
            `customTaskRemindRuleForPatients[${index}].remindMethod`
          )
          state.customTaskDetailForm.clearValidate(
            `customTaskRemindRuleForPatients[${index}].remindMsg`
          )
        }
      },
      addReminderRule: (type) => {
        if(type === 1)
        {
          state.dataForm.customTaskRemindRuleForPatients.push({
            id: '',
            remindObjectType:1,
            remindRuleType: 1,
            remindDay: null,
            frequencyInterval: null,
            remindEventType: 1,
            multitaskMerge: 0,
            remindTime: '',
            remindMethod: 1,
            remindMsg: '',
          })
        }else{
          state.dataForm.customTaskRemindRuleForUsers.push({
            id: '',
            remindObjectType:2,
            remindObjectRoles:'',
            remindObjectRoleArr:[],
            remindEventType: 1,
            remindTime: '',
            remindMethod: 1,
            remindMsg: '',
          })
        }
      },
      deleteReminderRule: (type,index) => {
        if(type === 1)
          state.dataForm.customTaskRemindRuleForPatients.splice(index, 1)
        else 
          state.dataForm.customTaskRemindRuleForUsers.splice(index, 1)
      },
      remindRuleTypeChange: (index) => {
        state.dataForm.customTaskRemindRuleForPatients[index].remindDay = null
        state.dataForm.customTaskRemindRuleForPatients[index].frequencyInterval = null
      },
      handleRadioChange: (e) => {
        if (e === 1) {
          // state.dataForm.baselineDayRange = ''
          state.dataForm.arm = ''
          state.dataForm.armArr = []
        }
      },
      routerGo: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      isTimeGreater: (time1, time2) => {
        // 解析时间字符串
        const [hours1, minutes1] = time1.split(':').map(Number)
        const [hours2, minutes2] = time2.split(':').map(Number)
        // 比较小时数
        if (hours1 > hours2) {
          return true
        } else if (hours1 < hours2) {
          return false
        } else {
          // 小时数相同，比较分钟数
          return minutes1 > minutes2
        }
      },
      saveData: () => {
        state.dataForm.taskDescription =
          state.customTaskDetailService.gainTxt('set')
        if (state.dataForm?.taskDescription?.length > 1000) {
          ElMessage.warning('任务说明内容超长')
          return
        }
        state.customTaskDetailForm.validate((valid) => {
          if (valid) {
            if (state.dataForm?.taskWindowPeriod == '1' && state.isTimeGreater(state.dataForm.windowStartTime, state.dataForm.windowEndTime)) {
              ElMessage.warning(
                '若开始与结束是同一天，则开始时间应早于结束时间'
              )
              return
            }
            state.loading = true
            if (state.dataForm?.armArr && state.dataForm.armArr?.length > 0) {
              state.dataForm.arm = state.dataForm.armArr.join(',')
            } else {
              state.dataForm.arm = ''
            }
            let form: any = deepClone(state.dataForm)

            if (!form?.isRemind) {
              form.customTaskRemindRuleForPatients = []
            }
            if (!form?.isRemindUser) {
              form.customTaskRemindRuleForUsers = []
            }
            else{
              form.customTaskRemindRuleForUsers.forEach(item => {
                item.remindObjectRoles = item.remindObjectRoleArr.filter(p=> p !=='').join(',');
              });
            }

            form.customTaskRemindRules = [];
            form.customTaskRemindRules = [...form.customTaskRemindRuleForPatients,...form.customTaskRemindRuleForUsers];

            const data = {
              ...form,
              siteIds: [...state.shuttleValue],
            }
            postCustomTask(store.state.studyItem.studyId, data)
              .then(() => {
                RESEARCHCENTER_INFOS.resetMethod.handleReset()
                RESEARCHCENTER_INFOS.contentVisible = false
                ElMessage.success('保存成功')
                state.loading = false
              })
              .catch(() => {
                state.loading = false
              })
          }
        })
      },
      // 穿梭框
      customTasksTransfer(studyId, customTaskId) {
        getCustomTasksTransfer(studyId, { customTaskId }).then((res) => {
          state.shuttleValue = []
          state.shuttleValueCopy = state.shuttleValue
          if (res?.customTaskSiteRuleList) {
            res.customTaskSiteRuleList.forEach((item) => {
              res.studySiteList.push(item)
              state.shuttleValue.push(item.siteId)
            })
          }
          state.shuttleData = res.studySiteList
          if (state.shuttleData) {
            state.shuttleData.forEach((ite) => {
              ite.statusStrAndsiteName = `${ite.edcSiteCode}-(${ite.siteStatusStr})  ${ite.siteName}`
            })
          }
        })
      },
      // 穿梭框取消
      shuttleCancel: () => {
        state.shuttleValue = state.shuttleValueCopy
        state.applyFlagShow = false
      },
      // 穿梭狂确定
      shuttleConfirm: () => {
        state.shuttleValueCopy = state.shuttleValue
        state.applyFlagShow = false
      },
      // 点击+号按钮
      addCustomSubtasksClick: () => {
        state.dataForm.customSubtasks.push({
          id: '',
          subtaskType: 1,
          specificItem: [],
          specificItems: '',
          changeDeadline: '7',
          timeUnit: 1,
        })
      },
      // 删除
      deleteCustomSubtasks: (index) => {
        state.dataForm.customSubtasks.splice(index, 1)
      },
      selectChange: (item) => {
        if (item.subtaskType === 1) {
          item.specificItem = item.specificItemCopy
        } else if (item.subtaskType === 2) {
          item.specificItems = item.specificItemsCopy

          if(item.timeUnit === 0 || item.changeDeadline === 0){
            item.changeDeadline = '7';
            item.timeUnit = 1;
          }
        }
      },
      checkboxRoleArr: [
        { roleId: 'ProjectAdmin', label: '运营专员' },
        { roleId: 'PI', label: '主要研究者' },
        { roleId: 'SUBI', label: '协助研究者' },
        { roleId: 'CRC', label: '研究协调员' }
      ],
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        // getRoles(store.state.studyItem.studyId).then((res) => {
        //   state.checkboxRoleArr = res || []
        // })
        state.customTasksTransfer(
          store.state.studyItem.studyId,
          RESEARCHCENTER_INFOS.researchContent.id || ''
        )
        // 获取物品类型
        getGoodsType(store.state.studyItem.studyId).then((res) => {
          state.options = res
        })
        // 获取自定义任务问卷（下拉框）
        getCustomTaskQuestsDrop(store.state.studyItem.studyId).then((res) => {
          state.questsDropoptions = res
        })
        if (RESEARCHCENTER_INFOS.stateVal === 0) {
          const loading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          getCustomTaskDetail(RESEARCHCENTER_INFOS.researchContent.id)
            .then((res: any) => {
              if (res.arm) {
                res.armArr = res.arm.split(',')
              } else {
                res.armArr = []
              }
              if (!res?.isRemind && !res?.customTaskRemindRules?.filter(p=>p.remindObjectType === 1).length) {
                res.customTaskRemindRuleForPatients = [
                  {
                    id: '',
                    remindObjectType:1,
                    remindRuleType: 1,
                    remindDay: null,
                    frequencyInterval: null,
                    remindEventType: 1,
                    multitaskMerge: 0,
                    remindTime: '',
                    remindMethod: 1,
                    remindMsg: '',
                  },
                ]
              }else{
                res.customTaskRemindRuleForPatients = res.customTaskRemindRules.filter(p=>p.remindObjectType === 1);
              }

              if (!res?.isRemindUser && !res?.customTaskRemindRules?.filter(p=>p.remindObjectType === 2).length) {
                res.customTaskRemindRuleForUsers = [
                  {
                    id: '',
                    remindObjectType:2,
                    remindObjectRoles:'',
                    remindObjectRoleArr:[],
                    remindEventType: 1,
                    remindTime: '',
                    remindMethod: 1,
                    remindMsg: '',
                  },
                ]
              }else{
                res.customTaskRemindRuleForUsers = res.customTaskRemindRules.filter(p=>p.remindObjectType === 2);
                res.customTaskRemindRuleForUsers.forEach(item => {
                  item.remindObjectRoleArr = item.remindObjectRoles.split(',') || [];
                });
              }

              if (!res?.taskWindowStartDay) {
                res.taskWindowStartDay = '1'
              }
              state.dataForm = res
              if (res?.customSubtasks.length) {
                res.customSubtasks.forEach((item) => {
                  if (item.subtaskType === 1) {
                    item.specificItemCopy = item.specificItem
                    item.specificItemsCopy = ''
                  } else if (item.subtaskType === 2) {
                    item.specificItemsCopy = item.specificItems
                    item.specificItemCopy = []
                  }
                })
              }
              state.customTaskDetailService.gainTxt('get', res.taskDescription)
              loading.close()
            })
            .catch(() => {
              loading.close()
            })
        }
      },
    })
    onMounted(() => {
      // 下拉分组信息
      studyStudyArmsInfo(store.state.studyItem.studyId).then((res: any) => {
        state.groupingList = res
      })
      state.onLoad()
    })

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style scoped lang="less">
.customTask-detail {
  width: 100%;
  min-width: 1000px;
  box-sizing: border-box;
  .subtask-box {
    width: 90%;
    background: #f2f2f2;
    .subtask-left {
      width: 20%;
      background: #d7d7d7;
    }
  }
  .resident {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 2px;
    position: absolute;
    top: 100%;
    left: 0;
  }
  .tips-resident {
    ::v-deep .el-form-item__error {
      display: none;
    }
  }
}

::v-deep .w-e-text-container {
  z-index: 10 !important;
  min-height: 300px !important;
}
::v-deep .w-e-toolbar {
  z-index: 11 !important;
}
.subject-radio {
  :deep(.el-radio__label) {
    display: flex;
    align-items: center;
  }
}
</style>
