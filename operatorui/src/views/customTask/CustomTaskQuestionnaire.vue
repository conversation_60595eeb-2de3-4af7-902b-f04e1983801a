<template>
  <div class="customTask">
    <div v-if="!RESEARCHCENTER_INFOS?.contentVisible">
      <trial-table
        ref="customTaskQuestionnaireRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        showbtnfalg
        hide-center
        :pagination="paginationConfig"
      >
        <template #operate="scope">
          <span class="editBtnBlue mr-3" @click="editCustomTaskInfoItem(scope.row, 0)">
            编辑
          </span>
        </template>
        <template #hideCenter>
          <div class="researchCenter-hideCenter">
            <div class="my-5 flex justify-between items-center">
              <span>自定义问卷列表</span>
              <div>
                <el-button
                  type="primary"
                  @click="editCustomTaskInfoItem(null, 1)"
                >新建</el-button>
              </div>
            </div>
          </div>
        </template>
      </trial-table>
    </div>
    <CustomTaskQuestionnaireDetails v-if="RESEARCHCENTER_INFOS?.contentVisible" />
  </div>
</template>

<script lang='ts'>
import { defineComponent, provide, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import CustomTaskQuestionnaireDetails from '@/views/customTask/CustomTaskQuestionnaireDetails.vue'
import { getCustomTaskQuests } from '@/api/customTask'

export default defineComponent({
  name: 'CustomTaskQuestionnaire', // 自定义问卷列表
  components: {
    CustomTaskQuestionnaireDetails,
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      stateVal: null,
      resetMethod: null,
    })
    const state = reactive({
      customTaskQuestionnaireRef: null,
      columns: [
        { label: '问卷名称', prop: 'questName', width: 200 },
        { label: '已关联自定义任务', prop: 'taskName', width: 300 },
        { label: '更新时间', prop: 'lastUpdateTime', width: 180, },
        { label: '更新人', prop: 'operatorName', width: 120 },
        { label: '创建时间', prop: 'createTime', width: 180, },
        { label: '创建人', prop: 'createOperatorName', width: 120 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },

      // 编辑/新建
      editCustomTaskInfoItem: (row, idx) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.stateVal = idx
        RESEARCHCENTER_INFOS.resetMethod = state.customTaskQuestionnaireRef
      },

      // 表格数据
      async getResearchCenterList(params) {
        const { studyId } = store.state.studyItem
        const rest = await getCustomTaskQuests(studyId, params)
        return {
          data: rest.items,
          total: +rest.totalItemCount,
        }
      },

    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
</style>
