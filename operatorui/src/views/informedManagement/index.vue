<template>
  <div class="researchCenter">
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <div class="title">
        <h3>知情同意书列表</h3>
        <el-button type="primary" class="knowBtn" @click="knowBtn(null)">
          新建
        </el-button>
      </div>
      <trial-table
        ref="researchCenterRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :search="searchConfig"
        :pagination="paginationConfig"
      >
        <template #icfVersionNumber="scope">
          <div class="tdTooltip" v-for="item in scope.row.icfVersions">
            <div v-if="!showToolTip(item.icfVersionNumber)">
              {{ item.icfVersionNumber }}
            </div>
            <el-tooltip
              v-else
              class="box-item"
              effect="dark"
              :content="item.icfVersionNumber"
              placement="top"
            >
              {{ item.icfVersionNumber }}
            </el-tooltip>
          </div>
        </template>
        <template #icfVersionDateStr="scope">
          <div v-for="(e, idx) in scope.row.icfVersions" :key="idx">
            {{ e.icfVersionDate }}
          </div>
        </template>
        <template #icfSiteCountBox="scope">
          <div
            style="color: #1e87f0; cursor: pointer"
            @click="icfSiteCountBtn(scope.row)"
          >
            {{ scope.row.icfSiteCount }}
          </div>
        </template>
        <template #operate="scope">
          <span class="editBtn" @click="knowBtn(scope.row)"> 编辑 </span>
        </template>
      </trial-table>
    </div>
    <!-- 查看已关联的中心 -->
    <trial-dialog
      v-model="popUpShow"
      :title="title"
      class="myDialogUp"
      :my-dialog-body-style="{ width: '70%' }"
    >
      <template #DialogBody>
        <trial-table
          ref="researchMyDialogRef"
          title=""
          border
          :request="getResearchCenterMyDialogList"
          :columns="myDialogColumns"
        >
          <template #operate="scope">
            <span
              :class="
                scope.row.enableStatus === '禁用' ? 'table-red' : 'table-green'
              "
              >{{ scope.row.enableStatus }}</span
            >
          </template>
        </trial-table>
      </template>
      <template #footer>
        <div class="centerflex" style="margin-top: 20px">
          <el-button size @click="popUpShow = false">取消</el-button>
        </div>
      </template>
    </trial-dialog>
    <div v-if="RESEARCHCENTER_INFOS?.contentVisible">
      <Details />
    </div>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, provide, reactive, toRefs } from 'vue'
import Details from '@/views/informedManagement/InformedConsentsDetails.vue'
import {
  getTemplates, // 获取知情同意书列表数据
  getTemplateRelatedSite, // 获取已绑定的中心
} from '@/api/informedManagement'

export default defineComponent({
  name: 'InformedConsentDetails', // 知情同意书
  components: {
    Details,
  },
  setup() {
    const store = useStore()
    // const router = useRouter()
    const RESEARCHCENTER_INFOS = reactive({
      id: '',
      researchContent: {},
      contentVisible: false,
      resetMethod: null,
    })
    const state = reactive({
      researchMyDialogRef: null,
      researchCenterRef: null,
      popUpShow: false,
      title: '已关联中心',
      detailsData: [],
      tyPingList: [],
      // 表格列配置，大部分属性跟el-table-column配置一样//sortable: true,排序
      columns: [
        // { type: 'selection' }, // table勾选框
        // doctorInfo.hospital + doctorInfo.department
        { label: '版本名称', width: 300, tdSlot: 'icfVersionNumber', showOverflowTooltip: false },
        { label: '版本日期', tdSlot: 'icfVersionDateStr', width: 120 },
        { label: '流程类型', prop: 'flowType', width: 150 },
        { label: '流程元素', prop: 'icfFeature', width: 500 },
        {
          label: '已关联中心',
          prop: 'icfSiteCount',
          width: 150,
          tdSlot: 'icfSiteCountBox',
        },
        { label: '更新时间', prop: 'lastUpdateTime', width: 180 },
        { label: '操作人', prop: 'operatorName', width: 150 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      myDialogColumns: [
        { label: '中心编号', prop: 'edcSiteCode', minWidth: 100 },
        { label: '中心名称', prop: 'siteName', minWidth: 300 },
        { label: '中心类别', prop: 'siteCategory', minWidth: 150 },
        { label: '中心状态', prop: 'edcSiteStatus', minWidth: 100 },
        { label: '生效时间', prop: 'effectiveDate', minWidth: 200 },
        {
          label: '状态',
          fixed: 'right',
          width: 120,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        labelWidth: '90px', // 必须带上单位
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            label: '版本名称',
            name: 'versionNumber',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      getResearchCenterMyDialogList: () => {
        return {
          data: []
        }
      },
      // 获取列表数据方法
      getResearchCenterList: async (params) => {
        try {
          const myParams = { ...params }
          const rest = await getTemplates(
            store.state.studyItem.studyId,
            myParams
          )
          // 必须要返回一个对象，包含data数组和total总数
          return {
            data: rest.items,
            total: +rest.totalItemCount,
          }
        } catch (e) {
          // console.log(e)
        }
      },
      // 查看已关联中心
      icfSiteCountBtn: (row) => {
        state.popUpShow = true
        getTemplateRelatedSite(row.id).then((res) => {
          state.researchMyDialogRef.tableData = res?.items || []
        })
      },
      // 编辑某项中心
      knowBtn: (row) => {
        if (!row) {
          RESEARCHCENTER_INFOS.id = null
        } else if (row) {
          RESEARCHCENTER_INFOS.id = '1'
          RESEARCHCENTER_INFOS.researchContent = { ...row }
        }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.researchCenterRef
      },
      showToolTip: (txt) => {
        let cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0;
        let otCount = txt.length - cnCount
        return (cnCount * 2 + otCount) > 38
      }
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)

    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.researchCenter {
  width: 100%;
  .title {
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    h3 {
      margin: 0;
      margin-top: 5px;
    }
  }
  .knowBtn {
    cursor: pointer; /*小手*/
  }
  .editBtn {
    color: #409eff;
    cursor: pointer; /*小手*/
  }
}
.table-red {
  color: rgb(226, 70, 70);
}
.table-green {
  color: green;
}
.myDialogUp {
  :deep(.my-el-table, .head) {
    padding: 0 !important;
  }
}
.tdTooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
