<template>
  <div class="informed-consent-details EditQuestionnaire">
    <div v-show="!QUESTIONNAIRE_CONTENT?.contentVisible" class="content-top">
      <el-tabs
        v-model="activeName"
        type="card"
        class="demo-tabs"
        @tab-click="tabsHandleClick"
      >
        <el-tab-pane label="知情流程" name="KnowTypes">
          <div class="flex justify-end">
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="submitInKnow('-1')"
              >保存</el-button
            >
          </div>
          <el-form
            ref="knowTypesRef"
            label-position="top"
            :model="studyICFBaseInfo"
            :rules="rules"
            class="elForm relative top-[-30px] mb-[50px]"
          >
            <el-form-item
              label="流程类型"
              :label-width="formLabelWidth"
              prop="flowType"
              style="width: 100%"
            >
              <el-select
                v-model="studyICFBaseInfo.flowType"
                style="width: 200px"
              >
                <el-option
                  v-for="item in isRequiredOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  @click="submitProcess(item.value)"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="studyICFBaseInfo.flowType === 2"
              label="签署模式"
              :label-width="formLabelWidth"
              prop="flowType"
            >
              <el-radio-group
                v-model="studyICFBaseInfo.signatureStrategy"
                @change="radioCAChange"
              >
                <el-radio :label="1"
                  >普通（完全由内部控制，不含CA认证服务）</el-radio
                >
                <el-radio :label="2"
                  >对接法大大，基于文档（支持单份）创建签署任务（知情内容填写由内部控制，签署调用法大大，最多支持受试者&研究者签名，含CA认证服务，使用前请确认可用签署份数充足）</el-radio
                >
                <el-radio :label="3"
                  >对接法大大，基于模板（支持多份）创建签署任务（知情内容填写、签署调用法大大，最多支持受试者&监护人&见证人&研究者签名，含CA认证服务，使用前请确认可用签署份数充足）</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="流程元素"
              :label-width="formLabelWidth"
              prop="flowType"
            >
              <el-checkbox-group
                v-if="studyICFBaseInfo?.flowType === 2"
                v-model="sunNumberList"
              >
                <div class="flex">
                  <div
                    class="mr-[30px]"
                    v-for="item in flowArr.filter((e) =>
                      e.showSignatureStrategy.includes(
                        studyICFBaseInfo.signatureStrategy
                      )
                    )"
                  >
                    <el-checkbox
                      :key="item.additional"
                      :label="item.additional"
                      :disabled="item.disabled"
                      @change="hanldeFlowOptionChange($event, item)"
                    >
                      {{ item.title }}
                    </el-checkbox>
                    <div v-if="item.subOptions">
                      <el-checkbox
                        class="ml-[20px]"
                        v-for="subItem in item.subOptions"
                        :key="subItem.additional"
                        :label="subItem.additional"
                        :disabled="subItem.disabled"
                        @change="hanldeFlowOptionChange($event, subItem, item)"
                      >
                        {{ subItem.title }}
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </el-checkbox-group>
              <el-checkbox-group v-else v-model="sunNumberList">
                <el-checkbox :label="2" disabled> 阅读知情同意 </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          v-if="sunNumber.includes(1) && id"
          label="知情视频"
          class="know-video"
          name="KnowVideo"
        >
          <el-button type="primary" @click="knowVideoBtn(null)">新增</el-button>
          <trial-table
            ref="KnowVideoTableRef"
            title=""
            :request="getKnowVideoist"
            :columns="columns"
          >
            <template #operate="scope">
              <el-button
                link
                size="small"
                type="primary"
                @click="knowVideoBtn(scope.row)"
                >编辑</el-button
              >
              <el-button
                link
                size="small"
                type="danger"
                @click="deleteVideoBtn(scope.row)"
                >删除</el-button
              >
            </template>
          </trial-table>
          <trial-dialog v-model="knowVideoDialogShow" :title="myTitle">
            <template #DialogBody>
              <el-form
                ref="knowVideoRef"
                label-position="top"
                :model="studyICFVideoInfo"
                :rules="knowVideoules"
              >
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="排序" prop="sort" style="width: 100%">
                      <el-input-number
                        v-model="studyICFVideoInfo.sort"
                        :min="0"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="视频标题"
                      prop="title"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="studyICFVideoInfo.title"
                        placeholder="请输入"
                        autocomplete="off"
                        maxlength="100"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <div class="flex w-full">
                      <el-form-item
                        label="最短观看时长"
                        prop="studyVideoViewSeconds"
                        style="width: 100%"
                      >
                        <el-input
                          v-model.trim="studyICFVideoInfo.studyVideoViewSeconds"
                          placeholder="请输入"
                          autocomplete="off"
                          maxlength="50"
                        />
                      </el-form-item>
                      <span style="line-height: 90px; margin-left: 10px"
                        >秒</span
                      >
                    </div>
                  </el-col>
                </el-row>
              </el-form>
              <div style="width: 100%" class="mb-[20px]">
                <div><span style="color: red">*</span>视频素材</div>
                <div>
                  <MyUpload
                    ref="StatementVideoUrlRef"
                    :upload-file-falg="true"
                    :request-fun="fileFun3"
                    :deletefile="deletefile3"
                    :file-list="fileList2"
                    :file-size="1 / 2.048"
                  />
                </div>
              </div>
              <div class="customInformation-body-logo-upload">
                <div
                  style="line-height: 25px"
                  class="flex items-center mb-[10px]"
                >
                  <span class="mr-[15px]">视频封面</span>
                  <el-icon
                    v-if="studyICFVideoInfo.videoCoverUrl"
                    class="cursor-pointer"
                    color="#d9001b"
                    @click="deleteVideoCover"
                    ><Delete
                  /></el-icon>
                </div>
                <MyUpload
                  ref="StatementImgUrlRef"
                  :upload-img-falg="true"
                  :request="postTemplateFile"
                  :request-fun="fileImg"
                  :file-size="1 / 2.048"
                />
                <p>可替换默认图片，图片大小不应超过500KB</p>
              </div>
              <!-- <div class="footer pos-rb">
                <el-button
                  type="primary"
                  size="large"
                  :loading="loading"
                  @click="submitInKnow('B')"
                  >保存</el-button
                >
              </div> -->
            </template>
            <template #footer>
              <div class="flex justify-center">
                <el-button
                  :loading="loading"
                  plain
                  @click="knowVideoDialogCancel"
                  >取消</el-button
                >
                <el-button
                  :loading="loading"
                  type="primary"
                  @click="knowVideoDialogConfirm"
                  >确定</el-button
                >
              </div>
            </template>
          </trial-dialog>
        </el-tab-pane>
        <el-tab-pane v-if="id" label="知情同意书" name="KnowInfos">
          <div v-if="signatureCACopy === 3">
            <el-button
              type="primary"
              @click="knowAddOrEditWrittenConsentBtn(null)"
              >新增</el-button
            >
            <trial-table
              ref="knowInfosTableRef"
              border
              :request="
                () => {
                  return { data: [] }
                }
              "
              :columns="knowInfosTableColumns"
            >
              <template #isRequiredStr="scope">
                <span>{{ scope.row.isRequired ? '是' : '否' }}</span>
              </template>
              <template #operate="scope">
                <el-button
                  link
                  size="small"
                  type="primary"
                  @click="knowAddOrEditWrittenConsentBtn(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  link
                  size="small"
                  type="danger"
                  @click="deleteWrittenConsentBtn(scope.row)"
                  >删除</el-button
                >
              </template>
            </trial-table>
            <trial-dialog
              v-model="knowWrittenConsentDialogShow"
              id="knowWrittenConsentDialogShow"
              :title="myTitle"
            >
              <template #DialogBody>
                <el-form
                  ref="knowBookRef"
                  label-position="top"
                  :model="studyICFBaseInfo"
                  :rules="rules"
                  class="elForm"
                >
                  <el-form-item
                    label="排序"
                    :label-width="formLabelWidth"
                    prop="displayOrder"
                    class="w-[100px] mr-[10px]"
                  >
                    <el-input
                      v-model.trim="studyICFBaseInfo.displayOrder"
                      placeholder="请输入"
                      autocomplete="off"
                      maxlength="16"
                    />
                  </el-form-item>
                  <el-form-item
                    label="版本名称"
                    :label-width="formLabelWidth"
                    prop="icfVersionNumber"
                    style="width: 30%"
                  >
                    <el-input
                      v-model.trim="studyICFBaseInfo.icfVersionNumber"
                      placeholder="请输入"
                      autocomplete="off"
                      maxlength="50"
                    />
                  </el-form-item>
                  <el-form-item
                    label="版本日期"
                    :label-width="formLabelWidth"
                    prop="icfVersionDate"
                    style="width: 180px"
                    class="mx-[10px]"
                  >
                    <el-date-picker
                      v-model="studyICFBaseInfo.icfVersionDate"
                      style="width: 100%"
                      type="date"
                      placeholder="年/月/日"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                  <el-form-item
                    label="是否必需"
                    :label-width="formLabelWidth"
                    prop="isRequired"
                    class="w-[100px] mr-[10px]"
                  >
                    <!-- :disabled="!templateDetailsObj.icfVersions.find((e) => {
                        return e.isRequired
                      })"-->

                    <el-select
                      v-model="studyICFBaseInfo.isRequired"
                      :disabled="
                        !templateDetailsObj.icfVersions.find((e) => {
                          return e.isRequired
                        }) ||
                        (myTitle !== '新增' &&
                          templateDetailsObj.icfVersions.length === 1)
                      "
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in mustOrNotOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <div style="width: 32%; display: flex">
                    <el-form-item
                      label="最短阅读时长"
                      :label-width="formLabelWidth"
                      prop="shortestDuration"
                      style="width: 94%"
                    >
                      <el-input
                        v-model.trim="studyICFBaseInfo.shortestDuration"
                        placeholder="请输入"
                        autocomplete="off"
                        maxlength="50"
                      />
                    </el-form-item>
                    <span style="line-height: 90px; margin-left: 10px">秒</span>
                  </div>
                  <div style="width: 100%">
                    <div style="font-size: 14px; margin-bottom: 5px">
                      <span style="color: red">*</span
                      >PDF知情同意书（只能上传PDF文件）
                    </div>
                    <div>
                      <MyUpload
                        ref="InformedConsentDetailsUploadRef"
                        upload-file-falg
                        :custom-information-form-data="
                          customInformationFormData
                        "
                        :request-fun="fileFun"
                        :deletefile="deletefile"
                        :file-list="fileList"
                        :file-size="1 / 2.048"
                        :before-file-upload-type="beforeFileUploadType"
                      />
                    </div>
                  </div>
                </el-form>
              </template>
              <template #footer>
                <div class="flex justify-center">
                  <el-button
                    :loading="loading"
                    plain
                    @click="knowWrittenConsentDialogShow = false"
                    >取消</el-button
                  >
                  <el-button
                    :loading="loading"
                    type="primary"
                    @click="submitInKnow('A')"
                    >保存</el-button
                  >
                </div>
              </template>
            </trial-dialog>
          </div>
          <div v-else>
            <div class="flex justify-between">
              <!-- <div v-if="signatureCACopy === 3" class="text-red-500">最少有1个必需的知情版本</div> -->
              <div class="flex-1 flex justify-end">
                <el-button
                  type="primary"
                  size="large"
                  :loading="loading"
                  @click="submitInKnow('A')"
                  >保存</el-button
                >
              </div>
            </div>
            <el-form
              ref="knowBookRef"
              label-position="top"
              :model="studyICFBaseInfo"
              :rules="rules"
              class="elForm"
            >
              <el-form-item
                label="版本名称"
                :label-width="formLabelWidth"
                prop="icfVersionNumber"
                style="width: 30%"
              >
                <el-input
                  v-model.trim="studyICFBaseInfo.icfVersionNumber"
                  placeholder="请输入"
                  autocomplete="off"
                  maxlength="50"
                />
              </el-form-item>
              <el-form-item
                label="版本日期"
                :label-width="formLabelWidth"
                prop="icfVersionDate"
                style="width: 180px"
                class="mx-[10px]"
              >
                <el-date-picker
                  v-model="studyICFBaseInfo.icfVersionDate"
                  style="width: 100%"
                  type="date"
                  placeholder="年/月/日"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <div style="width: 32%; display: flex">
                <el-form-item
                  label="最短阅读时长"
                  :label-width="formLabelWidth"
                  prop="shortestDuration"
                  style="width: 94%"
                >
                  <el-input
                    v-model.trim="studyICFBaseInfo.shortestDuration"
                    placeholder="请输入"
                    autocomplete="off"
                    maxlength="50"
                  />
                </el-form-item>
                <span style="line-height: 90px; margin-left: 10px">秒</span>
              </div>
              <div v-if="studyICFBaseInfo?.flowType === 1" style="width: 30%">
                <el-form-item
                  label="知情同意书形式"
                  :label-width="formLabelWidth"
                  prop="knowBooksListValue"
                  style="width: 100%"
                  class="formLeft"
                >
                  <el-select
                    v-model="studyICFBaseInfo.knowBooksListValue"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in knowBooks"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <div
                v-if="
                  (studyICFBaseInfo.knowBooksListValue === 1 &&
                    studyICFBaseInfo?.flowType === 1) ||
                  studyICFBaseInfo?.flowType === 2
                "
                style="width: 100%"
              >
                <div style="width: 100%">
                  <div style="font-size: 14px; margin-bottom: 5px">
                    <span style="color: red">*</span
                    >PDF知情同意书（只能上传PDF文件）
                  </div>
                  <div>
                    <MyUpload
                      ref="InformedConsentDetailsUploadRef"
                      upload-file-falg
                      :custom-information-form-data="customInformationFormData"
                      :request-fun="fileFun"
                      :deletefile="deletefile"
                      :file-list="fileList"
                      :file-size="1 / 2.048"
                      :before-file-upload-type="beforeFileUploadType"
                    />
                  </div>
                </div>
                <!-- <div v-show="studyICFBaseInfo?.flowType === 2" class="flex items-center">
                  <span class="mr-3" style="font-size: 14px">流程元素</span>
                  <el-checkbox-group v-model="sunNumberList">
                    <el-checkbox v-for="item in flowArr" :key="item.additional" :label="item.additional" :disabled="item.disabled" class="relative">
                      {{ item.title }}
                      <el-radio-group
                        v-if="item.additional === 64"
                        v-model="signatureCA"
                        class="flex flex-wrap absolute left-0 mt-[20px]"
                        @change="radioCAChange"
                      >
                        <el-radio :label="1">无CA认证</el-radio>
                        <el-radio :label="2">法大大CA认证（使用前确认套餐流量）</el-radio>
                      </el-radio-group>
                    </el-checkbox>
                  </el-checkbox-group>
                </div> -->
              </div>
              <div
                v-if="
                  studyICFBaseInfo.knowBooksListValue === 2 &&
                  studyICFBaseInfo?.flowType === 1
                "
                style="width: 100%; margin-top: 50px"
              >
                知情同意书内容
                <div>
                  <vue-qr v-if="siteUrl" :text="siteUrl" class="site" />
                  <img
                    v-else
                    class="site margin-t-10"
                    src="@/assets/logocd.png"
                    alt=""
                  />
                  <span>请先保存数据后再扫码预览效果</span>
                  <trial-wang-editor
                    ref="editorHome"
                    :toolbar-config="toolbarConfig"
                  />
                </div>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="sunNumber.includes(4) && id"
          label="讲解知情"
          name="Explain"
        >
          <div class="flex justify-end">
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="submitInKnow('D')"
              >保存</el-button
            >
          </div>
          <el-radio v-model="studyICFExplainInfo.explainICFType" label="1"
            >强制需要讲解</el-radio
          >
          <el-radio v-model="studyICFExplainInfo.explainICFType" label="2"
            >弹窗询问受试者是否需要讲解</el-radio
          >
        </el-tab-pane>
        <el-tab-pane
          v-if="sunNumber.includes(8) && id !== ''"
          label="知情问卷"
          name="knowQuestionnaire"
        >
          <div class="EditQuestionnaire-module">
            <div class="infos-body">
              <div class="infos-body-title">
                <h4>基本信息</h4>
                <el-button
                  type="primary"
                  @click="
                    () => {
                      QUESTIONNAIRE_INFOS.dialogVisible = true
                    }
                  "
                  >编辑</el-button
                >
              </div>
              <div class="infos-body-content">
                <div class="infos-body-content-items">
                  <div class="infos-body-content-items-label">DCT问卷名称</div>
                  <span
                    v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfName"
                    v-html="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfName"
                  />
                </div>
                <div class="infos-body-content-items">
                  <div class="infos-body-content-items-paralleling">
                    <div class="infos-body-content-items-label">问卷性质</div>
                    <!-- 0 = 未知, 1 = 客户问卷, 2 = 医生问卷, 3 = 非客户问卷, 4 = 上门医护问卷 -->
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType === 1
                      "
                      >受试者问卷</span
                    >
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType === 2
                      "
                      >研究者问卷</span
                    >
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType === 3
                      "
                      >非用户问卷</span
                    >
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questTemplateType === 4
                      "
                      >上门医护问卷</span
                    >
                  </div>
                  <div class="infos-body-content-items-paralleling">
                    <!-- 0 = 不同步, 1 = 同步EDC -->
                    <div class="infos-body-content-items-label">数据同步</div>
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.isSynchronization
                      "
                      >同步至EDC</span
                    >
                    <span v-else>不同步</span>
                  </div>
                </div>
                <div class="infos-body-content-items">
                  <div class="infos-body-content-items-paralleling">
                    <div class="infos-body-content-items-label">问卷样式</div>
                    <!-- 0 = 未知, 1 = 问卷完整列表, 2 = 问卷向导列表, 3 = 问卷Url, 4 = 上传图片的问卷 -->
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType === 1
                      "
                      >完整问卷</span
                    >
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType === 2
                      "
                      >向导式问卷</span
                    >
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType === 3
                      "
                      >问卷URL</span
                    >
                    <span
                      v-if="
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType &&
                        QUESTIONNAIRE_INFOS?.questionnaireInfo
                          ?.questDisplayType === 4
                      "
                      >上传图片的问卷</span
                    >
                  </div>
                  <div class="infos-body-content-items-paralleling">
                    <div class="infos-body-content-items-label">问卷类型</div>
                    <!-- v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.questPatientTemplateType" -->
                    <span>知情问卷</span>
                  </div>
                </div>
                <div class="infos-body-content-items">
                  <div class="infos-body-content-items-label">填写说明</div>
                  <span
                    v-if="QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfGuideline"
                    v-html="
                      QUESTIONNAIRE_INFOS?.questionnaireInfo?.crfGuideline
                    "
                  />
                </div>
              </div>
            </div>

            <div class="questionnaire-body">
              <div class="questionnaire-body-title">
                <h4>问卷内容</h4>
                <el-button type="primary" @click="setQuestionnaire('add')"
                  >新建题目</el-button
                >
              </div>
              <div class="questionnaire-body-content">
                <div
                  v-for="(item, index) in QUESTIONNAIRE_CONTENT
                    ?.questionnaireContentViews?.questTemplateItem"
                  :key="index"
                  class="questionnaire-body-content-module"
                >
                  <div class="questionnaire-body-content-module-head">
                    <div class="sort">
                      ({{ item.dctSort }})
                      <span v-if="item.isRequired">(必填)</span>
                    </div>
                    <!-- 0 = 未知, 1 = 仅患者端显示, 2 = 仅医生端显示, 3 = 医生患者都显示 -->
                    <div v-if="item?.dctQuestItemDisplayType" class="check">
                      <span v-if="item.dctQuestItemDisplayType === 1"
                        >仅患者端显示</span
                      >
                      <span v-if="item.dctQuestItemDisplayType === 2"
                        >仅医生端显示</span
                      >
                      <span v-if="item.dctQuestItemDisplayType === 3"
                        >医生患者都显示</span
                      >
                    </div>
                    <div class="btns">
                      <span
                        v-if="item.crfFieldType === 3"
                        class="btns-edit"
                        @click="setQuestionnaire('addList', item)"
                        >添加列表字段&nbsp;&nbsp;&nbsp;&nbsp;</span
                      >
                      <span
                        class="btns-delete"
                        @click="setQuestionnaire('delete', item)"
                        >删除</span
                      >
                      <span
                        class="btns-edit"
                        @click="setQuestionnaire('edit', item)"
                        >编辑</span
                      >
                    </div>
                  </div>
                  <div class="questionnaire-body-content-module-items">
                    <div class="questionnaire-body-content-module-item">
                      <div class="questionnaire-body-content-module-item-lable">
                        DCT中题目名称
                      </div>
                      <span v-if="item.fieldLabel" v-html="item.fieldLabel" />
                    </div>
                    <div class="questionnaire-body-content-module-item">
                      <div class="questionnaire-body-content-module-item-lable">
                        控件类型
                      </div>
                      <!-- 0 = 未知, 1 = 无控件, 2 = 文件上传, 3 = 单行文本控件, 4 = 多行文本控件,
                  5 = 数字控件, 6 = 日期控件, 7 = 时间控件
                  , 8 = 日期时间控件, 9 = 单选控件, 10 = 多选控件, 11 = 年月控件 -->
                      <div
                        v-for="(ite, idx) in QUESTIONNAIRE_CONTENT?.DropInfos
                          ?.questFieldControl"
                        :key="idx"
                      >
                        <span v-if="ite.itemCode === item?.crfFieldControl">{{
                          ite.itemName
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <!-- 列表时 -->
                  <div v-if="item.crfFieldType === 3">
                    <div
                      v-for="listItem in item.children"
                      :key="listItem.id"
                      class="questionnaire-list-module"
                    >
                      <div class="questionnaire-body-content-module-head">
                        <div class="sort">
                          ({{ listItem.dctSort }})
                          <span v-if="listItem.isRequired">(必填)</span>
                        </div>
                        <!-- 0 = 未知, 1 = 仅患者端显示, 2 = 仅医生端显示, 3 = 医生患者都显示 -->
                        <div
                          v-if="listItem?.dctQuestItemDisplayType"
                          class="check"
                        >
                          <span v-if="listItem.dctQuestItemDisplayType === 1"
                            >仅患者端显示</span
                          >
                          <span v-if="listItem.dctQuestItemDisplayType === 2"
                            >仅医生端显示</span
                          >
                          <span v-if="listItem.dctQuestItemDisplayType === 3"
                            >医生患者都显示</span
                          >
                        </div>
                        <div class="btns">
                          <span v-if="listItem.edcFieldCode" />
                          <span
                            v-if="!listItem.edcFieldCode"
                            class="btns-delete"
                            @click="setQuestionnaire('delete', listItem)"
                            >删除</span
                          >
                          <span
                            class="btns-edit"
                            @click="setQuestionnaire('editList', listItem)"
                            >编辑</span
                          >
                        </div>
                      </div>
                      <div class="questionnaire-body-content-module-items">
                        <div class="questionnaire-body-content-module-item">
                          <div
                            class="questionnaire-body-content-module-item-lable"
                          >
                            DCT中题目名称
                          </div>
                          <span
                            v-if="listItem.fieldLabel"
                            v-html="listItem.fieldLabel"
                          />
                        </div>
                        <div
                          class="questionnaire-body-content-module-item margin0"
                        >
                          <div
                            class="questionnaire-body-content-module-item-lable"
                          >
                            控件类型
                          </div>
                          <div
                            v-for="(ite, idx) in QUESTIONNAIRE_CONTENT
                              ?.DropInfos?.questFieldControl"
                            :key="idx"
                          >
                            <span
                              v-if="ite.itemCode === listItem.crfFieldControl"
                              >{{ ite.itemName }}</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 基本信息弹窗 -->
          <EditQuestionnaireInfo
            v-if="QUESTIONNAIRE_INFOS?.dialogVisible"
            :request="onLoad"
          />
        </el-tab-pane>
        <el-tab-pane
          v-if="sunNumber.includes(32) && id"
          label="知情声明"
          name="knowStatement"
        >
          <div class="margin-t-10">录制知情声明视频时，需朗读内容</div>
          <el-form-item style="width: 100%; margin-top: 20px">
            <el-input
              v-model.trim="studyICFStatementInfo.studyReciteText"
              placeholder="请输入"
              type="textarea"
              autocomplete="off"
              maxlength="100"
            />
          </el-form-item>
          <div class="footer pos-rb">
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="submitInKnow('C')"
              >保存</el-button
            >
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="sunNumber.includes(128) && id"
          label="核验身份"
          name="attestation"
        >
          <el-form
            ref="attestationRef"
            label-position="top"
            class="mt-2"
            :model="studyICFIdentityAuthentication"
            :rules="attestationRules"
          >
            <el-form-item
              label="认证方式-受试者端"
              style="width: 50%"
              prop="patientIdentityType"
            >
              <el-select
                v-model="studyICFIdentityAuthentication.patientIdentityType"
                disabled
                placeholder="请选择"
                class="w-full"
              >
                <el-option
                  label="人脸识别（北京一砂信息技术有限公司）"
                  :value="1"
                />
                <el-option label="人脸识别（法大大）" :value="2" />
              </el-select>
            </el-form-item>
          </el-form>
          <!-- <div class="flex justify-end mb-3">
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="submitInKnow('E')"
              >保存</el-button
            >
          </div> -->
        </el-tab-pane>
        <el-tab-pane
          v-if="sunNumber.includes(64)"
          label="签名"
          name="signature"
        >
          <div class="flex justify-end">
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="submitInKnow('F')"
              >保存</el-button
            >
          </div>
          <el-form
            v-if="signatureCACopy === 1"
            ref="signatureRef"
            label-position="top"
            class="mt-2"
            :model="studyICFSignature"
            :rules="signatureRules"
          >
            <div>
              <el-form-item
                label="签名方式-受试者端"
                style="width: 40%"
                prop="patientSignatureType"
              >
                <el-select
                  v-model="studyICFSignature.patientSignatureType"
                  placeholder="请选择"
                  class="w-full"
                >
                  <el-option label="手机验证码签名" :value="1" />
                  <el-option label="手写签名" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item
                label="签名方式-研究者端"
                style="width: 40%"
                prop="doctorSignatureType"
              >
                <el-select
                  v-model="studyICFSignature.doctorSignatureType"
                  placeholder="请选择"
                  class="w-full"
                >
                  <el-option label="密码签名" :value="2" />
                  <el-option label="手写签名" :value="3" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
          <el-form
            v-else-if="signatureCACopy === 2"
            ref="signatureRef"
            label-position="top"
            class="mt-2"
            :model="dctStudyICFDocControl"
            :rules="signatureRules"
          >
            <div>
              <div>
                基于输出分辨率96DPI(每英寸长度内的像素点数)，使用坐标定位需要指定盖章的文件页码，盖章点X坐标和Y坐标。以页码确定文件第几页，页面左上角确定原点，签章图片的中心点确定坐标点。其中文件页码数以1开始计算，X坐标为距离页面左侧距离，Y坐标为距离页面顶部距离。
                提供了获取签章位置工具<span
                  class="text-sky-500 cursor-pointer"
                  @click="externalClick"
                  >点击查看</span
                >
              </div>
              <div>
                签章位置工具备用地址：https://dev.fadada.com/api-doc/CFZDONY73P/NB2LFBHBUESC5CJB/5-1
              </div>
              <div class="mt-[60px]">
                参与人<span
                  v-show="jionHintShow"
                  style="color: #f56c6c"
                  class="ml-5 text-[15px]"
                  >至少需要有2位参与人</span
                >
              </div>
              <div class="flex">
                <el-form-item prop="participantArr">
                  <el-checkbox-group
                    v-model="dctStudyICFDocControl.participantArr"
                    @change="jionChange"
                  >
                    <el-checkbox :label="2">受试者</el-checkbox>
                    <div class="w-full flex">
                      <el-form-item
                        prop="subjectArr"
                        class="mr-5 min-w-[120px]"
                        :rules="
                          !dctStudyICFDocControl.participantArr.includes(2)
                            ? {
                                required: false,
                                message: '',
                                trigger: 'change',
                              }
                            : {
                                required: true,
                                message: '至少需要有1个控件',
                                trigger: 'change',
                              }
                        "
                      >
                        <el-checkbox-group
                          v-model="dctStudyICFDocControl.subjectArr"
                          class="flex flex-col ml-5"
                          @change="patientCheckChange"
                        >
                          <el-checkbox
                            :label="3"
                            :disabled="
                              !dctStudyICFDocControl?.participantArr.includes(2)
                            "
                            class="block mb-5"
                            >姓名</el-checkbox
                          >
                          <el-checkbox
                            :label="1"
                            :disabled="
                              !dctStudyICFDocControl?.participantArr.includes(2)
                            "
                            class="block mb-5"
                            >签名</el-checkbox
                          >
                          <el-checkbox
                            :label="2"
                            :disabled="
                              !dctStudyICFDocControl?.participantArr.includes(2)
                            "
                            class="block"
                            >签署日期</el-checkbox
                          >
                        </el-checkbox-group>
                      </el-form-item>
                    </div>
                    <el-checkbox :label="1" class="mt-5">研究者</el-checkbox>
                    <div class="w-full flex">
                      <el-form-item
                        prop="researchistArr"
                        class="mr-5 min-w-[120px]"
                        :rules="
                          !dctStudyICFDocControl.participantArr.includes(1)
                            ? {
                                required: false,
                                message: '',
                                trigger: 'change',
                              }
                            : [
                                {
                                  required: true,
                                  message: '至少需要有1个控件',
                                  trigger: 'change',
                                },
                              ]
                        "
                      >
                        <el-checkbox-group
                          v-model="dctStudyICFDocControl.researchistArr"
                          class="flex flex-col ml-5"
                          @change="doctorCheckChange"
                        >
                          <el-checkbox
                            :label="3"
                            :disabled="
                              !dctStudyICFDocControl?.participantArr.includes(1)
                            "
                            class="block mb-5"
                            >姓名</el-checkbox
                          >
                          <el-checkbox
                            :label="1"
                            :disabled="
                              !dctStudyICFDocControl?.participantArr.includes(1)
                            "
                            class="block mb-5"
                            >签名</el-checkbox
                          >
                          <el-checkbox
                            :label="2"
                            :disabled="
                              !dctStudyICFDocControl?.participantArr.includes(1)
                            "
                            class="block"
                            >签署日期</el-checkbox
                          >
                        </el-checkbox-group>
                      </el-form-item>
                    </div>
                  </el-checkbox-group>
                </el-form-item>
                <div class="mt-[32px]">
                  <div>
                    <div
                      v-for="(
                        item, index
                      ) in dctStudyICFDocControl.subjectDetailsArr"
                      :key="index"
                    >
                      <div class="flex items-center">
                        <span
                          class="mb-[18px] text-[14px] mx-5 whitespace-nowrap"
                          >所在页面</span
                        >
                        <el-form-item
                          :prop="`subjectDetailsArr[${index}].pageIndex`"
                          :rules="
                            (index === 0 &&
                              !dctStudyICFDocControl.subjectArr.includes(3)) ||
                            (index === 1 &&
                              !dctStudyICFDocControl.subjectArr.includes(1)) ||
                            (index === 2 &&
                              !dctStudyICFDocControl.subjectArr.includes(2))
                              ? { required: false }
                              : {
                                  required: true,
                                  validator: VALIDATEPASS,
                                  trigger: 'blur',
                                }
                          "
                        >
                          <el-input
                            v-model.trim="item.pageIndex"
                            :disabled="
                              (index === 0 &&
                                !dctStudyICFDocControl.subjectArr.includes(
                                  3
                                )) ||
                              (index === 1 &&
                                !dctStudyICFDocControl.subjectArr.includes(
                                  1
                                )) ||
                              (index === 2 &&
                                !dctStudyICFDocControl.subjectArr.includes(2))
                            "
                          />
                        </el-form-item>
                        <span
                          class="mb-[18px] text-[14px] mx-5 whitespace-nowrap"
                          >X坐标</span
                        >
                        <el-form-item
                          :prop="`subjectDetailsArr[${index}].positionX`"
                          :rules="
                            (index === 0 &&
                              !dctStudyICFDocControl.subjectArr.includes(3)) ||
                            (index === 1 &&
                              !dctStudyICFDocControl.subjectArr.includes(1)) ||
                            (index === 2 &&
                              !dctStudyICFDocControl.subjectArr.includes(2))
                              ? { required: false }
                              : {
                                  required: true,
                                  validator: VALIDATEPASS,
                                  trigger: 'blur',
                                }
                          "
                        >
                          <el-input
                            v-model.trim="item.positionX"
                            :disabled="
                              (index === 0 &&
                                !dctStudyICFDocControl.subjectArr.includes(
                                  3
                                )) ||
                              (index === 1 &&
                                !dctStudyICFDocControl.subjectArr.includes(
                                  1
                                )) ||
                              (index === 2 &&
                                !dctStudyICFDocControl.subjectArr.includes(2))
                            "
                          />
                        </el-form-item>
                        <span
                          class="mb-[18px] text-[14px] mx-5 whitespace-nowrap"
                          >Y坐标</span
                        >
                        <el-form-item
                          :prop="`subjectDetailsArr[${index}].positionY`"
                          :rules="
                            (index === 0 &&
                              !dctStudyICFDocControl.subjectArr.includes(3)) ||
                            (index === 1 &&
                              !dctStudyICFDocControl.subjectArr.includes(1)) ||
                            (index === 2 &&
                              !dctStudyICFDocControl.subjectArr.includes(2))
                              ? { required: false }
                              : {
                                  required: true,
                                  validator: VALIDATEPASS,
                                  trigger: 'blur',
                                }
                          "
                        >
                          <el-input
                            v-model.trim="item.positionY"
                            :disabled="
                              (index === 0 &&
                                !dctStudyICFDocControl.subjectArr.includes(
                                  3
                                )) ||
                              (index === 1 &&
                                !dctStudyICFDocControl.subjectArr.includes(
                                  1
                                )) ||
                              (index === 2 &&
                                !dctStudyICFDocControl.subjectArr.includes(2))
                            "
                          />
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                  <div class="mt-[36px]">
                    <div
                      v-for="(
                        item, index
                      ) in dctStudyICFDocControl.researchistDetailsArr"
                      :key="index"
                    >
                      <div class="flex items-center">
                        <span
                          class="mb-[18px] text-[14px] mx-5 whitespace-nowrap"
                          >所在页面</span
                        >
                        <el-form-item
                          :prop="`researchistDetailsArr[${index}].pageIndex`"
                          :rules="
                            (index === 0 &&
                              !dctStudyICFDocControl.researchistArr.includes(
                                3
                              )) ||
                            (index === 1 &&
                              !dctStudyICFDocControl.researchistArr.includes(
                                1
                              )) ||
                            (index === 2 &&
                              !dctStudyICFDocControl.researchistArr.includes(2))
                              ? { required: false }
                              : {
                                  required: true,
                                  validator: VALIDATEPASS,
                                  trigger: 'blur',
                                }
                          "
                        >
                          <el-input
                            v-model.trim="item.pageIndex"
                            :disabled="
                              (index === 0 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  3
                                )) ||
                              (index === 1 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  1
                                )) ||
                              (index === 2 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  2
                                ))
                            "
                          />
                        </el-form-item>
                        <span
                          class="mb-[18px] text-[14px] mx-5 whitespace-nowrap"
                          >X坐标</span
                        >
                        <el-form-item
                          :prop="`researchistDetailsArr[${index}].positionX`"
                          :rules="
                            (index === 0 &&
                              !dctStudyICFDocControl.researchistArr.includes(
                                3
                              )) ||
                            (index === 1 &&
                              !dctStudyICFDocControl.researchistArr.includes(
                                1
                              )) ||
                            (index === 2 &&
                              !dctStudyICFDocControl.researchistArr.includes(2))
                              ? { required: false }
                              : {
                                  required: true,
                                  validator: VALIDATEPASS,
                                  trigger: 'blur',
                                }
                          "
                        >
                          <el-input
                            v-model.trim="item.positionX"
                            :disabled="
                              (index === 0 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  3
                                )) ||
                              (index === 1 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  1
                                )) ||
                              (index === 2 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  2
                                ))
                            "
                          />
                        </el-form-item>
                        <span
                          class="mb-[18px] text-[14px] mx-5 whitespace-nowrap"
                          >Y坐标</span
                        >
                        <el-form-item
                          :prop="`researchistDetailsArr[${index}].positionY`"
                          :rules="
                            (index === 0 &&
                              !dctStudyICFDocControl.researchistArr.includes(
                                3
                              )) ||
                            (index === 1 &&
                              !dctStudyICFDocControl.researchistArr.includes(
                                1
                              )) ||
                            (index === 2 &&
                              !dctStudyICFDocControl.researchistArr.includes(2))
                              ? { required: false }
                              : {
                                  required: true,
                                  validator: VALIDATEPASS,
                                  trigger: 'blur',
                                }
                          "
                        >
                          <el-input
                            v-model.trim="item.positionY"
                            :disabled="
                              (index === 0 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  3
                                )) ||
                              (index === 1 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  1
                                )) ||
                              (index === 2 &&
                                !dctStudyICFDocControl.researchistArr.includes(
                                  2
                                ))
                            "
                          />
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          v-if="signatureCACopy === 3 && id"
          label="知情内容填写&签名"
          name="knowQuestionnaireAndSignature"
        >
          <div>
            <div class="flex items-center">
              <el-button
                type="primary"
                @click="previewKnowQuestionnaireAndSignature(0)"
                >配置模板</el-button
              >
              <el-button
                type="primary"
                @click="previewKnowQuestionnaireAndSignature(1)"
                >预览</el-button
              >
              <el-button type="primary" class="mr-[20px]" @click="onLoad"
                >刷新</el-button
              >
              <span class="text-[#F59C28]"
                >配置时，会将当前模板设为停用状态，若此时恰好有新的知情流程需要使用当前模板来创建签署任务，则会提示研究者稍后再操作</span
              >
            </div>
            <div class="my-[20px]">
              <p>
                <span>模板名称：</span>
                <span>{{
                  templateDetailsObj.icfSignTemplate?.templateName
                }}</span>
              </p>
              <p>
                <span>模板ID：</span>
                <span>{{
                  templateDetailsObj.icfSignTemplate?.templateId
                }}</span>
              </p>
              <p>
                <span>创建人：</span>
                <span>{{ templateDetailsObj.icfSignTemplate?.creator }}</span>
              </p>
              <p class="mb-[5px]">
                <span>最近更新时间：</span>
                <span>{{
                  templateDetailsObj.icfSignTemplate?.updateTime
                }}</span>
              </p>
              <div class="centerflex-h">
                <span>状态：</span>
                <span
                  v-if="
                    templateDetailsObj?.icfSignTemplate?.templateStatus ===
                    '草稿'
                  "
                  >草稿</span
                >
                <el-switch
                  v-else
                  v-model="
                    templateDetailsObj.icfSignTemplate.templateStatusFlag
                  "
                  size="large"
                  active-text="启用"
                  inactive-text="停用"
                  @change="handleChangeStatus"
                />
              </div>
            </div>
            <p class="text-[#D9001B] text-[16px]" style="font-weight: 400">
              <span class="leading-[30px]">注意事项：</span><br />
              <span class="leading-[30px]">
                1、对于同一份知情文件，请务必保障在知情同意书页签下上传的文件名称，与配置模板时从本地上传的文件名称一致，否则将无法做映射。
              </span>
              <br />
              <span class="leading-[30px]">
                2、某些相对固定的内容（例如中心伦理电话），若不希望让用户填写，在配置该控件的参与方时，可选择“未设置”，并使用固定的控件名称，当发起签署任务时将由系统自动填充内容。此类填充内容比较特殊，如需新增请联系技术协助处理。
              </span>
              <br />
              <span class="leading-[30px]"
                >2.1、若需合成中心伦理电话，请将控件名称设为：中心伦理电话</span
              >
              <br />
              <span class="leading-[30px]"
                >3、配置参与方名称时，必须使用指定名称（受试者、监护人、公正见证人、研究者），否则将无法与DCT的用户做映射。</span
              >
            </p>
          </div>
        </el-tab-pane>
      </el-tabs>
      <el-button size="large" @click="backRefresh">返回</el-button>
    </div>
    <!-- 新建编辑课题 -->
    <EditQuestionnaireContent
      v-if="QUESTIONNAIRE_CONTENT?.contentVisible"
      :request="onLoad"
    />
  </div>
</template>
<script lang="ts">
import { getshu } from '@trialdata/common-fun-css'
import {
  defineComponent,
  reactive,
  toRefs,
  inject,
  onMounted,
  provide,
  nextTick,
} from 'vue'
import { useStore } from 'vuex'
import MyUpload from '@/components/Upload/index.vue'
import VueQr from 'vue-qr/src/packages/vue-qr.vue'
import EditQuestionnaireInfo from '@/views/informedManagement/EditQuestionnaireInfo.vue'
import EditQuestionnaireContent from '@/views/informedManagement/EditQuestionnaireContent.vue'

import {
  postTemplate, // 保存/编辑知情同意书信息
  postTemplateFile, // 上传文件
  getTemplateDetails, // 获取详情页数据
  postSaveStudyICFVideosInfo,
  deleteStudyICFVideosInfo,
  deleteDeleteStudyICFVersion,
  postSignTemplateSetStatus,
  getSignTemplate,
} from '@/api/informedManagement'
import {
  getQuestTemplateItemDropInfo,
  deleteQuestTemplateItem,
  getQuestTemplateDropInfo,
} from '@/api/home'
import { deepClone } from '@/utils'
import { Delete } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'InformedConsentDetails', // 知情同意书详情
  components: {
    MyUpload,
    VueQr,
    EditQuestionnaireInfo,
    EditQuestionnaireContent,
    Delete,
  },
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const VALIDATEPASS = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入'))
      } else if (!/^[1-9]\d*$/.test(value)) {
        callback(new Error('请输入正整数!'))
      } else {
        callback()
      }
    }
    // const router = useRouter()
    const QUESTIONNAIRE_INFOS = reactive({
      id: null,
      questionnaireInfo: {
        crfGuideline: '',
        crfName: '',
        dctModuleContainerId: null,
        id: null,
        isSynchronization: 0,
        moduleContainer: null,
        questDisplayType: 2,
        questPatientTemplateType: 6,
        questTemplateItem: [],
        questTemplateType: 1,
        editOrAddList: '',
      },
      dialogVisible: false,
      DropInfos: {},
      templateDropInfos: {},
    })
    const QUESTIONNAIRE_CONTENT = reactive({
      questionnaireContent: {},
      questionnaireContentViews: {},
      contentVisible: false,
      DropInfos: {},
      templateDropInfos: {},
      versionsValue: 'ICF',
    })

    const state = reactive({
      // 总数据
      templateDetailsObj: {},
      incrementedMax: 0,
      signatureCA: 1,
      signatureCACopy: 1,
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          'uploadImage', // 上传图片
          'uploadVideo', // 上传视频
        ],
      },
      InformedConsentDetailsUploadRef: null,
      StatementImgUrlRef: null,
      StatementVideoUrlRef: null,
      loading: false,
      fileList: [],
      fileList2: [],
      sunNumber: [],
      sunNumberList: [2, 64],
      editionIndex: 0,
      versionsValue: '',
      versionsOptions: [],
      versionsObj: {}, // 当前版本对象
      previewDialogVisible: false, // 扫码预览显示隐藏
      postTemplateFile,
      studyKnowReadingTime: 10,
      studyVideoViewSeconds: 10,
      studyVideoUrl: '',
      studyVideoCoverUrl: '',
      editorHome: null,
      contentHtml: '',
      siteUrl: '',
      id: '',
      formLabelWidth: '140px',
      activeName: 'KnowTypes', // tab高亮
      knowTypesRef: null, // 知情流程
      knowBookRef: null, // 知情同意书
      knowVideoRef: null, // 知情视频
      radio: '1',
      flowArr: [
        {
          title: '知情视频',
          disabled: false,
          additional: 1,
          showSignatureStrategy: [1, 2, 3],
        },
        {
          title: '阅读知情同意',
          disabled: true,
          additional: 2,
          showSignatureStrategy: [1, 2, 3],
        },
        {
          title: '研究者讲解知情',
          disabled: false,
          additional: 4,
          showSignatureStrategy: [1, 2, 3],
        },
        {
          title: '知情内容填写',
          disabled: false,
          additional: 8,
          showSignatureStrategy: [1, 2],
          subOptions: [
            {
              title: '需审核',
              disabled: false,
              additional: 16,
            },
          ],
        },
        {
          title: '录制知情声明',
          disabled: false,
          additional: 32,
          showSignatureStrategy: [1],
        },
        {
          title: '核验身份',
          disabled: false,
          additional: 128,
          showSignatureStrategy: [1],
        },
        {
          title: '签名',
          disabled: true,
          additional: 64,
          showSignatureStrategy: [1, 2],
        },
        {
          title: '知情内容填写（按需）&签名',
          disabled: true,
          additional: 256,
          showSignatureStrategy: [3],
        },
      ],
      hanldeFlowOptionChange: (e, item, parent) => {
        if (!e && !parent) {
          let arr = item.subOptions || []
          arr.forEach((flow) => {
            state.sunNumberList = state.sunNumberList.filter((i) => {
              return i !== flow.additional
            })
          })
        }
        if (e && parent && !state.sunNumberList.includes(parent.additional)) {
          state.sunNumberList.push(parent.additional)
        }
      },
      sunNum: 0,
      // 上传文件
      customInformationFormData: {
        customInformationFormDataObj: {
          icfFilePath: '', // 文件声明路径
          icfFileName: '', // 文件名
          icfFileUrl: '', // 文件访问路径
        },
      },
      // 上传图片
      customInformation: {
        icfFileUrl: '',
        avatarUrl: '',
        projectContent: '',
        completeResearchConclusion: '',
      },
      // 是否必须选项
      mustOrNotOptions: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      // 流程类型
      isRequiredOptions: [
        {
          value: 2,
          label: '需电子签署',
        },
        {
          value: 1,
          label: '无需电子签署',
        },
      ],
      // 知情同意书形式
      knowBooks: [
        {
          value: 1,
          label: 'PDF',
        },
        {
          value: 2,
          label: '富文本',
        },
      ],
      rules: {
        displayOrder: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^[0-9]\d*$/,
            message: '请输入整数',
            trigger: 'blur',
          },
        ],
        icfVersionNumber: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        icfVersionDate: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        flowType: [{ required: true, message: '请输入', trigger: 'blur' }],
        shortestDuration: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        knowBooksListValue: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        isRequired: [{ required: true, message: '请选择', trigger: 'blur' }],
      },
      studyICFBaseInfo: {
        icfVersionNumber: '', // 版本名称
        icfVersionDate: '', // 版本日期
        flowType: 2, // 流程类型
        studyKnowReadingTime: null, // 默认阅读时间
        studyICF: '', // 富文本内容
        statementFilePath: '', // PDF声明的文件路径
        statementFileName: '', // PDF声明的文件名字
        statementFileUrl: '', // PDF声明的的访问路径
        icfFeature: null, // 知情视频：0x1、研究者讲解知情：0x4、知情问卷：0x8、录制知情声明：0x20
        knowBooksListValue: 1, // 知情同意书形式默认选择PDF
        signatureStrategy: 1, // 签名认证方式
      }, // 知情同意书信息
      studyICFVideoInfo: {
        studyVideoViewSeconds: null, // 知情视频阅读时长设置
        videoFilePath: '', // 知情视频的路径
        videoFileName: '', // 知情视频的文件名字
        videoUrl: '', // 知情视频的URL
        videoCoverFilePath: '', // 知情视频的封面路径
        videoCoverFileName: '', // 知情视频的封面文件名字
        videoCoverUrl: '', // 知情视频的封面URL
        displayOrder: 0, // 排序
        title: '', // 标题
        id: '',
      }, // 知情视频
      studyICFExplainInfo: {
        explainICFType: '1',
      }, // 讲解知情
      icfQuestTemplateInfo: {}, // 知情问卷
      studyICFStatementInfo: {
        studyReciteText: '', // 知情的朗读的文本
      }, // 知情声明
      beforeFileUploadType: ['.pdf'],
      // 身份认证
      attestationRef: null,
      studyICFIdentityAuthentication: {
        patientIdentityType: 1,
      },
      attestationRules: {
        patientIdentityType: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
      },
      // 签名
      signatureRef: null,
      studyICFSignature: {
        patientSignatureType: 1,
        doctorSignatureType: 2,
      },
      dctStudyICFDocControl: {
        participantArr: [1, 2],
        subjectArr: [1, 2, 3],
        researchistArr: [1, 2, 3],
        subjectDetailsArr: [
          {
            pageIndex: null,
            positionY: null,
            positionX: null,
            docControl: 3,
          },
          {
            pageIndex: null,
            positionY: null,
            positionX: null,
            docControl: 1,
          },
          {
            pageIndex: null,
            positionY: null,
            positionX: null,
            docControl: 2,
          },
        ],
        researchistDetailsArr: [
          {
            pageIndex: null,
            positionY: null,
            positionX: null,
            docControl: 3,
          },
          {
            pageIndex: null,
            positionY: null,
            positionX: null,
            docControl: 1,
          },
          {
            pageIndex: null,
            positionY: null,
            positionX: null,
            docControl: 2,
          },
        ],
      },
      jionHintShow: false,
      signatureRules: {
        patientSignatureType: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        doctorSignatureType: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        // subjectArr: [{ required: true, message: '至少需要有1个控件', trigger: 'change' }],
        // researchistArr: [{ required: true, message: '至少需要有1个控件', trigger: 'change' }],
      },
      // 知情列表
      getKnowVideoist: () => {
        return {
          data: [],
        }
      },
      KnowVideoTableRef: null,
      knowInfosTableRef: null,
      knowVideoDialogShow: false,
      myTitle: '新增',
      columns: [
        { label: '排序', prop: 'sort', minWidth: 120 },
        { label: '视频标题', prop: 'title', minWidth: 260 },
        {
          label: '最短观看时长(秒)',
          prop: 'studyVideoViewSeconds',
          minWidth: 160,
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      knowInfosTableColumns: [
        { label: '排序', prop: 'displayOrder', minWidth: 120 },
        { label: '版本名称', prop: 'icfVersionNumber', minWidth: 260 },
        { label: '版本日期', prop: 'icfVersionDate', minWidth: 160 },
        { label: '是否必需', tdSlot: 'isRequiredStr', minWidth: 120 },
        { label: '最短阅读时长', prop: 'shortestDuration', minWidth: 120 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 状态改变
      handleChangeStatus: (e) => {
        if (state.templateDetailsObj.icfSignTemplate?.templateId) {
          postSignTemplateSetStatus(
            state.templateDetailsObj.icfSignTemplate?.templateId,
            { enableStatus: e }
          ).then(() => {
            ElMessage.success({
              showClose: true,
              message: '修改成功',
            })
            state.onLoad()
          })
        }
      },
      knowVideoules: {
        sort: [{ required: true, message: '请输入', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        studyVideoViewSeconds: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
      },
      deleteVideoBtn: (row) => {
        // 删除
        ElMessageBox.confirm('是否确认删除？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            state.loading = true
            // 调用接口
            deleteStudyICFVideosInfo(row?.id)
              .then(() => {
                ElMessage.success({
                  showClose: true,
                  message: '删除成功',
                })
                state.onLoad()
                state.loading = false
              })
              .catch(() => {
                state.loading = false
              })
          })
          .catch(() => {})
      },
      // 新增-编辑-删除 多版本知情
      knowWrittenConsentDialogShow: false,
      knowAddOrEditWrittenConsentBtn: (row) => {
        if (row) {
          state.myTitle = '编辑'
          state.studyICFBaseInfo = deepClone(row)
          state.fileList = [
            {
              name: state.studyICFBaseInfo.statementFileName,
              url: state.studyICFBaseInfo.statementFileUrl,
            },
          ]
          //
        } else {
          state.studyICFBaseInfo = {
            ...state.studyICFBaseInfo,
            shortestDuration: '',
            icfVersionDate: '',
            icfVersionNumber: '',
            signatureStrategy: 1,
            displayOrder: state.templateDetailsObj.icfVersions.length
              ? null
              : 1,
            isRequired: 1,
            id: null,
          }
          state.fileList.length = 0
          // state.InformedConsentDetailsUploadRef.fileList.length = 0
          state.myTitle = '新增'
        }
        state.knowWrittenConsentDialogShow = true
      },
      deleteWrittenConsentBtn: (row) => {
        let isRequiredNum = 0
        state.templateDetailsObj.icfVersions.map((e) => {
          isRequiredNum += e.isRequired ? 1 : 0
        })
        if (
          state.templateDetailsObj.icfVersions.length < 2 ||
          isRequiredNum < 2
        ) {
          ElMessage.warning({
            message: '最少有1个必需的知情版本',
          })
          return
        }
        // 删除
        ElMessageBox.confirm('是否确认删除？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            state.loading = true
            // 调用接口
            deleteDeleteStudyICFVersion(row?.id)
              .then(() => {
                ElMessage.success({
                  showClose: true,
                  message: '删除成功',
                })
                state.onLoad()
                state.loading = false
              })
              .catch(() => {
                state.loading = false
              })
          })
          .catch(() => {})
      },
      // 知情视频相关
      knowVideoBtn: (row) => {
        if (row) {
          state.myTitle = '编辑'
          state.studyICFVideoInfo = deepClone(row)
          state.customInformation.icfFileUrl = row?.videoCoverUrl || ''
          if (row.videoFileName) {
            state.fileList2 = [
              {
                name: row.videoFileName,
                url: row.videoUrl,
              },
            ]
          }
          // 吧下边处理的拿出来
        } else {
          state.studyICFVideoInfo = {
            studyVideoViewSeconds: null, // 知情视频阅读时长设置
            videoFilePath: '', // 知情视频的路径
            videoFileName: '', // 知情视频的文件名字
            videoUrl: '', // 知情视频的URL
            videoCoverFilePath: '', // 知情视频的封面路径
            videoCoverFileName: '', // 知情视频的封面文件名字
            videoCoverUrl: '', // 知情视频的封面URL
            sort: state.incrementedMax, // 排序
            title: '', // 标题
            id: '',
          }
          state.fileList2 = []
          state.myTitle = '新增'
        }
        state.knowVideoDialogShow = true
        nextTick(() => {
          if (state?.StatementImgUrlRef) {
            state.StatementImgUrlRef.imageUrl = row?.videoCoverUrl || ''
          }
        })
      },
      deleteVideoCover: () => {
        // 删除视频封面
        state.studyICFVideoInfo.videoCoverFilePath = ''
        state.studyICFVideoInfo.videoCoverFileName = ''
        state.studyICFVideoInfo.videoCoverUrl = ''
        state.StatementImgUrlRef.imageUrl = ''
      },
      knowVideoDialogCancel: () => {
        state.knowVideoDialogShow = false
      },
      knowVideoDialogConfirm: () => {
        if (!state.studyICFVideoInfo?.videoUrl) {
          ElMessage.warning({
            showClose: true,
            message: '请完善知情视频',
          })
          return
        }
        if (
          state.KnowVideoTableRef?.tableData &&
          Array.isArray(state.KnowVideoTableRef?.tableData)
        ) {
          // 判断不让他排序重复
          const show = state.KnowVideoTableRef?.tableData.some(
            (item) =>
              item.sort == state.studyICFVideoInfo?.sort &&
              item.id != state.studyICFVideoInfo?.id
          )
          if (show) {
            ElMessage.warning({
              showClose: true,
              message: '排序不能重复',
            })
            return
          }
        }
        state.knowVideoRef.validate((valid) => {
          if (valid) {
            state.studyICFVideoInfo.icfInfoId = state.id
            state.loading = true
            postSaveStudyICFVideosInfo({ ...state.studyICFVideoInfo })
              .then((res) => {
                ElMessage.success({
                  showClose: true,
                  message: '保存成功',
                })
                state.knowVideoDialogShow = false
                state.loading = false
                state.onLoad()
              })
              .catch(() => {
                state.loading = false
              })
          }
        })
      },
      // 参与人提示显示
      jionChange: (val) => {
        if (!val.includes(1)) {
          state.dctStudyICFDocControl.researchistArr = []
          state.doctorCheckChange([])
        } else if (!val.includes(2)) {
          state.dctStudyICFDocControl.subjectArr = []
          state.patientCheckChange([])
        }
        if (state.dctStudyICFDocControl.participantArr.length > 1) {
          state.jionHintShow = false
        } else {
          state.jionHintShow = true
        }
      },
      patientCheckChange: (val) => {
        state.resetValue(val, state.dctStudyICFDocControl.subjectDetailsArr)
      },
      doctorCheckChange: (val) => {
        state.resetValue(val, state.dctStudyICFDocControl.researchistDetailsArr)
      },
      resetValue: (val, arr) => {
        arr.forEach((item) => {
          if (
            (!val.includes(1) && item.docControl === 1) ||
            (!val.includes(2) && item.docControl === 2) ||
            (!val.includes(3) && item.docControl === 3)
          ) {
            item.pageIndex = null
            item.positionX = null
            item.positionY = null
          }
        })
      },
      // 跳转法大大链接
      externalClick: () => {
        window.open('https://uat-dev.fadada.com/demo/sign/position')
      },
      setQuestionnaire: (flag, item) => {
        if (flag === 'delete') {
          ElMessageBox.confirm(`是否确认删除？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              deleteQuestTemplateItem(item.id).then(() => {
                ElMessage.success(`删除成功!`)
                state.onLoad()
              })
            })
            .catch(() => {})
        } else if (flag === 'edit' || flag === 'editList') {
          // 编辑
          QUESTIONNAIRE_CONTENT.questionnaireContent = item
          QUESTIONNAIRE_CONTENT.editOrAddList = flag
          if (
            QUESTIONNAIRE_CONTENT.questionnaireContent.specialFieldType === 0
          ) {
            QUESTIONNAIRE_CONTENT.questionnaireContent.specialFieldType = null
          }
          QUESTIONNAIRE_CONTENT.contentVisible = true
        } else if (flag === 'add' || flag === 'addList') {
          if (!state?.icfQuestTemplateInfo?.id) {
            ElMessage.error('您还未填写基本信息，请先填写基本信息呦~')
            return
          }
          // 新增
          QUESTIONNAIRE_CONTENT.editOrAddList = flag
          QUESTIONNAIRE_CONTENT.questionnaireContent = {
            crfFieldControl: null,
            crfFieldType: null,
            dctCode: '',
            studyId: store.state.studyItem.studyId,
            dctQuestItemDisplayType: null, // 3
            dctQuestTemplateId: state.icfQuestTemplateInfo.id,
            dctQuestUnit: null,
            dctSort: null,
            fieldDescription: '',
            fieldLabel: '',
            id: null,
            isRequired: 0,
            questTemplateOption: [],
            refDctCode: '',
            refItemValue: '',
            refType: 0,
            specialFieldType: null,
            parentId: item?.id || '',
            minimumYear: '1900',
            maximumYear: '2099',
            isAllowUK: 0,
            isFutureDate: 1,
          }
          QUESTIONNAIRE_CONTENT.contentVisible = true
        }
      },
      // ca切换
      radioCAChange: (val) => {
        if (val === 1) {
          state.sunNumberList = state.sunNumberList.filter(
            (element) => element !== 256
          )
          if (!state.sunNumberList.includes(64)) {
            state.sunNumberList.push(64)
          }
        } else if (val === 2) {
          state.sunNumberList = state.sunNumberList.filter(
            (element) => element !== 32 && element !== 128
          )
          if (!state.sunNumberList.includes(64)) {
            state.sunNumberList.push(64)
          }
        } else if (val === 3) {
          state.sunNumberList = state.sunNumberList.filter((element) =>
            [1, 2, 4, 256].includes(element)
          )
          if (!state.sunNumberList.includes(256)) {
            state.sunNumberList.push(256)
          }
        }
        // console.log(state.sunNumberList, 'state.sunNumberList')
        // state.flowArr.forEach((item) => {
        //   if (item.additional === 32 || item.additional === 128) {
        //     if (val === 1) {
        //       item.disabled = false
        //     } else if (val === 2) {
        //       item.disabled = true
        //     }
        //   }
        // })
      },
      // 点击tab
      tabsHandleClick: (tab: string, event: Event) => {
        switch (tab.index) {
          case '0':
            break
          case '1':
            // nextTick(() => {
            //   if (state?.StatementImgUrlRef) {
            //     state.StatementImgUrlRef.imageUrl =
            //       state.customInformation?.icfFileUrl
            //   }
            // })
            // if (state.relevanceObj?.refType === 0) {
            //   state.relevanceObj.refType = null
            // }
            // state.cleanQuestTemplateArr(state.questionnaireContent.id)
            break
          case '2':
            // if (state.questTemplateOptionArr.length) {
            //   break
            // }
            // state.getQuestTemplateOptionList()
            break
          // default: //
        }
      },
      // 返回
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      // 切换流程类型
      submitProcess: (val) => {
        if (val === 2 && !state.sunNumberList.includes(64)) {
          state.sunNumberList.push(64)
        }
      },
      // 上传图片
      fileImg: (res) => {
        state.studyICFVideoInfo.videoCoverFilePath = res.icfFilePath
        state.studyICFVideoInfo.videoCoverFileName = res.icfFileName
        state.studyICFVideoInfo.videoCoverUrl = res.icfFileUrl
      },
      // 上传文件
      fileFun: (fileObj) => {
        const myFormDataObj = new FormData()
        const fileName = fileObj.file.name
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        postTemplateFile(store.state.studyItem.studyId, myFormDataObj)
          .then((res) => {
            loading.close()
            ElMessage.success('上传成功')
            state.studyICFBaseInfo.statementFilePath = res.icfFilePath
            state.studyICFBaseInfo.statementFileName = res.icfFileName
            state.studyICFBaseInfo.statementFileUrl = res.icfFileUrl
            if (state.InformedConsentDetailsUploadRef.fileList.length === 0) {
              state.InformedConsentDetailsUploadRef.fileList.push({
                name: fileName,
                url: state.studyICFBaseInfo.statementFileUrl,
              })
            }
          })
          .catch(() => {
            state.InformedConsentDetailsUploadRef.fileList.length = 0
            state.studyICFBaseInfo.statementFilePath = ''
            state.studyICFBaseInfo.statementFileName = ''
            state.studyICFBaseInfo.statementFileUrl = ''
            loading.close()
          })
      },
      // 删除文件
      deletefile: () => {
        state.InformedConsentDetailsUploadRef.fileList.length = 0
        state.studyICFBaseInfo.statementFilePath = ''
        state.studyICFBaseInfo.statementFileName = ''
        state.studyICFBaseInfo.statementFileUrl = ''
      },
      // 上传视频
      fileFun3: (fileObj) => {
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        const myFormDataObj = new FormData()
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        postTemplateFile(store.state.studyItem.studyId, myFormDataObj)
          .then((res) => {
            loading.close()
            ElMessage.success('上传成功')
            state.studyICFVideoInfo.videoFilePath = res.icfFilePath
            state.studyICFVideoInfo.videoFileName = res.icfFileName
            state.studyICFVideoInfo.videoUrl = res.icfFileUrl
            if (state.StatementVideoUrlRef.fileList.length === 0) {
              state.StatementVideoUrlRef.fileList.push({
                name: state.studyICFVideoInfo.videoFileName,
                url: state.studyICFVideoInfo.videoUrl,
              })
            }
          })
          .catch(() => {
            state.StatementVideoUrlRef.fileList.length = 0
            state.studyICFVideoInfo.videoFilePath = ''
            state.studyICFVideoInfo.videoFileName = ''
            state.studyICFVideoInfo.videoUrl = ''
            loading.close()
          })
      },
      // 删除视频
      deletefile3: () => {
        state.StatementVideoUrlRef.fileList.length = 0
        state.studyICFVideoInfo.videoFilePath = ''
        state.studyICFVideoInfo.videoFileName = ''
        state.studyICFVideoInfo.videoUrl = ''
      },
      // 保存知情同意书(A)or知情视频(B)or知情声明(c)
      submitInKnow: (flag) => {
        if (flag === '-1') {
          // 知情流程
          state.knowTypesRef.validate((valid) => {
            if (valid) {
              state.loading = true
              const icfFeature =
                state.studyICFBaseInfo.flowType === 1
                  ? 2
                  : state.sunNumberList.reduce((item, ite) => {
                      return Number(item) + Number(ite)
                    })
              const infoObject = {
                id: state.id,
                studyICFBaseInfo: {
                  flowType: state.studyICFBaseInfo.flowType, // 流程类型
                  icfFeature, // 知情视频：0x1、研究者讲解知情：0x4、知情问卷：0x8、录制知情声明：0x20
                  signatureStrategy:
                    state.studyICFBaseInfo.flowType === 1
                      ? 0
                      : state.studyICFBaseInfo.signatureStrategy,
                },
              }
              postTemplate(store.state.studyItem.studyId, infoObject)
                .then((res) => {
                  ElMessage.success('保存成功')
                  state.loading = false
                  state.id = res.id
                  RESEARCHCENTER_INFOS.researchContent.id = res.id
                  state.sunNum = 0
                  state.onLoad()
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        }
        if (flag === 'A') {
          // 知情同意书的保存
          // 无需时 / 知情同意书形式pdf 但没传
          // console.log(state.templateDetailsObj.studyICFBaseInfo.signatureStrategy,'')
          if (
            ((state.studyICFBaseInfo.flowType === 1 &&
              state.studyICFBaseInfo.knowBooksListValue === 1) ||
              state.templateDetailsObj.studyICFBaseInfo.signatureStrategy) &&
            !state.studyICFBaseInfo?.statementFilePath
          ) {
            ElMessage.warning({
              showClose: true,
              message: '请完善知情同意书基本信息',
            })
            return
          }
          state.knowBookRef.validate((valid) => {
            // console.log(valid, state.studyICFBaseInfo, 'A valid', state.knowBookRef)
            if (valid) {
              if (
                state.studyICFBaseInfo.flowType === 1 &&
                state.studyICFBaseInfo.knowBooksListValue === 2
              ) {
                state.contentHtml = state.editorHome.gainTxt('set')
                state.studyICFBaseInfo.studyICF =
                  state.editorHome.gainTxt('set')
                if (!state.studyICFBaseInfo.studyICF) {
                  ElMessage.warning({
                    showClose: true,
                    message: '请完善知情同意书基本信息',
                  })
                  return
                }
                const infoObject = {
                  id: state.id,
                  icfVersions: [state.studyICFBaseInfo],
                }
                state.loading = true
                postTemplate(store.state.studyItem.studyId, infoObject)
                  .then((res) => {
                    ElMessage.success('保存成功')
                    RESEARCHCENTER_INFOS.researchContent.id = res.id
                    state.id = res.id
                    state.sunNum = 0
                    state.loading = false
                    state.onLoad()
                  })
                  .catch(() => {
                    state.loading = false
                  })
                return
              }
              if (state.studyICFBaseInfo.flowType === 2) {
                state.sunNum = state.sunNumberList.reduce((item, ite) => {
                  return Number(item) + Number(ite)
                })
                state.studyICFBaseInfo.icfFeature = state.sunNum
                const infoObject = {
                  id: state.id,
                  icfVersions: [state.studyICFBaseInfo],
                  studyICFExplainInfo: state.studyICFExplainInfo,
                }
                state.loading = true
                if (state.sunNumberList.includes(4)) {
                  postTemplate(store.state.studyItem.studyId, infoObject)
                    .then((res) => {
                      ElMessage.success('保存成功')
                      state.id = res.id
                      RESEARCHCENTER_INFOS.researchContent.id = res.id
                      state.sunNum = 0
                      state.onLoad()
                      state.loading = false
                      state.knowWrittenConsentDialogShow = false
                    })
                    .catch(() => {
                      state.loading = false
                    })
                } else {
                  infoObject.studyICFExplainInfo = {}
                  postTemplate(store.state.studyItem.studyId, infoObject)
                    .then((res) => {
                      ElMessage.success('保存成功')
                      state.id = res.id
                      RESEARCHCENTER_INFOS.researchContent.id = res.id
                      state.sunNum = 0
                      state.onLoad()
                      state.loading = false
                      state.knowWrittenConsentDialogShow = false
                    })
                    .catch(() => {
                      state.loading = false
                    })
                }
              } else {
                // 第三种
                if (
                  !state.studyICFBaseInfo?.isRequired &&
                  state.templateDetailsObj.icfVersions?.length &&
                  state.templateDetailsObj.icfVersions.filter(
                    (e) => e.isRequired
                  ).length < 1
                ) {
                  ElMessage.warning('至少有一个必需的知情同意书')
                  return
                }
                const infoObject = {
                  id: state.id,
                  icfVersions: [state.studyICFBaseInfo],
                }
                postTemplate(store.state.studyItem.studyId, infoObject).then(
                  (res) => {
                    ElMessage.success('保存成功')
                    state.id = res.id
                    RESEARCHCENTER_INFOS.researchContent.id = res.id
                    state.sunNum = 0
                    state.onLoad()
                    state.knowWrittenConsentDialogShow = false
                  }
                )
              }
            }
          })
        } else if (flag === 'B') {
          // 以前的, 知情多段视频之后就不用了
          if (
            !state.studyICFVideoInfo?.studyVideoViewSeconds ||
            !state.studyICFVideoInfo?.videoFilePath
          ) {
            ElMessage.warning({
              showClose: true,
              message: '请完善知情视频基本信息',
            })
          } else {
            const infoObject = {
              id: state.id,
              studyICFVideoInfo: state.studyICFVideoInfo,
            }
            state.loading = true
            postTemplate(store.state.studyItem.studyId, infoObject)
              .then(() => {
                ElMessage.success('保存成功')
                state.loading = false
                state.onLoad()
              })
              .catch(() => {
                state.loading = false
              })
          }
        } else if (flag === 'C') {
          const infoObject = {
            id: state.id,
            studyICFStatementInfo: {
              studyReciteText: state.studyICFStatementInfo.studyReciteText,
            },
          }
          state.loading = true
          postTemplate(store.state.studyItem.studyId, infoObject)
            .then(() => {
              ElMessage.success('保存成功')
              state.loading = false
              state.onLoad()
            })
            .catch(() => {
              state.loading = false
            })
        } else if (flag === 'D') {
          const infoObject = {
            id: state.id,
            studyICFExplainInfo: {
              explainICFType: state.studyICFExplainInfo.explainICFType,
            },
          }
          state.loading = true
          postTemplate(store.state.studyItem.studyId, infoObject)
            .then(() => {
              ElMessage.success('保存成功')
              state.loading = false
              state.onLoad()
            })
            .catch(() => {
              state.loading = false
            })
        } else if (flag === 'E') {
          state.attestationRef.validate((valid) => {
            if (valid) {
              const infoObject = {
                id: state.id,
                studyICFIdentityAuthentication: {
                  ...state.studyICFIdentityAuthentication,
                },
              }
              state.loading = true
              postTemplate(store.state.studyItem.studyId, infoObject)
                .then(() => {
                  ElMessage.success('保存成功')
                  state.loading = false
                  state.onLoad()
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        } else if (flag === 'F') {
          if (
            state.signatureCACopy === 2 &&
            state.dctStudyICFDocControl.participantArr.length < 2
          ) {
            state.jionHintShow = true
            return
          }
          state.signatureRef.validate((valid) => {
            if (valid) {
              const infoObject: any = {
                id: state.id,
              }
              if (state.signatureCACopy === 1) {
                infoObject.studyICFSignature = { ...state.studyICFSignature }
              } else if (state.signatureCACopy === 2) {
                const arr: any = []
                state.dctStudyICFDocControl.participantArr.forEach((item) => {
                  if (item === 1) {
                    arr.push({
                      isValid: 1,
                      userType: 1,
                      docControls: [],
                    })
                  } else if (item === 2) {
                    arr.push({
                      isValid: 1,
                      userType: 2,
                      docControls: [],
                    })
                  }
                })
                if (state.dctStudyICFDocControl.participantArr.includes(1)) {
                  arr[0].docControls = state.disposeSaveData(
                    state.dctStudyICFDocControl.researchistDetailsArr,
                    state.dctStudyICFDocControl.researchistArr
                  )
                }
                if (state.dctStudyICFDocControl.participantArr.includes(2)) {
                  arr[1].docControls = state.disposeSaveData(
                    state.dctStudyICFDocControl.subjectDetailsArr,
                    state.dctStudyICFDocControl.subjectArr
                  )
                }
                infoObject.dctStudySignTaskActors = deepClone(arr)
              }
              state.loading = true
              postTemplate(store.state.studyItem.studyId, infoObject)
                .then((res) => {
                  ElMessage.success('保存成功')
                  state.loading = false
                  state.id = res?.id
                  state.onLoad()
                })
                .catch(() => {
                  state.loading = false
                })
            }
          })
        }
      },
      disposeSaveData: (arr1, arr2) => {
        const participantArr = []
        arr1.forEach((item) => {
          if (
            (arr2.includes(1) && item.docControl === 1) ||
            (arr2.includes(2) && item.docControl === 2) ||
            (arr2.includes(3) && item.docControl === 3)
          ) {
            if (item?.pageIndex) {
              item.pageIndex = item.pageIndex / 1
            }
            if (item?.positionY) {
              item.positionY = item.positionY / 1
            }
            if (item?.positionX) {
              item.positionX = item.positionX / 1
            }
            item.isValid = 1
            participantArr.push(item)
          } else {
            item.isValid = 0
            item.pageIndex = 0
            item.positionY = 0
            item.positionX = 0
            participantArr.push(item)
          }
        })
        return participantArr
      },
      arrSort: (arr) => {
        const order = [3, 1, 2]
        const sortedArr = arr.sort((a, b) => {
          const indexA = order.indexOf(a.docControl)
          const indexB = order.indexOf(b.docControl)
          return indexA - indexB
        })
        return sortedArr
      },
      disposeData: (arr) => {
        const arrData = arr.reduce((acc, item) => {
          if (item.pageIndex === 0) {
            item.pageIndex = null
          }
          if (item.positionX === 0) {
            item.positionX = null
          }
          if (item.positionY === 0) {
            item.positionY = null
          }
          if (
            !acc.includes(item.docControl) &&
            item.isValid &&
            item.docControl !== 0
          ) {
            acc.push(item.docControl)
          }
          return acc
        }, [])
        return arrData
      },
      //
      previewKnowQuestionnaireAndSignature: (flag) => {
        if (state?.id) {
          getSignTemplate(state.id, { type: flag + 1 }).then((res) => {
            window.open(res)
          })
        }
      },
      onLoad: () => {
        if (RESEARCHCENTER_INFOS?.id || state?.id) {
          getTemplateDetails(RESEARCHCENTER_INFOS.researchContent.id).then(
            (res: any) => {
              // 取总对象
              res.icfSignTemplate.templateStatusFlag =
                res?.icfSignTemplate?.templateStatus === '启用' || false
              state.templateDetailsObj = res
              const i = getshu(res.studyICFBaseInfo.icfFeature)
              state.sunNumberList = deepClone(i)
              state.sunNumber = i
              // res.studyICFBaseInfo.icfFeature
              // 0 = 未知, 1 = 观看知情视频, 2 = 阅读知情同意, 4 = 讲解知情同意, 8 = 知情问卷, 16 = 知情问卷审核, 32 = 知情视频录制, 64 = 知情签名, 128 = 认证身份
              // res.studyICFBaseInfo?.signatureStrategy 0 = 未知, 1 = 无CA认证, 2 = 法大大CA认证
              if (res?.studyICFBaseInfo?.signatureStrategy) {
                state.signatureCA = res.studyICFBaseInfo.signatureStrategy // 绑值的
              }
              state.signatureCACopy = res.studyICFBaseInfo?.signatureStrategy // 这个做显示用的
              state.id = res.id
              // 处理法大大CA
              if (
                res?.dctStudySignTaskActors &&
                res?.dctStudySignTaskActors?.length > 0
              ) {
                const participantArr = res.dctStudySignTaskActors.reduce(
                  (acc, item) => {
                    if (!acc.includes(item.userType) && item.isValid) {
                      acc.push(item.userType)
                    }
                    return acc
                  },
                  []
                )
                if (
                  Array.isArray(participantArr) &&
                  participantArr.length > 1
                ) {
                  // 为空代表还为绑定过就不走下边的逻辑
                  state.dctStudyICFDocControl.participantArr = participantArr
                  let arr1: any = []
                  let arr2: any = []
                  res.dctStudySignTaskActors.forEach((item) => {
                    if (item.userType === 1) {
                      arr1 = item.docControls
                    }
                    if (item.userType === 2) {
                      arr2 = item.docControls
                    }
                  })
                  if (Array.isArray(arr1) && arr1.length > 1) {
                    state.dctStudyICFDocControl.researchistDetailsArr =
                      state.arrSort(arr1)
                    state.dctStudyICFDocControl.researchistArr =
                      state.disposeData(arr1)
                  }
                  if (Array.isArray(arr2) && arr2.length > 0) {
                    state.dctStudyICFDocControl.subjectDetailsArr =
                      state.arrSort(arr2)
                    state.dctStudyICFDocControl.subjectArr =
                      state.disposeData(arr2)
                  }
                }
              }
              // 知情声明
              if (res.studyICFStatementInfo) {
                state.studyICFStatementInfo = res.studyICFStatementInfo
              }
              state.studyICFIdentityAuthentication =
                res?.studyICFIdentityAuthentication
              state.studyICFSignature = res?.studyICFSignature
              // 知情视频 -- 现在应该是个数组了
              // if (res.studyICFVideoInfo) {
              //   state.studyICFVideoInfo = res.studyICFVideoInfo
              //   state.customInformation.icfFileUrl = res.studyICFVideoInfo?.videoCoverUrl || ''
              //   if (res.studyICFVideoInfo.videoFileName) {
              //     state.fileList2 = [
              //       {
              //         name: res.studyICFVideoInfo.videoFileName,
              //         url: res.studyICFVideoInfo.videoUrl,
              //       },
              //     ]
              //   }
              // }
              nextTick(() => {
                if (res?.icfInfoVideos && state.KnowVideoTableRef) {
                  if (
                    Array.isArray(res.icfInfoVideos) &&
                    res?.icfInfoVideos?.length > 0
                  ) {
                    const maxNum = res.icfInfoVideos.reduce((max, obj) => {
                      return obj.sort > max ? obj.sort : max
                    }, 0) // 初始值设为0，假设数组至少有一个元素
                    // 将最大值加1
                    state.incrementedMax = maxNum + 1
                  }
                  // console.log(res.icfInfoVideos).sort
                  state.KnowVideoTableRef.tableData = res.icfInfoVideos
                }
                if (res?.icfVersions?.length && state.knowInfosTableRef) {
                  state.knowInfosTableRef.tableData = res?.icfVersions || []
                }
              })
              if (res.studyICFExplainInfo) {
                res.studyICFExplainInfo.explainICFType === 0
                  ? (res.studyICFExplainInfo.explainICFType = 1)
                  : res.studyICFExplainInfo.explainICFType
                state.studyICFExplainInfo.explainICFType =
                  res.studyICFExplainInfo.explainICFType.toString()
              }
              // 知情同意书
              if (res.studyICFBaseInfo) {
                // res.icfVersions
                state.studyICFBaseInfo =
                  res?.icfVersions.length === 1
                    ? {
                        ...res.studyICFBaseInfo,
                        ...res.icfVersions[0],
                      }
                    : res.studyICFBaseInfo
                if (
                  res.icfVersions.length &&
                  res.icfVersions[0].knowBooksListValue === 1
                ) {
                  state.fileList = [
                    {
                      name: res.icfVersions[0].statementFileName,
                      url: res.icfVersions[0].statementFileUrl,
                    },
                  ]
                }
              }
              // 知情问卷
              if (res.icfQuestTemplateInfo) {
                state.icfQuestTemplateInfo = res.icfQuestTemplateInfo
              }
              if (res.icfVersions?.length && res.icfVersions[0].studyICF) {
                nextTick(() => {
                  if (state?.editorHome?.gainTxt) {
                    state.editorHome.gainTxt('get', res.icfVersions[0].studyICF)
                  }
                })
              }
              QUESTIONNAIRE_INFOS.id = res.id
              QUESTIONNAIRE_INFOS.questionnaireInfo = res.icfQuestTemplateInfo
              QUESTIONNAIRE_CONTENT.questionnaireContentViews =
                res.icfQuestTemplateInfo
            }
          )
        } // 基本信息下拉
        getQuestTemplateDropInfo().then((res) => {
          QUESTIONNAIRE_INFOS.templateDropInfos = res
          QUESTIONNAIRE_CONTENT.templateDropInfos = res
        })
        // 所有下拉框
        getQuestTemplateItemDropInfo().then((res) => {
          QUESTIONNAIRE_INFOS.DropInfos = res
          QUESTIONNAIRE_CONTENT.DropInfos = res
        })
      },
    })
    onMounted(() => {
      if (
        store?.state?.studyItem?.studyId &&
        store?.getters?.app?.authorization
      ) {
        state.siteUrl = `${window.location.origin}doctorui/#/privacyAgreementTermsOfService==type=4==${RESEARCHCENTER_INFOS.researchContent.id}==${store.getters.app.authorization}`
      }
      state.onLoad()
    })
    // 传值
    provide('QUESTIONNAIRE_INFOS', QUESTIONNAIRE_INFOS)
    provide('QUESTIONNAIRE_CONTENT', QUESTIONNAIRE_CONTENT)

    return {
      QUESTIONNAIRE_INFOS,
      QUESTIONNAIRE_CONTENT,
      RESEARCHCENTER_INFOS,
      VALIDATEPASS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.informed-consent-details {
  width: 100%;
  // 知情同意书多版本弹窗
  #knowWrittenConsentDialogShow {
    :deep(.my-dialog-body) {
      width: 80%;
    }
  }
  .content-top {
    min-height: calc(100vh - 140px);
    max-width: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 20px 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    border-radius: 6px;
    .demo-tabs {
      width: calc(100% - 68px);
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

.elForm {
  width: 80%;
  display: flex;
  flex-wrap: wrap;
  .formLeft {
    margin-left: 3%;
  }
}
.EditQuestionnaire {
  color: #333;
  ::v-deep .footer {
    margin: 40px 0 0 0;
    display: flex;
    justify-content: flex-end;
  }
  ::v-deep .pos-rb {
    margin: 200px 0 0 0;
  }
  .nav-module {
    margin: 10px 0 0 0;
    display: flex;
    .nav-btn-le {
      width: 50%;
      display: flex;
      align-items: center;
      h4 {
        margin: 0 30px 0 0;
      }
    }
    .nav-btn-ri {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .infos-body {
    width: 100%;
    // height: 200px;
    margin: 20px 0;
    padding: 0 20px 10px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 5px;
    .infos-body-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .infos-body-content {
      .infos-body-content-items {
        display: flex;
        align-items: center;
        margin: 0px 0 20px 0;
        span {
          max-width: 80%;
          display: -webkit-box;
          word-break: break-all;
          word-wrap: break-word;
          overflow: hidden;
          /*…省略形式*/
          text-overflow: ellipsis;
          /*从上向下垂直排列子元素*/
          -webkit-box-orient: vertical;
          /*文本可分两行*/
          -webkit-line-clamp: 3;
        }
        .infos-body-content-items-paralleling {
          width: 400px;
          display: flex;
          align-items: center;
        }
        .infos-body-content-items-label {
          width: 100px;
          text-align: right;
          color: #999;
          margin: 0 20px 0 0;
          white-space: nowrap;
        }
      }
    }
  }
  .questionnaire-body {
    width: 100%;
    // height: 200px;
    margin: 20px 0;
    padding: 0 20px 30px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 5px;
    .questionnaire-body-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .questionnaire-body-content {
      width: 100%;
      padding: 0 40px;
      box-sizing: border-box;
      .questionnaire-body-content-module {
        width: 100%;
        padding: 12px 20px;
        margin: 0 0 20px 0;
        box-sizing: border-box;
        min-height: 130px;
        border-radius: 5px;
        background: #f2f2f2;
        .questionnaire-body-content-module-head {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .sort {
            width: 117px;
            color: #fe664e;
            span {
              margin: 0 0 0 16px;
            }
          }
          .check {
            width: 200px;
            text-align: center;
            color: #fe664e;
          }
          .btns {
            display: flex;
            align-content: center;
            .btns-delete {
              margin: 0 40px 0 0;
              color: #fd004e;
            }
            .btns-edit {
              color: #409eff;
            }
            &:hover {
              cursor: pointer;
            }
          }
        }
        .questionnaire-body-content-module-items {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin: 20px 0 0 0;
          .questionnaire-body-content-module-item {
            width: 100%;
            margin: 0 0 20px 0;
            display: flex;
            align-items: center;
            .questionnaire-body-content-module-item-lable {
              width: 150px;
              margin: 0 20px 0 0;
              text-align: right;
              color: #666;
            }
          }
        }
      }
    }
  }
}
.el-popper.is-pure {
  z-index: 1000000 !important;
}
.know-video {
  :deep(.my-el-table) {
    padding: 0;
  }
}
</style>
