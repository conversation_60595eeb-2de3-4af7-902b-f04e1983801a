<template>
  <div id="InformedVersionDetails">
    <div class="InformedVersionDetails-top">
      <h4>{{ siteName }}</h4>
      <div>
        <el-button plain size @click="routerGo">返回</el-button>
        <el-button type="primary" size @click="submit">保 存</el-button>
      </div>
    </div>
    <div class="InformedBtn">
      <el-button type="primary" size @click="popUpShow = true"
        >请选择知情版本</el-button
      >
      <span v-html="editionText" />
    </div>

    <trial-table
      v-if="editionIcfVersions?.length"
      class="!w-[50%]"
      border
      :request="
        () => {
          return {
            data: [
              {
                icfVersions: editionIcfVersions,
              },
            ],
          }
        }
      "
      :columns="[
        {
          label: '版本名称',
          tdSlot: 'icfVersionNumberStr',
          width: 600,
          showOverflowTooltip: false,
        },
        { label: '版本日期', tdSlot: 'icfVersionDateStr' },
      ]"
    >
      <template #icfVersionNumberStr="scope">
        <div class="tdTooltip" v-for="item in scope.row.icfVersions">
          <div v-if="!showToolTip(item.icfVersionNumber)">
            {{ item.icfVersionNumber }}
          </div>
          <el-tooltip
            v-else
            class="box-item"
            effect="dark"
            :content="item.icfVersionNumber"
            placement="top"
          >
            {{ item.icfVersionNumber }}
          </el-tooltip>
        </div>
      </template>
      <template #icfVersionDateStr="scope">
        <div v-for="(e, idx) in scope.row.icfVersions" :key="idx">
          {{ e.icfVersionDate }}
        </div>
      </template>
    </trial-table>

    <el-form
      ref="dataFormRef"
      :model="dataForm"
      label-width="80px"
      label-position="top"
      :rules="formRules"
      class="form flex"
    >
      <el-form-item label="生效规则" prop="effectiveType">
        <el-select
          v-model="dataForm.effectiveType"
          style="width: 260px"
          placeholder="请选择生效规则"
        >
          <el-option
            v-for="item in rulesOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="dataForm.effectiveType === 1" class="ml-[20px]" label="生效时间" prop="dataPockerOne">
        <el-date-picker
          v-model="dataForm.dataPockerOne"
          type="datetime"
          style="width: 260px"
          placeholder="请选择生效时间"
        />
      </el-form-item>
    </el-form>
    <div class="w-full">
      <div style="margin: 20px 0 10px 0">伦理审批件</div>
      <div style="width: 50%; margin-bottom: 20px">
        <div>
          <MyUpload
            ref="InformedVersionDetailsUploadRef"
            upload-file-falg
            :request-fun="fileFun"
            :deletefile="deletefile"
            :file-list="fileList"
            :file-size="1 / 2.048"
            :before-file-upload-type="beforeFileUploadType"
          />
        </div>
      </div>
    </div>
    <div style="margin-top: 20px">
      状态
      <div style="display: flex; line-height: 32px">
        <el-switch v-model="isEnableVal" style="margin-right: 10px" />
        <span v-if="isEnableVal">启用</span>
        <span v-else>禁用</span>
      </div>
    </div>
    <!-- 选择知情版本弹窗 -->
    <trial-dialog
      v-model="popUpShow"
      :title="title"
      class="myDialogUp"
      :my-dialog-body-style="{ width: '70%' }"
    >
      <template #DialogBody>
        <trial-table
          ref="informedVersionDetailsRef"
          title=""
          border
          :request="getResearchCenterList"
          :columns="columns"
        >
          <template #icfVersionNumberStr="scope">
            <div class="tdTooltip" v-for="item in scope.row.icfVersions">
              <div v-if="!showToolTipDialog(item.icfVersionNumber)">
                {{ item.icfVersionNumber }}
              </div>
              <el-tooltip
                v-else
                class="box-item"
                effect="dark"
                :content="item.icfVersionNumber"
                placement="top"
              >
                {{ item.icfVersionNumber }}
              </el-tooltip>
            </div>
          </template>
          <template #icfVersionDateStr="scope">
            <div v-for="(e, idx) in scope.row.icfVersions" :key="idx">
              {{ e.icfVersionDate }}
            </div>
          </template>
          <template #operate="scope">
            <span
              v-show="scope.row.bindStatus === '未绑定'"
              style="color: #1e87f0; cursor: pointer"
              @click="popUpBtn(scope.row)"
            >选择</span>
          </template>
        </trial-table>
      </template>
      <template #footer>
        <div class="centerflex" style="margin-top: 20px">
          <el-button size @click="popUpShow = false">取消</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, reactive, toRefs, nextTick } from 'vue';
import MyUpload from '@/components/Upload/index.vue'
import { parseTime } from '@/utils'
import {
  getTemplateVersion, // 知情版本弹窗信息
  postTemplateFile, // 上传PDF
  postTemplateSiteInfo, // 保存中心知情版本
  getTemplateSiteInfoDetails, // 中心知情版本Id，获取中心知情版本详情
} from '@/api/informedManagement'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'InformedVersionDetails', // 中心知情版本详情
  components: {
    MyUpload,
  },
  props: {
    stateChange: {
      type: Object,
      default: () => {},
    },
    requestLoad: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const store = useStore()
    const state = reactive({
      formRules: {
        effectiveType: [{ required: true, message: '请选择' }],
        dataPockerOne: [{ required: true, message: '请选择' }],
      },
      rulesOptions: [
        {
          value: 1,
          label: '按配置时间自动生效',
        },
        {
          value: 2,
          label: '不自动生效，人工按需推送',
        },
      ],
      studyId: null, // 中心ID
      siteName: '',
      title: '选择知情版本',
      editionText: '', // 请选择知情版本文案
      editionDateTimeText: '', // 生效时间文案
      dataFormRef: null,
      InformedVersionDetailsUploadRef: null,
      dataForm: {
        id: '', // 主建ID
        dctStudyICFInfoId: '', // 问卷知情版本ID
        effectiveDate: '', // 该知情生效日期
        ethicsCommitteeFilePath: '', // 伦理委员会的PDF文件路径
        ethicsCommitteeFileName: '', // 伦理委员会的PDF文件名
        ethicsCommitteeFileUrl: '', // 伦理委员会的PDF访问路径
        isEnable: true, // 是否启用
        effectiveType: 1, // 生效规则
        dataPockerOne: ''
      },
      isEnableVal: true,
      // 知情版本日期 信息
      editionIcfVersions: [],
      fileList: [], // 用于回显上传的文件
      popUpShow: false,
      beforeFileUploadType: ['.pdf'],
      informedVersionDetailsRef: null,
      columns: [
        { label: '版本名称', tdSlot: 'icfVersionNumberStr', width: 250, showOverflowTooltip: false },
        { label: '版本日期', tdSlot: 'icfVersionDateStr', minWidth: 150 },
        { label: '流程类型', prop: 'flowType', minWidth: 150 },
        { label: '流程元素', prop: 'icfFeature', minWidth: 500 },
        { label: '绑定状态', prop: 'bindStatus', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      getResearchCenterList: async() => {
        const res = await getTemplateVersion(store.state.studyItem.studyId, props.stateChange.studyId)
        return {
          data: res || []
        }
      },
      // 知情版本弹窗选择
      popUpBtn: async(val) => {
        state.editionIcfVersions = []
        await nextTick()
        state.editionIcfVersions = val?.icfVersions || []
        state.dataForm.dctStudyICFInfoId = val.studyICFInfoId
        state.editionText = ''
        state.popUpShow = false
      },
      // 跳转
      routerGo: () => {
        props.stateChange.stateChangeFalg = true
        props.requestLoad()
      },
      // 上传文件
      fileFun: (fileObj) => {
        const myFormDataObj = new FormData()
        const fileName = fileObj.file.name
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        // const myFormDataObj = new FormData()
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        postTemplateFile(store.state.studyItem.studyId, myFormDataObj)
          .then((res) => {
            loading.close()
            ElMessage.success('上传成功')
            state.dataForm.ethicsCommitteeFilePath = res.icfFilePath
            state.dataForm.ethicsCommitteeFileName = res.icfFileName
            state.dataForm.ethicsCommitteeFileUrl = res.icfFileUrl
            if (state.InformedVersionDetailsUploadRef.fileList.length === 0) {
              state.InformedVersionDetailsUploadRef.fileList.push({
                name: fileName,
                url: state.dataForm.ethicsCommitteeFileUrl,
              })
            }
          })
          .catch(() => {
            state.InformedVersionDetailsUploadRef.fileList.length = 0
            state.dataForm.ethicsCommitteeFilePath = ''
            state.dataForm.ethicsCommitteeFileName = ''
            state.dataForm.ethicsCommitteeFileUrl = ''
            loading.close()
          })
      },
      // 删除文件
      deletefile: () => {
        state.InformedVersionDetailsUploadRef.fileList.length = 0
        state.dataForm.ethicsCommitteeFilePath = ''
        state.dataForm.ethicsCommitteeFileName = ''
        state.dataForm.ethicsCommitteeFileUrl = ''
      },
      // 保存
      submit: () => {
        if (!state.editionIcfVersions?.length) {
          state.editionText = '请选择'
          return
        }
        state.dataFormRef.validate((valid) => {
          if (valid) {
            const loading = ElLoading.service({
              lock: true,
              text: 'Loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            if (state.dataForm.dataPockerOne && state.dataForm?.effectiveType === 1) {
              state.dataForm.dataPockerOne = parseTime(
                new Date(state.dataForm.dataPockerOne),
                '{y}-{m}-{d} {h}:{i}:{s}'
              )
              state.dataForm.effectiveDate = state.dataForm.dataPockerOne
            }
            state.dataForm.isEnable = state.isEnableVal
            postTemplateSiteInfo(state.studyId, state.dataForm)
              .then((res) => {
                loading.close()
                state.dataForm.id = res.id
                state.editionDateTimeText = ''
                state.editionText = ''
                ElMessage.success('保存成功')
                props.stateChange.stateChangeFalg = true
                props.requestLoad()
              })
              .catch(() => {
                loading.close()
              })
          }
        })
      },
      onLoad: async() => {
        state.siteName = props.stateChange.siteName
        if (props.stateChange?.id) {
          getTemplateSiteInfoDetails(props.stateChange.id).then((res: any) => {
            // if (!res?.effectiveType) {
            //   state.dataForm?.effectiveType = 1
            // }
            state.dataForm = res
            state.editionIcfVersions = res.icfVersions
            state.dataForm.dataPockerOne = res?.effectiveDate
            state.isEnableVal = res.isEnable
            if (res.ethicsCommitteeFileName) {
              state.fileList = [
                {
                  name: res.ethicsCommitteeFileName,
                  url: res.ethicsCommitteeFileUrl
                }
              ]
            }
          })
        }
        state.studyId = props.stateChange.studyId
      },
      showToolTip: (txt) => {
        const cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0
        const otCount = txt.length - cnCount
        return (cnCount * 2 + otCount) > 38
      },
      showToolTipDialog: (txt) => {
        const cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0
        const otCount = txt.length - cnCount
        return (cnCount * 2 + otCount) > 28
      },
    })
    onBeforeMount(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
#InformedVersionDetails {
  width: 100%;
  height: 100%;
  min-width: 1000px;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  // overflow-x: scroll;
  .InformedVersionDetails-top {
    width: 100%;
    display: flex;
    h4 {
      flex: 1;
      margin: 0;
      line-height: 32px;
    }
    div {
      margin-left: 20px;
      display: flex;
    }
  }
  .InformedBtn {
    margin-top: 30px;
    span {
      display: block;
      width: 100%;
      color: red;
      margin-top: 5px;
      font-size: 14px;
    }
  }
  .form {
    margin-top: 20px;
    .formVlaue {
      display: flex;
    }
  }
  .dataTime {
    // .el-tooltip__trigger .el-tooltip__trigger
    :deep(.select-trigger) {
      width: 90%;
    }
    div {
      display: flex;
      margin-bottom: 10px;
      font-size: 14px;
      span {
        color: red;
        margin-right: 5px;
      }
    }
    p {
      font-size: 12px;
      margin: 0;
      margin-top: 5px;
      color: red;
    }
  }
  .myDialogUp {
    width: 100%;
  }
}
:deep(.my-el-table) {
  padding: 0 !important;
}
.tdTooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
