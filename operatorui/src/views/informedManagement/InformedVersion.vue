<template>
  <div>
    <div v-show="stateChange.stateChangeFalg" class="InformedVersion">
      <div class="InformedVersion-Left">
        <div class="leftTitle">中心列表</div>
        <div class="leftContent">
          <div
            v-for="(item, idx) in coreList"
            :key="idx"
            :class="{ activeCss: activeVar == idx }"
            @click="coreBtn(item, idx)"
            v-html="item.siteName"
          />
        </div>
      </div>
      <div class="InformedVersion-Right">
        <div class="rightTitle">中心信息</div>
        <div class="rightTop">
          <div class="rightTop-left" v-html="coreName" />
          <div class="rightTop-right">
            <span>{{ siteCategoryName }}</span>
            <span>{{ isActiveFlag == 0 ? '失活' : '激活' }}</span>
          </div>
        </div>
        <div style="margin: 20px 0 20px 0">知情同意书</div>
        <div class="rightContent">
          <div class="rightContent-top">
            <div><span></span>当前中心下签署的最新版知情</div>
            <el-button type="primary" @click="versionBtn((id = null))">
              新建
            </el-button>
          </div>
          <el-table
            :data="tableData"
            border
            max-height="400"
            class="my-el-table"
            :row-class-name="tableRowClassName"
          >
            <el-table-column
              prop="icfVersionNumber"
              label="版本名称"
              width="300"
            >
              <template #default="scope">
                <div class="tdTooltip" v-for="item in scope.row.icfVersions">
                  <div v-if="!showToolTip(item.icfVersionNumber)">
                    {{ item.icfVersionNumber }}
                  </div>
                  <el-tooltip
                    v-else
                    class="box-item"
                    effect="dark"
                    :content="item.icfVersionNumber"
                    placement="top"
                  >
                    {{ item.icfVersionNumber }}
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="icfVersionDate" label="版本日期" width="120">
              <template #default="scope">
                <div v-for="(e, idx) in scope.row.icfVersions" :key="idx">
                  {{ e.icfVersionDate }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="effectiveDate" label="生效时间" width="300">
              <template #default="scope">
                <span style="display: inline-block; width: 130px">{{
                  scope.row.effectiveDate
                }}</span>
                <span
                  :class="
                    scope.row.effectiveStatus == '未满足' ? 'gray' : 'blue'
                  "
                  >{{ scope.row.effectiveStatus }}</span
                >
              </template>
            </el-table-column>
            <el-table-column prop="enableStatus" label="状态" width="80">
              <template #default="scope">
                <span
                  :class="scope.row.enableStatus == '禁用' ? 'red' : 'green'"
                  >{{ scope.row.enableStatus }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="lastUpdateTime"
              label="更新时间"
              width="300"
            ></el-table-column>
            <el-table-column
              prop="operatorName"
              label="操作人"
            ></el-table-column>
            <el-table-column label="操作" fixed="right" width="80">
              <template #default="scope">
                <span
                  @click="versionBtn(scope.row)"
                  style="color: #1e87f0; cursor: pointer"
                  >编辑</span
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div v-if="!stateChange.stateChangeFalg">
      <InformedConsentDetails
        :state-change="stateChange"
        :request-load="onLoad"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { useStore } from 'vuex'
import { defineComponent, onBeforeMount, reactive, toRefs } from 'vue'
import InformedConsentDetails from '@/views/informedManagement/InformedVersionDetails.vue'
import {
  getTemplateSiteInfos, // 获取知情同意书列表数据
  getAllSites, // 获取课题下所有中心列表
} from '@/api/informedManagement'

export default defineComponent({
  name: 'InformedVersion', // 中心知情版本
  components: {
    InformedConsentDetails,
  },
  setup() {
    const store = useStore()
    const state = reactive({
      activeVar: 0,
      isActiveFlag: null,
      siteCategoryName: null,
      stateChange: {
        stateChangeFalg: true,
        id: '',
        siteName: '',
        studyId: '',
      },
      coreId: null, // 中心ID
      coreName: null, // 中心名称
      coreList: [], // 中心列表
      tableData: [],
      tableRowClassName(row) {
        if (row.row.isCurrentEffectiveICF === true) {
          return 'success-row'
        }
      },
      // 中心
      coreBtn: (item, idx) => {
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        state.activeVar = idx
        state.coreName = item.siteName
        state.coreId = item.id
        state.siteCategoryName = item.siteCategory
        state.isActiveFlag = item.edcSiteStatus
        getTemplateSiteInfos(item.id)
          .then((res) => {
            state.tableData = res
            loading.close()
          })
          .catch(() => {
            loading.close()
          })
      },
      // 新建or编辑
      versionBtn: (val) => {
        state.stateChange.stateChangeFalg = !state.stateChange.stateChangeFalg
        state.stateChange.siteName = state.coreName
        if (val !== null) {
          state.stateChange.id = val.id
        } else {
          state.stateChange.id = null
        }
        state.stateChange.studyId = state.coreId
      },
      onLoad: async () => {
        // 获取课题下所有中心列表
        getAllSites(store.state.studyItem.studyId).then((res) => {
          res.forEach((item) => {
            item.siteName = item.edcSiteName
            item.siteName = item.edcSiteCode + '-' + item.siteName
          })
          if (state.coreId == null) {
            state.coreId = res[0].id
            state.coreName = res[0].siteName
            state.siteCategoryName = res[0].siteCategory
            state.isActiveFlag = res[0].edcSiteStatus
          }
          state.coreList = res
          // 默认获取第一个中心的详情数据
          if (state.coreId) {
            getTemplateSiteInfos(state.coreId).then((res) => {
              state.tableData = res
            })
          }
        })
      },
      showToolTip: (txt) => {
        let cnCount = txt.match(/[\u4e00-\u9fa5]/g)?.length ?? 0;
        let otCount = txt.length - cnCount
        return (cnCount * 2 + otCount) > 38
      }
    })
    onBeforeMount(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>
<style lang="less" scoped>
.InformedVersion {
  width: 100%;
  height: 100%;
  min-width: 1000px;
  overflow-x: scroll;
  display: flex;
  .InformedVersion-Left {
    width: 250px;
    .leftTitle {
      margin-bottom: 10px;
    }
    .leftContent {
      height: calc(100vh - 150px);
      padding: 10px 20px;
      box-sizing: border-box;
      background: #fff;
      overflow-y: scroll;
      border-radius: 10px;
      div {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 14px;
        line-height: 40px;
        cursor: pointer;
        &:first-child {
          margin: 0;
        }
      }
    }
  }
  .InformedVersion-Right {
    flex: 1;
    margin-left: 30px;
    overflow: hidden;
    .rightTitle {
      margin-bottom: 10px;
    }
    .rightTop {
      width: 100%;
      padding: 20px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 10px;
      display: flex;
      .rightTop-left {
        flex: 1;
      }
      .rightTop-right {
        width: 110px;
        display: flex;
        justify-content: space-between;
        margin-left: 50px;
      }
    }
    .rightContent {
      width: 100%;
      height: calc(100vh - 280px);
      padding: 20px;
      box-sizing: border-box;
      background: #fff;
      border-radius: 10px;
      .rightContent-top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        div {
          display: flex;
          line-height: 20px;
          color: #666;
          margin-top: 6px;
          span {
            width: 20px;
            height: 20px;
            background: rgba(241, 171, 43, 0.7);
            border-radius: 3px;
            margin-right: 8px;
          }
        }
      }
    }
  }
}
.my-el-table {
  width: 100%;
  &:deep(th) {
    background: #f6f6f6 !important;
    color: rgba(0, 0, 0, 0.85);
  }
}
::v-deep .el-table.is-scrolling-right th.el-table-fixed-column--right {
  background: #f6f6f6 !important;
}
::v-deep .el-table__header-wrapper tr th.el-table-fixed-column--left,
.el-table__header-wrapper tr th.el-table-fixed-column--right {
  background: #f6f6f6 !important;
}
.activeCss {
  color: #1e87f0 !important;
}

.blue {
  color: #1e87f0;
}

.gray {
  color: rgb(147, 144, 144);
}

.red {
  color: rgb(226, 70, 70);
}

.green {
  color: green;
}
::v-deep .success-row {
  background: rgba(241, 171, 43, 0.7) !important;
}
.tdTooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
