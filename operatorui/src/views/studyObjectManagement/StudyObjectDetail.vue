<template>
  <div class="details">
    <div class="head">
      <div class="headLeft">
        <span v-if="!stateChange.id">新建药品</span>
        <span v-else>编辑药品</span>
      </div>
      <div class="headRight">
        <el-row>
          <el-button size="small" @click="routerGo('back')">返回</el-button>
          <el-button
            v-if="stateChange?.id"
            class="hidde"
            size="small"
            type="danger"
            @click="confirmBtm('danger')"
          >删除</el-button>
          <el-button size="small" type="primary" @click="confirmBtm('primary')">保存</el-button>
        </el-row>
      </div>
    </div>
    <!-- 新建药品 -->
    <div class="main">
      <div class="mainLeft">
        <!-- :rules="rules"  ref="loginForm" ref不可重名-->
        <el-form
          ref="detailsNewForm"
          :model="StudyObjectDetailsForm"
          :label-position="labelPosition"
          :rules="rules"
        >
          <el-form-item label="IP类别" style="width: 30%" prop="drugCategory">
            <el-select
              v-model="StudyObjectDetailsForm.drugCategory"
              placeholder="请选择"
            >
              <el-option label="药品" :value="1" />
              <el-option label="营养品" :value="2" />
            </el-select>
          </el-form-item>
          <el-row>
            <el-col :span="11">
              <el-form-item label="药品名称" prop="drugName">
                <el-input
                  v-model="StudyObjectDetailsForm.drugName"
                  placeholder="请填写药品名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="1" />
            <el-col :span="11">
              <el-form-item
                label="药品显示名称(用户端)"
                prop="drugDisplayName"
                style="width: 100%"
              >
                <el-input
                  v-model="StudyObjectDetailsForm.drugDisplayName"
                  placeholder="请填写药品显示名称"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="11">
              <el-form-item label="规格" prop="drugSpecifics">
                <el-input
                  v-model="StudyObjectDetailsForm.drugSpecifics"
                  style="width: 100%"
                  placeholder="请填写规格"
                />
              </el-form-item>
            </el-col>
            <el-col :span="1" />
            <el-col :span="11">
              <el-form-item label="单位" prop="drugDoseUnit">
                <el-select
                  v-if="StudyObjectDetailsList?.length"
                  v-model="StudyObjectDetailsForm.drugDoseUnit"
                  placeholder="请选择单位"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in StudyObjectDetailsList"
                    :key="item.itemName"
                    :value="item.itemName"
                    :label="item.itemName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item label="生产厂家" prop="manufacturer">
                <el-input
                  v-model="StudyObjectDetailsForm.manufacturer"
                  placeholder="请填写生产厂家"
                />
              </el-form-item>
            </el-col>
            <el-col :span="1" />
            <el-col :span="11">
              <el-form-item label="有效期" prop="validateTill">
                <el-input
                  v-model="StudyObjectDetailsForm.validateTill"
                  placeholder="请填写有效期"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="23">
              <el-form-item label="贮存" prop="storage">
                <el-input
                  v-model="StudyObjectDetailsForm.storage"
                  placeholder="请填写贮存信息"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="23">
              <el-form-item label="给药方式" prop="medicationTips">
                <el-input
                  v-model="StudyObjectDetailsForm.medicationTips"
                  placeholder="请填写给药方式"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="11">
              <el-form-item prop="drugSpecialType" label="是否盲态">
                <el-select
                  v-model="StudyObjectDetailsForm.drugSpecialType"
                  placeholder="请选择是否盲态"
                  style="width: 100%"
                >
                  <el-option label="开放" value="1">开放</el-option>
                  <el-option label="盲态" value="2">盲态</el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" />
          </el-row>
        </el-form>
      </div>
      <div class="mainRight">
        <span>药品图片</span>
        <img src="@/assets/svg/drugsCompleteIcon.svg" alt="" />
      </div>
    </div>

    <!-- 其他设置 -->
    <div class="head">
      <div class="headLeft">
        <span>其他设置</span>
      </div>
    </div>
    <div class="container">
      <el-form
        ref="detailsForm"
        :model="StudyObjectDetailsForm"
        :label-position="labelPosition"
        :rules="rules"
      >
        <!-- 因需求有部分变更暂时注释该代码 -->
        <!-- <el-row>
          <el-col :span="6">
            <el-form-item label="最小刻度（控制每次-+值）" prop="scale">
              <el-input
                v-model.trim="StudyObjectDetailsForm.scale"
                placeholder="请填写数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="最小可调给药剂量" prop="minDose">
              <el-input
                v-model.trim="StudyObjectDetailsForm.minDose"
                placeholder="请填写数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="1" />
          <el-col :span="6">
            <el-form-item label=" 最大可调给药剂量" prop="maxDose">
              <el-input
                v-model.trim="StudyObjectDetailsForm.maxDose"
                placeholder="请填写数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
          </el-col>
        </el-row> -->
        <span style="margin: 0">物资申请</span>
        <el-form-item>
          <el-switch v-model="StudyObjectDetailsForm.isApplication" />
          <span v-if="StudyObjectDetailsForm.isApplication">受试者可申请</span>
          <span v-else>受试者不可申请</span>
        </el-form-item>
        <el-row>
          <el-col :span="6">
            <el-form-item label="默认申请剂量" prop="recommendApplyNumber">
              <el-input
                v-model.trim="StudyObjectDetailsForm.recommendApplyNumber"
                placeholder="请填写数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="1" />
          <el-col :span="6">
            <el-form-item label=" 最小可调申请剂量" prop="minApplyNumber">
              <el-input
                v-model.trim="StudyObjectDetailsForm.minApplyNumber"
                placeholder="请填写数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="1" />
          <el-col :span="6">
            <el-form-item label=" 最大可调申请剂量" prop="maxApplyNumber">
              <el-input
                v-model.trim="StudyObjectDetailsForm.maxApplyNumber"
                placeholder="请填写数值"
                oninput="value=value.replace(/[^0-9.]/g,'')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import {
  studyDrugDoseUnitDictionariesInfo,
  studyDrug,
  studyArmsInfo,
  studyDeleteDrug,
} from '@/api/studyObjectManagement'

export default defineComponent({
  name: 'StudyObjectDetails', // 给药方案管理
  props: {
    stateChange: {
      type: Object,
    },
    studyObjectManagementList: {
      type: Object,
    },
    requestLoad: {
      type: Function,
      default: () => {},
    },
  },
  setup(props) {
    const store = useStore()
    const state = reactive({
      MyStateChangeFalg: false,
      labelPosition: 'top',
      detailsList: '',
      studyObjectManagementList: {}, // 研究对象带过来的数据
      // studyArmsInfoList: {},
      StudyObjectDetailsList: {
        itemName: '',
        armName: '',
      },
      // 药品信息 命名带意义的StudyObjectDetailsForm
      StudyObjectDetailsForm: {
        id: null,
        studyId: '',
        drugCategory: 1, // IP类别
        drugImgUrl: null,
        drugName: '', // 药品名称
        drugDisplayName: '', // 药品显示名称（用户端）
        drugSpecifics: '', // 药物规格
        drugDoseUnit: '', // 药品单位
        manufacturer: '', // 生产厂家
        validateTill: null, // 有效期
        storage: '', // 贮存
        medicationTips: '', // 给药方式
        drugSpecialType: '', // 适配分组
        isApplication: true, // 物资申请
        scale: 1, // 最小刻度
        minDose: 0, // 最小可调给药剂量
        maxDose: 0, // 最大可调给药剂量
        recommendApplyNumber: 1, // 默认申请剂量
        minApplyNumber: 0, // 最小可调申请剂量
        maxApplyNumber: 0, // 最大可调申请剂量
        study: null,
        drugSchemes: null,
      },
      detailsForm: null,
      detailsNewForm: null,
      rules: {
        drugName: [
          { required: true, message: '请填写药品名称', trigger: 'blur' },
        ],
        drugDisplayName: [
          { required: true, message: '请填写药品显示名称', trigger: 'blur' },
        ],
        drugDoseUnit: [
          { required: true, message: '请选择单位', trigger: 'blur' },
        ],
        drugSpecialType: [
          { required: true, message: '请选择是否盲态', trigger: 'blur' },
        ],
        // scale: [{ required: true, message: '请填写数值', trigger: 'blur' }],
        // minDose: [{ required: true, message: '请填写数值', trigger: 'blur' }],
        // maxDose: [{ required: true, message: '请填写数值', trigger: 'blur' }],
        recommendApplyNumber: [
          { required: true, message: '请填写数值', trigger: 'blur' },
        ],
        minApplyNumber: [
          { required: true, message: '请填写数值', trigger: 'blur' },
        ],
        maxApplyNumber: [
          { required: true, message: '请填写数值', trigger: 'blur' },
        ],
        drugCategory: [
          { required: true, message: '请选择', trigger: 'blur' },
        ]
      },
      // tab切换
      stateTabChange: (flag) => {
        props.stateChange.tabChangeFalg = flag
      },
      // 跳转
      routerGo: (type) => {
        if (type === 'back') {
          props.stateChange.stateChangeFalg = true
        }
      },
      // 删除确认
      confirmBtm: (type) => {
        // 新建药品是false其他设置是true
        state.StudyObjectDetailsForm.id = props.stateChange.id
        state.StudyObjectDetailsForm.studyId = store.state.studyItem.studyId
        let myFlay = false
        if (type === 'primary') {
          state.detailsNewForm.validate((valid) => {
            if (valid) {
              myFlay = true
            }
          })
          state.detailsForm.validate((valid) => {
            if (valid) {
              if (
                state.StudyObjectDetailsForm.recommendApplyNumber / 1 <
                state.StudyObjectDetailsForm.minApplyNumber / 1
              ) {
                ElMessage.warning({
                  showClose: true,
                  message: '默认申请剂量不可小于最小可调申请剂量',
                })
              } else if (
                state.StudyObjectDetailsForm.recommendApplyNumber / 1 >
                state.StudyObjectDetailsForm.maxApplyNumber / 1
              ) {
                ElMessage.warning({
                  showClose: true,
                  message: '默认申请剂量不可大于最大可调申请剂量',
                })
              } else {
                studyDrug(state.StudyObjectDetailsForm).then(() => {
                  ElMessage.success({
                    message: '保存成功',
                    duration: 1000,
                  })
                  props.stateChange.stateChangeFalg = true
                  props.requestLoad()
                })
              }
            }
          })
        } else if (type === 'danger') {
          ElMessageBox.confirm('是否确认删除？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          }).then(() => {
            studyDeleteDrug(props.stateChange?.id || '').then(() => {
              ElMessage.success({
                type: 'success',
                message: '删除成功!',
              })
              props.stateChange.stateChangeFalg = true
              props.requestLoad()
            })
          })
        }
      },
    })
    // 获取药品编辑信息
    onMounted(() => {
      if (props.stateChange?.id) {
        state.StudyObjectDetailsForm = props.stateChange.ruleFormList
      }
      // 获取单位信息
      studyDrugDoseUnitDictionariesInfo().then((res: any) => {
        state.StudyObjectDetailsList = res
      })
      // studyArmsInfo(store.state.studyItem.studyId).then((res: any) => {
      //   state.studyArmsInfoList = res
      // })
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.details {
  width: 100%;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  .head {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    border-bottom: 2px solid #ccc;
    .headLeft {
      width: 50%;
      display: flex;
      span {
        height: 40px;
        line-height: 40px;
        color: black;
        text-align: left;
        &:last-child {
          margin-left: 20px;
        }
      }
    }
  }
  //新建药品
  .main {
    width: 94%;
    margin: 0 auto;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    .mainLeft {
      flex: 1;
    }
    .mainRight {
      width: 200px;
      margin-left: 200px;
      span {
        display: block;
        margin-top: 10px;
      }
      img {
        display: block;
        margin-top: 10px;
      }
    }
  }
  //   其他设置
  .container {
    width: 94%;
    margin: 0 auto;
    margin-top: 20px;
    span {
      margin-left: 10px;
    }
  }
}
.active {
  // color:rgb(0, 124, 240) !important;
  font-weight: 400;
  border-bottom: 2px solid rgb(0, 124, 240);
}
.hidde {
  display: none;
}
</style>
