<template>
  <div class="studyObjectManagement">
    <div v-show="stateChange.stateChangeFalg">
      <div v-if="studyObjectManagementList" class="head">
        <span>研究性质</span>
        <div class="head-box">
          <div>
            <span>研究对象</span>
            <span v-if="studyObjectManagementList?.studyObject" v-html="studyObjectManagementList.studyObject" />
          </div>
          <div class="head-boxBottom">
            <div>
              <span>分组设计</span>
              <span v-if="studyObjectManagementList?.groupingDesign" v-html="studyObjectManagementList.groupingDesign" />
            </div>
            <div>
              <span>盲法设置</span>
              <span v-if="studyObjectManagementList?.blindedSet" v-html="studyObjectManagementList.blindedSet" />
            </div>
            <div>
              <span>随机方式</span>
              <span v-if="studyObjectManagementList?.randomFashion" v-html="studyObjectManagementList.randomFashion" />
            </div>
          </div>
        </div>
      </div>
      <div class="main">
        <div class="mainTop">
          <span>研究对象</span>
          <el-button type="primary" @click="stateTabChange(false, null)">
            新建药品
          </el-button>
        </div>
        <div
          v-for="(item, index) in studyObjectManagementList.drugInfos"
          :key="index"
          class="containerContent"
        >
          <div class="edit">
            <span
              class="editBtn"
              @click="stateTabChange(false, item)"
            >编辑</span>
          </div>
          <div class="containerContentBox">
            <div class="containerContentBoxLeft">
              <img src="@/assets/svg/drugsCompleteIcon.svg" alt="">
            </div>
            <div class="containerContentBoxRight">
              <div class="content">
                <div>
                  <span>药物名称</span>
                  <span>{{ item.drugName }}</span>
                </div>
                <div>
                  <span>药物显示名称</span>
                  <span>{{ item.drugDisplayName }}</span>
                </div>
              </div>
              <div class="content">
                <div>
                  <span>规格</span>
                  <span>{{ item.drugSpecifics }}</span>
                </div>
                <div>
                  <span>单位</span>
                  <span>{{ item.drugDoseUnit }}</span>
                </div>
              </div>
              <div class="content">
                <div>
                  <span>生产厂家</span>
                  <span>{{ item.manufacturer }}</span>
                </div>
                <div>
                  <span>有效期</span>
                  <span>{{ item.validateTill }}</span>
                </div>
              </div>
              <div class="single">
                <div>
                  <span>贮存</span>
                  <span>{{ item.storage }}</span>
                </div>
              </div>
              <div class="single">
                <div>
                  <span>给药方式</span>
                  <span>{{ item.medicationTips }}</span>
                </div>
              </div>
              <div class="content">
                <div>
                  <span>物资申请</span>
                  <span v-if="item.isApplication">受试者可申请</span>
                  <span v-else>受试者不可申请</span>
                </div>
                <div>
                  <span>是否盲态</span>
                  <span v-if="item.drugSpecialType === 1">开放</span>
                  <span v-if="item.drugSpecialType === 2">盲态</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!stateChange.stateChangeFalg">
      <StudyObjectDetails :state-change="stateChange" :request-load="onLoad" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { studyDrugs, studyDrugInfoId } from '@/api/studyObjectManagement'
import StudyObjectDetails from '@/views/studyObjectManagement/StudyObjectDetail.vue'

export default defineComponent({
  name: 'StudyObjectManagement', // 研究对象管理
  components: {
    StudyObjectDetails,
  },
  setup() {
    const store = useStore()
    const state = reactive({
      stateChange: {
        stateChangeFalg: true,
        tabChangeFalg: true,
        id: '',
        studyObjectManagementList: {},
        ruleFormList: {},
      },
      studyObjectManagementList: {
        studyObject: '', // 研究对象
        groupingDesign: '', // 分组设计
        blindedSet: '', // 盲法设置
        randomFashion: '', // 随即方式
        drugInfos: [], // 研究对象药物列表信息
      },

      stateTabChange: (flag, e) => {
        if (e) {
          state.stateChange.id = e.id
        } else {
          state.stateChange.id = ''
        }
        if (!state.stateChange.id) {
          state.stateChange.stateChangeFalg = false
          state.stateChange.tabChangeFalg = flag
          return
        }
        studyDrugInfoId(state.stateChange.id).then((res: any) => {
          res.drugSpecialType === 1 ? res.drugSpecialType = '开放' : res.drugSpecialType = '盲态'
          state.stateChange.ruleFormList = res
          state.stateChange.stateChangeFalg = false
          state.stateChange.tabChangeFalg = flag
          state.stateChange.studyObjectManagementList =
          state.studyObjectManagementList
        })
      },
      // 获取数据
      onLoad: () => {
        studyDrugs(store.state.studyItem.studyId).then((res: any) => {
          state.studyObjectManagementList = res
        })
      }
    })
    onMounted(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="scss" scoped>
.studyObjectManagement {
  width: 100%;
  // min-width: 1000px;
  // overflow-x: scroll;
  // .innerbox::-webkit-scrollbar,
  // .innerbox::-webkit-scrollbar-thumb,
  // .innerbox::-webkit-scrollbar-track{
  //   opacity: 0;
  //  }
  .head {
    width: 100%;
    .head-box {
      width: 100%;
      background: #fff;
      margin-top: 10px;
      padding: 20px 40px;
      box-sizing: border-box;
      div {
        span {
          &:first-child {
            color: rgb(156, 152, 152);
          }
          &:last-child {
            margin-left: 10px;
          }
        }
      }
      .head-boxBottom {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
      }
    }
  }
  .main {
    width: 100%;
    margin-top: 20px;
    .mainTop {
      display: flex;
      justify-content: space-between;
      height: 30px;
      line-height: 30px;
      span {
        font-weight: 700;
      }
      div {
        display: inline-block;
        color: #fff;
        padding: 0 10px;
        background: rgb(64, 158, 255);
        cursor: pointer;
      }
    }
    .containerContent {
      width: 100%;
      background: #fff;
      padding: 10px 20px 20px;
      margin-top: 20px;
      box-sizing: border-box;
      overflow: hidden;
      .edit {
        text-align: right;
        span {
          color: rgb(0, 127, 247);
          cursor: pointer;
        }
      }
      .containerContentBox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .containerContentBoxLeft {
          width: 120px;
          img {
            display: block;
            width: 100px;
            height: 100px;
          }
        }
        .containerContentBoxRight {
          flex: 1;
          .content {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            div {
              width: 50%;
              display: flex;
              span {
                &:first-child {
                  width: 130px;
                  text-align: right;
                  color: rgb(156, 152, 152);
                }
                &:last-child {
                  margin-left: 10px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
          .single {
            display: flex;
            margin-top: 15px;
            div {
              width: 100%;
              display: flex;
              span {
                &:first-child {
                  width: 130px;
                  text-align: right;
                  color: rgb(156, 152, 152);
                }
                &:last-child {
                  margin-left: 10px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
