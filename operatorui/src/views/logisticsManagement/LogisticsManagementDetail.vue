<template>
  <div class="logistics-detail">
    <div class="flex justify-between">
      <span>编辑</span>
      <div>
        <el-button plain @click="routerGo">返回</el-button>
        <el-button type="primary" :loading="loading" @click="saveData">保存</el-button>
      </div>
    </div>
    <div class="box-border w-full py-5 pl-5">
      <div class="mb-5">
        <div class="mb-2">物品类型</div>
        <el-input v-model="allDataObj.goodsTypeName" size="large" style="width:30%" disabled />
      </div>
      <div class="flex items-center mb-5">
        <el-button type="primary" class="mr-4" @click="applyFlagShow = true">适用中心</el-button>
        <span>已选择 {{ shuttleValue.length }}/{{ shuttleData.length }} 家中心</span>
      </div>
      <div>
        <div class="ft-15 flex items-center justify-between">
          <span>物流相关配置</span>
          <div>
            <el-button type="primary" class="mr-4" @click="issueRule(1)">批量设置OMS渠道映射</el-button>
            <el-button type="primary" class="mr-4" @click="issueRule(2)">批量设置收件地址-受试者寄出</el-button>
            <el-button type="primary" class="mr-4" @click="issueRule(3)">批量设置收件地址-中心寄出</el-button>
          </div>
        </div>
        <!-- 表格 -->
        <trial-table
          ref="logisticsManagementDetailRef"
          title=""
          :request="getResearchCenterApplyList"
          :columns="columns"
          :showbtnfalg="true"
          :hide-center="true"
          @selectionChange="handleSelectionChange"
        >
          <template #patientAddress="scope">
            <span v-html="scope.row.siteLogisticsConfigsObj?.patientAddress" />
          </template>
          <template #siteAddress="scope">
            <span v-html="scope.row.siteLogisticsConfigsObj?.siteAddress" />
          </template>
          <template #operate="scope">
            <span class="editBtnBlue" @click="issueRule(0, scope.row, scope.$index)">
              设置
            </span>
          </template>
        </trial-table>
      </div>
    </div>
    <trial-dialog v-model="applyFlagShow" :my-dialog-body-style="myDialogBodyStyle">
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="中心名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :left-default-checked="leftDefaultChecked"
            :right-default-checked="rightDefaultChecked"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain @click="shuttleCancel">取消</el-button>
          <el-button type="primary" @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="addressShow" :title="myTitle">
      <template #DialogBody>
        <div class="MyDialog-box">
          <div v-if="addressShowNum === 1 || addressShowNum === 0" class="pt-5">
            <el-form
              ref="logisticsFormRef"
              label-position="top"
              label-width="80px"
              :model="dataForm"
              :rules="rules"
            >
              <el-form-item label="OMS渠道映射" prop="channelId">
                <el-select
                  v-model="dataForm.channelId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in omsMapList"
                    :key="item.channelId"
                    :label="item.channelName"
                    :value="item.channelId"
                  />
                </el-select>
              </el-form-item>

            </el-form>
          </div>
          <!-- 默认收件地址-受试者寄出 -->
          <el-form
            ref="logisticsFormRef"
            :model="dataForm"
            :rules="rules"
          >
            <div v-if="addressShowNum === 2 || addressShowNum === 0" class="mt-10">
              <div class="mb-5">默认收件地址-受试者寄出</div>
              <div class="flex">
                <el-form-item label="联系人" style="width: 40%;margin-right:50px">
                  <el-input
                    v-model.trim="dataForm.patientShipperAddress.name"
                    placeholder="请输入"
                  />
                </el-form-item>
                <el-form-item
                  label="联系电话"
                  style="width: 40%"
                  prop="patientShipperAddress.mobile"
                  :rules="{ pattern: /^1[3-9][0-9]\d{8}$/, message: '请输入正确的手机号', trigger: 'blur' }"
                >
                  <el-input
                    v-model.trim="dataForm.patientShipperAddress.mobile"
                    placeholder="请输入"
                  />
                </el-form-item>
              </div>
              <el-form-item label="所在地区" style="width:100%">
                <el-cascader
                  v-model="dataForm.citys"
                  clearable
                  style="width: 95%"
                  :options="cityOptions"
                  :props="{
                    expandTrigger: 'hover',
                  }"
                />
              </el-form-item>
              <el-form-item label="详细地址">
                <el-input
                  v-model.trim="dataForm.patientShipperAddress.addressDetail"
                  style="width: 95%"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item label="地址可编辑">
                <el-select
                  v-model="dataForm.patientShipperAddressEditable"
                  placeholder="请选择"
                >
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>
            </div>
            <div v-if="addressShowNum === 3 || addressShowNum === 0" class="mt-10">
              <div class="mb-5">默认收件地址-中心寄出</div>
              <div class="flex">
                <el-form-item label="联系人" style="width: 40%;margin-right:50px">
                  <el-input
                    v-model.trim="dataForm.siteShipperAddress.name"
                    placeholder="请输入"
                  />
                </el-form-item>
                <el-form-item
                  label="联系电话"
                  style="width: 40%"
                  prop="siteShipperAddress.mobile"
                  :rules="{ pattern: /^1[3-9][0-9]\d{8}$/, message: '请输入正确的手机号', trigger: 'blur' }"
                >
                  <el-input
                    v-model.trim="dataForm.siteShipperAddress.mobile"
                    placeholder="请输入"
                  />
                </el-form-item>
              </div>
              <el-form-item label="所在地区" style="width:100%">
                <el-cascader
                  v-model="dataForm.citysTwo"
                  clearable
                  style="width: 95%"
                  :options="cityOptions"
                  :props="{
                    expandTrigger: 'hover',
                  }"
                />
              </el-form-item>
              <el-form-item label="详细地址">
                <el-input
                  v-model.trim="dataForm.siteShipperAddress.addressDetail"
                  style="width: 95%"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item label="地址可编辑">
                <el-select
                  v-model="dataForm.siteShipperAddressEditable"
                  placeholder="请选择"
                >
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div class="footer-flex-end justify-center">
          <el-button size="large" @click="cancelSelect">取 消</el-button>
          <el-button size="large" type="primary" @click="submit">保 存</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'
import { getCitys } from '@/api/home'
import { useStore } from 'vuex'
import { getChannels, putGoodsTypeAndSiteLogisticsConfigs, getGoodsTypeDetail } from '@/api/logisticsManagement'
import { getCustomTasksTransfer } from '@/api/customTask'
import { deepClone } from '@/utils'

export default defineComponent({
  name: 'LogisticsManagementDetail',
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      loading: false,
      logisticsManagementDetailRef: null,
      logisticsFormRef: null, // 表单得ref
      applyFlagShow: false, // 穿梭框显示
      shuttleValue: [], // 选中项绑定值
      shuttleValueCopy: [],
      shuttleData: [], // 穿梭框中的数据
      leftDefaultChecked: [], // 初始状态下选中的
      rightDefaultChecked: [], // 初始状态下选中的
      allDataObj: {},
      shuttleProps: {
        key: 'siteId',
        label: 'statusStrAndsiteName'
      },
      myDialogBodyStyle: {
        width: '60%'
      },
      // 表格头部
      columns: [
        { type: 'selection' }, // table勾选框
        { label: '中心信息', prop: 'siteLogisticsConfigsObj.siteName', width: 300 },
        { label: 'OMS渠道映射', prop: 'siteLogisticsConfigsObj.channelName', width: 150 },
        {
          label: '默认收件地址-受试者寄出',
          prop: 'siteLogisticsConfigsObj.patientAddress',
          numberToStingSort: 'patientAddress', // 自定义单元格内容的插槽名称
          width: 300
        },
        {
          label: '默认收件地址-中心寄出',
          prop: 'siteLogisticsConfigsObj.siteAddress',
          numberToStingSort: 'siteAddress', // 自定义单元格内容的插槽名称
          width: 300
        },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      rules: {
        channelId: [{ required: true, message: '请选择', trigger: 'blur' }],
      },
      omsMapList: [],
      dataFormCopy: {
        citys: [],
        citysTwo: [],
        siteShipperAddressEditable: 0,
        patientShipperAddressEditable: 0,
        channelId: '',
        channelName: '',
        siteShipperAddress: {
          addressId: '',
          name: '',
          mobile: '',
          province: '',
          city: '',
          area: '',
          provinceCode: '',
          cityCode: '',
          areaCode: '',
          addressDetail: ''
        },
        patientShipperAddress: {
          addressId: '',
          name: '',
          mobile: '',
          province: '',
          city: '',
          area: '',
          provinceCode: '',
          cityCode: '',
          areaCode: '',
          addressDetail: ''
        }
      },
      dataForm: { // 中心或者受试者寄出的表单
        citys: [],
        citysTwo: [],
        siteShipperAddressEditable: 0,
        patientShipperAddressEditable: 0,
        channelId: '',
        channelName: '',
        siteShipperAddress: {
          addressId: '',
          name: '',
          mobile: '',
          province: '',
          city: '',
          area: '',
          provinceCode: '',
          cityCode: '',
          areaCode: '',
          addressDetail: ''
        },
        patientShipperAddress: {
          addressId: '',
          name: '',
          mobile: '',
          province: '',
          city: '',
          area: '',
          provinceCode: '',
          cityCode: '',
          areaCode: '',
          addressDetail: ''
        }
      },
      addressShow: false, // 设置弹窗显示和隐藏
      myTitle: '',
      addressShowNum: 0, // 控制显示的内容
      multipleSelection: [], // 多选的数组
      cityOptions: [], // 省市区
      pitchOnCertainLineIndx: null, // 选中某行
      tableList: [],

      routerGo: () => {
        RESEARCHCENTER_INFOS.resetMethod.handleReset()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      // 穿梭框点击取消的时候
      shuttleCancel: () => {
        state.shuttleValue = state.shuttleValueCopy
        state.applyFlagShow = false
        state.dataForm = deepClone(state.dataFormCopy)
      },
      // 穿梭框点击确定的时候
      shuttleConfirm: () => {
        state.shuttleValueCopy = state.shuttleValue
        state.tab()
        state.applyFlagShow = false
      },
      // 多选框
      handleSelectionChange: (val) => {
        state.multipleSelection = deepClone(val)
      },
      // 设置的时间
      issueRule: (idx, row, rowIndex) => {
        state.addressShowNum = idx
        if (idx === 0) {
          state.pitchOnCertainLineIndx = rowIndex // 选中某行
          state.dataForm = deepClone(row.siteLogisticsConfigsObj)
          state.myTitle = row.siteName
          state.addressShow = true
        } else {
          if (!state.multipleSelection.length) {
            ElMessage.warning({
              showClose: true,
              message: '请选择',
            })
          } else {
            state.myTitle = '批量设置'
            state.addressShow = true
            state.dataForm = deepClone(state.dataFormCopy)
          }
        }
      },
      // 取消
      cancelSelect: () => {
        state.addressShow = false
      },
      async getResearchCenterApplyList() {
        return {
          data: []
        }
      },
      // 转化地址用的
      returnAddress: (ite) => {
        const name = {
          siteAddress: '',
          patientAddress: ''
        }
        if (ite.siteLogisticsConfigsObj) {
          const sAddress = ite.siteLogisticsConfigsObj.siteShipperAddress
          const pAddress = ite.siteLogisticsConfigsObj.patientShipperAddress
          if (ite.siteLogisticsConfigsObj.siteShipperAddress) {
            name.siteAddress = `
              ${sAddress.name}${sAddress.mobile && sAddress.name ? ',' : ''}${sAddress.mobile}${sAddress.mobile && sAddress.name ? '<br>' : ''}
              ${sAddress.province}
              ${sAddress.city}
              ${sAddress.area}
              ${sAddress.addressDetail}
            `
          }
          if (ite.siteLogisticsConfigsObj.patientShipperAddress) {
            name.patientAddress = `
              ${pAddress.name}${pAddress.mobile && pAddress.name ? ',' : ''}${pAddress.mobile}${pAddress.mobile && pAddress.name ? '<br>' : ''}
              ${pAddress.province}
              ${pAddress.city}
              ${pAddress.area}
              ${pAddress.addressDetail}
            `
          }
        }
        return name
      },
      // 得到新的表格数据
      tab: () => {
        state.tableList = []
        state.shuttleData.forEach((ite: any) => {
          state.shuttleValue.forEach((item) => {
            if (ite.siteId === item) {
              const address = state.returnAddress(ite)
              if (address?.siteAddress || address?.patientAddress) {
                ite.siteLogisticsConfigsObj.siteAddress = address.siteAddress
                ite.siteLogisticsConfigsObj.patientAddress = address.patientAddress
              }
              state.tableList.push(ite)
            }
          })
        })
        if (state.logisticsManagementDetailRef.tableData) {
          state.logisticsManagementDetailRef.tableData = []
        }
        state.logisticsManagementDetailRef.tableData = state.tableList
      },
      // 弹窗的保存事件
      submit: () => {
        if (state.addressShowNum !== 1) {
          // 处理城市
          if (state.dataForm?.citysTwo &&
                state.dataForm.citysTwo.length === 3) {
            const sAddress = state.dataForm.siteShipperAddress
            sAddress.provinceCode = state.dataForm.citysTwo[0]
            sAddress.cityCode = state.dataForm.citysTwo[1]
            sAddress.areaCode = state.dataForm.citysTwo[2]
            state.cityOptions.forEach((item: any) => {
              item.children.forEach((itemClild: any) => {
                itemClild.children.forEach((itemClildClild: any) => {
                  if (itemClildClild.value === state.dataForm.citysTwo[2]) {
                    sAddress.province = item.label
                    sAddress.city = itemClild.label
                    sAddress.area = itemClildClild.label
                    return
                  }
                })
              })
            })
          } else {
            const sAddress = state.dataForm.siteShipperAddress
            sAddress.province = ''
            sAddress.city = ''
            sAddress.area = ''
            sAddress.provinceCode = ''
            sAddress.cityCode = ''
            sAddress.areaCode = ''
            state.dataForm.siteAddress = ''
          }

          // 处理城市
          if (state.dataForm?.citys &&
                state.dataForm.citys.length === 3) {
            const pAddress = state.dataForm.patientShipperAddress
            pAddress.provinceCode = state.dataForm.citys[0]
            pAddress.cityCode = state.dataForm.citys[1]
            pAddress.areaCode = state.dataForm.citys[2]
            state.cityOptions.forEach((item: any) => {
              item.children.forEach((itemClild: any) => {
                itemClild.children.forEach((itemClildClild: any) => {
                  if (itemClildClild.value === state.dataForm.citys[2]) {
                    pAddress.province = item.label
                    pAddress.city = itemClild.label
                    pAddress.area = itemClildClild.label
                    return
                  }
                })
              })
            })
          } else {
            const pAddress = state.dataForm.patientShipperAddress
            pAddress.province = ''
            pAddress.city = ''
            pAddress.area = ''
            pAddress.provinceCode = ''
            pAddress.cityCode = ''
            pAddress.areaCode = ''
            state.dataForm.patientAddress = ''
          }
        }
        state.logisticsFormRef.validate((valid) => {
          if (valid) {
            if (state.addressShowNum === 0) {
              const tableDataSome: any = {}
              tableDataSome.siteLogisticsConfigsObj = deepClone(state.dataForm)
              const address = state.returnAddress(tableDataSome)
              tableDataSome.siteLogisticsConfigsObj.siteAddress = address.siteAddress
              tableDataSome.siteLogisticsConfigsObj.patientAddress = address.patientAddress
              state.omsMapList.forEach((item: any) => {
                if (tableDataSome.siteLogisticsConfigsObj.channelId === item.channelId) {
                  tableDataSome.siteLogisticsConfigsObj.channelName = item.channelName
                }
              })
              state.logisticsManagementDetailRef.tableData[state.pitchOnCertainLineIndx].siteLogisticsConfigsObj = deepClone(tableDataSome.siteLogisticsConfigsObj)
            } else if (state.addressShowNum === 1) {
              state.omsMapList.forEach((ite: any) => {
                if (ite.channelId === state.dataForm.channelId) {
                  state.dataForm.channelName = ite.channelName
                }
              })
              state.multipleSelection.forEach((ite: any) => {
                state.logisticsManagementDetailRef.tableData.forEach((item) => {
                  const siteLObj = item.siteLogisticsConfigsObj
                  if (ite.siteLogisticsConfigsObj.siteId === siteLObj.siteId) {
                    siteLObj.channelId = state.dataForm.channelId
                    siteLObj.channelName = state.dataForm.channelName
                  }
                })
              })
            } else {
              state.multipleSelection.forEach((ite: any) => {
                state.logisticsManagementDetailRef.tableData.forEach((item) => {
                  const siteLObj = item.siteLogisticsConfigsObj
                  if (ite.siteLogisticsConfigsObj.siteId === siteLObj.siteId) {
                    if (state.addressShowNum === 2) {
                      const ressId = siteLObj.patientShipperAddress.addressId
                      siteLObj.patientShipperAddress = deepClone(state.dataForm.patientShipperAddress)
                      siteLObj.patientShipperAddress.addressId = ressId
                      const address = state.returnAddress(item)
                      siteLObj.patientAddress = address.patientAddress
                      siteLObj.citys = [siteLObj.patientShipperAddress?.provinceCode, siteLObj.patientShipperAddress?.cityCode, siteLObj.patientShipperAddress?.areaCode]
                    } else if (state.addressShowNum === 3) {
                      const ressId = siteLObj.siteShipperAddress.addressId
                      siteLObj.siteShipperAddress = deepClone(state.dataForm.siteShipperAddress)
                      siteLObj.siteShipperAddress.addressId = ressId
                      const address = state.returnAddress(item)
                      siteLObj.siteAddress = address.siteAddress
                      siteLObj.citysTwo = [siteLObj.siteShipperAddress?.provinceCode, siteLObj.siteShipperAddress?.cityCode, siteLObj.siteShipperAddress?.areaCode]
                    }
                  }
                })
              })
            }
            state.addressShow = false
          }
        })
      },
      // 保存整个列表
      saveData: () => {
        if (!state.shuttleValue.length) {
          ElMessage.warning({
            showClose: true,
            message: '请选择中心',
          })
          return
        }
        const siteLogisticsConfigs = []
        let selectAllOmsChannel = false
        state.logisticsManagementDetailRef.tableData.forEach((item) => {
          if (!item.siteLogisticsConfigsObj?.studyId) {
            item.siteLogisticsConfigsObj.studyId = store.state.studyItem.studyId
          }
          siteLogisticsConfigs.push(item.siteLogisticsConfigsObj)
        })

        for (const i of siteLogisticsConfigs) {
          if (!i.channelId) {
            selectAllOmsChannel = true
            ElMessage.warning({
              showClose: true,
              message: '请完善OMS映射渠道',
            })
            break
          }
        }
        if (selectAllOmsChannel) return
        state.loading = true
        const data = {
          id: state.allDataObj?.id,
          goodsTypeName: state.allDataObj?.goodsTypeName,
          siteLogisticsConfigs
        }
        putGoodsTypeAndSiteLogisticsConfigs(store.state.studyItem.studyId, data).then((res) => {
          ElMessage.success('保存成功')
          RESEARCHCENTER_INFOS.resetMethod.handleReset()
          RESEARCHCENTER_INFOS.contentVisible = false
          state.loading = false
        }).catch(() => {
          state.loading = false
        })
      },
      // 进入页面加载，写在了onMounted中
      onLoad: async() => {
        const res: any = await getCustomTasksTransfer(store.state.studyItem.studyId, {})
        if (res?.customTaskSiteRuleList) {
          res.customTaskSiteRuleList.forEach((item) => {
            res.studySiteList.push(item)
          })
        }
        state.shuttleData = res.studySiteList

        const rest: any = await getGoodsTypeDetail(store.state.studyItem.studyId, RESEARCHCENTER_INFOS.researchContent.id)
        if (state.shuttleData) {
          state.shuttleData.forEach((ite: any) => {
            ite.statusStrAndsiteName = `${ite.edcSiteCode}-(${ite.siteStatusStr})  ${ite.siteName}`
            ite.siteLogisticsConfigsObj = deepClone(state.dataFormCopy)
            ite.siteLogisticsConfigsObj.siteId = ite.siteId
            ite.siteLogisticsConfigsObj.siteName = ite.siteName
            if (rest.siteLogisticsConfigs?.length) {
              rest.siteLogisticsConfigs.forEach((item) => {
                if (item?.siteShipperAddress) {
                  item.citysTwo = [item.siteShipperAddress?.provinceCode, item.siteShipperAddress?.cityCode, item.siteShipperAddress?.areaCode]
                }
                if (item?.patientShipperAddress) {
                  item.citys = [item.patientShipperAddress?.provinceCode, item.patientShipperAddress?.cityCode, item.patientShipperAddress?.areaCode]
                }
                if (ite.siteId === item.siteId) {
                  state.shuttleValue.push(item.siteId)
                  ite.siteLogisticsConfigsObj = item
                }
              })
            }
          })
        }
        state.shuttleValueCopy = state.shuttleValue
        state.allDataObj = rest
        state.tab()
      }
    })
    onMounted(() => {
      getCitys().then((res: any) => {
        if (res && res?.length && Array.isArray(res)) {
          state.cityOptions = res
        }
      })
      // 获取渠道
      const data = {
        goodTypeName: RESEARCHCENTER_INFOS.researchContent?.goodsTypeName
      }
      getChannels(store.state.studyItem.studyId, data).then((res: any) => {
        state.omsMapList = res
      })
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.logistics-detail {
  width: 100%;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  :deep(.my-el-table) {
    padding: 0;
  }
}

.MyDialog-box {
  border-top: 1px solid #d7d7d7;
}
</style>
