<template>
  <div class="customTask">
    <div v-if="!RESEARCHCENTER_INFOS?.contentVisible">
      <trial-table
        ref="logisticsManagementRef"
        title=""
        :request="getResearchCenterList"
        :columns="columns"
        :showbtnfalg="true"
        :hide-center="true"
      >
        <!-- :requestExport="getResearchCenterExport" -->
        <template #operate="scope">
          <span class="editBtnBlue mr-3" @click="editCustomTaskInfoItem(scope.row)">
            编辑
          </span>
        </template>
        <template #hideCenter>
          <div class="researchCenter-hideCenter">
            <div class="my-5 flex justify-between items-center">
              <span>托寄物品</span>
              <div>
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="showVisitClFlag = true"
                >同步OMS</el-button>
              </div>
            </div>
          </div>
        </template>
      </trial-table>
    </div>
    <LogisticsManagementDetail v-if="RESEARCHCENTER_INFOS?.contentVisible" />
    <trial-dialog v-model="showVisitClFlag" title="提示" :my-dialog-body-style="myDialogBodyStyle">
      <template #DialogBody>
        是否确认同步OMS系统中托寄物品信息？
      </template>
      <template #footer>
        <div class="mt-10 text-center">
          <el-button size="large" :loading="loading" @click="showVisitClFlag = false">取 消</el-button>
          <el-button
            size="large"
            type="primary"
            :loading="loading"
            @click="syncOMS"
          >确 定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, provide, reactive, toRefs } from 'vue'
import LogisticsManagementDetail from '@/views/logisticsManagement/LogisticsManagementDetail.vue'
import { getGoodsType, getPullOmsGoodsType } from '@/api/logisticsManagement'
import { useStore } from 'vuex'
import { parseTime } from '@/utils'

export default defineComponent({
  name: 'LogisticsManagement', // 托寄物品类型
  components: {
    LogisticsManagementDetail,
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
    })
    const state = reactive({
      loading: false,
      logisticsManagementRef: null,
      showVisitClFlag: false,
      myDialogBodyStyle: {
        width: '30%',
        minHeight: '0',
      },
      columns: [
        { label: '物品类型', prop: 'goodsTypeName', width: 300 },
        { label: '更新时间', prop: 'lastUpdateTime', width: 200 },
        { label: '更新人', prop: 'lastUpdateUserName', width: 150, },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 编辑/新建
      editCustomTaskInfoItem: (row) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.logisticsManagementRef
      },
      // 同步oms
      syncOMS: () => {
        state.loading = true
        getPullOmsGoodsType(store.state.studyItem.studyId).then(() => {
          ElMessage.success('同步成功')
          state.logisticsManagementRef.refresh()
          state.showVisitClFlag = false
          state.loading = false
        }).catch(() => {
          state.showVisitClFlag = false
          state.loading = false
        })
      },
      // 表格数据
      async getResearchCenterList() {
        const rest: any = await getGoodsType(store.state.studyItem.studyId)
        if (rest.length) {
          rest.forEach((item) => {
            item.lastUpdateTime = parseTime(new Date(item.lastUpdateTime), '{y}-{m}-{d} {h}:{i}:{s}')
          })
        }
        return {
          data: rest || []
        }
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">

</style>
