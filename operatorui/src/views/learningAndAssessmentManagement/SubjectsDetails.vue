<template>
  <div class="details w-full EditQuestionnaire">
    <el-tabs v-model="activeName" @tab-change="tabsHandleChange">
      <el-tab-pane label="学习资料" name="learningMaterials">
        <div class="content-top box-border w-full py-5 details-tabs-box">
          <div class="flex justify-end">
            <el-button plain @click="routerGo">返回</el-button>
            <el-button type="primary" @click="preservation">保存</el-button>
          </div>
          <div class="box-border px-2.5">
            <el-form
              ref="detailsNewForm"
              label-position="top"
              label-width="80px"
              :model="dataForm"
              :rules="rules"
            >
              <!-- 标题/权重值输入框 -->
              <div class="w-4/5 flex mb-5 justify-between">
                <el-form-item label="标题" style="width: 55%" prop="trianingTitle">
                  <el-input
                    v-model.trim="dataForm.trianingTitle"
                    placeholder="请输入"
                    autocomplete="off"
                    maxlength="666"
                  />
                </el-form-item>
                <el-form-item
                  label="权重值（数值越大排序越前）"
                  style="width: 30%"
                  prop="weightedValue"
                >
                  <el-input
                    v-model.trim="dataForm.weightedValue"
                    placeholder="请输入正整数"
                    autocomplete="off"
                    maxlength="666"
                  />
                </el-form-item>

                <div class="mb-5" style="width: 10%">
                  <div class="ft-14 mr-5 mb-1.5">状态</div>
                  <el-switch
                    v-model="dataForm.isEnable"
                    class="mt-1.5"
                    :active-text="dataForm.isEnable ? '启用' : '禁用'"
                  />
                </div>
              </div>
              <div>
                <div style="font-size: 14px; margin-bottom: 5px">
                  分类标签
                </div>
                <el-form-item>
                  <el-select
                    v-model="dataForm.trainingDocumentTagIds"
                    placeholder="请选择分类标签"
                    multiple 
                    prop="trainingDocumentTagIds"
                  >
                  <el-option
                    v-for="it in tagDropoptions"
                    :key="it.id"
                    :label="it.tagName"
                    :value="it.id"
                  />
                  </el-select>
                </el-form-item>
              </div>
              <div>
                <div style="font-size: 14px; margin-bottom: 5px">
                  <span style="color: red">*</span>学习资料类型
                </div>
                <el-form-item>
                  <el-select
                    v-model="dataForm.trainingType"
                    placeholder="请选择学习资料类型"
                  >
                    <el-option label="上传文件" :value="1" />
                    <el-option label="富文本" :value="2" />
                  </el-select>
                </el-form-item>
              </div>
              <!-- 上传 -->
              <div v-if="dataForm.trainingType === 1" style="width: 100%">
                <div style="width: 50%; margin-bottom: 20px">
                  <!-- <div style="font-size: 14px; margin-bottom: 5px">
                    <span style="color: red">*</span>学习资料（请上传视频或PDF文件）
                  </div> -->
                  <div>
                    <MyUpload
                      ref="SubjectsDetailsMyUploadRef"
                      upload-file-falg
                      :custom-information-form-data="customInformationFormData"
                      :request-fun="fileFun"
                      :deletefile="deletefile"
                      :file-list="fileList"
                      :file-size="1 / 2.048"
                      :before-file-upload-type="beforeFileUploadType"
                    />
                  </div>
                </div>
              </div>
              <!-- 富文本 -->
              <div v-show="dataForm.trainingType === 2">
                <div style="font-size: 14px; margin-bottom: 5px">
                  <span style="color: red">*</span>富文本内容
                </div>
                <div>
                  <!-- <vue-qr v-if="siteUrl" :text="siteUrl" class="site" />
                  <img v-else class="site" src="@/assets/logocd.png" alt="" /> -->
                  <!-- <span>请先保存数据后再扫码预览效果</span> -->
                  <trial-wang-editor ref="subEditorTermsService" editorHeight="400px" :toolbar-config="toolbarConfig" :editor-config="editorConfig" />
                </div>
              </div>
            </el-form>
          </div>       
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="applyShow || stateVal === 2" label="适用规则" name="applyCentre">
        <div class="content-top box-border w-full py-5 details-tabs-box">
          <div class="flex justify-between items-center mb-5">
            <div class="apply-centre-hint ft-15">
              若多份学习资料适用于相同中心、或相同下发规则，可使用批量下发功能
            </div>
            <div class="flex justify-end">
              <el-button plain @click="routerGo">返回</el-button>
              <el-button type="primary" @click="saveApplyCenter">保存</el-button>
            </div>
          </div>
          <div class="flex items-center mb-5">
            <el-button type="primary" class="mr-4" @click="applyCentreClick">适用中心</el-button>
            <span>已选择 {{ shuttleValue.length }}/{{ shuttleData.length }} 家中心</span>
          </div>
          <div v-if="shuttleValue && shuttleValue.length">
            <div class="ft-15 flex items-center justify-between">
              <span>下发规则</span>
              <el-button type="primary" class="mr-4" @click="issueRule(1)">批量设置</el-button>
            </div>
            <!-- 表格 -->
            <trial-table
              ref="subjectsDetailsRef"
              title=""
              :request="getResearchCenterApplyList"
              :columns="columns"
              :showbtnfalg="true"
            >
              <!-- :requestExport="getResearchCenterExport" -->
              <template #operate="scope">
                <span class="editBtn" @click="issueRule(scope.row, scope.$index)">
                  设置
                </span>
              </template>
            </trial-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <trial-dialog v-model="flagShow">
      <template #footer>
        <div class="ejectTop box-border w-full -mt-10 pb-2.5">
          <h3 class="w-full text-center">{{ issueTopic }}</h3>
        </div>
        <div style="width: 100%">
          <div class="checkbox-bottom w-full h-full">
            <el-form ref="subjectsDetailsFormRef" label-position="top" :rules="subjectsDetailsRules" :model="dialogForm" label-width="auto" class="subject-radio">
              <el-form-item label="下发规则" prop="distributionRule" class="mb-[20px]">
                <el-radio-group v-model="dialogForm.distributionRule" @change="handleRadioChange">
                  <div class="flex flex-col flex-1">
                    <el-radio :label="1" class="w-full flex mb-[20px]">
                      <div>每个受试者入组后，第</div>
                      <el-form-item
                        prop="specificRules"
                        class="mb-0 mx-1.5 leading-7"
                        :rules="dialogForm.distributionRule === 1 ? [{ required: true, message: '请输入非负整数', trigger: 'change'}] : [{ required: false, message: '', trigger: 'change'}]"
                      >
                        <el-input
                          v-model.trim="dialogForm.specificRules"
                          :disabled="dialogForm.distributionRule !== 1"
                          placeholder="请输入非负整数"
                        />
                      </el-form-item>
                      <span>天</span>
                      <el-popover
                        placement="top-start"
                        :width="200"
                        trigger="hover"
                        content="DCT目前定义入组当天为第1天"
                      >
                        <template #reference>
                          <el-icon class="label-warning ml-1.5"><Warning /></el-icon>
                        </template>
                      </el-popover>
                    </el-radio>
                    <el-radio :label="2" class="flex w-full">
                      <div>指定时间</div>
                      <el-form-item
                        prop="specificRulesDate"
                        class="mb-0 mx-1.5 leading-7"
                        :rules="dialogForm.distributionRule === 2 ? [{ required: true, message: '请选择日期时间', trigger: 'change'}] : [{ required: false, message: '', trigger: 'change'}]"
                      >
                        <el-date-picker
                          v-model="dialogForm.specificRulesDate"
                          type="datetime"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          :disabled="dialogForm.distributionRule !== 2"
                          placeholder="选择日期时间"
                        />
                      </el-form-item>
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
              <div v-if="groupingDesignShow !== 1" class="mt-[20px] max-w-[260px]">
                <el-form-item label="适用组别" prop="armArr">
                  <el-select
                    v-model="dialogForm.armArr"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    clearable
                    placeholder="请选择"
                    class="w-full"
                  >
                    <el-option
                      v-for="(item, index) in groupingList"
                      :key="index"
                      :label="item.armName"
                      :value="item.armCode"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div class="flex justify-center mt-7">
          <el-button plain @click="clickBtn('back')">取消</el-button>
          <el-button type="primary" @click="clickBtn('add')">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="applyFlagShow" :my-dialog-body-style="myDialogBodyStyle">
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="中心名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :left-default-checked="leftDefaultChecked"
            :right-default-checked="rightDefaultChecked"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain @click="shuttleCancel">取消</el-button>
          <el-button type="primary" @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import {
  defineComponent,
  inject,
  nextTick,
  onMounted,
  reactive,
  toRefs,
} from 'vue'
import { useStore } from 'vuex'
import MyUpload from '@/components/Upload/index.vue'
import { Warning } from '@element-plus/icons-vue'
import {
  postDocumentFile, // 上传文件
  postTrainingDocument, // 保存新建或者编辑
  getTrainingDocumentDetails, // 获取详情数据
  getSiteRules,
  getTransfer,
  putDistribution,
  getTagDrop
} from '@/api/learningAndAssessmentManagement'
import { studyStudyArmsInfo } from '@/api/dosageRegimenManagement'
import { deepClone } from '@/utils'

export default defineComponent({
  name: 'SubjectsDetails', // 学习资料——受试者端
  components: {
    MyUpload,
    Warning,
  },
  props: {
    // 请求数据的方法
    request: {
      type: Function,
      default: () => {},
    },
  },
  setup({ request }) {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const base64LimitSizeNum = 0.5 * 1024 * 1024
    const state = reactive({
      editorConfig: {
        MENU_CONF: {
          uploadImage: {
            server: '/api', // 没有配置地址
            base64LimitSize: base64LimitSizeNum,
            // 单个文件上传失败
            onError() {
              ElMessage.error(`上传图片最大${base64LimitSizeNum}kb`)
            },
          }
        }
      },
      toolbarConfig: {
        // 删除菜单
        excludeKeys: [
          'insertTable', // 插入表格
          'blockquote', // 引用
          // 'uploadImage', // 上传图片
          'uploadVideo' // 上传视频
        ],
      },
      stateVal: 0, // 编辑/新建
      subEditorTermsService: null, // 富文本ref
      SubjectsDetailsMyUploadRef: null,
      contentHtml: '',
      siteUrl: '',
      dataForm: {
        id: null, // 主建ID
        userType: 2, // 0 未知、1 医生、2 患者
        fileType: 0, // 0 = 未知, 1 = 图片, 2 = 音频, 3 = PDF, 4 = 视频, 10 = 其它
        trianingTitle: '', // 标题
        weightedValue: null, // 权重值
        isEnable: true, // 是否启用禁用
        minimumTrainingTime: 0, // 最小学习时间
        trainingFileId: null, // 学习资料的ID
        trainingFileUrl: '', // 访问路径
        videoCoverId: '', // 视频的封面
        videoCoverUrl: '', // 视频的封面URL
        trainingType: 1, // 0 = 未知, 1 = 文件形式, 2 = 富文本形式
        trainingText: '', // 富文本内容
        filePath: '', // 上传文件路径
        trainingDocumentTagIds: [], // 分类标签选中
      },
      detailsNewForm: null,
      fileList: [], // 用于回显上传的文件
      rules: {
        trianingTitle: [{ required: true, message: '请输入', trigger: 'blur' }],
        weightedValue: [
          { required: true, message: '请输入', trigger: 'blur' },
          {
            pattern: /^[1-9]\d*$/,
            message: '请填写正整数',
            trigger: 'blur',
          },
        ],
      },
      // 上传文件
      customInformationFormData: {
        customInformationFormDataObj: {
          icfFilePath: '', // 文件声明路径
          icfFileName: '', // 文件名
          icfFileUrl: '', // 文件访问路径
        },
      },
      // tab
      activeName: 'learningMaterials',
      // 是否显示适用中心
      applyShow: false,
      subjectsDetailsRef: null, // 表格的ref
      beforeFileUploadType: ['.pdf', '.mp4', 'mp3', '.jpg', '.png', '.jpeg'],
      // 表格头部
      columns: [
        // { type: 'selection' }, // table勾选框
        { label: '中心信息', prop: 'siteName', width: 400 },
        { label: '中心状态', prop: 'siteStatusStr', width: 150 },
        { label: '下发规则', prop: 'distributionRuleStr', width: 300 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      flagShow: false, // 弹窗显示
      issueTopic: '', // 弹窗头部
      applyFlagShow: false, // 穿梭框显示
      shuttleValue: [], // 选中项绑定值
      shuttleValueCopy: [], // 拷贝一份选中项绑定值
      shuttleData: [], // 穿梭框中的数据
      leftDefaultChecked: [], // 初始状态下选中的
      rightDefaultChecked: [], // 初始状态下选中的
      shuttleProps: {
        key: 'siteId',
        label: 'statusStrAndsiteName'
      },
      myDialogBodyStyle: {
        width: '60%'
      },
      tagDropoptions: [], // 分类标签集合
      figureNum: null, // 点击表格中某一个数据
      batchSetBtn: false, // 点击批量设置的时候
      applyCentreNew: true, // 新建时点击学习资料保存后走适用中心接口
      groupingList: [],
      subjectsDetailsFormRef: null,
      dialogForm: {
        distributionRule: null,
        specificRulesDate: '',
        specificRules: '',
        arm: '',
        armArr: []
      },
      groupingDesignShow: store.state.studyItem.groupingDesign,
      handleRadioChange: (e) => {
        if (e === 1) {
          state.dialogForm.specificRulesDate = ''
        } else if (e === 2) {
          state.dialogForm.specificRules = ''
        }
      },
      subjectsDetailsRules: {
        distributionRule: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        armArr: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      // 上传
      fileFun: (fileObj) => {
        const myFormDataObj = new FormData()
        const fileName = fileObj.file.name
        const pos = fileName.lastIndexOf('.')
        const lastName = fileName.substring(pos, fileName.length)
        const limitFileType = lastName.toLowerCase()
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        // 0 = 未知, 1 = 图片, 2 = 音频, 3 = PDF, 4 = 视频, 10 = 其它
        var fileType = 0
        if (limitFileType === '.pdf') {
          fileType = 3
        } else if (limitFileType === '.png' || limitFileType === '.jpg' || limitFileType === '.jpeg') {
          fileType = 1
        } else {
          fileType = 4
        }
        state.dataForm.fileType = fileType
        myFormDataObj.append('CheckImageFiles', fileObj.file)
        postDocumentFile(
          store.state.studyItem.studyId,
          fileType,
          myFormDataObj
        )
          .then((res) => {
            loading.close()
            ElMessage.success('上传成功')
            state.dataForm.trainingFileId = res[0].dctCommonFileId
            state.dataForm.trainingFileUrl = res[0].fileAccessUrl
            state.dataForm.filePath = res[0].filePath
            if (state.SubjectsDetailsMyUploadRef.fileList.length === 0) {
              state.SubjectsDetailsMyUploadRef.fileList.push({
                name: fileName,
                url: state.dataForm.trainingFileUrl
              })
            }
          })
          .catch(() => {
            state.SubjectsDetailsMyUploadRef.fileList.length = 0
            state.dataForm.trainingFileId = ''
            state.dataForm.filePath = ''
            state.dataForm.trainingFileUrl = ''
            loading.close()
          })
      },
      // 删除文件
      deletefile: () => {
        state.SubjectsDetailsMyUploadRef.fileList.length = 0
        state.dataForm.trainingFileId = ''
        state.dataForm.trainingFileUrl = ''
        state.dataForm.filePath = ''
      },
      // 返回
      routerGo: () => {
        request()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      // 保存学习资料
      preservation: () => {
        state.contentHtml = state.subEditorTermsService.gainTxt('set')
        if (
          state.dataForm.trainingType === 1 &&
          (!state.dataForm.trainingFileId ||
            !state.dataForm.trainingFileUrl)
        ) {
          ElMessage.warning({
            showClose: true,
            message: '请完善基本信息',
          })
        } else if (state.dataForm.trainingType === 2 && !state.contentHtml) {
          ElMessage.warning({
            showClose: true,
            message: '请完善基本信息',
          })
        } else {
          state.detailsNewForm.validate((valid) => {
            if (valid) {
              const loading = ElLoading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0, 0, 0, 0.7)',
              })
              if (state.dataForm.trainingType === 1) {
                state.contentHtml = ''
              } else if (state.dataForm.trainingType === 2) {
                state.dataForm.videoCoverId = ''
                state.dataForm.videoCoverUrl = ''
                state.dataForm.trainingFileUrl = ''
              }
              state.dataForm.weightedValue = Number(state.dataForm.weightedValue)
              state.dataForm.trainingText = state.contentHtml
              //   保存接口
              postTrainingDocument(store.state.studyItem.studyId, state.dataForm)
                .then((res) => {
                  state.dataForm.id = res.id
                  ElMessage.success('保存成功')
                  state.applyShow = true
                  state.activeName = 'applyCentre'
                  if (state.stateVal === 1 && state.applyCentreNew) {
                    state.applicationCenter()
                  }
                  loading.close()
                })
                .catch(() => {
                  loading.close()
                })
            }
          })
        }
      },
      // tab切换
      tabsHandleChange: (name: String) => {
        switch (name) {
          case 'learningMaterials':
            state.applyCentreNew = false
            break
          case 'applyCentre':
            state.applyCentreNew = false
            break
        }
      },
      // 点击批量设置的时候
      issueRule: (row, index) => {
        state.flagShow = true
        state.figureNum = null

        state.dialogForm = {
          distributionRule: null,
          specificRulesDate: '',
          specificRules: '',
          arm: '',
          armArr: []
        }
        if (row === 1) {
          state.issueTopic = '批量设置'
          state.batchSetBtn = true
        } else {
          if (row.distributionRule === 1) {
            row.specificRulesDate = null
          } else if (row.distributionRule === 2) {
            row.specificRules = null
          }
          state.batchSetBtn = false
          state.issueTopic = `${row.siteName}`
          // 在这里赋值
          state.dialogForm = deepClone(row)
          state.figureNum = index
        }
      },
      clickBtn: (type) => {
        if (type === 'back') {
          state.flagShow = false
        } else if (type === 'add') {
          state.subjectsDetailsFormRef.validate((valid) => {
            if (valid) {
              if (state.batchSetBtn) { // 批量的
                // 处理数据
                state.subjectsDetailsRef.tableData.forEach((item) => {
                  item.distributionRule = state.dialogForm.distributionRule
                  item.specificRules = state.dialogForm.specificRules
                  item.specificRulesDate = state.dialogForm.specificRulesDate
                  // item.arm = state.dialogForm.arm
                  if (state.dialogForm?.armArr) {
                    item.arm = state.dialogForm.armArr.join(',')
                  } else {
                    item.arm = ''
                  }
                  if (state.dialogForm.distributionRule === 1) {
                    item.distributionRuleStr = `每个受试者入组，第${state.dialogForm.specificRules}天`
                  } else if (state.dialogForm.distributionRule === 2) {
                    item.distributionRuleStr = '指定时间，' + state.dialogForm.specificRulesDate
                  }
                })
              } else {
                if (state.dialogForm.distributionRule === 1) {
                  state.dialogForm.distributionRuleStr = `每个受试者入组，第${state.dialogForm.specificRules}天`
                } else if (state.dialogForm.distributionRule === 2) {
                  state.dialogForm.distributionRuleStr = '指定时间，' + state.dialogForm.specificRulesDate
                }
                if (state.dialogForm?.armArr) {
                  state.dialogForm.arm = state.dialogForm.armArr.join(',')
                } else {
                  state.dialogForm.arm = ''
                }
                state.subjectsDetailsRef.tableData[state.figureNum] = deepClone(state.dialogForm)
              }
              state.flagShow = false
            }
          })
        }
      },
      async getResearchCenterApplyList() {
        const rest:any = await getSiteRules(RESEARCHCENTER_INFOS.researchContent.id || state.dataForm.id)
        if (rest && rest.length > 0) {
          rest.forEach((item) => {
            if (item?.arm) {
              item.armArr = item.arm.split(',')
            } else {
              item.armArr = []
            }
          })
        }
        return {
          data: rest
        }
      },
      applyCentreClick: () => {
        state.applyFlagShow = true
      },
      // 得到穿梭狂中的数据
      applicationCenter: () => {
        const data = {
          // 就是批量的传false 单个的传true
          hasTrainingSiteRule: true
        }
        getTransfer(store.state.studyItem.studyId, RESEARCHCENTER_INFOS.researchContent.id || state.dataForm.id, data).then((res) => {
          res.trainingSiteRuleList.forEach((item) => {
            if (item?.arm) {
              item.armArr = item.arm.split(',')
            } else {
              item.armArr = []
            }
          })
          res.studySiteList.forEach((item) => {
            if (item?.arm) {
              item.armArr = item.arm.split(',')
            } else {
              item.armArr = []
            }
          })
          state.shuttleValue = []
          state.shuttleValueCopy = state.shuttleValue
          nextTick(() => {
            if (state.subjectsDetailsRef?.tableData) {
              state.subjectsDetailsRef.tableData = res.trainingSiteRuleList
            }
          })
          if (res?.trainingSiteRuleList) {
            res.trainingSiteRuleList.forEach((item) => {
              res.studySiteList.push(item)
              state.shuttleValue.push(item.siteId)
            })
          }
          state.shuttleData = res.studySiteList
          if (state.shuttleData) {
            state.shuttleData.forEach((ite) => {
              ite.statusStrAndsiteName = `${ite.edcSiteCode}-(${ite.siteStatusStr})  ${ite.siteName}`
            })
          }
        })
      },
      // 穿梭框点击确定的时候
      shuttleConfirm: () => {
        // 里面存放的是key值
        state.shuttleValueCopy = state.shuttleValue
        if (state.subjectsDetailsRef?.tableData) {
          state.subjectsDetailsRef.tableData = []
        }
        if (state.shuttleData) {
          state.shuttleData.forEach((item) => {
            if (state.shuttleValue) {
              state.shuttleValue.forEach((ite) => {
                if (ite === item.siteId) {
                  // state.shuttleValue.push(item.siteId)
                  state.subjectsDetailsRef.tableData.push(item)
                }
              })
            }
          })
        }
        state.applyFlagShow = false
      },
      // 穿梭框点击取消的时候
      shuttleCancel: () => {
        state.shuttleValue = state.shuttleValueCopy
        state.applyFlagShow = false
      },
      // 保存适用中心的信息
      saveApplyCenter: () => {
        if (!state.subjectsDetailsRef?.tableData) {
          ElMessage.warning('请选择适用中心')
          return
        }
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        // 保存的时候
        const trainingDocumentIds = []
        if (RESEARCHCENTER_INFOS.researchContent.id) {
          trainingDocumentIds.push(RESEARCHCENTER_INFOS.researchContent.id)
        } else if (state.dataForm.id) {
          trainingDocumentIds.push(state.dataForm.id)
        }
        if (state.subjectsDetailsRef?.tableData) {
          state.subjectsDetailsRef.tableData.forEach((item) => {
            delete item.statusStrAndsiteName
          })
        }
        const trainingSiteRules = state.subjectsDetailsRef.tableData
        const data = {
          trainingDocumentIds,
          trainingSiteRules
        }
        putDistribution(data).then(() => {
          loading.close()
          RESEARCHCENTER_INFOS.contentVisible = false
          ElMessage.success('保存成功')
        }).catch(() => {
          loading.close()
        })
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        state.stateVal = RESEARCHCENTER_INFOS.stateVal
        if (RESEARCHCENTER_INFOS.stateVal === 2) {
          state.applyCentreNew = false
          const loading = ElLoading.service({
            lock: true,
            text: 'Loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          getTrainingDocumentDetails(RESEARCHCENTER_INFOS.researchContent.id)
            .then((res) => {
              state.dataForm = res
              if (res.trainingType === 2) {
                state.subEditorTermsService.gainTxt('get', res.trainingText)
              }
              if (res.trainingType === 1) {
                state.fileList = [
                  {
                    name: res.trainingFileName,
                    url: res.trainingFileUrl,
                  },
                ]
              }
              loading.close()
            })
            .catch(() => {
              loading.close()
            })
          state.applicationCenter()
        }
        
        // 获取分类标签集合（下拉框）
        getTagDrop(store.state.studyItem.studyId).then((res) => {
          state.tagDropoptions = res || []
        })
      },
    })
    onMounted(() => {
      if (
        store?.state?.studyItem?.studyId &&
        store?.getters?.app?.authorization
      ) {
        state.siteUrl = `${window.location.origin}doctorui/#/privacyAgreementTermsOfService==type=4==${RESEARCHCENTER_INFOS.researchContent.id}==${store.getters.app.authorization}`
      }
      // 下拉分组信息
      studyStudyArmsInfo(store.state.studyItem.studyId).then((res: any) => {
        state.groupingList = res
      })
      state.onLoad()
    })

    return {
      ...toRefs(state),
      RESEARCHCENTER_INFOS,
    }
  },
})
</script>

<style scoped lang="less" >
.details {
  .content-top {
    background: #fff;
    border-radius: 5px;
    }
    .apply-centre-hint {
      color: #f59a23;
    }
  }

.ejectTop {
  border-bottom: 2px solid #ccc;
}
.issue {
  span {
    color: red;
  }
}
.checkbox-bottom {
  :deep(.el-checkbox__inner) {
    border-radius: 50%;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .label-warning {
    cursor: pointer; /*小手*/
  }
}

:deep(.w-e-toolbar) {
  z-index: 11 !important;
}
:deep(.w-e-text-container) {
  z-index: 10 !important;
  min-height: 500px !important;
}

.details-tabs-box {
  padding: 10px 20px 20px;
  height: 100%
}
:deep(.el-tabs) {
  min-height: calc(100vh - 120px);
  background: #fff;
  .el-tabs__nav-scroll {
    background: #f0f2f5;
  }
  .el-tabs__nav {
    background: #fff;
  }
  .el-tabs__item {
    padding: 0;
  }
  .el-tabs__item,
  .is-top {
    min-width: 97px;
    text-align: center;
  }
  .el-tabs__nav-wrap::after,
  .el-tabs__active-bar {
    height: 0;
  }
  .el-tabs__item.is-active {
    color: #fff;
    background-color: #304156;
    border-radius: 5px;
  }
  .el-tabs__item {
    color: #000;
  }
}
.editBtn {
  color: #409eff;
  cursor: pointer; /*小手*/
}
:deep(.my-el-table) {
  padding: 0 !important;
}
.subject-radio {
  :deep(.el-radio__label) {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
