<template>
  <div class="researcher w-full">
    <div v-if="!RESEARCHCENTER_INFOS?.contentVisible && !RESEARCHCENTER_INFOS?.batchVisible">
      <!-- 学习资料列表-研究者 -->
      <div>
        <trial-table
          ref="DoctorInformationRef"
          title=""
          :request="getResearchCenterList"
          :columns="columns"
          :search="searchConfig"
          :showbtnfalg="true"
          :hide-center="true"
          :default-sort="defaultSort"
          @selectionChange="handleSelectionChange"
        >
          <template #operate="scope">
            <span class="editBtn" @click="editResearchCenterInfoItem(scope.row, 2)">
              编辑
            </span>
          </template>
          <template #hideCenter>
            <div class="researchCenter-hideCenter">
              <div class="my-5 flex justify-between items-center">
                <span>学习资料列表-研究者</span>
                <div>
                  <el-button type="success" @click="batchBtn">批量下发</el-button>
                  <el-button
                    type="primary"
                    @click="editResearchCenterInfoItem(null, 1)"
                  >新建</el-button>
                </div>
              </div>
            </div>
          </template>
        </trial-table>
      </div>
    </div>
    <DoctorDetails
      v-if="RESEARCHCENTER_INFOS?.contentVisible"
    />
    <DoctorBatch v-if="RESEARCHCENTER_INFOS?.batchVisible" :tips-msg="true" />
  </div>
</template>

<script lang='ts'>
import { defineComponent, provide, reactive, toRefs } from 'vue'
// import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import {
  getTrainingDocument, // 获取列表数据
} from '@/api/learningAndAssessmentManagement'
import DoctorDetails from '@/views/learningAndAssessmentManagement/DoctorDetails.vue'
import DoctorBatch from '@/views/learningAndAssessmentManagement/SubjectsBatch.vue'
export default defineComponent({
  name: 'DoctorInformation', // 研究者学习资料
  components: {
    DoctorDetails,
    DoctorBatch
  },
  setup() {
    const store = useStore()
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      stateVal: null,
      resetMethod: null,
      multipleSelection: [],
      batchVisible: false, // 批量下发的显示隐藏
    })
    const state = reactive({
      flagShow: false,
      columns: [
        { type: 'selection' }, // table勾选框
        { label: '标题', prop: 'trianingTitle', width: 280 },
        { label: '适用中心', prop: 'trainingSiteRuleNum', width: 120 },
        { label: '权重值', prop: 'weightedValue', width: 120, sortable: true },
        { label: '状态', prop: 'enableState', width: 120 },
        { label: '更新时间', prop: 'lastUpdateTime', width: 200, sortable: true },
        { label: '操作人', prop: 'operatorName', width: 100 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            label: '标题',
            name: 'title',
            type: 'input',
            defaultValue: null,
          },
          {
            type: 'select',
            label: '状态',
            name: 'isEnable',
            options: [
              {
                name: '启用',
                value: 'true'
              },
              {
                name: '禁用',
                value: 'false'
              }
            ],
            filterable: true,
          },
        ],
      },
      multipleSelection: [], // 多选数据
      researchCenterRef: null,
      studyName: store.state.studyItem.studyName,
      defaultSort: { prop: 'lastUpdateTime', order: 'descending' },
      // 多选框点击事件
      handleSelectionChange(val) {
        state.multipleSelection = val
      },
      // 批量下发学习资料
      batchBtn: () => {
        if (!state.multipleSelection.length) {
          ElMessage.warning({
            showClose: true,
            message: '请选择要下发的内容',
          })
        } else {
          state.flagShow = true
          RESEARCHCENTER_INFOS.multipleSelection = state.multipleSelection
          RESEARCHCENTER_INFOS.batchVisible = true
          RESEARCHCENTER_INFOS.contentVisible = false
        }
      },
      // 编辑/新建
      editResearchCenterInfoItem: (row, idx) => {
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.batchVisible = false
        RESEARCHCENTER_INFOS.stateVal = idx
      },
      async getResearchCenterList(params) {
        const data = {
          userType: 1,
          title: params.title,
          isEnable: params.isEnable,
          pageIndex: 1,
          pageSize: 1000
        }
        // 获取研究者学习资料下面的信息studyId=课题ID、userType=角色（0 未知 1 医生 2 患者）、标题、状态
        const rest = await getTrainingDocument(store.state.studyItem.studyId, data)
        return {
          data: rest.items
        }
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less" scoped>
.editBtn {
  color: #409eff;
  cursor: pointer; /*小手*/
}
</style>
