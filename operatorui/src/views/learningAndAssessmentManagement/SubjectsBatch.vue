<template>
  <div class="subjectsBatch p-5">
    <div class="flex items-center justify-between">
      <h3>批量下发</h3>
      <div>
        <el-button plain @click="RESEARCHCENTER_INFOS.batchVisible = false">返回</el-button>
        <el-button type="primary" :loading="loading" @click="batchCli">保存</el-button>
      </div>
    </div>
    <div class="ft-15 px-2.5">
      <div class="subjectsBatch-choice mb-6">已选择{{ multipleSelection.length }}条数据</div>
      <div v-for="(item, index) in multipleSelection" :key="index" class="mb-2">
        <div>{{ item.trianingTitle }}</div>
      </div>
      <el-divider />
      <!-- 主模块 -->
      <div class="content-top box-border w-full py-2.5">
        <div class="apply-centre-center mb-5">
          <el-button type="primary" class="mr-4" @click="applyCentreClick">适用中心</el-button>
          <span>已选择 {{ shuttleValue.length }}/{{ shuttleData.length }} 家中心</span>
        </div>
        <div v-if="shuttleValue && shuttleValue.length">
          <div class="ft-15 flex items-center justify-between">
            <span>下发规则</span>
            <el-button type="primary" class="mr-4" @click="issueRule(1)">批量设置</el-button>
          </div>
          <!-- 表格 -->
          <trial-table
            ref="SubjectsBatchRef"
            title=""
            :request="getResearchCenterApplyList"
            :columns="columns"
            :showbtnfalg="true"
          >
            <!-- :requestExport="getResearchCenterExport" -->
            <template #operate="scope">
              <span class="editBtn" @click="issueRule(scope.row, scope.$index)">
                设置
              </span>
            </template>
          </trial-table>
        </div>
      </div>
    </div>
    <trial-dialog v-model="applyFlagShow" :my-dialog-body-style="myDialogBodyStyle">
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="中心名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :left-default-checked="leftDefaultChecked"
            :right-default-checked="rightDefaultChecked"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain size @click="shuttleCancel">取消</el-button>
          <el-button type="primary" size @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="flagShow">
      <template #footer>
        <div class="ejectTop box-border w-full -mt-10 pb-2.5">
          <h3 class="w-full text-center">{{ issueTopic }}</h3>
        </div>
        <div style="width: 100%">
          <div class="checkbox-bottom w-full h-full">
            <el-form ref="subjectsBatchFormRef" label-position="top" :rules="subjectsBatchRules" :model="dialogForm" label-width="auto" class="subject-radio">
              <el-form-item label="下发规则" prop="distributionRule" class="mb-[20px]">
                <el-radio-group v-model="dialogForm.distributionRule" @change="handleRadioChange">
                  <div class="flex flex-col flex-1">
                    <el-radio :label="1" class="w-full flex mb-[20px]">
                      <div>{{ tipsMsg ? '第1例受试者入组' : '每个受试者入组后'}}，第</div>
                      <el-form-item
                        prop="specificRules"
                        class="mb-0 mx-1.5 leading-7"
                        :rules="dialogForm.distributionRule === 1 ? [{ required: true, message: '请输入非负整数', trigger: 'change'}] : [{ required: false, message: '', trigger: 'change'}]"
                      >
                        <el-input
                          v-model.trim="dialogForm.specificRules"
                          :disabled="dialogForm.distributionRule !== 1"
                          placeholder="请输入非负整数"
                        />
                      </el-form-item>
                      <span>天</span>
                      <el-popover
                        placement="top-start"
                        :width="200"
                        trigger="hover"
                        content="DCT目前定义入组当天为第1天"
                      >
                        <template #reference>
                          <el-icon class="label-warning ml-1.5"><Warning /></el-icon>
                        </template>
                      </el-popover>
                    </el-radio>
                    <el-radio :label="2" class="flex w-full">
                      <div>指定时间</div>
                      <el-form-item
                        prop="specificRulesDate"
                        class="mb-0 mx-1.5 leading-7"
                        :rules="dialogForm.distributionRule === 2 ? [{ required: true, message: '请选择日期时间', trigger: 'change'}] : [{ required: false, message: '', trigger: 'change'}]"
                      >
                        <el-date-picker
                          v-model="dialogForm.specificRulesDate"
                          type="datetime"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          :disabled="dialogForm.distributionRule !== 2"
                          placeholder="选择日期时间"
                        />
                      </el-form-item>
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
              <div v-if="groupingDesignShow !== 1 && !tipsMsg" class="mt-[20px] max-w-[260px]">
                <el-form-item label="适用组别" prop="armArr">
                  <el-select
                    v-model="dialogForm.armArr"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    clearable
                    placeholder="请选择"
                    class="w-full"
                  >
                    <el-option
                      v-for="(item, index) in groupingList"
                      :key="index"
                      :label="item.armName"
                      :value="item.armCode"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <div class="flex justify-center mt-7">
          <el-button plain @click="clickBtn('back')">取消</el-button>
          <el-button type="primary" @click="clickBtn('add')">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { getTransfer, putDistribution } from '@/api/learningAndAssessmentManagement'
import { Warning } from '@element-plus/icons-vue'
import { studyStudyArmsInfo } from '@/api/dosageRegimenManagement'
import { deepClone } from '@/utils'

export default defineComponent({
  name: 'SubjectsBatch', // 批量下发
  components: {
    Warning
  },
  props: {
    // 弹窗显示文字
    tipsMsg: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const store = useStore()
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      multipleSelection: [],
      // tab
      activeName: 'learningMaterials',
      // 是否显示适用中心
      applyShow: false,
      SubjectsBatchRef: null, // 表格的ref
      // 表格头部
      columns: [
        // { type: 'selection' }, // table勾选框
        { label: '中心信息', prop: 'siteName', width: 400 },
        { label: '中心状态', prop: 'siteStatusStr', width: 150 },
        { label: '下发规则', prop: 'distributionRuleStr', width: 300 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      flagShow: false, // 弹窗显示
      issueTopic: '', // 弹窗头部
      applyFlagShow: false, // 穿梭框显示
      shuttleValue: [], // 选中项绑定值
      shuttleValueCopy: [], // 拷贝一份选中项绑定值
      shuttleData: [], // 穿梭框中的数据
      leftDefaultChecked: [], // 初始状态下选中的
      rightDefaultChecked: [], // 初始状态下选中的
      shuttleProps: {
        key: 'siteId',
        label: 'statusStrAndsiteName'
      },
      myDialogBodyStyle: {
        width: '60%'
      },
      figureNum: null, // 点击表格中某一个数据
      batchSetBtn: false, // 点击批量设置的时候
      groupingList: [],
      subjectsBatchFormRef: null,
      dialogForm: {
        distributionRule: null,
        specificRulesDate: '',
        specificRules: '',
        arm: '',
        armArr: [],
      },
      groupingDesignShow: store.state.studyItem.groupingDesign,
      handleRadioChange: (e) => {
        if (e === 1) {
          state.dialogForm.specificRulesDate = ''
        } else if (e === 2) {
          state.dialogForm.specificRules = ''
        }
      },
      subjectsBatchRules: {
        distributionRule: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        armArr: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      loading: false,
      // 点击批量设置的时候
      issueRule: (row, index) => {
        state.flagShow = true
        state.figureNum = null

        state.dialogForm = {
          distributionRule: null,
          specificRulesDate: '',
          specificRules: '',
          arm: '',
          armArr: [],
        }
        if (row === 1) {
          state.issueTopic = '批量设置'
          state.batchSetBtn = true
          // 让表格中的所有都加上
        } else {
          if (row.distributionRule === 1) {
            row.specificRulesDate = null
          } else if (row.distributionRule === 2) {
            row.specificRules = null
          }
          state.batchSetBtn = false
          state.issueTopic = `${row.siteName}`
          // 在这里赋值
          state.dialogForm = deepClone(row)
          state.figureNum = index
        }
      },
      clickBtn: (type) => {
        // SubjectsBatchRef
        if (type === 'back') {
          state.flagShow = false
        } else if (type === 'add') {
          state.subjectsBatchFormRef.validate((valid) => {
            if (valid) {
              if (state.batchSetBtn) { // 批量的
                // 处理数据
                state.SubjectsBatchRef.tableData.forEach((item) => {
                  item.distributionRule = state.dialogForm.distributionRule
                  item.specificRules = state.dialogForm.specificRules
                  item.specificRulesDate = state.dialogForm.specificRulesDate
                  if (state.dialogForm?.armArr) {
                    item.arm = state.dialogForm.armArr.join(',')
                  } else {
                    item.arm = ''
                  }
                  item.armArr = state.dialogForm.armArr
                  if (state.dialogForm.distributionRule === 1) {
                    // 第1例受试者入组
                    item.distributionRuleStr =  `${props?.tipsMsg ? '第1例受试者入组' : '每个受试者入组后'}，第${state.dialogForm.specificRules}天`
                  } else if (state.dialogForm.distributionRule === 2) {
                    item.distributionRuleStr = '指定时间，' + state.dialogForm.specificRulesDate
                  }
                })
              } else {
                if (state.dialogForm.distributionRule === 1) {
                  state.dialogForm.distributionRuleStr = `每个受试者入组，第${state.dialogForm.specificRules}天`
                } else if (state.dialogForm.distributionRule === 2) {
                  state.dialogForm.distributionRuleStr = '指定时间，' + state.dialogForm.specificRulesDate
                }
                if (state.dialogForm?.armArr) {
                  state.dialogForm.arm = state.dialogForm.armArr.join(',')
                } else {
                  state.dialogForm.arm = ''
                }
                state.SubjectsBatchRef.tableData[state.figureNum] = deepClone(state.dialogForm)
              }
              state.flagShow = false
            }
          })
        }
      },
      async getResearchCenterApplyList() {
        return {
          data: []
        }
      },
      applyCentreClick: () => {
        state.applyFlagShow = true
      },
      // 穿梭框点击确定的时候
      shuttleConfirm: () => {
        // 里面存放的是key值
        state.shuttleValueCopy = state.shuttleValue
        if (state.SubjectsBatchRef?.tableData) {
          state.SubjectsBatchRef.tableData = []
        }
        if (state.shuttleData) {
          state.shuttleData.forEach((item) => {
            if (state.shuttleValue) {
              state.shuttleValue.forEach((ite) => {
                if (ite === item.siteId) {
                  // state.shuttleValue.push(item.siteId)
                  state.SubjectsBatchRef.tableData.push(item)
                }
              })
            }
          })
        }
        state.applyFlagShow = false
      },
      batchCli: () => {
        if (!state.SubjectsBatchRef?.tableData) {
          ElMessage.warning('请选择适用中心')
          return
        }
        const loading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        state.loading = true
        // 保存的时候
        const trainingDocumentIds = []
        state.multipleSelection.forEach((item) => {
          trainingDocumentIds.push(item.id)
        })
        if (state.SubjectsBatchRef?.tableData) {
          state.SubjectsBatchRef.tableData.forEach((item) => {
            delete item.statusStrAndsiteName
          })
        }
        const trainingSiteRules = state.SubjectsBatchRef.tableData
        const data = {
          trainingDocumentIds,
          trainingSiteRules
        }
        putDistribution(data).then(() => {
          loading.close()
          ElMessage.success('保存成功')
          RESEARCHCENTER_INFOS.batchVisible = false
          state.loading = false
        }).catch(() => {
          loading.close()
          state.loading = false
        })
      },
      shuttleCancel: () => {
        state.shuttleValue = state.shuttleValueCopy
        state.applyFlagShow = false
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
      // 111
        const data = {
          // 就是批量的传false 单个的传true
          hasTrainingSiteRule: false
        }
        getTransfer(store.state.studyItem.studyId, state.multipleSelection[0].id || '1', data).then((res) => {
          state.shuttleValue = []
          state.shuttleValueCopy = state.shuttleValue
          if (res?.trainingSiteRuleList) {
            res.trainingSiteRuleList.forEach((item) => {
              res.studySiteList.push(item)
              state.shuttleValue.push(item.siteId)
            })
          }
          res.studySiteList.forEach((item) => {
            if (item?.arm) {
              item.armArr = item.arm.split(',')
            } else {
              item.armArr = []
            }
          })
          state.shuttleData = res.studySiteList
          if (state.shuttleData) {
            state.shuttleData.forEach((ite) => {
              ite.statusStrAndsiteName = `${ite.edcSiteCode}-(${ite.siteStatusStr})  ${ite.siteName}`
            })
          }
        })
      }
    })
    onMounted(() => {
      // 下拉分组信息
      studyStudyArmsInfo(store.state.studyItem.studyId).then((res: any) => {
        state.groupingList = res
      })
      state.multipleSelection = RESEARCHCENTER_INFOS?.multipleSelection
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.subjectsBatch {
  background: #fff;
}
.content-top {
  background: #fff;
  border-radius: 5px;
}
.ejectTop {
  border-bottom: 2px solid #ccc;
}
.issue {
  span {
    color: red;
  }
}
.checkbox-bottom {
  :deep(.el-checkbox__inner) {
    border-radius: 50%;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .label-warning {
    cursor: pointer; /*小手*/
  }
}
.editBtn {
  color: #409eff;
  cursor: pointer; /*小手*/
}
:deep(.my-el-table) {
  padding: 0 !important;
}
.subject-radio {
  :deep(.el-radio__label) {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
