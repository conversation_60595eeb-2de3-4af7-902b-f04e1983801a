<template>
  <div>
    <trial-table
      ref="DocumentTagRef"
      title=""
      :request="getRequestList"
      :columns="columns"
      :search="searchConfig"
      showbtnfalg
      hide-center
      :pagination="paginationConfig"
    >
      <template #operate="scope">
        <span
          class="text-[#409eff] cursor-pointer"
          @click="editAddItem(scope.row)"
        >
          编辑
        </span>
        <span
          class="text-[#f56c6c] cursor-pointer ml-3"
          @click="deleteItem(scope.row)"
          >删除</span
        >
      </template>
      <template #hideCenter>
        <div class="my-5 flex justify-end items-center">
          <el-button type="primary" @click="editAddItem(null)">新增</el-button>
        </div>
      </template>
    </trial-table>

    <trial-dialog
      v-model="documentTagDialogVisible"
      title="标签"
      my-title-class="flex mb-3 mt-0"
    >
      <template #DialogBody>
        <el-form
          ref="documentTagFormRef"
          label-position="top"
          class="px-3"
          :model="documentTagFormData"
          :rules="documentTagRules"
        >
          <el-form-item label="标签名称" prop="tagName">
            <el-input
              v-model.trim="documentTagFormData.tagName"
              max-length="8"
              autocomplete="off"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #footer>
        <div class="flex justify-center">
          <el-button plain @click="closeDialogVisible">取消</el-button>
          <el-button
            :loading="documentTagLoading"
            type="primary"
            @click="documentTagSubmit"
            >确定</el-button
          >
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang="ts">
import {
  getDcoumentTagPagedList,
  postDocumentTag,
  deleteDocumentTag,
} from '@/api/learningAndAssessmentManagement'
import { defineComponent, reactive, toRefs } from 'vue'
// import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'DocumentTag', // 用户标签
  setup() {
    const store = useStore()
    const state = reactive({
      DocumentTagRef: null,
      columns: [
        { label: '标签名称', prop: 'tagName', minWidth: 200 },
        {
          label: '更新时间',
          prop: 'updateTime',
          minWidth: 200,
        },
        { label: '更新人', prop: 'updator', minWidth: 100 },
        {
          label: '创建时间',
          prop: 'createTime',
          minWidth: 200,
        },
        { label: '创建人', prop: 'creator', minWidth: 100 },
        {
          minWidth: 160,
          label: '操作',
          fixed: 'right',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      // 搜索配置
      searchConfig: {
        inputWidth: '200px', // 必须带上单位
        fields: [
          {
            label: '标签名称',
            name: 'tagName',
            type: 'input',
            defaultValue: null,
          },
        ],
      },
      // 分页配置
      paginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        // pageSize: 5, // 每页条数
        // pageSizes: [5, 10, 20],
        style: { textAlign: 'left' },
      },
      // 编辑/新建
      editAddItem: (row) => {
        // console.log(row)
        if (row) {
          state.documentTagFormData = {
            id: row.id,
            tagName: row.tagName,
            studyId: row.studyId,
          }
        } else {
          state.documentTagFormData = {
            id: null,
            tagName: '',
            studyId: store.state.studyItem.studyId,
          }
        }
        state.documentTagDialogVisible = true
      },
      deleteItem: (e) => {
        ElMessageBox.confirm(`是否确认删除？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
          .then(() => {
            deleteDocumentTag(e.id).then(() => {
              ElMessage.success(`删除成功!`)
              state.DocumentTagRef.refresh()
            })
          })
          .catch(() => {})
      },
      async getRequestList(params) {
        // 获取数据
        const rest: any = await getDcoumentTagPagedList(
          store.state.studyItem.studyId,
          params
        )
        return {
          data: rest.items,
          total: rest.totalItemCount,
        }
      },
      // 弹窗数据
      documentTagFormData: {
        id: null,
        tagName: '',
        tagDesc: '',
        effectiveRule: 1,
      },
      documentTagFormRef: null,
      documentTagDialogVisible: false,
      documentTagRules: {
        tagName: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 10, message: '最多支持10个中文字符', trigger: 'blur' },
        ],
      },
      documentTagLoading: false,
      closeDialogVisible: () => {
        state.documentTagDialogVisible = false
      },
      documentTagSubmit: () => {
        state.documentTagFormRef.validate((valid) => {
          if (valid) {
            state.documentTagLoading = true
            const data = {
              ...state.documentTagFormData,
              studyId: store.state.studyItem.studyId,
            }
            postDocumentTag(data)
              .then(() => {
                state.DocumentTagRef.refresh()
                state.documentTagLoading = false
                state.closeDialogVisible()
              })
              .catch(() => {
                state.documentTagLoading = false
              })
          }
        })
      },
    })
    return {
      ...toRefs(state),
    }
  },
})
</script>
