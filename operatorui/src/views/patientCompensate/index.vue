<template>
  <div>
    <div v-show="!RESEARCHCENTER_INFOS?.contentVisible">
      <trial-table
        ref="patientCompensateRef"
        title=""
        :request="getapprovalProcessList"
        :columns="approvalProcessColumns"
        :pagination="projectPaginationConfig"
      >
        <!-- 工具栏 -->
        <template #toolbar>
          <el-button type="primary" @click="approvalProcessEditItemForm(null)">
            新建
          </el-button>
        </template>
        <template #operate="scope">
          <el-button size="small" text type="primary" @click="approvalProcessEditItemForm(scope.row)">
            编辑
          </el-button>
        </template>
      </trial-table>
    </div>
    <PatientCompensateDetail v-if="RESEARCHCENTER_INFOS?.contentVisible" />
  </div>
</template>

<script lang="ts">
import { defineComponent, provide, reactive, toRefs } from 'vue'
import PatientCompensateDetail from '@/views/patientCompensate/PatientCompensateDetail.vue'
import { getPeriodicCompensationList } from '@/api/patientCompensate'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'PatientCompensate', // 周期性补偿
  components: {
    PatientCompensateDetail
  },
  setup() {
    const RESEARCHCENTER_INFOS = reactive({
      researchContent: {},
      contentVisible: false, // 基本信息显示隐藏
      resetMethod: null,
      setTitle: ''
    })
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      patientCompensateRef: null,
      // 表格列配置大部分属性跟el-table-column配置一样//sortable: true,排序
      approvalProcessColumns: [
        // { type: 'selection' },
        { label: '模板名称', prop: 'templateName', minWidth: 260 },
        { label: 'CRF版本', prop: 'versionNumber', minWidth: 150 },
        { label: '适用中心', prop: 'siteCount', minWidth: 120 },
        { label: '更新时间', prop: 'lastUpdateTimeDisplay', minWidth: 200 },
        { label: '更新人', prop: 'lastUpdater', minWidth: 150 },
        { label: '创建时间', prop: 'createTimeDisplay', minWidth: 200 },
        { label: '创建人', prop: 'creator', minWidth: 150 },
        { label: '状态', prop: 'statusDisplay', minWidth: 150 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      projectPaginationConfig: {
        layout: 'total, prev, pager, next, sizes', // 分页组件显示哪些功能
        style: { textAlign: 'left' },
      },
      // 请求函数
      async getapprovalProcessList(params) {
        // params是从组件接收的-包含分页和搜索字段。
        try {
          const data = await getPeriodicCompensationList(studyId, params)
          // 必须要返回一个对象,包含data数组和total总数
          return {
            data: data?.items || [],
            total: +data.totalItemCount,
          }
        } catch (e) {
          console.log(e)
        }
      },
      // 刷新
      approvalProcessRefresh: () => {
        state.patientCompensateRef.refresh()
      },
      // 新增-编辑
      approvalProcessEditItemForm: (row) => {
        if (row) {
          RESEARCHCENTER_INFOS.setTitle = '编辑'
        } else {
          RESEARCHCENTER_INFOS.setTitle = '新建'
        }
        RESEARCHCENTER_INFOS.researchContent = { ...row }
        RESEARCHCENTER_INFOS.contentVisible = true
        RESEARCHCENTER_INFOS.resetMethod = state.patientCompensateRef
      },
    })
    provide('RESEARCHCENTER_INFOS', RESEARCHCENTER_INFOS)
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state),
    }
  }
})
</script>
