<template>
  <div class="SelfHelpApply">
    <div class="flex justify-between items-center">
      <h3>自助补偿/报销申请</h3>
      <el-button type="primary" :loading="loading" @click="saveDate">保存</el-button>
    </div>
    <div class="SelfHelpApply-box">
      <el-form ref="selfHelpApplyFromRef" :model="ruleForm" :rules="rules" label-position="top">
        <el-row>
          <el-col :span="8">
            <el-form-item label="单次申请金额上限-受试者端" prop="singleApplyAmountUpperLimitForPatient" class="mr-5">
              <el-input
                v-model.trim="ruleForm.singleApplyAmountUpperLimitForPatient"
                class="w-full"
                placeholder="请输入"
                maxlength="12"
                oninput="value=value.replace(/[^\d.]/g, '')
                  .replace(/^\./, '')
                  .replace(/(\.[^\.]*)\./g, '$1')
                  .replace(/^0+(\d)/, '$1')
                  .replace(/^(\d+\.\d\d).*$/, '$1')"
                @change="changeInputPatient"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单次申请金额上限-研究端" prop="singleApplyAmountUpperLimitForDoctor">
              <el-input
                v-model.trim="ruleForm.singleApplyAmountUpperLimitForDoctor"
                class="w-full"
                placeholder="请输入"
                maxlength="12"
                oninput="value=value.replace(/[^\d.]/g, '')
                  .replace(/^\./, '')
                  .replace(/(\.[^\.]*)\./g, '$1')
                  .replace(/^0+(\d)/, '$1')
                  .replace(/^(\d+\.\d\d).*$/, '$1')"
                @change="changeInputDoctor"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <trial-table
          ref="SelfHelpApplyTableRef"
          title="中心列表"
          :request="getSelfHelpApplyList"
          :columns="SelfHelpApplyColumns"
          :pagination="false"
          @selectionChange="handleSelectionChange"
        >
          <template #toolbar>
            <el-button :loading="loading" type="primary" @click="SelfHelpApplyEditItemForm(null)">
              批量设置
            </el-button>
          </template>
          <template #operate="scope">
            <el-button :loading="loading" size="small" text type="primary" @click="SelfHelpApplyEditItemForm(scope.row, scope.$index)">
              设置
            </el-button>
          </template>
        </trial-table>
        <el-form-item class="mt-5" label="" prop="relevance">
          <el-checkbox-group v-model="ruleForm.relevance">
            <el-checkbox label="1">关联审批流通过后，费用发放至受试者个人余额</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <trial-dialog v-model="setflagShow">
      <template #footer>
        <div class="font-semibold set-flag-title mb-8">{{ myTitle }}</div>
        <el-form
          ref="setFormRef"
          class="mt-5"
          :model="setForm"
          :rules="setFromRules"
          label-position="top"
        >
          <el-row>
            <el-col :span="11">
              <el-form-item label="申请控制-受试者端" prop="applyControlTypeForPatient">
                <el-select
                  v-model="setForm.applyControlTypeForPatient"
                  class="w-full"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, index) in applyArr"
                    :key="index"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="2" />
            <el-col v-if="setForm.applyControlTypeForPatient === 1" :span="11">
              <el-form-item label="审批流-受试者端申请" prop="applyAPRForPatient">
                <el-select
                  v-model="setForm.applyAPRForPatient"
                  class="w-full"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, index) in approvalList"
                    :key="index"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="13" />
            <el-col v-if="setForm.applyControlTypeForPatient === 1" :span="11">
              <el-form-item label="科目" prop="subjectsArr">
                <el-checkbox-group v-model="setForm.subjectsArr">
                  <el-checkbox
                    v-for="(item, index) in subjectOptions"
                    :key="index"
                    :label="item.subjectId"
                  >
                    {{ item.subjectName }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider />
          <el-row>
            <el-col :span="11">
              <el-form-item label="申请控制-研究端" prop="applyControlTypeForDoctor">
                <el-select
                  v-model="setForm.applyControlTypeForDoctor"
                  class="w-full"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, index) in applyArr"
                    :key="index"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="2" />
            <el-col v-if="setForm.applyControlTypeForDoctor === 1" :span="11">
              <el-form-item label="审批流-研究端申请" prop="applyAPRForDoctor">
                <el-select
                  v-model="setForm.applyAPRForDoctor"
                  class="w-full"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, index) in approvalList"
                    :key="index"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="flex justify-center mt-10">
          <el-button plain @click="setflagShow = false">取消</el-button>
          <el-button type="primary" @click="setConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { defineComponent, onActivated, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'
import { getSelfCompensationApply, getSimple, postSelfCompensationApply } from '@/api/patientCompensate'
import { deepClone, delay } from '@/utils'

export default defineComponent({
  name: 'SelfHelpApply', // 自助申请
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const state = reactive({
      loading: false,
      selfHelpApplyFromRef: null,
      ruleForm: {
        singleApplyAmountUpperLimitForDoctor: '',
        singleApplyAmountUpperLimitForPatient: '',
        relevance: ['1']
      },
      rules: {
        singleApplyAmountUpperLimitForPatient: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        singleApplyAmountUpperLimitForDoctor: [
          { required: true, message: '请输入', trigger: 'blur' },
        ]
      },
      SelfHelpApplyTableRef: null,

      SelfHelpApplyColumns: [
        { type: 'selection' },
        { label: '中心信息', prop: 'siteName', minWidth: 260 },
        { label: '申请控制-受试者端', prop: 'applyControlTypeForPatientName', minWidth: 230 },
        { label: '审批流-受试者端申请', prop: 'applyAPRForPatientName', minWidth: 180 },
        { label: '申请控制-研究端', prop: 'applyControlTypeForDoctorName', minWidth: 180 },
        { label: '审批流-研究端申请', prop: 'applyAPRForDoctorName', minWidth: 230 },
        {
          label: '操作',
          fixed: 'right',
          width: 180,
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      selectedItems: [],

      myTitle: '',
      setflagShow: false,
      setFormRef: null,
      setForm: {
        applyControlTypeForPatientName: '',
        applyControlTypeForPatient: 0,
        applyAPRForPatientName: '',
        applyAPRForPatient: '',
        applyControlTypeForDoctorName: '',
        applyControlTypeForDoctor: 0,
        applyAPRForDoctorName: '',
        applyAPRForDoctor: '',
        subjects: '',
        subjectsArr: [],
      },
      setFormIndex: -1,
      setFromRules: {
        applyControlTypeForPatient: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        applyAPRForPatient: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        applyControlTypeForDoctor: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        applyAPRForDoctor: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        subjectsArr: [
          { required: true, message: '请选择', trigger: 'change' },
        ]
      },
      applyArr: [
        {
          name: '不可申请',
          value: 0
        },
        {
          name: '可申请',
          value: 1
        }
      ],
      approvalList: [],
      subjectOptions: [
        {
          subjectId: '1',
          subjectName: '营养费',
        },
        {
          subjectId: '2',
          subjectName: '交通费',
        },
        {
          subjectId: '3',
          subjectName: '检查费',
        },
        {
          subjectId: '4',
          subjectName: '住宿费',
        },
        {
          subjectId: '5',
          subjectName: '抽血补偿',
        },
        {
          subjectId: '6',
          subjectName: '访视补助',
        },
        {
          subjectId: '7',
          subjectName: '治疗费',
        },
        {
          subjectId: '8',
          subjectName: '误工费',
        },
        {
          subjectId: '10',
          subjectName: '药物费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        // {
        //   subjectId: '11',
        //   subjectName: '口服给药器费',
        // },
        {
          subjectId: '9',
          subjectName: '其他',
        },
      ],
      handleSelectionChange: (arr) => {
        state.selectedItems = arr
      },
      async getSelfHelpApplyList() {
        // params是从组件接收的-包含分页和搜索字段。
        return {
          data: [],
        }
      },
      SelfHelpApplyEditItemForm: (row, index) => {
        state.setForm = {
          applyControlTypeForPatientName: '',
          applyControlTypeForPatient: 0,
          applyAPRForPatientName: '',
          applyAPRForPatient: '',
          applyControlTypeForDoctorName: '',
          applyControlTypeForDoctor: 0,
          applyAPRForDoctorName: '',
          applyAPRForDoctor: '',
          subjects: '',
          subjectsArr: [],
        }
        if (row) {
          if (row?.subjects) {
            row.subjectsArr = row?.subjects.split(',')
          }
          state.setForm = deepClone(row)
          state.setFormIndex = index
          state.myTitle = '设置'
        } else {
          if (state.selectedItems?.length < 2) {
            ElMessage.warning('请选择两条或以上的数据')
            return
          }
          state.setFormIndex = -1
          state.myTitle = '批量设置'
        }
        state.setflagShow = true
      },
      setConfirm: () => {
        state.setFormRef.validate((valid) => {
          if (valid) {
            state.applyArr.forEach((item) => {
              if (item.value === state.setForm?.applyControlTypeForPatient) {
                state.setForm.applyControlTypeForPatientName = item.name
                if (state.setForm?.applyControlTypeForPatient === 0) {
                  state.setForm.applyAPRForPatient = ''
                  state.setForm.applyAPRForPatientName = ''
                  state.setForm.subjects = ''
                  state.setForm.subjectsArr = []
                }
              }
              if (item.value === state.setForm?.applyControlTypeForDoctor) {
                state.setForm.applyControlTypeForDoctorName = item.name
                if (state.setForm?.applyControlTypeForDoctor === 0) {
                  state.setForm.applyAPRForDoctor = ''
                  state.setForm.applyAPRForDoctorName = ''
                }
              }
            })
            state.approvalList.forEach((item) => {
              if (item?.code === state.setForm?.applyAPRForPatient) {
                state.setForm.applyAPRForPatientName = item?.name
              }
              if (item?.code === state.setForm?.applyAPRForDoctor) {
                state.setForm.applyAPRForDoctorName = item?.name
              }
            })
            const SelfTableData = state.SelfHelpApplyTableRef.tableData
            SelfTableData.forEach((item, index) => {
              if (state?.myTitle === '设置' && index === state?.setFormIndex) {
                state.changeTableValue(item)
              } else if (state?.myTitle === '批量设置') {
                state.selectedItems.forEach(ite => {
                  if (ite?.siteId === item?.siteId) {
                    state.changeTableValue(item)
                  }
                })
              }
            })
            state.setflagShow = false
          }
        })
      },
      changeTableValue: (item) => {
        item.subjects = state.setForm.subjectsArr.sort().join(',')
        item.applyAPRForDoctor = state.setForm.applyAPRForDoctor
        item.applyAPRForDoctorName = state.setForm.applyAPRForDoctorName
        item.applyControlTypeForDoctor = state.setForm.applyControlTypeForDoctor
        item.applyControlTypeForDoctorName = state.setForm.applyControlTypeForDoctorName
        item.applyAPRForPatient = state.setForm.applyAPRForPatient
        item.applyAPRForPatientName = state.setForm.applyAPRForPatientName
        item.applyControlTypeForPatient = state.setForm.applyControlTypeForPatient
        item.applyControlTypeForPatientName = state.setForm.applyControlTypeForPatientName
      },
      saveDate: () => {
        delay(() => {
          state.selfHelpApplyFromRef.validate((valid) => {
            if (valid) {
              // 请配置中心的信息
              const tableDataCopy = state.SelfHelpApplyTableRef.tableData
              // let nextTo = true
              // for (const i of tableDataCopy) {
              //   if ((i.applyControlTypeForPatient === 1 && i.applyAPRForPatient === '') || (i.applyControlTypeForDoctor === 1 && i.applyAPRForDoctor === '')) {
              //     nextTo = false
              //     break
              //   }
              // }
              // if (!nextTo) {
              //   ElMessage.warning('请配置中心的信息')
              //   return
              // }

              if (state.ruleForm.relevance?.length) {
                state.ruleForm.enableDistributionBalance = true
              } else {
                state.ruleForm.enableDistributionBalance = false
              }

              const data = {
                ...state.ruleForm,
                studySiteCompensationConfigurationViewModels: tableDataCopy
              }
              const selfHelpApplyFromLoading = ElLoading.service({
                lock: true,
                text: 'Loading',
                background: 'rgba(0, 0, 0, 0.7)',
              })
              state.loading = true
              postSelfCompensationApply(studyId, data).then(() => {
                state.loading = false
                selfHelpApplyFromLoading.close()
                ElMessage.success('保存成功')
                state.onLoad()
              }).catch(() => {
                state.loading = false
                selfHelpApplyFromLoading.close()
              })
            }
          })
        }, 500)
      },
      changeInputPatient: (value) => {
        if (value.indexOf('.') !== -1 && !value.split('.')[1]?.length) {
          state.ruleForm.singleApplyAmountUpperLimitForPatient = value.split('.')[0]
        } else {
          state.ruleForm.singleApplyAmountUpperLimitForPatient = value
        }
      },
      changeInputDoctor: (value) => {
        if (value.indexOf('.') !== -1 && !value.split('.')[1]?.length) {
          state.ruleForm.singleApplyAmountUpperLimitForDoctor = value.split('.')[0]
        } else {
          state.ruleForm.singleApplyAmountUpperLimitForDoctor = value
        }
      },
      // 进入页面加载，写在了onMounted中
      onLoad: () => {
        delay(() => {
          getSelfCompensationApply(studyId).then((res) => {
            if (res.enableDistributionBalance) {
              state.ruleForm.relevance = ['1']
            } else {
              state.ruleForm.relevance = []
            }
            state.ruleForm.singleApplyAmountUpperLimitForPatient = res?.singleApplyAmountUpperLimitForPatient || ''
            state.ruleForm.singleApplyAmountUpperLimitForDoctor = res?.singleApplyAmountUpperLimitForDoctor || ''
            state.SelfHelpApplyTableRef.tableData = res?.studySiteCompensationConfigurationViewModels || []
          })
        }, 300)
      }
    })
    onMounted(() => {
      getSimple(studyId).then((res) => {
        state.approvalList = res
      })
      state.onLoad()
    })
    onActivated(() => {
      state.onLoad()
    })
    return {
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.SelfHelpApply {
  padding: 20px;
  background: #fff;
  border-radius: 5px;
  .SelfHelpApply-box {
    padding: 0 20px;
  }
  :deep(.my-el-table) {
    padding: 20px 0;
  }
}
</style>
