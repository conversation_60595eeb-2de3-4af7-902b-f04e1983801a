<template>
  <div class="PatientCompensateDetail">
    <div class="delete-pos-tr">
      <el-button size="default" :loading="loading" @click="backRefresh">返回</el-button>
    </div>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="基础信息" name="basicsMsg">
        <div>
          <div class="flex justify-end">
            <el-button :loading="loading" type="primary" @click="saveData">
              保存
            </el-button>
          </div>
          <el-form ref="PatientCompensateFromRef" :model="ruleForm" :rules="rules" label-position="top">
            <el-row class="flex">
              <el-col :span="7">
                <el-form-item label="模板名称" prop="templateName" class="mr-3">
                  <el-input
                    v-model.trim="ruleForm.templateName"
                    class="w-full"
                    placeholder="请输入"
                    clearable
                    maxlength="999"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="CRF版本" prop="versionId" class="mr-3">
                  <el-select
                    v-model="ruleForm.versionId"
                    class="w-full"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="(item, index) in CRFoptions"
                      :key="index"
                      :label="item.versionStatus === 1 ? `${item.versionNumber}测试版本` : item.versionStatus === 2 ? `${item.versionNumber}正式版本` : item.versionNumber"
                      :value="item.studyVersionId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="组别标签" prop="armCodeArr" class="mr-3">
                  <el-select
                    v-model="ruleForm.armCodeArr"
                    class="w-full"
                    placeholder="请选择"
                    clearable
                    multiple
                    collapse-tags
                  >
                    <el-option
                      v-for="(item, index) in studyArms"
                      :key="index"
                      :label="item.armName"
                      :value="item.armCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="单次申请金额上限" prop="maxCompensationAmount" style="width: 30%">
              <el-input
                v-model.trim="ruleForm.maxCompensationAmount"
                class="w-full"
                placeholder="请输入"
                maxlength="12"
                oninput="value=value.replace(/[^\d.]/g, '')
                  .replace(/^\./, '')
                  .replace(/(\.[^\.]*)\./g, '$1')
                  .replace(/^0+(\d)/, '$1')
                  .replace(/^(\d+\.\d\d).*$/, '$1')"
                @change="changeInputDoctor"
              />
            </el-form-item>
            <div class="flex items-center mb-5">
              <div class="mr-3">状态</div>
              <el-switch v-model="ruleForm.statusBol" />
            </div>
            <el-form-item label="科目" prop="checkboxnode">
              <el-checkbox-group v-model="ruleForm.checkboxnode">
                <el-checkbox
                  v-for="(item, index) in subjectOptions"
                  :key="index"
                  :label="item.subjectId"
                >
                  {{ item.subjectName }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <div>
              <div class="flex items-center mb-5">
                <el-button :loading="loading" type="primary" class="mr-4" @click="applyFlagShow = true">适用中心</el-button>
                <span>已选择 {{ shuttleValue.length }}/{{ shuttleData.length }} 家中心</span>
              </div>
              <div>
                <div class="ft-15 flex items-center justify-between">
                  <span>中心</span>
                  <el-button :loading="loading" type="primary" class="mr-4" @click="issueRule(null)">批量设置</el-button>
                </div>
                <!-- 表格 -->
                <trial-table
                  ref="patientCompensateTabRef"
                  title=""
                  :request="getResearchCenterApplyList"
                  :columns="columns"
                  :showbtnfalg="true"
                  @selectionChange="patientCompensateChange"
                >
                  <!-- :requestExport="getResearchCenterExport" -->
                  <template #operate="scope">
                    <span class="editBtnBlue" @click="issueRule(scope.row, scope.$index)">
                      设置
                    </span>
                  </template>
                </trial-table>
              </div>
            </div>
            <el-form-item class="mt-5" label="" prop="isAutomaticDistributionValue">
              <el-checkbox-group v-model="ruleForm.isAutomaticDistributionValue">
                <el-checkbox label="1">关联审批流通过后，费用发放至受试者个人余额</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="compensationIdCopy !== 'none'" label="补偿设置" name="compensate">
        <div class="flex items-center justify-between mb-5">
          <div style="color: #f59a23;">根据当前配置，系统将自动生成待确认的单据，由已配置的确认对象进行确认并正式提交申请</div>
          <el-button :loading="loading" type="primary" @click="saveDataDeploy">保存</el-button>
        </div>
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          class="tableData"
          :header-cell-style="tableHeaderColor"
          header-cell-class-name="tablewrap2"
          :cell-style="cellStyle"
          scrollbar-always-on
        >
          <!-- :min-width="index === 0 ? '120' : '200'" -->
          <el-table-column
            v-for="(item, index) in tableLabel"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :fixed="index === 0 ? 'left' : false"
            :width="index === 0 ? '120' : ''"
            :min-width="index === 0 ? '120' : '150'"
          >
            <template #default="scope">
              <span v-if="index === 0">
                {{ scope.row[item.prop] }}
              </span>
              <span
                v-else-if="scope.$index === 0 && index !== 0"
              >
                <el-checkbox
                  v-model="scope.row[item.prop].enableCompensation"
                  label="开启补偿"
                />
              </span>
              <span
                v-else-if="scope.$index === 1 && index !== 0"
                class="editBtnBlue"
              >
                <span @click="editClick(scope.row[item.prop], item.prop, scope.$index)">编辑</span>
              </span>
              <span v-else-if="scope.row.left === '合计'">
                {{ scope.row[item.prop]?.amount }}
              </span>
              <span v-else>
                <el-input
                  v-model="scope.row[item.prop].amount"
                  maxlength="12"
                  @input="computerInput(scope.row[item.prop].amount, item.prop, scope.$index)"
                  @change="changeInput(scope.row[item.prop].amount, item.prop, scope.$index)"
                />
                <!-- oninput="value=value.replace(/[^\d.]/g, '')
                  .replace(/^\./, '')
                  .replace(/(\.[^\.]*)\./g, '$1')
                  .replace(/^0+(\d)/, '$1')
                  .replace(/^(\d+\.\d\d).*$/, '$1')" -->
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <trial-dialog v-model="applyFlagShow" :my-dialog-body-style="myDialogBodyStyle">
      <template #footer>
        <div class="w-full flex justify-center">
          <el-transfer
            v-model="shuttleValue"
            filterable
            filter-placeholder="中心名称"
            :titles="['未选择', '已选择']"
            :data="shuttleData"
            :props="shuttleProps"
            class="transfer"
          />
        </div>
        <div class="flex justify-center">
          <el-button plain :loading="loading" @click="shuttleCancel">取消</el-button>
          <el-button type="primary" :loading="loading" @click="shuttleConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="setflagShow">
      <template #footer>
        <div class="font-semibold set-flag-title mb-8">{{ myTitle }}</div>
        <el-form
          ref="setListApprovaFormRef"
          class="mt-5"
          :model="setForm"
          :rules="setRules"
          label-position="top"
        >
          <el-form-item label="草稿单据确认对象" prop="relationRoleArr">
            <el-checkbox-group v-model="setForm.relationRoleArr">
              <el-checkbox
                v-for="(item, index) in roleArr"
                :key="index"
                :label="item.value"
                :disabled="item.value === '受试者'"
              >
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="审批流" prop="approvalProcessId" style="width: 60%">
            <el-select
              v-model="setForm.approvalProcessId"
              class="w-full"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in approvalList"
                :key="index"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="flex justify-center mt-10">
          <el-button plain :loading="loading" @click="setflagShow = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="setConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
    <trial-dialog v-model="touchEditShow">
      <template #footer>
        <div>
          <div class="mb-3">
            {{ touchEditRow.visitName }}
          </div>
          <el-divider />
          <el-form ref="touchEditFromRef" :model="touchEditForm" :rules="touchEditRules" label-position="top">
            <el-form-item label="触发逻辑" prop="triggreType">
              <el-radio-group v-model="touchEditForm.triggreType">
                <el-radio :label="1" size="large" :disabled="touchEditRow?.quests?.length === 0">完成访视任务</el-radio>
                <el-radio :label="2" size="large">录入实际随访日期</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="touchEditForm.triggreType === 1 && touchEditRow?.quests?.length" class="approval" prop="checkboxAr">
              <el-checkbox-group v-model="touchEditForm.checkboxAr">
                <div
                  v-for="item in touchEditRow.quests"
                  :key="item.questTemplateId"
                >
                  <el-checkbox
                    :label="item.questTemplateId"
                  >
                    {{ item.questTemplateName }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </div>
        <div class="flex justify-center">
          <el-button plain :loading="loading" @click="touchEditShow = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="touchEditConfirm">确定</el-button>
        </div>
      </template>
    </trial-dialog>
  </div>
</template>

<script lang='ts'>
import { getCustomTasksTransfer } from '@/api/customTask'
import { getDropStudyVersionsCRF } from '@/api/home'
import { getCompensationConfig, getPeriodicCompensationt, getSimple, putCompensationConfig, putPeriodicCompensation } from '@/api/patientCompensate'
import { calculate, deepClone } from '@/utils'
import { defineComponent, inject, onMounted, reactive, toRefs } from 'vue'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'PatientCompensateDetail', // 费用补偿详情
  components: {},
  setup() {
    const store = useStore()
    const { studyId } = store.state.studyItem
    const RESEARCHCENTER_INFOS = inject('RESEARCHCENTER_INFOS')
    const state = reactive({
      // 处理触发逻辑的哪些
      touchEditShow: false,
      touchEditFromRef: null,
      touchEditRules: {
        triggreType: [
          { required: true, message: '请选择', trigger: 'change' },
        ],
        checkboxAr: [
          { required: true, message: '至少选择一个', trigger: 'change' },
        ]
      },
      touchEditForm: {
        triggreType: 0,
        checkboxAr: [],
      },
      tableRowIndex: -1,
      tableColumn: '',
      touchEditRow: {},

      loading: false,
      compensationConfigAll: {},
      compensationIdCopy: '',
      versionIdCopy: '',
      activeName: 'basicsMsg',

      PatientCompensateFromRef: null,
      CRFoptions: [],
      subjectOptions: [
        {
          subjectId: '1',
          subjectName: '营养费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '2',
          subjectName: '交通费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '3',
          subjectName: '检查费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '4',
          subjectName: '住宿费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '5',
          subjectName: '抽血补偿',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '6',
          subjectName: '访视补助',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '7',
          subjectName: '治疗费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '8',
          subjectName: '误工费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        {
          subjectId: '10',
          subjectName: '药物费',
          dctPeriodicCompensationId: '',
          id: ''
        },
        // {
        //   subjectId: '11',
        //   subjectName: '口服给药器费',
        //   dctPeriodicCompensationId: '',
        //   id: ''
        // },
        {
          subjectId: '9',
          subjectName: '其他',
          dctPeriodicCompensationId: '',
          id: ''
        },
      ],
      studyArms: [], // 组别用的
      ruleForm: {
        id: '',
        templateName: '',
        versionId: '',
        armCodeArr: [],
        maxCompensationAmount: '',
        statusBol: true,
        checkboxnode: [],
        isAutomaticDistributionValue: ['1']
      },
      rules: {
        templateName: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        versionId: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
        maxCompensationAmount: [
          { required: true, message: '请输入', trigger: 'blur' },
        ],
        checkboxnode: [
          { type: 'array', required: true, message: '请选择', trigger: 'change' },
        ]
      },
      shuttleValue: [],
      shuttleData: [],
      shuttleValueCopy: [],
      applyFlagShow: false,
      patientCompensateTabRef: null,
      myDialogBodyStyle: {
        width: '60%'
      },
      columns: [
        { type: 'selection' }, // table勾选框
        { label: '中心信息', prop: 'siteName', width: 230 },
        { label: '草稿单据确认对象', prop: 'relationRoleName', width: 260 },
        { label: '审批流', prop: 'approvalProcessName', width: 300 },
        {
          label: '操作',
          fixed: 'right',
          align: 'center',
          tdSlot: 'operate', // 自定义单元格内容的插槽名称
        },
      ],
      shuttleProps: {
        key: 'siteId',
        label: 'statusStrAndsiteName'
      },

      setflagShow: false,
      setListApprovaFormRef: null,
      myTitle: '',
      setForm: {
        relationRoleArr: [],
        approvalProcessId: '',
        relationRole: '',
        approvalProcessName: '',
        relationRoleName: ''
      },
      setIndex: -1,
      setRules: {
        relationRoleArr: [
          { type: 'array', required: true, message: '请选择', trigger: 'change' },
        ],
        approvalProcessId: [
          { required: true, message: '请选择', trigger: 'blur' },
        ],
      },
      approvalList: [],
      // checkboxRoleArr: [
      //   { roleId: 'ProjectAdmin', label: '运营专员' },
      //   { roleId: 'PI', label: '主要研究者' },
      //   { roleId: 'SUBI', label: '协助研究者' },
      //   { roleId: 'CRC', label: '研究协调员' }
      // ],
      roleArr: [
        {
          value: 'CRC',
          name: '研究协调员'
        },
        {
          value: 'SUBI',
          name: '协助研究者'
        },
        {
          value: 'PI',
          name: '主要研究者'
        },
        {
          value: '受试者',
          name: '受试者'
        },
      ],
      setCheckedList: [],
      // 补偿设置的东西
      tableData: [],
      tableLabel: [],
      // 处理编辑的东西
      touchEditConfirm: () => {
        if (state.touchEditForm?.triggreType === 0) {
          state.touchEditForm.triggreType = ''
        }
        state.touchEditFromRef.validate((valid) => {
          if (valid) {
            let selectQuestsArr = []
            // 得到的行列还回去
            if (state.touchEditForm?.triggreType === 2) {
              selectQuestsArr = []
            } else if (state.touchEditForm?.triggreType === 1) {
              state.touchEditForm.checkboxAr.forEach((item) => {
                state.touchEditRow.quests.forEach((ite) => {
                  if (ite.questTemplateId === item) {
                    selectQuestsArr.push(ite)
                  }
                })
              })
            }
            state.tableData[state.tableRowIndex][state.tableColumn].dctPeriodicCompensationVisitTask = selectQuestsArr
            state.tableData[state.tableRowIndex][state.tableColumn].triggreType = state.touchEditForm.triggreType
            state.touchEditShow = false
          }
        })
      },
      // 处理money后边只有小数点得
      changeInputDoctor: (value) => {
        if (value.indexOf('.') !== -1 && !value.split('.')[1]?.length) {
          state.ruleForm.maxCompensationAmount = value.split('.')[0]
        } else {
          state.ruleForm.maxCompensationAmount = value
        }
      },
      tableHeaderColor: ({ columnIndex }) => {
        if (columnIndex !== 0) {
          return {
            'background-color': '#ebebeb',
            'text-align': 'center',
            'height': '60px',
          }
        }
      },
      cellStyle: ({ columnIndex, rowIndex, row }) => {
        if (rowIndex === 0 || rowIndex === 1) {
          return {
            'background-color': '#e6f6fe',
            'text-align': 'center',
          }
        }
        if (row.left === '合计') {
          return {
            'background-color': '#fde6c8',
            'text-align': 'center',
            'height': '60px',
          }
        }
        if (columnIndex === 0) {
          return {
            'background-color': '#ebebeb',
            'text-align': 'center',
            'height': '60px',
          }
        }
      },
      computerInput: (value, prop, rowIndex) => {
        const valueInput = value.replace(/[^\d.]/g, '')
          .replace(/^\./, '')
          .replace(/(\.[^\.]*)\./g, '$1')
          .replace(/^0+(\d)/, '$1')
          .replace(/^(\d+\.\d\d).*$/, '$1')
        // 去改值
        state.tableData[rowIndex][prop].amount = valueInput
        let sumMoney = 0
        state.tableData.forEach((item, index) => {
          if (index !== 0 && index !== 1 && (index + 1) !== state.tableData?.length) {
            sumMoney = calculate(sumMoney, item[prop].amount, 'add')
          }
        })
        state.tableData[state.tableData?.length - 1][prop].amount = sumMoney
      },
      changeInput: (value, prop, rowIndex) => {
        let valueInput = ''
        if (value.indexOf('.') !== -1 && !value.split('.')[1]?.length) {
          valueInput = value.split('.')[0]
        } else if (!value) {
          valueInput = '0'
        } else {
          valueInput = value
        }
        state.tableData[rowIndex][prop].amount = valueInput
      },
      editClick: (row, prop, rowIndex) => {
        state.touchEditForm.checkboxAr = []
        if (row.triggreType === 1) {
          row.dctPeriodicCompensationVisitTask.forEach((item) => {
            state.touchEditForm.checkboxAr.push(item.questTemplateId)
          })
        } else {
          row.quests.forEach((item) => {
            state.touchEditForm.checkboxAr.push(item.questTemplateId)
          })
        }
        state.touchEditForm.triggreType = row.triggreType
        state.touchEditRow = row
        state.tableRowIndex = rowIndex
        state.tableColumn = prop
        state.touchEditShow = true
      },
      patientCompensateChange: (arr) => {
        state.setCheckedList = arr
      },
      // 穿梭框点击取消的时候
      shuttleCancel: () => {
        state.shuttleValue = state.shuttleValueCopy
        state.applyFlagShow = false
      },
      // 穿梭框点击确定的时候
      shuttleConfirm: () => {
        // 里面存放的是key值
        state.shuttleValueCopy = state.shuttleValue
        if (state.patientCompensateTabRef?.tableData) {
          state.patientCompensateTabRef.tableData = []
        }
        if (state.shuttleData) {
          state.shuttleData.forEach((item) => {
            item.id = item?.id || ''
            item.dctPeriodicCompensationId = item?.dctPeriodicCompensationId || state.ruleForm.id
            if (state.shuttleValue) {
              state.shuttleValue.forEach((ite) => {
                if (ite === item.siteId) {
                  // state.shuttleValue.push(item.siteId)
                  state.patientCompensateTabRef.tableData.push(item)
                }
              })
            }
          })
        }
        state.applyFlagShow = false
      },
      // 设置的确定
      setConfirm: () => {
        state.setListApprovaFormRef.validate((valid) => {
          if (valid) {
            state.setForm.relationRole = ''
            state.setForm.relationRole = state.setForm.relationRoleArr + ''
            state.setForm.relationRoleName = state.setForm.relationRoleArr
              .map(val => {
                const found = state.roleArr.find(role => role.value === val)
                return found ? found.name : val
              })
              .join(',')
            state.approvalList.forEach((item) => {
              if (item.code === state.setForm.approvalProcessId) {
                state.setForm.approvalProcessName = item.name
              }
            })
            if (state.myTitle === '批量设置') {
              state.patientCompensateTabRef.tableData.forEach((item) => {
                state.setCheckedList.forEach((ite) => {
                  if (ite.siteId === item.siteId) {
                    item.relationRole = state.setForm.relationRole
                    item.relationRoleName = state.setForm.relationRoleName
                    item.approvalProcessId = state.setForm.approvalProcessId
                    item.approvalProcessName = state.setForm.approvalProcessName
                  }
                })
              })
            } else {
              const tableDataIndex = state.patientCompensateTabRef.tableData[state.setIndex]
              tableDataIndex.relationRole = state.setForm.relationRole
              tableDataIndex.relationRoleName = state.setForm.relationRoleName
              tableDataIndex.approvalProcessId = state.setForm.approvalProcessId
              tableDataIndex.approvalProcessName = state.setForm.approvalProcessName
            }
            state.setflagShow = false
          }
        })
      },
      // 设置和批量设置
      issueRule: (row, index) => {
        state.setForm = {
          relationRoleArr: [],
          approvalProcessId: '',
          relationRole: '',
          approvalProcessName: '',
          relationRoleName: ''
        }
        if (row) {
          state.setIndex = index
          state.setForm.relationRoleArr = row.relationRole ? row.relationRole.split(',') : []
          state.setForm.approvalProcessId = row.approvalProcessId

          state.myTitle = row.siteName
        } else {
          if (state.setCheckedList?.length < 2) {
            ElMessage.warning('请选择两条或以上的数据')
            return
          }
          state.myTitle = '批量设置'
        }
        state.setflagShow = true
      },
      // 表格
      async getResearchCenterApplyList() {
        return {
          data: []
        }
      },
      // 保存
      saveData: () => {
        state.PatientCompensateFromRef.validate((valid) => {
          if (valid) {
            if (state.shuttleValue.length === 0) {
              ElMessage.warning('请选择中心')
              return
            }
            let nextTo = true
            const tableDataCopy = state.patientCompensateTabRef.tableData
            for (const i of tableDataCopy) {
              if (!i.approvalProcessId || !i.relationRole) {
                nextTo = false
                break
              }
            }
            if (!nextTo) {
              ElMessage.warning('请配置已选中心的信息')
              return
            }
            if (state.ruleForm?.isAutomaticDistributionValue.includes('1')) {
              state.ruleForm.isAutomaticDistribution = true
            } else {
              state.ruleForm.isAutomaticDistribution = false
            }
            state.ruleForm.dctPeriodicCompensationSubject = []
            if (state.ruleForm.checkboxnode?.length) {
              state.ruleForm.checkboxnode.forEach((item) => {
                state.subjectOptions.forEach((ite) => {
                  ite.dctPeriodicCompensationId = state.ruleForm?.id
                  if (item === ite.subjectId) {
                    state.ruleForm.dctPeriodicCompensationSubject.push(ite)
                  }
                })
              })
            }
            if (state.ruleForm?.statusBol) {
              state.ruleForm.status = 1
            } else {
              state.ruleForm.status = 0
            }
            state.CRFoptions.forEach((item) => {
              if (item.studyVersionId === state.ruleForm?.versionId) {
                state.ruleForm.versionNumber = item.versionNumber
              }
            })
            state.ruleForm.dctPeriodicCompensationSiteConfig = state.patientCompensateTabRef.tableData
            if (state.ruleForm?.armCodeArr) {
              state.ruleForm.armCode = state.ruleForm.armCodeArr + ''
            }
            const data = {
              ...state.ruleForm,
              studyId
            }
            const patientCompensateLoading = ElLoading.service({
              lock: true,
              text: 'Loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
            state.loading = true
            // 拿着调用接口
            putPeriodicCompensation(data).then((res) => {
              state.compensationIdCopy = res?.id
              state.versionIdCopy = res?.versionId
              ElMessage.success('保存成功')
              state.loading = false
              patientCompensateLoading.close()
              state.onLoad()
            }).catch(() => {
              state.loading = false
              patientCompensateLoading.close()
            })
          }
        })
      },
      applicationCenter: async () => {
        const rest = await getCustomTasksTransfer(studyId, { })
        if (rest?.customTaskSiteRuleList) {
          rest.customTaskSiteRuleList.forEach((item) => {
            rest.studySiteList.push(item)
          })
        }
        state.shuttleData = rest.studySiteList
        if (state.shuttleData?.length) {
          state.shuttleData.forEach((ite) => {
            ite.statusStrAndsiteName = `${ite.edcSiteCode}-${ite.siteName}`
          })
        }
      },
      // 返回
      backRefresh: () => {
        RESEARCHCENTER_INFOS.resetMethod.refresh()
        RESEARCHCENTER_INFOS.contentVisible = false
      },
      compensationSet: () => {
        getCompensationConfig(state.compensationIdCopy, state.versionIdCopy).then((res) => {
          state.compensationConfigAll = deepClone(res)
          state.tableLabel = res.visits.map((item, index) => {
            item['prop'] = `prop${index}`
            item['label'] = item.visitTemplateName
            return item
          })
          state.tableLabel.unshift({
            prop: 'left',
            label: ''
          })
          res.subjects.push({
            subjectName: '合计',
          })
          state.tableData = res.subjects.map(subject => {
            const tabItem = {
              left: subject.subjectName
            }
            res.visits.forEach(visit => {
              if (visit.prop !== 'left') {
                const propKey = `${visit.prop}`
                let propValue = {}
                if (subject.subjectName !== '合计') {
                  const config = res.configs.find(config => config.subjectId === subject.id && config.visitId === visit.visitTemplateId)
                  propValue = {
                    subjectId: subject?.id || '',
                    visitId: visit?.visitTemplateId || '',
                    visitName: visit?.visitTemplateName || '',
                    amount: config?.amount || 0,
                    subjectName: subject.subjectName,
                    id: config?.id || '',
                    dctPeriodicCompensationId: config?.dctPeriodicCompensationId || ''
                  }
                } else {
                  propValue = {
                    amount: 0
                  }
                }
                tabItem[propKey] = propValue
              }
            })
            return tabItem
          })
          state.tableData.forEach(item => {
            if (item.left !== '合计') {
              Object.keys(item).forEach(key => {
                if (key !== 'left') {
                  state.tableData[state.tableData.length - 1][key].amount = calculate(state.tableData[state.tableData.length - 1][key].amount, item[key].amount, 'add')
                }
              })
            }
          })
          state.tableData.unshift(
            {
              left: '是否开启'
            },
            {
              left: '触发逻辑'
            }
          )
          state.tableData.forEach((item, index) => {
            if (index === 0 || index === 1) {
              res.visits.forEach((visit) => {
                if (visit.prop !== 'left') {
                  const propKey = `${visit.prop}`
                  const config = res.triggres.find(config => config.visitId === visit.visitTemplateId)
                  let propValue = {}
                  if (index === 0) {
                    propValue = {
                      visitId: visit?.visitTemplateId || '',
                      enableCompensation: config?.enableCompensation || false,
                    }
                  } else {
                    propValue = {
                      visitId: visit?.visitTemplateId || '',
                      visitName: visit?.visitTemplateName || '',
                      quests: config?.quests || [],
                      dctPeriodicCompensationVisitTask: config?.dctPeriodicCompensationVisitTask || [],
                      dctPeriodicCompensationId: config?.dctPeriodicCompensationId || '',
                      id: config?.id || '',
                      triggreType: config?.triggreType
                    }
                  }
                  item[propKey] = propValue
                }
              })
            }
          })
        })
      },
      // 保存配置
      saveDataDeploy: () => {
        const configs = []
        const triggresOne = []
        const triggresTwo = []
        state.tableData.forEach((item, index) => {
          if (index !== 0 && index !== 1 && (index + 1) !== state.tableData.length) {
            Object.keys(item).forEach(key => {
              if (key !== 'left') {
                const allObj = item[key]
                configs.push({...allObj})
              }
            })
          } else if (index === 0) {
            Object.keys(item).forEach(key => {
              if (key !== 'left') {
                const allObj = item[key]
                triggresOne.push({...allObj})
              }
            })
          } else if (index === 1) {
            Object.keys(item).forEach(key => {
              if (key !== 'left') {
                const allObj = item[key]
                triggresTwo.push({...allObj})
              }
            })
          }
        })
        const triggres = []
        triggresOne.forEach((item) => {
          const config = triggresTwo.find(config => config?.visitId === item?.visitId)
          const data = {
            enableCompensation: item.enableCompensation,
            ...config
          }
          triggres.push(data)
        })

        const data = {
          configs,
          triggres,
          dctPeriodicCompensationId: state.compensationConfigAll?.dctPeriodicCompensationId || state.ruleForm?.id,
          visits: state.compensationConfigAll?.visits || [],
          subjects: state.compensationConfigAll?.subjects || [],
          isAdd: state.compensationConfigAll?.isAdd
        }
        const deployLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        state.loading = true
        putCompensationConfig(data).then(() => {
          ElMessage.success('保存成功')
          deployLoading.close()
          state.loading = false
          state.backRefresh()
          // state.compensationSet()
        }).catch(() => {
          state.loading = false
          deployLoading.close()
        })
      },
      // 进入页面加载，写在了onMounted中
      onLoad: async() => {
        state.applicationCenter()
        const enterLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        getPeriodicCompensationt(studyId, state.compensationIdCopy).then((res) => {
          enterLoading.close()
          if (RESEARCHCENTER_INFOS.setTitle === '新建' && state.compensationIdCopy === 'none') {
            state.studyArms = res.studyArms
            return
          }
          state.studyArms = res.studyArms
          if (res?.isAutomaticDistribution) {
            res.isAutomaticDistributionValue = ['1']
          } else {
            res.isAutomaticDistributionValue = []
          }
          res.checkboxnode = []
          if (res.dctPeriodicCompensationSubject?.length) {
            res.dctPeriodicCompensationSubject.forEach((item) => {
              state.subjectOptions.forEach((ite) => {
                if (item.subjectId === ite.subjectId) {
                  ite.dctPeriodicCompensationId = item.dctPeriodicCompensationId || state.ruleForm?.id
                  ite.id = item.id
                }
              })
              state.subjectOptions.forEach((ite) => {
                if (item.subjectId === ite.subjectId) {
                  res.checkboxnode.push(ite.subjectId)
                }
              })
            })
          }
          res.armCodeArr = res?.armCode ? res.armCode.split(',') : []
          if (res.status === 1) {
            res.statusBol = true
          } else {
            res.statusBol = false
          }
          state.shuttleValue = []
          res.dctPeriodicCompensationSiteConfig.forEach((item) => {
            state.shuttleValue.push(item.siteId)
            state.shuttleData.forEach((ite: any) => {
              if (ite.siteId === item.siteId) {
                ite.dctPeriodicCompensationId = item.dctPeriodicCompensationId
                ite.relationRole = item.relationRole
                ite.approvalProcessId = item.approvalProcessId
                ite.approvalProcessName = item.approvalProcessName
                ite.id = item.id
              }
            })
          })
          state.patientCompensateTabRef.tableData = res.dctPeriodicCompensationSiteConfig
          state.shuttleValueCopy = state.shuttleValue
          state.ruleForm = res
          state.versionIdCopy = res?.versionId
        }).catch(() => {
          enterLoading.close()
        })

        if (state.compensationIdCopy !== 'none') {
          state.compensationSet()
        }
      }
    })
    onMounted(() => {
      if (RESEARCHCENTER_INFOS.setTitle === '新建') {
        state.compensationIdCopy = 'none'
      } else if (RESEARCHCENTER_INFOS.setTitle === '编辑') {
        state.compensationIdCopy = RESEARCHCENTER_INFOS.researchContent?.id || ''
        state.versionIdCopy = RESEARCHCENTER_INFOS.researchContent?.versionId || ''
      }
      getDropStudyVersionsCRF(studyId).then((res) => {
        state.CRFoptions = res
      })
      getSimple(studyId).then((res) => {
        state.approvalList = res
      })
      state.onLoad()
    })
    return {
      RESEARCHCENTER_INFOS,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="less">
.PatientCompensateDetail {
  min-width: 1100px;
  overflow-y: auto;
  background: #fff;
  padding: 20px;
  position: relative;
}
:deep(.my-el-table) {
  padding: 0 !important;
}
.set-flag-title {
  border-bottom: 2px solid #d7d7d7;
}
.delete-pos-tr {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 999;
}
.approval {
  max-height: 450px;
  overflow: auto;
}
.tableData {
  :deep(.tablewrap2 .cell) {
    display: -webkit-box !important;
    word-break: break-all;
    word-wrap: break-word;
    overflow: hidden;
    /*…省略形式*/
    text-overflow: ellipsis;
    /*从上向下垂直排列子元素*/
    -webkit-box-orient: vertical;
    /*文本可分两行*/
    -webkit-line-clamp: 2;
  }
}
</style>
