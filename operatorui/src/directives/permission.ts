import { DirectiveBinding } from 'vue'
import store from '@/store'
// 自定义权限指令
function checkPermission(menuNode, value) {
  // 检查当前节点的menuId是否在目标权限数组中
  if (value.includes(menuNode.menuId)) {
    return true
  }
  // 递归检查子菜单
  if (menuNode.childSysMenus?.length) {
    return menuNode.childSysMenus.some(child => checkPermission(child, value))
  }
  return false
}
const permission = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    // 这里需要替换为实际获取用户权限的方法
    const systemPermissionTree = store.getters?.account?.userinfo?.systemPermissionTree || []
    if (value && value instanceof Array && value.length > 0) {
      const hasPermission = systemPermissionTree?.childSysMenus?.some(menuNode => checkPermission(menuNode, value))
      if (!hasPermission && !systemPermissionTree.menuId?.includes(value)) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      // throw new Error('使用方式：v-permission=[\'admin\', \'editor\']');
    }
  }
}

export default permission
