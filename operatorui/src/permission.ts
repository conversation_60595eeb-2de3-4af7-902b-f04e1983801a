import router from '@/router'
import store from '@/store'
import { TOKEN } from '@/store/modules/app' // TOKEN变量名

const getPageTitle = (title) => {
  const appTitle = store.getters.app.title
  if (title) {
    return `${title} - ${appTitle}`
  }
  return appTitle
}

// 白名单，里面是路由对象的name
const WhiteList = [
  'Login',
  // 'Study',
  'forbidden',
  'server-error',
  'not-found',
  'Lock',
  'ForgetPassword',
]

// vue-router4的路由守卫不再是通过next放行，而是通过return返回true或false或者一个路由地址
router.beforeEach(async (to: any) => {
  // console.log(to)
  document.title = getPageTitle(!!to.meta && to.meta.title)

  if (WhiteList.includes(to.name)) {
    return true
  }
  if (!window.localStorage[TOKEN]) {
    return {
      name: 'Login',
      query: {
        redirect: to.fullPath, // redirect是指登录之后可以跳回到redirect指定的页面
      },
      replace: true,
    }
  } else {
    // 获取用户角色信息，根据角色判断权限
    let userinfo = store.getters.account.userinfo
    if (!userinfo) {
      try {
        // 获取用户信息
        userinfo = await store.dispatch('account/getUserinfo')
      } catch (err) {
        return false
      }
    }

    // 判断是否处于锁屏状态
    if (to.name !== 'Lock') {
      const { authorization } = store.getters.app
      if (!!authorization && !!authorization.screenCode) {
        return {
          name: 'Lock',
          query: {
            redirect: to.path,
          },
          replace: true,
        }
      }
    }
    // 检查是否有 /home 页面
    // store.state.menu.menus.some(menu => menu.url === '/home')
    // console.log('store.state.menu',store.state.menu);
    if (to.query?.delAllTags === '1') {
      store.commit('setStudyItem', {})
      sessionStorage.setItem('studyInfo', `{}`)
    }
    if (!store.state.menu.menus?.length) {
      // 设置初始的左侧菜单栏
      store.dispatch(
        'menu/generateMenus',
        store.state.account.userinfo && store.state.account.userinfo.role
      )
    }
    if (!store.state.menu.menus.some(menu => menu?.url === '/home') && to.path === '/home') {
      // const store = useStore()
      // console.log(store.state.menu.menus, '打印菜单')
      if (store.state.menu.menus.length > 0) {
        const firstMenuUrl = store.state.menu.menus[0].url
        return { path: firstMenuUrl }
      }
    }

    // 如果没有权限，跳转到403页面
    // console.log(to.meta, to.meta.roles, userinfo, userinfo.role, '打印如果没权限的时候跳403')
    // if (
    //   !!to.meta &&
    //   !!to.meta.roles &&
    //   !to.meta.roles.includes(userinfo.role)
    // ) {
    //   return { path: '/403', replace: true }
    // }
  }
})
