<template>
  <el-config-provider :locale="locale">
    <router-view />
    <!-- 两下小时退登的弹框 -->
    <trial-dialog
      v-model="outLoginFlag.outLoginFlag"
      title="温馨提示"
      :my-dialog-body-style="myDialogBodyStyle"
      class="!z-[99999]"
    >
      <template #DialogBody>
        <div class="w-full mg-b-22-px centerflex flex-wrap">
          <div class="w-full py-8 centerflex text-red-600">
            您已连续操作两小时，为确保信息安全，请重新登录后再使用
          </div>
        </div>
      </template>
      <template #footer>
        <div class="w-full">
          <el-button
            class="w-full"
            size="large"
            type="primary"
            @click="outLogin"
          >重新登录</el-button>
        </div>
      </template>
    </trial-dialog>
  </el-config-provider>
</template>

<script>
// import * as echarts from 'echarts'
import { defineComponent, reactive, toRefs, onMounted, onBeforeUnmount } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'dayjs/locale/zh-cn'

export default defineComponent({
  name: 'App',
  setup() {
    // provide('ec', echarts)
    const store = useStore()
    const route = useRoute()
    const router = useRouter()
    const state = reactive({
      intervalId: null,
      locale: zhCn,
      outLoginFlag: {
        outLoginFlag: false
      },
      myDialogBodyStyle: {
        width: '30%',
        minHeight: '200px'
      },
      outLogin: () => {
        sessionStorage.setItem('studyInfo', '{}')
        localStorage.setItem('logingOutTime', '')
        state.outLoginFlag.outLoginFlag = false
        // 清除token
        store.dispatch('app/clearToken')
        store.commit('setStudyItem', '')
        // 清除标签栏
        store.dispatch('tags/delAllTags')
        router.push('/login')
      },
    })

    onMounted(() => {
      // 登录后 两小时后打开重新登录弹窗
      state.intervalId = setInterval(() => {
        const myLogingOutTime = localStorage.getItem('logingOutTime')
        if (store?.getters?.app?.authorization && route.path !== '/login') {
          // 1668146349000 - 1668146345000 = 4000 = 4秒
          // 1小时 =  60分钟 * 60秒 * 1000毫秒
          const timeDifference = 60 * 60 * 1000 * 2
          // const myLogingOutTimeNum = myLogingOutTime / 1
          const newDatems = Date.parse(Date())
          if (!myLogingOutTime || myLogingOutTime === 'null') {
            localStorage.setItem('logingOutTime', Date.parse(Date()))
          } else if (
            newDatems - myLogingOutTime / 1 > timeDifference &&
            !state?.outLoginFlag?.outLoginFlag &&
            route.path !== '/login'
          ) {
            state.outLoginFlag.outLoginFlag = true
            // console.log(newDatems - myLogingOutTime / 1)
          }
        } else if (state.outLoginFlag?.outLoginFlag) {
          state.outLoginFlag.outLoginFlag = false
        }
      }, 3000)
    })

    onBeforeUnmount(() => {
      if (state.intervalId) {
        clearInterval(state.intervalId)
      }
    })

    return {
      ...toRefs(state),
    }
  },
})
</script>

<style lang="less">
@import '@/style/common.less';
html,
body,
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
.el-overlay {
  z-index: 20000 !important;
}
.el-select {
  min-width: 150px;
}
// .w-e-text-container,.w-e-toolbar {
//   // z-index: 0 !important;
// }
div[data-we-id] {
  z-index: 0;
}

.w-e-droplist {
  max-height: 300px;
  overflow: auto;
}
</style>
