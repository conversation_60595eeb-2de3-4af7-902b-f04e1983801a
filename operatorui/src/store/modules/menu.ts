// import { allMenus } from '@/router'
import { getMenusByProject, transferMenuData } from '@/api/menu'
import store from '@/store'
// const hasPermission = (role, route) => {
//   if (!!route.meta && !!route.meta.roles && !route.meta.roles.includes(role)) {
//     return false
//   }
//   return true
// }

// const generateUrl = (path, parentPath) => {
//   return path.startsWith('/')
//     ? path
//     : path
//     ? `${parentPath}/${path}`
//     : parentPath
// }
// 方式一：根据角色生成菜单
// const getFilterMenus = (arr, role, parentPath = '') => {
//   const menus = []
//   arr.forEach(item => {
//     if (hasPermission(role, item) && !item.hidden) {
//       const menu = {
//         url: generateUrl(item.path, parentPath),
//         title: item.meta.title,
//         icon: item.icon,
//       }
//       if (item.children) {
//         if (item.children.filter(child => !child.hidden).length <= 1) {
//           menu.url = generateUrl(item.children[0].path, menu.url)
//         } else {
//           menu.children = getFilterMenus(item.children, role, menu.url)
//         }
//       }
//       menus.push(menu)
//     }
//   })

//   return menus
// }

export default {
  namespaced: true,
  state: {
    menus: [],
  },
  mutations: {
    SET_MENUS(state, data) {
      state.menus = data
    },
  },
  actions: {
    async generateMenus({ commit }, role, _studyId = '') {
      /* role 是当前账户角色，遍历菜单数组 判断角色权限是否包含indexOf，
      是则push进新数组，最终生成出页面展示的数组*/

      // // 方式一：根据角色生成菜单
      // const menus = getFilterMenus(allMenus, role)
      // commit('SET_MENUS', menus)

      const studyId = _studyId || store.state?.studyItem?.studyId || JSON.parse(sessionStorage.getItem('studyInfo') ?? '{}')?.studyId
      // console.log('studyId', _studyId, studyId, store.state?.studyItem, sessionStorage.getItem('studyInfo'))
      if (!studyId) {
        // const childSysMenus = [
        //   {
        //     menuId: 'study',
        //     menuName: '我的项目',
        //     menuUrl: '/study',
        //     isNavMenu: 1,
        //     childSysMenus: []
        //     // childSysMenus: [
        //     //   {
        //     //     isNavMenu: 1,
        //     //     menuId: 'studyList',
        //     //     menuName: '我的项目',
        //     //     menuUrl: '/study',
        //     //     childSysMenus: []
        //     //   },
        //     // ]
        //   },
        //   {
        //     menuId: 'studySetting',
        //     menuName: '项目设置',
        //     menuUrl: '/studySetting',
        //     isNavMenu: 1,
        //     childSysMenus: []
        //   },
        //   {
        //     menuId: 'roleManage',
        //     menuName: '角色管理',
        //     menuUrl: '/roleManage',
        //     isNavMenu: 1,
        //     childSysMenus: []
        //   },
        // ]
        // console.log(store.getters?.account?.userinfo?.systemPermissionTree?.childSysMenus)
        const operatoruiSystemPermissionTreeStr = localStorage.getItem('operatoruiSystemPermissionTree')
        let operatoruiSystemPermissionTree = {}
        if (operatoruiSystemPermissionTreeStr) {
          operatoruiSystemPermissionTree = JSON.parse(operatoruiSystemPermissionTreeStr)
        }
        const transferData = transferMenuData(store.getters?.account?.userinfo?.systemPermissionTree?.childSysMenus || operatoruiSystemPermissionTree?.childSysMenus || [])
        // console.log('transferData', transferData)
        commit('SET_MENUS', transferData)
        // alert(`transferData${JSON.stringify(transferData) }`)
        // console.log(`commit('SET_MENUS', transferData)`,transferData)
        return
      }
      if (!store.getters.app.authorization) {
        // commit('SET_MENUS', [
        //   {
        //     url: '/home',
        //     title: 'Dashboard',
        //     roles: ['SUBI', 'admin', 'PM', 'CRA', 'CRC', 'PI', 'DM', 'Audit'],
        //   },
        // ])
        return
      }
      getMenusByProject(studyId).then((res: any) => {
        // res.childSysMenus.push({
        //   menuId: 'test',
        //   menuName: '系统管理',
        //   menuUrl: '/test',
        //   childSysMenus: [
        //     // {
        //     //   menuId: 'testuser',
        //     //   menuName: '用户管理',
        //     //   menuUrl: '/test',
        //     //   childSysMenus: []
        //     // },
        //     {
        //       menuId: 'test.auth',
        //       menuName: '角色管理',
        //       menuUrl: '/testAuth',
        //       childSysMenus: []
        //     },
        //     {
        //       menuId: 'test.menu',
        //       menuName: '菜单管理',
        //       menuUrl: '/testMenu',
        //       childSysMenus: []
        //     }
        //   ]
        // })
        // console.log(res, 'childSysMenus')
        // 删除res.childSysMenus第一个元素
        // res.childSysMenus.shift()
        const transferData = transferMenuData(res?.childSysMenus || [])
        commit('SET_MENUS', transferData)
      })
    },
  },
}
