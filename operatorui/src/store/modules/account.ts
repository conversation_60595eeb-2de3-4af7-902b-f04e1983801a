// import { GetUserinfo } from '@/api/login'

export default {
  namespaced: true,
  state: {
    userinfo: null,
  },
  mutations: {
    // 保存用户信息
    setUserinfo(state, data) {
      state.userinfo = data
    },
    // 清除用户信息
    clearUserinfo(state) {
      state.userinfo = null
    },
  },
  actions: {
    // 获取用户信息
    async getUserinfo({ commit }) {
      const { code, data } = {
        code: 200,
        // message: '获取用户信息成功',
        data: {
          id: 1,
          name: 'admin-YJ',
          role: 'admin', // 返回一个角色
          mobile: '15224646618',
          avatar:'https://img0.baidu.com/it/u=1567854224,3210394646&fm=26&fmt=auto&gp=0.jpg',
        },
      } // await GetUserinfo()
      if (+code === 200) {
        commit('setUserinfo', data)
        return Promise.resolve(data)
      }
    },
  },
}
