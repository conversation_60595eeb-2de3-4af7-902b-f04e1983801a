<template>
  <div class="header">
    <div class="navigation">
      <logo v-if="device === 'mobile'" class="mobile" />
      <hamburger />
      <breadcrumbs />
    </div>
    <div class="action">
      <error-log />
      <userinfo />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import Logo from '@/layout/components/Sidebar/Logo.vue'
import Hamburger from '@/layout/components/Topbar/Hamburger.vue'
import Breadcrumbs from '@/layout/components/Topbar/Breadcrumbs.vue'
import Userinfo from '@/layout/components/Topbar/Userinfo.vue'
import ErrorLog from '@/components/ErrorLog/index.vue'
import { useStore } from 'vuex'

export default defineComponent({
  components: {
    Logo,
    Hamburger,
    Breadcrumbs,
    Userinfo,
    ErrorLog
  },
  setup() {
    const store = useStore()
    const device = computed(() => store.state.app.device)

    return {
      device
    }
  }
})
</script>
<style lang="scss" scoped>
.header {
  height: 48px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  .navigation {
    display: flex;
    align-items: center;
    overflow: hidden;
  }
  .action {
    display: flex;
    align-items: center;
  }
}
.mobile {
  padding-right: 0;
  &:deep(.logo) {
    max-width: 24px;
    max-height: 24px;
  }
  &:deep(.title) {
    display: none;
  }
}
</style>
