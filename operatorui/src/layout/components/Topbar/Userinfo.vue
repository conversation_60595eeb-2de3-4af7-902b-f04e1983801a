<template>
  <el-dropdown trigger="click" @command="handleCommand">
    <div class="userinfo">
      <template v-if="!userinfo">
        <i class="el-icon-user" />
        admin
      </template>
      <template v-else>
        <img v-if="userinfo.avatar" v-lazy="userinfo.avatar" class="avatar">
        {{ userinfo.name?userinfo.name:'设置' }}
      </template>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <!-- <el-dropdown-item command="a">个人中心</el-dropdown-item> -->
        <el-dropdown-item command="b">切换项目</el-dropdown-item>
        <el-dropdown-item command="c">
          <lock-modal ref="lockModalRef" />
        </el-dropdown-item>
        <el-dropdown-item command="d">退出登录</el-dropdown-item>
        <el-dropdown-item command="f">关于我们</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <trial-dialog 
    v-model="AboutusDialogVisible" 
    title="关于我们"
    myTitleClass="text-[18px]"
    :myDialogBodyStyle="{
      'width': '30%',
      'minHeight': '200px',
      'height': '200px'
    }"
    showClose
    closeOnClickModal
  >
    <template #DialogBody>
      <div class="text-center font-bold text-[#333]">{{ currentVersion }}</div>
    </template>
  </trial-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, nextTick, } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useUserinfo } from '@/components/Avatar/hooks/useUserinfo'
import LockModal from '@/layout/components/Topbar/LockModal.vue'
import { getCurrentVersion } from '@/api/login'

export default defineComponent({
  components: {
    LockModal,
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const { userinfo } = useUserinfo()
    const state = reactive({
      AboutusDialogVisible: false,
      currentVersion: '',
      lockModalRef: null,
      // 通用跳转-方法
      toPages: (path) => {
        if (path === '/login' || path === '/forgetPassword' && process.env.NODE_ENV !== 'development') {
          store.commit('setRefreshalg', true)
        }
        router.push(path)
      },
      // 退出
      logout: () => {
        localStorage.setItem('logingOutTime', '')
        // 清除token
        store.dispatch('app/clearToken')
        sessionStorage.setItem('studyInfo', `{}`)
        store.commit('setStudyItem', '')
        // 清除标签栏
        store.dispatch('tags/delAllTags')
        state.toPages('/login')
      },
      handleCommand: (command: string | number | Object) => {
        if (command === 'a') {
          state.toPages('/home/<USER>')
        } else if (command === 'b') {
          // 判断当前路由是/study就不继续执行
          // console.log(router.currentRoute.value.path, '当前路由是/study')
          if (router.currentRoute.value.path === '/study') {
            return
          }
          sessionStorage.setItem('studyInfo', '{}')
          store.commit('setStudyItem', '')
          // 清除标签栏
          store.dispatch('tags/delAllTags')
          store.dispatch(
            'menu/generateMenus',
            store.state.account.userinfo && store.state.account.userinfo.role
          )
          state.toPages('/study')
        } else if (command === 'c') {
          state.lockModalRef.dialogVisible = true
        } else if (command === 'd') {
          state.logout()
        } else if (command === 'f') {
          state.AboutusDialogVisible = true
          getCurrentVersion({
            appType: 9
          }).then((res: any) => {
            state.currentVersion = res
          })
        }
      }
    })

    onMounted(() => {
      if (!store?.state?.studyItem?.studyId) {
        // 没菜单 不退出登录
        // console.log('store?.state?.studyItem?.studyId', 'state.logout')
        // state.logout()
      }
    })

    return {
      userinfo,
      ...toRefs(state)
    }
  }
})
</script>

<style lang="scss" scoped>
.userinfo {
  min-width: 126px;
  padding: 0 16px;
  box-sizing: border-box;
  line-height: 48px;
  cursor: pointer;
  display: flex;
  align-items: center;
  &:hover {
    background: #f5f5f5;
  }
  .el-icon-user {
    font-size: 20px;
    margin-right: 8px;
  }
  .avatar {
    margin-right: 8px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }
}
</style>
