<template>
  <div class="wrapper">
    <sidebar />
    <div class="right">
      <div class="top">
        <topbar />
        <tagsbar />
      </div>
      <el-backtop
        target=".mainScoll"
        :right="20"
        :bottom="20"
      >
        <img src="@/assets/svg/back_top.svg" alt="">
      </el-backtop>
      <div class="main mainScoll">
        <Content />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import Sidebar from '@/layout/components/Sidebar/index.vue'
import Topbar from '@/layout/components/Topbar/index.vue'
import Tagsbar from '@/layout/components/Tagsbar/index.vue'
import Content from '@/layout/components/Content/index.vue'
import { useResizeHandler } from '@/layout/hooks/useResizeHandler'

export default defineComponent({
  name: 'Layout',
  components: {
    Sidebar,
    Topbar,
    Tagsbar,
    Content
  },
  setup() {
    useResizeHandler()
  }
})
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  height: 100%;

  .right {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .top {
      background: #fff;
    }
    .main {
      flex: 1;
      background: #f0f2f5;
      padding: 16px;
      overflow: auto;
    }
  }
}
</style>
