import request from '@/utils/request'
// 建立知情版本关联
export const postRelationBuilding = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/RelationBuilding`,
    method: "post",
    data
  });
};

// 知情 隐私 服务
// studyId,
// studyTextType : StudyPrivacy = 2, StudySLA = 3,StudyICF = 4, 
export const studyIcfPrivacySLA = (id, data) => {
  return request({
    url: `api/Operator/Study/${id}/Text`,
    method: "post",
    data
  });
};

// 知情
// studyId,
// studyTextType : StudyPrivacy = 2, StudySLA = 3,StudyICF = 4, 
export const studyKnow = (id) => {
  return request({
    url: `api/Operator/Study/${id}/Text?studyTextType=4`,
    method: "get"
  });
};

// 隐私
// studyId,
// studyTextType : StudyPrivacy = 2, StudySLA = 3,StudyICF = 4, 
export const studyPrivacy = (id,) => {
  return request({
    url: `api/Operator/Study/${id}/Text?studyTextType=2`,
    method: "get"
  });
};

// 服务
// studyId,
// studyTextType : StudyPrivacy = 2, StudySLA = 3,StudyICF = 4, 
export const studyService = (id) => {
  return request({
    url: `api/Operator/Study/${id}/Text?studyTextType=3`,
    method: "get"
  });
};

// 课题设置信息 studyVersionId ----->
export const getDropStudyId = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}`,
    method: "get",
  });
};

// 同步课题信息
export const getDropStudySyn = (id) => {
  return request({
    url: `api/Operator/Study/${id}/Sync`,
    method: "post",
  });
};

// 同步CRF版本
export const postDropStudySyncVersion = (studyid, id) => {
  return request({
    url: `api/Operator/Study/${studyid}/SyncVersion?versionNumber=${id}`,
    method: "post",
  });
};

// 获取入排问卷的模板 studyVersionId ----->
export const getIEQuestTemplateInfo = (id) => {
  return request({
    url: `api/Operator/Study/${id}/IEQuestTemplateInfo`,
    method: "get",
  });
};

// 提交测试版本（发布入排问卷）
export const postIEQuestTemplateInfo = (id) => {
  return request({
    url: `api/Operator/Study/${id}/IEQuestTemplate/Publish`,
    method: "put",
  });
};

// 重置测试版本
export const resetIEQuestTemplateInfo = (id) => {
  return request({
    url: `api/Operator/Study/IE/${id}/Reset`,
    method: "post",
  });
};

// 提交测试版本（发布CRF问卷）
export const postCrfPublish = (id) => {
  return request({
    url: `api/Operator/Study/CRF/${id}/Publish`,
    method: "post",
  });
};

// 重置测试版本（重置CRF问卷）
export const resetCrfPublish = (id) => {
  return request({
    url: `api/Operator/Study/CRF/${id}/Reset`,
    method: "post",
  });
};

// 保存入排
export const postIEQuestTemplate = (data) => {
  return request({
    url: `api/Operator/Study/IEQuestTemplate`,
    method: "post",
    data
  });
};
// 保存问卷基本信息
// {
//   "id": "string",
//   "dctModuleContainerId": "string",
//   "crfName": "string",
//   "questTemplateType": 0,
//   "isSynchronization": 0,
//   "questDisplayType": 0,
//   "questPatientTemplateType": 0,
//   "crfGuideline": "string"
// }
export const postQuestTemplate = (data) => {
  return request({
    url: `api/Operator/Study/QuestTemplate`,
    method: "post",
    data
  });
};

// 删除问卷题目questTemplateItemId
export const deleteQuestTemplateItem = (id) => {
  return request({
    url: `api/Operator/Study/QuestTemplateItem/${id}`,
    method: "delete",
  });
};

// 新增问卷题目基本信息
export const postQuestTemplateItem = (studyVersionId,data) => {
  return request({
    url: `api/Operator/Study/${studyVersionId}/QuestTemplateItem`,
    method: "post",
    data
  });
};
// 新增问卷题目基本信息 table
export const postQuestTemplateItemTable = (studyVersionId,data) => {
  return request({
    url: `api/Operator/Study/${studyVersionId}/QuestTemplateItem/Table`,
    method: "post",
    data
  });
};
// 保存问卷题目基本信息（修改）
// {
//   "id": "string",
//   "dctQuestTemplateId": "string",
//   "dctQuestItemDisplayType": 0,
//   "dctSort": 0,
//   "dctCode": "string",
//   "fieldLabel": "string",
//   "isRequired": 0,
//   "crfFieldControl": 0,
//   "crfFieldType": 0,
//   "dctQuestUnit": "string",
//   "specialFieldType": 0,
//   "fieldDescription": "string"
// }
export const putQuestTemplateItemBaseInfo = (data) => {
  return request({
    url: `api/Operator/Study/QuestTemplateItem/BaseInfo`,
    method: "put",
    data
  });
};
// 保存问卷题目基本信息table
export const putQuestTemplateItemBaseInfoTable = (data) => {
  return request({
    url: `api/Operator/Study/QuestTemplateItem/Table/BaseInfo`,
    method: "put",
    data
  });
};
// 修改题目关联条件
// {
//   "questTemplateItemId": "string",
//   "refDctCode": "string",
//   "refItemValue": "string",
//   "refType": 0
// }
export const putQuestTemplateItemRef = (data) => {
  return request({
    url: `api/Operator/Study/QuestTemplateItem/Ref`,
    method: "put",
    data
  });
};
// 修改table关联条件
export const putQuestTemplateItemRefTable = (data) => {
  return request({
    url: `api/Operator/Study/QuestTemplateItem/Table/Ref`,
    method: "put",
    data
  });
};

// 获取问卷基本信息下拉框数据信息
export const getQuestTemplateDropInfo = () => {
  return request({
    url: `api/Operator/Study/QuestTemplate/DropInfo`,
    method: "get",
  });
};

// 获取所有下拉框数据信息
/*
questStyleType 问卷样式信息
questFieldControl 问卷题目控件类型
questFieldType 问卷题目数据类型
questSpecialFieldType 问卷题目特殊类型
*/
export const getQuestTemplateItemDropInfo = () => {
  return request({
    url: `api/Operator/Study/QuestTemplateItem/DropInfo`,
    method: "get",
  });
};

// 获取某一条题目选项基本信息questTemplateOptionId
export const getQuestTemplateOption = (id) => {
  return request({
    url: `api/Operator/Study/${id}/QuestTemplateOption`,
    method: "get",
  });
};

// 删除题目选项 questTemplateOptionId
export const deleteQuestTemplateOption = (id) => {
  return request({
    url: `api/Operator/Study/QuestTemplateOption/${id}`,
    method: "delete",
  });
};

// 保存选项基本信息
// {
//   "id": "string",
//   "dctQuestItemId": "string",
//   "itemValue": "string",
//   "itemName": "string",
//   "itemContent": "string",
//   "gaugeValue": 0,
//   "sort": 0
// }
export const postQuestTemplateOption = (data) => {
  return request({
    url: `api/Operator/Study/QuestTemplateOption`,
    method: "post",
    data
  });
};

// 删除问卷模板questTemplateId
export const deleteQuestTemplate = (id) => {
  return request({
    url: `api/Operator/Study/QuestTemplate/${id}`,
    method: "delete",
  });
};

//  编辑crf ----------------->
// 获取课题版本
export const getDropStudyVersionsIE = (id) => {
  return request({
    url: `api/Operator/Study/DropStudyVersions/${id}?type=IE`,
    method: "get",
  });
};

export const getDropStudyVersionsCRF = (id) => {
  return request({
    url: `api/Operator/Study/DropStudyVersions/${id}?type=CRF`,
    method: "get",
  });
};
// studyVersionId--->
// 获取CRF表
export const getStudyCrf = (id) => {
  return request({
    url: `api/Operator/Study/CRF/${id}`,
    method: "get",
  });
};
// 获取CRF访视父模板
export const getVisitContainer = (id) => {
  return request({
    url: `api/Operator/Study/VisitContainer/${id}`,
    method: "get",
  });
};
// 删除CRF访视父模板
export const deleteVisitContainer = (id) => {
  return request({
    url: `api/Operator/Study/VisitContainer/${id}`,
    method: "delete",
  });
};
/** 保存CRF访视父模板
 * 
 * @param {
  "id": "string",
  "dctStudyVersionId": "string",
  "visitName": "string",
  "dctSort": "string",
  "edcXId": 0,
  "dctVisitDisplay": 0
}
 * @returns 
 */
export const postVisitContainer = (data) => {
  return request({
    url: `api/Operator/Study/VisitContainer`,
    method: "post",
    data
  });
};
// 获取CRF访视模板
export const getVisitTemplateId = (id) => {
  return request({
    url: `api/Operator/Study/VisitTemplate/${id}`,
    method: "get",
  });
};

// 删除CRF访视模板
export const deleteVisitTemplateId = (id) => {
  return request({
    url: `api/Operator/Study/VisitTemplate/${id}`,
    method: "delete",
  });
};

// 获取CRF访视特殊任务字段
export const getVisitTemplateDropInfo = () => {
  return request({
    url: `api/Operator/Study/VisitTemplate/DropInfo`,
    method: "get",
  });
};

// 保存CRF访视模板
export const getVisitTemplateDrop = (data) => {
  return request({
    url: `api/Operator/Study/VisitTemplate`,
    method: "post",
    data
  });
};

// 获取CRF模块问卷模板questTemplateId
export const getQuestTemplateInfoId = (id) => {
  return request({
    url: `api/Operator/Study/QuestTemplateInfo/${id}`,
    method: "get",
  });
};

// 获取题目
export const getQuestTemplateItem = (id) => {
  return request({
    url: `api/Operator/Study/QuestTemplateItem/${id}`,
    method: "get",
  });
};

// 获取CRF关联题目
export const getDropRefQuestTemplateItem = (id) => {
  return request({
    url: `api/Operator/Study/DropRefQuestTemplateItem/${id}`,
    method: "get",
  })
}
// 列表的关联题目
export const getDropRefQuestTemplateItemTable = (id) => {
  return request({
    url: `api/Operator/Study/DropRefQuestTemplateItem/Table/${id}`,
    method: "get",
  })
}

// 获取课题信息->自定义信息
export const getStudyInfo = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}`,
    method: "get",
  })
}

// 上传图片logo
export const postStudyFile = (studyId,data) => {
  return request({
    url: `api/Operator/Study/File/${studyId}`,
    method: "post",
    data
  })
}

// 保存自定义信息
export const postStudyCustomInfo = (data) => {
  return request({
    url: `api/Operator/Study/StudyCustomInfo`,
    method: "post",
    data
  })
}

// 获取省市区
export const getCitys = () => {
  return request({
    url: `api/Operator/Study/City`,
    method: "get",
  })
}

// 获取中心列表
export const getSites = (studyId,data) => {
  return request({
    url: `api/Operator/Study/${studyId}/Sites`,
    method: "get",
    params: { ...data }
  })
}

// 获取中心详情
export const getSiteDetail = (studyId,siteId) => {
  return request({
    url: `api/Operator/Study/${studyId}/SiteDetail/${siteId}`,
    method: "get"
  })
}

// 保存中心详情
export const postSite = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/Site`,
    method: "post",
    data
  })
}

// 常见问题
// 获取列表
export const getStudyFaqs = (id, data) => {
  return request({
    url: `api/Operator/Study/${id}/FaqsPagedList`,
    method: "get",
    params: { ...data }
  });
};

// 保存
export const postStudyFaq = (data) => {
  return request({
    url: `api/Operator/Study/Faq`,
    method: "post",
    data
  });
};



// 删除export const studyService = (id) => {
  export const deleteStudyFaq = (id) => {
    return request({
      url: `api/Operator/Study/Faq/${id}`,
      method: "delete"
    });
  };

  // 保存crf行的数据
  export const addModuleContainer = (data) => {
    return request({
      url: 'api/Operator/Study/ModuleContainer',
      method: 'post',
      data
    })
  }

  // 删除
  export const deleteModuleContainer = (id) => {
    return request({
      url: `api/Operator/Study/ModuleContainer/${id}`,
      method: 'delete'
    })
  }
  // 建立联系
  export const buildCrfRelation = (questTemplateId, visitTemplateId) => {
    return request({
      url: `api/Operator/Study/CRF/Build/${questTemplateId}/${visitTemplateId}`,
      method: 'post'
    })
  }
  // 删除联系
  export const deleteBuildCrfRelation = (questTemplateId, visitTemplateId) => {
    return request({
      url: `api/Operator/Study/CRF/Build/${questTemplateId}/${visitTemplateId}`,
      method: 'delete'
    })
  }

  // 上传问卷的HTML文件 questTemplateId
  export const postQuestFileHTML = (id, data) => {
    return request({
      url: `api/Operator/Study/QuestFile/${id}`,
      method: 'post',
      data
    })
  }
  /*查询医患绑定方式
  **/
  export const getDPAutomaticBind = (id) => {
    return request({
      url: `api/Operator/Study/${id}/DPAutomaticBind`,
      method: "get"
    })
  }
  /**保存绑定方式
   * 
   * @param studyId 
   * @param {
      "dpAutomaticBind": 0
    }
   * @returns 
   */
  export const postDPAutomaticBind = (id, data) => {
    return request({
      url: `api/Operator/Study/${id}/DPAutomaticBind`,
      method: 'post',
      data
    })
  }

// 获取关联子访视
export const getVisitTemplate = (studyVersionId, data) => {
  return request({
    url: `api/Operator/Study/${studyVersionId}/VisitTemplate/RefVisit`,
    method: 'get',
    params: { ...data }
  })
}

/**保存Dct角色信息
  * 
  * @param studyId 
  * @param {
    "userId": string
    "roles": string
  }
  * @returns 
  */
export const postDctUserRole = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/UserRole`,
    method: 'post',
    data
  })
}

/** 标签分页列表
  *
  * @param studyId
  * @param {
    "tagName": string
    "pageIndex": number
    "pageSize" number
  }
  * @returns
  */
export const getPagedList = (studyId, params) => {
  return request({
    url: `api/Operator/Study/${studyId}/PatientTag/PagedList`,
    method: 'get',
    params: params
  })
}

/** 保存受试者标签
  *
  * @param {
  "id": "string",
  "studyId": "string",
  "tagName": "string",
  "tagDesc": "string",
  "effectiveRule": 0,
  "createTime": "2024-08-30T05:49:10.983Z",
  "createUserName": "string",
  "lastUpdateTime": "2024-08-30T05:49:10.983Z",
  "lastUpdateUserName": "string"
  }
  * @returns
  */
export const postPatientTag = (data) => {
  return request({
    url: `api/Operator/Study/PatientTag`,
    method: 'post',
    data
  })
}

export const deletePatientTag = (id) => {
  return request({
    url: `api/Operator/Study/PatientTag/${id}`,
    method: 'delete',
  })
}

// 获取过程身份核验配置
export const getProcessIdentityVerification = (studyId: string) => {
  return request({
    url: `api/Operator/Study/${studyId}/IdentityVerification`,
    method: 'get'
  })
}

// 提交过程身份核验配置
export const postProcessIdentityVerification = (studyId: string, data: unknown) => {
  return request({
    url: `api/Operator/Study/${studyId}/IdentityVerification`,
    method: 'post',
    data
  })
}

// 获取签名 和 审阅配置
export const getQuestSigntureSet = (studyId: string, PurposeOfSignature: Number) => {
  return request({
    url: `api/Operator/Study/${studyId}/QuestSigntureSet`,
    method: 'get',
    params: {
      PurposeOfSignature: PurposeOfSignature
    }
  })
}

// 提交签名 和 审阅配置
export const postSetQuestSignture = (data: any) => {
  return request({
    url: `api/Operator/Study/SetQuestSignture`,
    method: 'post',
    data
  })
}
