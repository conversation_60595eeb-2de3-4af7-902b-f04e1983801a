import request from '@/utils/request'

// 给药方案列表
export const studyDrugScheme = (id) => {
  return request({
    url: `api/Operator/Study/${id}/DrugSchemes`,
    method: 'get'
  })
}

// 给药方案/给药周期
export const studyDrugDoseUnitDictionariesInfoTrue = (id) => {
  return request({
    url: `api/Operator/Study/DrugPeriodNodeInfo/${id}?isStartNode=1`,
    method: 'get'
  })
}

// 给药方案/给药周期
export const studyDrugDoseUnitDictionariesInfoFalse = (id) => {
  return request({
    url: `api/Operator/Study/DrugPeriodNodeInfo/${id}?isStartNode=0`,
    method: 'get'
  })
}

// 给药方案/服用药品信息
export const studyDropDrugInfos = (id) => {
  return request({
    url: `api/Operator/Study/DropDrugInfos/${id}`,
    method: 'get'
  })
}

// 给药方案/保存药品
export const studyDropScheme = (data) => {
  return request({
    url: `api/Operator/Study/DrugScheme`,
    method: 'post',
    data
  })
}

// 给药方案/删除药品
export const studyDeleteDrug = (id, data) => {
  return request({
    url: `api/Operator/Study/DrugScheme/${id}`,
    method: 'delete',
    data
  })
}

// 给药方案/药品信息
export const studyDrugInfo = (id) => {
  return request({
    url: `api/Operator/Study/DrugSchemeInfo/${id}`,
    method: 'get'
  })
}

// 给药方案/CRF版本信息
export const studyDropStudyVersions = (id) => {
  return request({
    url: `api/Operator/Study/DropStudyVersions/${id}`,
    method: 'get'
  })
}

// 给药方案/下拉分组信息 WithoutBlind=0代表显示盲态，WithoutBlind=1显示正常组别
export const studyStudyArmsInfo = (id) => {
  return request({
    url: `api/Operator/Study/StudyArmsInfo/${id}?WithoutBlind=1`,
    method: 'get'
  })
}

// 给药方案/根据版本Id获取问卷下拉数据
export const getQuestTemplateDropSource = (id) => {
  return request({
    url: `api/Operator/Study/${id}/QuestTemplate/DropSource`,
    method: 'get'
  })
}

//  给药方案/根据课题Id，获取课题下所有给药方案
export const getQuestTemplateDrugRegimenes = (id) => {
  return request({
    url: `api/Operator/Study/${id}/DrugRegimenes`,
    method: 'get'
  })
}

//  给药方案/保存新的给药方案数据
export const postQuestTemplateDrugRegimen = (id, data) => {
  return request({
    url: `api/Operator/Study/${id}/DrugRegimen`,
    method: 'post',
    data
  })
}

//  给药方案/获取新的给药方案详情数据
export const getQuestTemplateDrugRegimen = (id) => {
  return request({
    url: `api/Operator/Study/DrugRegimen/${id}`,
    method: 'get'
  })
}
