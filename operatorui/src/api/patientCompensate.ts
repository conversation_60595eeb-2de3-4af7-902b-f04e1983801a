import request from '@/utils/request'

// 获取自助申请信息
export const getSelfCompensationApply = (studyId) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/SelfCompensationApply`,
    method: 'get',
  })
}

// 保存自助申请信息
export const postSelfCompensationApply = (studyId, data) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/SelfCompensationApply`,
    method: 'post',
    data
  })
}

// 获取审批流
export const getSimple = (studyId) => {
  return request({
    url: `api/Operator/APR/${studyId}/Simple`,
    method: 'get',
  })
}

// 获取周期性补偿列表
export const getPeriodicCompensationList = (studyId, params) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/PeriodicCompensationList`,
    method: 'get',
    params
  })
}

// 获取周期性补偿基础信息
export const getPeriodicCompensationt = (studyId, compensationId) => {
  return request({
    url: `api/Operator/Compensation/${studyId}/${compensationId}/PeriodicCompensation`,
    method: 'get',
  })
}

// 保存周期性补偿基础信息
export const putPeriodicCompensation = (data) => {
  return request({
    url: `api/Operator/Compensation/PeriodicCompensation`,
    method: 'put',
    data
  })
}

// 获取周期性补偿配置信息
export const getCompensationConfig = (compensationId, versionId) => {
  return request({
    url: `api/Operator/Compensation/${compensationId}/${versionId}/CompensationConfig`,
    method: 'get',
  })
}

// 保存周期性补偿配置信息
export const putCompensationConfig = (data) => {
  return request({
    url: `api/Operator/Compensation/CompensationConfig`,
    method: 'put',
    data
  })
}
