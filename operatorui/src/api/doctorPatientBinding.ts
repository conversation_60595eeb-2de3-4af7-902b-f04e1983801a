import request from '@/utils/request'

/** 查询医患绑定列表
  */
  export const getUserSubordinates = (studyId,data) => {
    return request({
      url: `api/Operator/DoctorPatient/${studyId}/UserSubordinates`, 
      method: "get",
      params: { ...data }
    });
  };
/** 保存医患绑定列表
传入
{
  "dctSiteId": "string",
  "userType": 0,
  "userIds": [
    "string"
  ],
  "patientIds": [
    "string"
  ]
}
  */
export const postUserSubordinates = (studyId,data) => {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/UserSubordinates`, 
    method: "post",
    data
  });
};
/** 获取医患绑定穿梭框
  */
export const getUserSubordinatesTransfer = (studyId,data) => {
  return request({
    url: `api/Operator/DoctorPatient/${studyId}/UserSubordinates/Transfer`, 
    method: "get",
    params: { ...data }
  });
};

/*查询医生分组列表分页信息
  **/
export const getUserArmInfos = (id,data) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/UserArmInfos`, 
    method: "get",
    params: {...data}
  })
}
/**保存用户的分组信息
  * @param {
    "dctSiteId": "string",
    "dctUserIds": [
      "string"
    ],
    "armInfos": [
      {
        "armCode": "string",
        "armName": "string"
      }
    ]
  }
  * @returns  Boolean
  **/
export const postUserArmInfo = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatient/${id}/UserArmInfo`, 
    method: 'post',
    data
  })
}
  /**某课题下分组信息
  * @param studyId 
  * @returns  [{
        "id": "string",
        "armCode": "string",
        "armName": "string",
        "edcStudyArmId": 0,
        "requirepatcnt": 0,
        "remark": "string"
      }]
  **/
  export const getStudyArmsInfo = (id) => {
    return request({
      url: `api/Operator/Study/StudyArmsInfo/${id}`, 
      method: "get"
    })
  }