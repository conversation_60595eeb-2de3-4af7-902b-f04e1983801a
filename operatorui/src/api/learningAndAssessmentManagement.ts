import request from '@/utils/request'

// 获取学习资料下面的信息studyId=课题ID、userType=角色（0 未知 1 医生 2 患者）
export const getTrainingDocument = (id, data) => {
  return request({
    url: `api/Operator/Study/${id}/TrainingDocuments`,
    method: 'get',
    params: { ...data },
  })
}

// 获取分类标签集合（下拉框）
export const getTagDrop = (studyId) => {
  return request({
    url: `/api/Operator/Study/${studyId}/TrainingDocument/Tag`,
    method: 'get',
  })
}

// 上传学习资料PDF
export const postDocumentFile = (id, fileType, data) => {
  return request({
    url: `api/Operator/Study/${id}/Training/File?fileType=${fileType}`,
    method: 'post',
    data,
  })
}

// 保存学习资料
export const postTrainingDocument = (id, data) => {
  return request({
    url: `api/Operator/Study/${id}/TrainingDocument`,
    method: 'post',
    data,
  })
}

// 获取学习资料下面的信息studyId=课题ID、userType=角色（0 未知 1 医生 2 患者）
export const getTrainingDocumentDetails = (id) => {
  return request({
    url: `api/Operator/Study/TrainingDocument/${id}`,
    method: 'get',
  })
}

// 根据学习资料Id，获取学习资料对应的中心下发规则信息
export const getSiteRules = (trainingId) => {
  return request({
    url: `api/Operator/Study/${trainingId}/SiteRules`,
    method: 'get',
  })
}

// 获取课题下中心和学习资料适用中心规则信息（穿梭框）
export const getTransfer = (studyId, trainingId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/TrainingDocument/${trainingId}/Transfer`,
    method: 'get',
    params: { ...data },
  })
}

// 批量保存学习资料适用中心下发规则
export const putDistribution = (data) => {
  return request({
    url: `api/Operator/Study/TrainingDocument/Distribution`,
    method: 'put',
    data,
  })
}

/** 学习资料标签列表分页 *
 * 
 * @param studyId 
 * @param params {
  "tagName": string
  "pageIndex": number
  "pageSize" number
 }
 * @returns 
 */
export const getDcoumentTagPagedList = (studyId, params) => {
  return request({
    url: `/api/Operator/Study/${studyId}/TrainingDocument/Tag/PagedList`,
    method: 'get',
    params: params,
  })
}

/** 保存学习资料标签
 * 
 * @param data {
  "id": "string",
  "tagName": "string",
  "studyId": "string"
 }
 * @returns 
 */
export const postDocumentTag = (data) => {
  return request({
    url: `/api/Operator/Study/TrainingDocument/Tag`,
    method: 'post',
    data,
  })
}

/** 删除学习资料标签
 *
 * @param id
 * @returns
 */
export const deleteDocumentTag = (id) => {
  return request({
    url: `/api/Operator/Study/${id}/TrainingDocument/Tag`,
    method: 'delete',
  })
}
