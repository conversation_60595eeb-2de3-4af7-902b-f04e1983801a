import request from '@/utils/request'

/**
 * 获取图形验证码
 */
export const getImgVerifyCode = () => {
  return request({
    url: 'api/Home/GetImgVerifyCode',
    method: 'get',
  })
}

/**
 * 验证图形验证码
 * @param data
 * {
 *    "mobile": "string"
 *    "verifyCode": "string"
 *    "uuid": "string"
 * }
 * @returns
 */
export const imgVerifyCodeValidate = (data) => {
  return request({
    url: 'api/Home/ImgVerifyCodeValidate',
    method: 'post',
    data,
  })
}

/**
 * 校验短信验证码
 * {
 *    "mobile": "string",
 *    "verifyCode": "string"
 * }
 */
export const mobileVerifyCodeValidate = (data) => {
  return request({
    url: 'api/Operator/MobileVerifyCodeValidate',
    method: 'post',
    data,
  })
}

/**
 * 运营更改密码
 * {
 *     "mobile": "string",
 *     "verifyCode": "string",
 *     "password": "string",
 *     "confirmPassword": "string"
 * }
 */
export const resetPassword = (data) => {
  return request({
    url: 'api/Operator/ResetPassword',
    method: 'post',
    data,
  })
}
