import request from '@/utils/request'

// 获取审批流列表
export const getAPR = (studyId, params) => {
  return request({
    url: `api/Operator/APR/${studyId}`,
    method: 'get',
    params
  })
}

// 得到审批流列表详情
export const getAPRChart = (studyId, headCode) => {
  return request({
    url: `api/Operator/APR/${studyId}/${headCode}/APRChart`,
    method: 'get',
  })
}

// 保存审批流列表详情
export const postAPRChart = (studyId, headCode, data) => {
  return request({
    url: `api/Operator/APR/${studyId}/${headCode}/APRChart`,
    method: 'post',
    data
  })
}

// 获取角色列表
export const getRoles = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/Roles`,
    method: 'get',
  })
}

