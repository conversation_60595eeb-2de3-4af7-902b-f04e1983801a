import request from '@/utils/request'

// 获取已绑定的中心
export const getTemplateRelatedSite = (id) => {
  return request({
    url: `api/Operator/DoctorPatientICF/Template/${id}/RelatedSite?pageIndex=1&pageSize=1000`,
    method: 'get'
  })
}

// 获取课题下所有中心列表
export const getAllSites = (id) => {
  return request({
    url: `api/Operator/Study/${id}/AllSites`,
    method: 'get'
  })
}

// 获取知情同意书列表数据
export const getTemplates = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Templates`,
    method: 'get',
    params: { ...data }
  })
}

// 上传知情同意书PDFpostStudyICFFileInfo
export const postTemplateFile = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Template/File`,
    method: 'post',
    data
  })
}

// 保存/编辑知情同意书信息postStudyICFEditInfo
export const postTemplate = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Template`,
    method: 'post',
    data
  })
}

// 获取详情知情同意书信息getStudyICFDetails
export const getTemplateDetails = (id) => {
  return request({
    // url: `api/Operator/DoctorPatientICF/${id}/StudyICFDetails`,
    url: `api/Operator/DoctorPatientICF/Template/${id}/Details`,
    method: 'get'
  })
}

// 获取中心知情版本信息getStudySiteICFInfo
export const getTemplateSiteInfos = (id) => {
  return request({
    // url: `api/Operator/DoctorPatientICF/${id}/StudySiteICFInfo`,
    url: `api/Operator/DoctorPatientICF/Template/${id}/SiteInfos`,
    method: 'get'
  })
}

// 获取详情知情版本弹窗信息getStudyICFVersionNumber
export const getTemplateVersion = (id, siteId) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/Template/Version/${siteId}`,
    method: 'get'
  })
}

// 中心知情版本Id，获取中心知情版本详情
export const getTemplateSiteInfoDetails = (id) => {
  return request({
    url: `api/Operator/DoctorPatientICF/Template/${id}/SiteInfo/Details`,
    method: 'get'
  })
}

// 保存中心版本知情
export const postTemplateSiteInfo = (id, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/Template/${id}/SiteInfo`,
    method: 'post',
    data
  })
}

// 保存某段知情视频
export const postSaveStudyICFVideosInfo = (data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/SaveStudyICFVideosInfo`,
    method: 'post',
    data,
  })
}

// 删除知情某段视频
export const deleteStudyICFVideosInfo = (id) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/DeleteStudyICFVideosInfo`,
    method: 'delete'
  })
}

// 删除知情同意书某个版本
export const deleteDeleteStudyICFVersion = (id) => {
  return request({
    url: `api/Operator/DoctorPatientICF/${id}/DeleteStudyICFVersion`,
    method: 'delete'
  })
}
// 启用 - 禁用模板状态 {enableStatus: boolean}
export const postSignTemplateSetStatus = (signTemplateId, data) => {
  return request({
    url: `api/Operator/DoctorPatientICF/SignTemplate/${signTemplateId}/SetStatus`,
    method: 'post',
    data,
  })
}

// 获取模板 签署预览 地址
export const getSignTemplate = (studyICFId, params) => {
  return request({
    url: `api/Operator/DoctorPatientICF/Template/${studyICFId}/Url`,
    method: 'get',
    params: params
  })
}
