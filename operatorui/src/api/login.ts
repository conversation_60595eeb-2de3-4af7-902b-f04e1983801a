import request from '@/utils/request'

// 登录接口
/*发送手机验证码
{
  "mobile": "string"
}
*/
export const postSentVerifyCode = (data) => {
  return request({
    url: "api/Operator/SentVerifyCode",
    method: "post",
    data
  });
};

/*运营 - 手机号登录
{
  "mobile": "string",
  "verifyCode": "stri",
  "openId": "string"
}
*/
export const postLoginByCode = (data) => {
  return request({
    url: "api/Operator/LoginByCode",
    method: "post",
    data
  });
};

/*运营 - 账号密码登录
{
  "nameOrMail": "string",
  "password": "string",
  "openId": "string"
}
*/
export const Login = (data) => {
  return request({
    url: "api/Operator/LoginByName",
    method: "post",
    data
  });
};

// 获取登录用户信息
// export const GetUserinfo = () => {
//   return request({
//     url: '/api/userinfo',
//     method: 'get',
//   })
// }

// 获取课题（项目列表）UserStudyBreifInfo
export const getUserStudyBreifInfo = (id, params) => {
  return request({
    url: `api/Operator/Study/${id}/UserStudyBreifInfo`,
    method: "get",
    params: { ...params }
    // params: params
  });
};

// 获取用户对课题的权限
export const getPermission = (id) => {
  return request({
    url: `api/Operator/Study/${id}/UserStudyBreifInfo/Permission`,
    method: 'get'
  })
}

/**新建项目
{
  "edcStudyId": 0
}
 */
export const postNewStudy = (data) => {
  return request({
    url: `api/Operator/Study`,
    method: "post",
    data
  });
};

// 获取用户当前中心角色
export const getUserStudyPermission = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/UserStudyPermission`,
    method: 'get'
  })
}
// 获取版本号
export const getCurrentVersion = (params) => {
  return request({
    url: `api/Operator/CurrentVersion`,
    method: 'get',
    params: params
  })
}
