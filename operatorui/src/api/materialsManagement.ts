import request from '@/utils/request'

// 同步oms
export const getPullOmsMaterial = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/PullOmsMaterial`,
    method: 'get',
  })
}

// 获取课题下的物资列表
export const getMaterials = (studyId, params = {}) => {
  return request({
    url: `api/Operator/Study/${studyId}/Materials`,
    method: 'get',
    params: params // isPlan
  })
}

// 获取物资详情
export const getMaterial = (studyId, materialId) => {
  return request({
    url: `api/Operator/Study/${studyId}/Material/${materialId}`,
    method: 'get',
  })
}

// 获取物资映射关系
export const getMaterialMapping = (studyId, materialId) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialMapping/${materialId}`,
    method: 'get',
  })
}

// 保存物资映射关系
export const putMaterialMapping = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialMapping`,
    method: 'put',
    data
  })
}

// 获取OMS物资映射列表
export const getOMSMaterialMapping = (materialId) => {
  return request({
    url: `api/Operator/Study/${materialId}/MaterialMapping`,
    method: 'get',
  })
}

// 获取课题下的物资发放计划列表
export const getMaterialDistributionPlans = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialDistributionPlans`,
    method: 'get',
  })
}

// 获取物资发放计划详情
export const getMaterialDistributionPlan = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialDistributionPlan`,
    method: 'get',
    params: { ...data }
  })
}

// 保存物资发放计划
export const postMaterialDistributionPlan = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialDistributionPlan`,
    method: 'post',
    data
  })
}

// 获取人工补单的信息
export const getManualReplenishment = (studyId, materialId) => {
  return request({
    url: `api/Operator/Study/${studyId}/ManualReplenishment`,
    method: 'get',
    params: {
      materialId: materialId
    }
  })
}

// 保存人工补单
export const postManualReplenishment = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/ManualReplenishment`,
    method: 'put',
    data
  })
}

// 保存物资图片
export const postMaterialNew = (studyId, materialId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialNew/${materialId}`,
    method: 'post',
    data
  })
}

// 保存物资信息
export const postMaterialInfo = (studyId, materialId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/MaterialInfo/${materialId}`,
    method: 'post',
    data
  })
}
