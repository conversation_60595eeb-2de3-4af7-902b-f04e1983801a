import request from '@/utils/request'

/*研究对象*/ 
export const studyDrugs = (id) => {
    return request({
      url: `api/Operator/Study/${id}/Drugs`, 
      method: "get",
    });
  };

  /*研究对象/药品单位信息*/ 
export const studyDrugDoseUnitDictionariesInfo = () => {
  return request({
    url: `api/Operator/Study/DrugDoseUnitDictionariesInfo`, 
    method: "get",
  });
};

  /*研究对象/课题下拉分组信息*/ 
  export const studyArmsInfo = (id,data) => {
    return request({
      url: `api/Operator/Study/studyArmsInfo/${id}`, 
      method: "get",
      data
    });
  };

/*研究对象/保存药品*/ 
export const studyDrug = (data) => {
  return request({
    url: `api/Operator/Study/Drug`, 
    method: "post",
    data
  });
};

/*研究对象/药品信息*/ 
export const studyDrugInfoId = (id) => {
  return request({
    url: `api/Operator/Study/DrugInfo/${id}`, 
    method: "get",
  });
};

/*研究对象/删除药品*/ 
export const studyDeleteDrug = (id) => {
  return request({
    url: `api/Operator/Study/Drug/${id}`, 
    method: "delete",
  });
};

