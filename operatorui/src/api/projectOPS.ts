import request from '@/utils/request'

// 获取公告列表
export const getOutOfServiceConfigList = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/OutOfServiceConfigList`,
    method: 'get',
    params: { ...data }
  })
}

// 获取公告详情
export const getOutOfServiceConfig = (id) => {
  return request({
    url: `api/Operator/Study/${id}/OutOfServiceConfig`,
    method: 'get',
  })
}

// 保存公告
export const putOutOfServiceConfig = (data) => {
  return request({
    url: `api/Operator/Study/OutOfServiceConfig`,
    method: 'put',
    data
  })
}

// 删除公告
export const deleteDeleteOutOfServiceConfig = (id) => {
  return request({
    url: `api/Operator/Study/${id}/DeleteOutOfServiceConfig`,
    method: 'delete',
  })
}
