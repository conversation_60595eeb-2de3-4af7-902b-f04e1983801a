import request from '@/utils/request'

// 测试
export const TestError = () => {
  return request({
    url: '/api/500',
    method: 'get',
  })
}
/* 获取权限树列表 */
export const getPermissionTreeList = (params) => {
  return request({
    url: 'api/Operator/Permission/TreeList',
    method: 'get',
    params: params,
  })
}

/* 设置权限状态
  status： 权限状态（1激活，0失活）
*/
export const setPermissionStatus = (permissionRecordId, { status }) => {
  return request({
    url: `/api/Operator/Permission/${permissionRecordId}/SetStatus/${status}`,
    method: 'post'
  })
}
/* 获取权限详情 */
export const getPermissionDetail = (permissionRecordId) => {
  return request({
    url: `/api/Operator/Permission/${permissionRecordId}/Detail`,
    method: 'get',
  })
}
/* 保存权限 */
export const savePermission = (data) => {
  return request({
    url: 'api/Operator/Permission',
    method: 'post',
    data,
  })
}
/* 获取角色分页列表 */
export const getRolePagedList = (params) => {
  return request({
    url: 'api/Operator/Permission/Role/PagedList',
    method: 'get',
    params: params
  })
}
/* 设置角色状态 */
export const setRoleStatus = (data) => {
  return request({
    url: `/api/Operator/Permission/Role/SetStatus`,
    method: 'post',
    data,
  })
}
/* 获取角色详情 */
export const getRoleDetail = (roleId) => {
  return request({
    url: `/api/Operator/Permission/Role/${roleId}/Detail`,
    method: 'get',
  })
}
/* 获取角色绑定权限树 */
export const getRolePermissionTreeList = (roleId, params) => {
  return request({
    url: `/api/Operator/Permission/Role/${roleId}/RolePermissionTreeList`,
    method: 'get',
    params: params
  })
}
/* 保存角色关联权限 */
export const saveRoleAndRelationPermission = (data) => {
  return request({
    url: 'api/Operator/Permission/RoleAndRelationPermission',
    method: 'post',
    data,
  })
}
/* 获取角色分组列表 */
export const getRoleGroupList = () => {
  return request({
    url: '/api/Operator/Permission/RoleGroupList',
    method: 'get'
  })
}
