import request from '@/utils/request'

export const getCustomTasks = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/CustomTasks`,
    method: 'get',
  })
}

// 获取穿梭框中的数据
export const getCustomTasksTransfer = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/CustomTaskSiteRule/Transfer`,
    method: 'get',
    params: { ...data }
  })
}

// 新建保存数据接口api/Operator/Study/11/CustomTask
export const postCustomTask = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/CustomTask`,
    method: 'post',
    data
  })
}

// 获取详情页接口
export const getCustomTaskDetail = (customTaskId) => {
  return request({
    url: `api/Operator/Study/CustomTask/${customTaskId}`,
    method: 'get',
  })
}

// 获取自定义任务问卷（下拉框）api/Operator/Study/1/CustomTask/Quests/Drop
export const getCustomTaskQuestsDrop = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/CustomTask/Quests/Drop`,
    method: 'get',
  })
}

// 获取自定义任务问卷 api/Operator/Study/1/CustomTask/Quests
export const getCustomTaskQuests = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/CustomTask/Quests`,
    method: 'get',
    params: { ...data }
  })
}

// 获取问卷模板 /api/Operator/Study/1/CustomTask/Quest/2
export const getCustomTaskQuestsMessage = (studyId, questTemplateId) => {
  return request({
    url: `api/Operator/Study/${studyId}/CustomTask/Quest/${questTemplateId}`,
    method: 'get',
  })
}

// 保存课题下自定义问卷api/Operator/Study/1/CustomTask/Quest
export const postCustomTaskQuests = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/CustomTask/Quest`,
    method: 'post',
    data
  })
}

// 获取核查规则
export const getQuestTemplateVerificationRule = (ruleId) => {
  return request({
    url: `api/Operator/Study/${ruleId}/QuestTemplate/VerificationRules`,
    method: 'get',
  })
}
/* 保存核查规则
 **params {
  "id": "string",
  "dctQuestTemplateId": "string",
  "isEnable": true,
  "verificationRuleLevel": 0,
  "verificationRuleCode": "string",
  "isDeleted": true
}
*/
export const postQuestTemplateVerificationRule = (data) => {
  return request({
    url: `api/Operator/Study/QuestTemplateVerificationRule`,
    method: 'post',
    data
  })
}
// 删除问卷核查规则
export const deleteQuestTemplateVerificationRule = (ruleId) => {
  return request({
    url: `api/Operator/Study/QuestTemplateVerificationRule/${ruleId}`,
    method: 'delete',
  })
}
// 获取角色列表
export const getRoles = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/Roles`,
    method: 'get',
  })
}
