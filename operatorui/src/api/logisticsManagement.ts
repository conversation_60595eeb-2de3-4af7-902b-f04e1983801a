import request from '@/utils/request'

// 同步oms
export const getPullOmsGoodsType = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/PullOmsGoodsType`,
    method: 'get',
  })
}

// 获取渠道
export const getChannels = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/Channels`,
    method: 'get',
    params: { ...data }
  })
}

// 保存数据/GoodsTypeAndSiteLogisticsConfigs
export const putGoodsTypeAndSiteLogisticsConfigs = (studyId, data) => {
  return request({
    url: `api/Operator/Study/${studyId}/GoodsTypeAndSiteLogisticsConfigs`,
    method: 'put',
    data
  })
}

// 获取详情/api/Operator/Study/1/GoodsTypeDetail/1
export const getGoodsTypeDetail = (studyId, goodsTypeId) => {
  return request({
    url: `/api/Operator/Study/${studyId}/GoodsTypeDetail/${goodsTypeId}`,
    method: 'get',
  })
}

// 获取列表
export const getGoodsType = (studyId) => {
  return request({
    url: `api/Operator/Study/${studyId}/GoodsType`,
    method: 'get',
  })
}
