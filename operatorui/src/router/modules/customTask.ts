const Layout = () => import('@/layout/index.vue')
const customTask = () => import('@/views/customTask/index.vue')
const customTaskQuestionnaire = () => import('@/views/customTask/CustomTaskQuestionnaire.vue')

export default [
  {
    path: '/customTask',
    component: Layout,
    name: '',
    meta: {
      title: '自定义任务管理',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/customTask',
        name: 'CustomTask',
        component: customTask,
        hidden: true,
        meta: {
          title: '自定义任务-受试者',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/customTaskQuestionnaire',
        name: 'CustomTaskQuestionnaire',
        component: customTaskQuestionnaire,
        meta: {
          title: '自定义问卷',
          roles: ['admin', 'visitor'],
        },
      },
    ],
  },
]
