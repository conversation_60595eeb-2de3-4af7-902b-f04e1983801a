const Layout = () => import('@/layout/index.vue')
const informedManagement = () => import('@/views/informedManagement/index.vue')
const informedVersion = () => import('@/views/informedManagement/InformedVersion.vue')

export default [
  {
    path: '/informedManagement',
    component: Layout,
    name: '',
    meta: {
      title: '知情管理',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/informedManagement',
        name: 'InformedManagement',
        component: informedManagement,
        meta: {
          title: '知情同意书',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/informedVersion',
        name: 'InformedVersion',
        component: informedVersion,
        meta: {
          title: '中心知情版本',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
