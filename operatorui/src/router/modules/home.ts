const Layout = () => import('@/layout/index.vue')
const Home = () => import('@/views/home/<USER>')
const Personal = () => import('@/views/home/<USER>')
const PrivacyAgreement = () => import('@/views/home/<USER>')
const TermsService = () => import('@/views/home/<USER>')
const CommonProblem = () => import('@/views/home/<USER>')
// const SubjectSetting = () => import('@/views/home/<USER>/index.vue')
const EditQuestionnaire = () => import('@/views/home/<USER>/editQuestionnaire/index.vue')
const SupervisionManagement = () => import('@/views/home/<USER>/SupervisionManagement.vue')
const Informed = () => import('@/views/home/<USER>')
const CustomInformation = () => import('@/views/home/<USER>/customInformation/index.vue')
const ResearchCenter = () => import('@/views/home/<USER>/researchCenter/index.vue')
const DoctorPatientBonding = () => import('@/views/home/<USER>')
const GroupingProcess = () => import('@/views/home/<USER>/groupingProcess/index.vue')
const CustomLabel = () => import('@/views/home/<USER>')

const ProcessIdentityVerification = () => import('@/views/home/<USER>')

const QuestionnaireSignature = () => import('@/views/home/<USER>')

const QuestionnaireReviewMethod = () => import('@/views/home/<USER>')

import store from '@/store'

export default [
  {
    path: '/home',
    component: Layout,
    name: '项目设置',
    meta: {
      title: '',
    },
    icon: 'home',
    beforeEnter: () => { // 路由守卫,通过审核后可以进入
      // console.log(store.state?.studyItem, 'store?.getters?.account?.userinfo?.token',store?.getters?.account?.userinfo?.token);
      if (store?.getters?.account?.userinfo?.token) {
        if (!store.state?.studyItem) {
          return { path: '/study' }
        } else {
          return true
        }
      } else {
        return { path: '/login' }
      }
    },
    children: [
      // EditQuestionnaire
      // {
      //   path: '/home/<USER>',
      //   name: 'SubjectSetting',
      //   component: SubjectSetting,
      //   // redirect: '/home/<USER>/editQuestionnaire',
      //   meta: {
      //     title: 项目设置',
      //     // roles: ['admin', 'visitor'],
      //   },
      //   // children: [
      //   // ],
      // },
      {
        path: '/doctorPatientBonding',
        name: 'DoctorPatientBonding',
        component: DoctorPatientBonding,
        meta: {
          title: '医患绑定方式',
        },
      },
      {
        path: '/processIdentityVerification',
        name: 'ProcessIdentityVerification',
        component: ProcessIdentityVerification,
        meta: {
          title: '过程身份核验',
        },
      },
      // 问卷签署
      {
        path: '/questionnaireSignature',
        name: 'QuestionnaireSignature',
        component: QuestionnaireSignature,
        meta: {
          title: '问卷签署',
        },
      },
      // 问卷审阅
      // Questionnaire review method
      {
        path: '/questionnaireReviewMethod',
        name: 'QuestionnaireReviewMethod',
        component: QuestionnaireReviewMethod,
        meta: {
          title: '问卷审阅',
        },
      },
      {
        path: 'customInformation',
        name: 'CustomInformation',
        component: CustomInformation,
        meta: {
          title: '自定义信息',
        },
      },
      {
        path: 'researchCenter',
        name: 'ResearchCenter',
        component: ResearchCenter,
        meta: {
          title: '研究中心',
        },
      },
      {
        path: 'groupingProcess',
        name: 'GroupingProcess',
        component: GroupingProcess,
        meta: {
          title: 'DCT入组',
        },
      },
      {
        path: 'editQuestionnaire',
        name: 'EditQuestionnaire',
        component: EditQuestionnaire,
        meta: {
          title: '编辑初筛问卷',
          // roles: ['admin','visitor'], //'admin',
        },
      },
      {
        path: 'supervisionManagement',
        name: 'SupervisionManagement',
        component: SupervisionManagement,
        meta: {
          title: '编辑CRF',
          // roles: ['admin', 'visitor'],
        },
      },
      {
        path: '',
        name: 'home',
        component: Home,
        meta: {
          title: '项目设置', // informed
          affix: true,
        },
      },
      // PrivacyAgreement隐私协议
      // TermsService 服务条款
      {
        path: '/informed',
        name: 'Informed',
        component: Informed,
        meta: {
          title: '知情同意书',
        },
        hidden: true,
      },
      {
        path: '/customLabel',
        name: 'CustomLabel',
        component: CustomLabel,
        meta: {
          title: '用户标签',
        },
        hidden: true,
      },
      {
        path: '/privacyAgreement',
        name: 'PrivacyAgreement',
        component: PrivacyAgreement,
        meta: {
          title: '隐私协议',
        },
        hidden: true,
      },
      {
        path: '/termsService',
        name: 'TermsService',
        component: TermsService,
        meta: {
          title: '服务条款',
        },
        hidden: true,
      },
      {
        path: '/commonProblem',
        name: 'CommonProblem',
        component: CommonProblem,
        meta: {
          title: '常见问题',
        },
        hidden: true,
      },
      {
        path: 'personal',
        name: 'Personal',
        component: Personal,
        meta: {
          title: '个人中心',
        },
        hidden: true,
      },
    ],
  },
]
