// import store from '@/store'
const Layout = () => import('@/layout/index.vue')
const Study = () => import('@/views/login/study.vue')
const StudySetting = () => import('@/views/login/StudySetting.vue')
const StudyRoleSetting = () => import('@/views/login/StudyRoleSetting.vue')
const RoleManage = () => import('@/views/login/RoleManage.vue')

export default [
//   {
//     path: '/study',
//     name: 'Study',
//     component: Study,
//   },
  {
    path: '/study',
    component: Layout,
    // redirect: '/study/studyList',
    meta: {
      title: '我的项目',
    },
    roles: ['admin', 'visitor'],
    // beforeEnter: () => {
    //   console.log(store.state, 'store.state')
    //   if (!store.state?.menu?.menus?.length) {
    //     store.dispatch(
    //       'menu/generateMenus',
    //       store.state.account.userinfo && store.state.account.userinfo.role
    //     )
    //     return { path: '/study' }
    //   }
    // },
    children: [
      {
        path: '/study',
        name: 'Study',
        component: Study,
        meta: {
          title: '我的项目',
          roles: ['admin', 'visitor'],
        }
      },
    ],
  },
  {
    path: '/studySetting',
    component: Layout,
    // redirect: '/study/studyList',
    meta: {
      title: '项目设置',
    },
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/studySetting',
        name: 'StudySetting',
        component: StudySetting,
        meta: {
          title: '项目设置',
          roles: ['admin', 'visitor'],
        }
      },
      {
        path: '/studyRoleSetting',
        name: 'StudyRoleSetting',
        component: StudyRoleSetting,
        meta: {
          title: '项目角色设置',
          roles: ['admin', 'visitor'],
        }
      },
    ],
  },
  {
    path: '/roleManage',
    component: Layout,
    // redirect: '/study/studyList',
    meta: {
      title: '角色管理',
    },
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/roleManage',
        name: 'RoleManage',
        component: RoleManage,
        meta: {
          title: '角色管理',
          roles: ['admin', 'visitor'],
        }
      },
    ],
  },
]
