const Layout = () => import('@/layout/index.vue')
const materialsManagement = () => import('@/views/materialsManagement/index.vue')
const releaseplan = () => import('@/views/materialsManagement/releaseplan/index.vue')

export default [
  {
    path: '/materialsManagement',
    component: Layout,
    name: '',
    meta: {
      title: '物资管理',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/materialsManagement',
        name: 'MaterialsManagement',
        component: materialsManagement,
        meta: {
          title: '物资列表',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/releaseplan',
        name: 'Releaseplan',
        component: releaseplan,
        meta: {
          title: '发放计划',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
