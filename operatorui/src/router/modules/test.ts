const Layout = () => import('@/layout/index.vue')
const List = () => import('@/views/test/index.vue')
const Auth = () => import('@/views/test/Auth.vue')
const MenuMnagement = () => import('@/views/test/MenuMnagement.vue')
// const Nest = () => import('@/views/test/Nest.vue')
// // const Product = () => import('@/views/test/nest/product.vue')
// const Calendar = () => import('@/views/test/nest/Calendar.vue')
const Iscache = () => import('@/views/test/Cache.vue')
const Nocache = () => import('@/views/test/Nocache.vue')
const ErrorLog = () => import('@/views/test/error-log/index.vue')
const Dragover = () => import('@/views/test/dragover.vue')
const AutoGeneratedCode = () => import('@/views/test/AutoGeneratedCode.vue')

export default [
  {
    path: '/test',
    component: Layout,
    name: 'test',
    meta: {
      title: '权限管理',
    },
    // icon: 'el-icon-location',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '',
        name: 'TestList',
        component: List,
        meta: {
          title: '用户管理',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/testAuth',
        name: 'TestAuth',
        component: Auth,
        meta: {
          title: '角色管理',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/testMenu',
        name: 'MenuMnagement',
        component: MenuMnagement,
        meta: {
          title: '菜单管理',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: 'cache',
        name: 'TestCache',
        component: Iscache,
        meta: {
          title: '该页面可缓存',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: 'dragover',
        name: 'TestDragover',
        component: Dragover,
        meta: {
          title: '拖拽测试',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: 'nocache',
        name: 'TestNoCache',
        component: Nocache,
        meta: {
          title: '该页面不缓存',
          roles: ['admin', 'visitor'],
          noCache: true, // 不缓存页面
        },
      },
      {
        path: 'auto',
        name: 'AutoGeneratedCode',
        component: AutoGeneratedCode,
        meta: {
          title: '一键自动生成',
          roles: ['admin', 'visitor'],
        },
      },
      // {
      //   path: 'nest',
      //   name: 'nest',
      //   component: Nest,
      //   redirect: '/test/nest/product',
      //   meta: {
      //     title: '功能菜单',
      //     roles: ['admin', 'visitor'],
      //   },
      //   children: [
      //     {
      //       path: 'product',
      //       name: 'Product',
      //       component: Product,
      //       meta: {
      //         title: '产品菜单',
      //         roles: ['admin','visitor'], //'admin',
      //       },
      //     },
      //     {
      //       path: 'calendar',
      //       name: 'Calendar',
      //       component: Calendar,
      //       meta: {
      //         title: '日历',
      //         roles: ['admin', 'visitor'],
      //       },
      //     },
      //   ],
      // },
      {
        path: 'error-log',
        name: 'TestErrorLog',
        component: ErrorLog,
        meta: {
          title: '测试错误日志',
          roles: ['admin', 'visitor'],
        },
      },
    ],
  },
]
