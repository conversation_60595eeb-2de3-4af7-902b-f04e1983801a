const Layout = () => import('@/layout/index.vue')
const patientCompensate = () => import('@/views/patientCompensate/index.vue')
const selfHelpApply = () => import('@/views/patientCompensate/SelfHelpApply.vue')

export default [
  {
    path: '/patientCompensate',
    component: Layout,
    name: '',
    meta: {
      title: '受试者补偿/报销',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/patientCompensate',
        name: 'PatientCompensate',
        component: patientCompensate,
        meta: {
          title: '周期性补偿',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/selfHelpApply',
        name: 'SelfHelpApply',
        component: selfHelpApply,
        meta: {
          title: '自助申请',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
