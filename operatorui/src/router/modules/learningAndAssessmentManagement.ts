import component from '../../shims-vue'
const Layout = () => import('@/layout/index.vue')
const learningAndAssessmentManagement = () =>
  import('@/views/learningAndAssessmentManagement/index.vue')
const doctorInformation = () =>
  import('@/views/learningAndAssessmentManagement/DoctorInformation.vue')
const documentTag = () =>
  import('@/views/learningAndAssessmentManagement/DocumentTag.vue')

export default [
  {
    path: '/learningAndAssessmentManagement',
    component: Layout,
    name: '',
    meta: {
      title: '学习&测评管理',
    },
    // icon: 'el-icon-message',
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '/learningAndAssessmentManagement',
        name: 'LearningAndAssessmentManagement',
        component: learningAndAssessmentManagement,
        meta: {
          title: '学习资料-受试者端',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/documentTag',
        name: 'DocumentTag',
        component: documentTag,
        meta: {
          title: '标签管理',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/doctorInformation',
        name: 'DoctorInformation',
        component: doctorInformation,
        meta: {
          title: '学习资料-研究者端',
          roles: ['admin', 'visitor'],
        },
      },
    ],
  },
]
