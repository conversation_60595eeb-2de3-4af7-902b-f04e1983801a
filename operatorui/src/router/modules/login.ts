// import { getItem } from '@/utils/storage'
import store from '@/store'
const Login = () => import('@/views/login/index.vue')
const ForgetPassword = () => import('@/views/login/ForgetPassword.vue')
// const Study = () => import('@/views/login/study.vue')

export default [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    beforeEnter: () => { // 无token时才可以进入登录页!getItem('VEA-TOKEN')
      var url = window.location.href// 获取url地址
      const flag = url.split('?flag=')[1]// 获取newStore后面的参数
      if (Number(flag) === 2) {
        // 清除token
        store.dispatch('app/clearToken')
        store.commit('setStudyItem', '')
        // 清除标签栏
        store.dispatch('tags/delAllTags')
      }
      if (!store?.getters?.account?.userinfo?.token) {
        return true
      } else {
        return false
      }
    }
  },
  {
    path: '/forgetPassword',
    name: 'ForgetPassword',
    component: ForgetPassword,
  },
  // {
  //   path: '/study',
  //   name: 'Study',
  //   component: Study,
  // },
  // {
  //   path: '/study',
  //   component: Layout,
  //   redirect: '/study/studyList',
  //   children: [
  //     {
  //       path: 'studyList',
  //       name: 'Study',
  //       component: Study,
  //       meta: {
  //         title: '我的项目',
  //         // roles: ['admin', 'visitor'],
  //       },
  //     },
  //   ]
  // },
]
