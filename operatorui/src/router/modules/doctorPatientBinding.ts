const Layout = () => import('@/layout/index.vue')
const DoctorPatientBinding = () => import('@/views/doctorPatientBinding/index.vue')
const DoctorList = () => import('@/views/doctorPatientBinding/DoctorList.vue')

export default [
  {
    path: '/doctorPatientBinding',
    component: Layout,
    name: '',
    meta: {
      title: '医患绑定',
    },
    roles: ['admin', 'visitor'],
    children: [
      {
        path: '',
        name: 'DoctorPatientBinding',
        component: DoctorPatientBinding,
        meta: {
          title: '医患绑定',
          roles: ['admin', 'visitor'],
        },
      },
      {
        path: '/doctorList',
        name: 'DoctorList',
        component: DoctorList,
        meta: {
          title: '医生列表',
          roles: ['admin', 'visitor'],
        },
      }
    ],
  },
]
