import { createRouter, createWebHashHistory } from 'vue-router'
import redirect from '@/router/modules/redirect'
import error from '@/router/modules/error'
import login from '@/router/modules/login'
import study from '@/router/modules/study'
import lock from '@/router/modules/lock'
import home from '@/router/modules/home'
import test from '@/router/modules/test'
import informedManagement from '@/router/modules/informedManagement'
import dosageRegimenManagement from '@/router/modules/dosageRegimenManagement'
import studyObjectManagement from '@/router/modules/studyObjectManagement'
import learningAndAssessmentManagement from '@/router/modules/learningAndAssessmentManagement'
import doctorPatientBinding from '@/router/modules/doctorPatientBinding'
import customTask from '@/router/modules/customTask'
import logisticsManagement from '@/router/modules/logisticsManagement'
import materialsManagement from '@/router/modules/materialsManagement'
import projectOPS from '@/router/modules/projectOPS'
import patientCompensate from '@/router/modules/patientCompensate'
import approvalProcess from '@/router/modules/approvalProcess'
import riskControlManagement from '@/router/modules/riskControlManagement'

// 左侧菜单
export const allMenus = [...home, ...test]

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    ...redirect, // 统一的重定向配置
    ...login,
    ...study,
    ...studyObjectManagement,
    ...informedManagement,
    ...dosageRegimenManagement,
    ...allMenus,
    ...error,
    ...lock,
    ...learningAndAssessmentManagement,
    ...doctorPatientBinding,
    ...customTask,
    ...logisticsManagement,
    ...materialsManagement,
    ...projectOPS,
    ...patientCompensate,
    ...approvalProcess,
    ...riskControlManagement,
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { left: 0, top: 0 }
    }
  },
})

export default router
