import { createApp } from 'vue'
import App from '@/App.vue'
import '@/assets/style/element-variables.scss'
import 'element-plus/dist/index.css'
// 引入路由
import router from '@/router'
// 路由懒加载
import lazyPlugin from 'vue3-lazy'
// 引入store
import store from '@/store'
// 权限控制
import '@/permission'
// 引入svg图标注册脚本
import 'vite-plugin-svg-icons/register'
// 缓存vuex
import initStorePersistence from '@/store/persistence'
// 引入tailwindcss
import '@/style/tailwind.less'
import 'default-passive-events'
initStorePersistence(store) // 缓存vuex
import trialTable from '@trialdata/table'
import '@trialdata/table/style.css'
import trialWangEditor from '@trialdata/wang-editor'
import '@trialdata/wang-editor/style.css'
import trialDialog from '@trialdata/dialog'
import '@trialdata/dialog/style.css'
import permission from '@/directives/permission';

// 在打开时,插入配置选项, 这个目前只在打开的第一次执行, 也许可以设置一个5分钟轮询,
// 来检查是否有新版本, 要更新系统
// const scriptElement = document.createElement('script')
// scriptElement.src = '/operatorui/config.js?v=' + new Date().toISOString().slice(5, 16)
// document.body.appendChild(scriptElement)

const app = createApp(App)
// 注册全局组件
import * as Components from '@/global-components'
Object.entries(Components).forEach(([key, component]) => {
  app.component(key, component)
})

// 错误日志
import useErrorHandler from '@/error-log'
useErrorHandler(app)
// 注册自定义指令
app.directive('permission', permission)
app
  .use(lazyPlugin, {
    loading: 'loading.gif',
    error: 'error.jpg',
  })
  .use(trialWangEditor)
  .use(trialTable)
  .use(trialDialog)
  .use(store)
  .use(router)
  .mount('#app')
